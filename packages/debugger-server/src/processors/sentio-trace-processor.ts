import {
  DecodedCall,
  DecodedCallTrace,
  DecodedDynamicLog,
  LocationWithInstructionIndex,
  SentioCallTrace,
  SentioExternalCallTrace,
  SentioLogTrace,
  SentioRootCallTrace,
  SourceInfo
} from '@sentio/debugger-common'
import { AbiProvider } from '../providers/abi-provider'
import { SourceInfoProvider } from '../providers/source-info-provider'
import { decodeCall, decodeCallInput, decodeCallOutput, decodedResult } from '../decoders/call-decoder'
import { decodeLog } from '../decoders/log-decoder'
import { ContractMetadataProvider } from '../providers/contract-metadata-provider'
import { decodeInternalCallInputsOutputs } from '../decoders/internal-call-decoder'
import * as Codec from '@truffle/codec'
import { PreProcessorResult } from './compilation-pre-processor'
import { SolidityServiceDefinition } from '../gen/service/solidity/protos/solidity'
import { Client } from 'nice-grpc'

export class SentioTraceProcessor {
  sourceInfoProvider: SourceInfoProvider
  abiProvider: AbiProvider
  // codeProvider: CodeProvider
  contractMetadataProvider: ContractMetadataProvider
  consoleAddress?: string

  constructor(
    preProcessResult: PreProcessorResult,
    sourceInfo: { [key: string]: SourceInfo } | undefined,
    solidityClient?: Client<SolidityServiceDefinition>,
    consoleAddress?: string
    // codes: Record<string, string>,
    // web3Instance: ExtendedWeb3
  ) {
    this.abiProvider = new AbiProvider(preProcessResult, solidityClient)
    // this.codeProvider = new CodeProvider(codes, web3Instance)
    this.sourceInfoProvider = new SourceInfoProvider(preProcessResult)
    this.contractMetadataProvider = new ContractMetadataProvider(sourceInfo)
    this.consoleAddress = consoleAddress
  }

  public async processTracesAsync(result: SentioExternalCallTrace): Promise<DecodedCallTrace> {
    // firstly decode the root call
    const decodedRootCall = await this.decodeRootCall(result, 0)
    // this.callStack.push(decodedRootCall)
    if (result.traces) {
      for (const trace of result.traces) {
        if (trace.type.includes('LOG')) {
          await this.sourceInfoProvider.enterAsync(result.to)
          const logTrace = trace as SentioLogTrace
          const decodedLog = await decodeLog(logTrace, this.abiProvider, this.sourceInfoProvider)
          if (decodedLog) {
            decodedRootCall.logs?.push(decodedLog)
          }
        } else if (this.isDynamicLogCall(trace as SentioCallTrace)) {
          const decodedDynamicLog = this.decodeDynamicLog(trace as SentioCallTrace)
          if (decodedDynamicLog) {
            decodedRootCall!.dynamicLogs?.push(decodedDynamicLog)
          }
        } else {
          const callTrace = trace as SentioCallTrace
          if (
            callTrace.error &&
            !callTrace.type.includes('JUMP') &&
            !callTrace.type.includes('CALL') &&
            !callTrace.type.includes('CREATE') &&
            !callTrace.type.includes('SELFDESTRUCT')
          ) {
            // handle the error case
            decodedRootCall.calls.push(await this.handleError(callTrace, result.to, 1))
          } else {
            const decodedChildCall = await this.decodeTraceRecInternal(callTrace, result.input, 1)
            if (decodedChildCall) {
              decodedRootCall.calls.push(decodedChildCall)
            }
          }
        }
      }
    }
    // trace ends
    return decodedRootCall
  }

  private isDynamicLogCall(call: SentioCallTrace): boolean {
    return (
      call.type == 'STATICCALL' && (call as SentioCallTrace).to?.toLowerCase() == this.consoleAddress?.toLowerCase()
    )
  }

  private async decodeTraceRecInternal(
    trace: SentioCallTrace,
    callData: string,
    level: number
  ): Promise<DecodedCallTrace | undefined> {
    let decodedCall: DecodedCallTrace | undefined
    let currentAddress: string
    if (trace.type == '86') {
      trace.type = 'JUMP'
    }
    if (!trace.type.includes('JUMP')) {
      try {
        decodedCall = await this.decodeExternalCall(trace, level)
        callData = trace.input!
        currentAddress = trace.to!
      } catch (e) {
        console.error(e)
        return undefined
      }
    } else {
      decodedCall = await this.decodeInternalCall(trace, callData, level)
      currentAddress = trace.from
    }
    if (!decodedCall) {
      return undefined
    }
    if (trace.traces) {
      for (const subTrace of trace.traces) {
        if (subTrace.type.includes('LOG')) {
          await this.sourceInfoProvider.enterAsync(currentAddress)
          const logTrace = subTrace as SentioLogTrace
          const decodedLog = await decodeLog(logTrace, this.abiProvider, this.sourceInfoProvider)
          if (decodedLog) {
            decodedCall!.logs?.push(decodedLog)
          }
        } else if (this.isDynamicLogCall(subTrace as SentioCallTrace)) {
          const decodedDynamicLog = this.decodeDynamicLog(subTrace as SentioCallTrace)
          if (decodedDynamicLog) {
            decodedCall!.dynamicLogs?.push(decodedDynamicLog)
          }
        } else {
          const callTrace = subTrace as SentioCallTrace
          if (
            callTrace.error &&
            !callTrace.type.includes('JUMP') &&
            !callTrace.type.includes('CALL') &&
            !callTrace.type.includes('CREATE') &&
            !callTrace.type.includes('SELFDESTRUCT')
          ) {
            // handle the error case
            decodedCall!.calls.push(await this.handleError(callTrace, currentAddress, level + 1))
          } else {
            const decodedChildCall = await this.decodeTraceRecInternal(callTrace, callData, level + 1)
            if (decodedChildCall) {
              decodedCall!.calls.push(decodedChildCall)
            }
          }
        }
      }
    }
    return decodedCall
  }

  private async decodeRootCall(rootCall: SentioRootCallTrace, level: number): Promise<DecodedCallTrace> {
    await this.sourceInfoProvider.enterAsync(rootCall.to)
    const toAddress = rootCall.to!
    const abiDescriptor = await this.abiProvider.findAbi({
      address: toAddress,
      signature: rootCall.input!.slice(0, 10),
      data: rootCall.input,
      output: rootCall.output,
      pc: rootCall.pc
    })
    const contractName = this.contractMetadataProvider.findContractName(toAddress) ?? toAddress
    let decodedCall: DecodedCall = decodedResult(
      rootCall.input?.slice(0, 10),
      rootCall.input ? [rootCall.input] : [],
      rootCall.output ? [rootCall.output] : []
    )

    if (!abiDescriptor) {
      console.warn(`Cannot find abi for ${toAddress} with input ${rootCall.input}`)
    } else {
      if (abiDescriptor.abiItem?.type === 'fallback' || abiDescriptor.abiItem?.type === 'receive') {
        decodedCall = decodedResult(abiDescriptor.abiItem.type)
      } else {
        decodedCall = decodeCall(abiDescriptor.abiItem, rootCall.input, rootCall.output)
      }
    }

    let decodedError = undefined
    if (rootCall.error && rootCall.error !== '' && rootCall.output) {
      const abiDescriptor = await this.abiProvider.findAbi({
        address: toAddress,
        signature: rootCall.output?.slice(0, 10),
        data: rootCall.output
      })
      if (abiDescriptor) {
        const call = decodeCall(abiDescriptor.abiItem, rootCall.output, '0x')
        decodedError = {
          name: call.functionName,
          inputs: call.inputs
        }
      }
    }

    const location =
      abiDescriptor?.location ??
      this.sourceInfoProvider.getPCLocation(rootCall.pc ?? 0, this.sourceInfoProvider.currentContext?.context) ??
      {}
    const locationWithIndex: LocationWithInstructionIndex = { ...location, instructionIndex: rootCall.startIndex }
    if (rootCall.revertReason || rootCall.error || decodedError) {
      decodedCall.returnValue = undefined
    }
    return {
      depth: level,
      gas: rootCall.gas,
      gasUsed: rootCall.gasUsed,
      from: rootCall.from,
      to: rootCall.to!,
      startIndex: rootCall.startIndex,
      endIndex: rootCall.endIndex,
      value: rootCall.value,
      rawInput: rootCall.input,
      rawOutput: rootCall.output,
      fromContractName: this.contractMetadataProvider.findContractName(rootCall.from) || '',
      toContractName: this.contractMetadataProvider.findContractName(rootCall.to!) || '',
      contractName: contractName!,
      type: rootCall.type,
      calls: [],
      logs: [],
      dynamicLogs: [],
      error: rootCall.error,
      decodedError,
      revertReason: rootCall.revertReason,
      location: locationWithIndex,
      refund: rootCall.refund,
      ...decodedCall
    }
  }

  private decodeDynamicLog(callTrace: SentioCallTrace): DecodedDynamicLog {
    const signatureHash = callTrace.input?.slice(0, 10)
    const abiDescriptor = this.abiProvider.findDynamicLogAbi(signatureHash!)
    if (!abiDescriptor) {
      console.warn('dynamic log abi not found')
      return { functionName: 'unknown' }
    }
    const decodedCall = decodeCallInput(abiDescriptor.abiItem, callTrace.input!, false)
    return {
      functionName: decodedCall.functionName,
      inputs: decodedCall.inputs,
      instructionIndex: callTrace.startIndex
    }
    return undefined as any
  }

  private async decodeExternalCall(callTrace: SentioCallTrace, level: number): Promise<DecodedCallTrace> {
    const toAddress = callTrace.to!

    const address = callTrace.codeAddress ?? callTrace.to ?? callTrace.from
    if (!address) {
      throw new Error(`no address found for: ` + JSON.stringify(callTrace))
    }
    await this.sourceInfoProvider.enterAsync(address)
    const location = this.sourceInfoProvider.getPCLocation(callTrace.pc) ?? {}
    const locationWithIndex: LocationWithInstructionIndex = { ...location, instructionIndex: callTrace.startIndex }

    const signatureHash = callTrace.input?.slice(0, 10)
    await this.sourceInfoProvider.enterAsync(toAddress)

    let decodedCall: DecodedCall = decodedResult(
      callTrace.input?.slice(0, 10),
      callTrace.input ? [callTrace.input] : []
    )
    let isPrecompiled = false
    let abiDescriptor = this.abiProvider.findPrecompileAbi(toAddress)
    if (abiDescriptor) {
      isPrecompiled = true
    } else {
      abiDescriptor = await this.abiProvider.findAbi({
        address: toAddress,
        signature: signatureHash,
        data: callTrace.input,
        output: callTrace.output
      })
    }
    if (!abiDescriptor) {
      console.warn(`Cannot find abi for ${toAddress} with input ${callTrace.input}`)
    } else {
      if (abiDescriptor.abiItem?.type === 'fallback' || abiDescriptor.abiItem?.type === 'receive') {
        decodedCall = decodedResult(abiDescriptor.abiItem.type)
      } else {
        decodedCall = decodeCallInput(abiDescriptor.abiItem, callTrace.input!, isPrecompiled)
      }
    }

    let decodedError = undefined
    if (callTrace.error && callTrace.error !== '' && callTrace.output) {
      const abiDescriptor = await this.abiProvider.findAbi({
        address: toAddress,
        signature: callTrace.output.slice(0, 10),
        data: callTrace.output
      })
      if (abiDescriptor) {
        const call = decodeCall(abiDescriptor.abiItem, callTrace.output, '0x')
        decodedError = {
          name: call.functionName,
          inputs: call.inputs
        }
      }
    }

    let defLocation = abiDescriptor?.location
    if (!defLocation && signatureHash) {
      const defPc = this.sourceInfoProvider.getDefPcWithSignature(signatureHash)
      if (defPc) {
        defLocation = this.sourceInfoProvider.getPCLocation(defPc)
      }
    }

    const contractName = this.contractMetadataProvider.findContractName(toAddress) ?? toAddress

    return {
      depth: level,
      from: callTrace.from,
      to: callTrace.to!,
      startIndex: callTrace.startIndex,
      endIndex: callTrace.endIndex,
      value: callTrace.type != 'DELEGATECALL' ? callTrace.value : undefined,
      rawInput: callTrace.input,
      rawOutput: callTrace.output,
      // value field for DELEGATECALLs mess up with fund flow
      fromContractName: this.contractMetadataProvider.findContractName(callTrace.from) || '',
      toContractName: contractName || '',
      contractName: contractName!,
      type: callTrace.type,
      calls: [],
      logs: [],
      dynamicLogs: [],
      location: locationWithIndex,
      defLocation,
      ...decodedCall,
      gas: callTrace.gas,
      gasUsed: callTrace.gasUsed,
      error: callTrace.error,
      decodedError,
      revertReason: callTrace.revertReason,
      returnValue:
        callTrace.output && !callTrace.revertReason && !callTrace.error && !decodedError
          ? decodeCallOutput(abiDescriptor?.abiItem, callTrace.output, isPrecompiled)
          : undefined
    }
  }

  private async decodeInternalCall(
    callTrace: SentioCallTrace,
    callData: string,
    level: number
  ): Promise<DecodedCallTrace | undefined> {
    await this.sourceInfoProvider.enterAsync(callTrace.codeAddress ?? callTrace.from)
    const location = this.sourceInfoProvider.getPCLocation(callTrace.pc)
    const codeDataBytes = Codec.Conversion.toBytes(callData)
    // jump in to an internal function
    const functionDefinitionNode = this.sourceInfoProvider.getFunctionDefNode(callTrace.functionPc)
    if (!functionDefinitionNode) {
      console.warn(
        `callTrace - index: ${callTrace.startIndex}, pc: ${callTrace.pc}, functionPc: ${callTrace.functionPc} is not a function call`
      )
      return undefined
    }

    // stack allocation for function inputs is reversed if viaIR is enabled
    // this is the expected behavior in Yul
    // note that for a parameter with multiple words, word order is reversed too,
    let inputStack = callTrace.inputStack || []
    if (this.contractMetadataProvider.isCompiledViaIR(callTrace.from)) {
      const reversedInputs = inputStack.splice(-functionDefinitionNode.parameterStackSize).reverse()
      inputStack = inputStack.concat(reversedInputs)
    }
    const userDefinedTypes = this.sourceInfoProvider.userDefinedTypes
    const decodedRes = decodeInternalCallInputsOutputs(
      functionDefinitionNode,
      inputStack,
      callTrace.inputMemory || [],
      callTrace.outputStack || [],
      callTrace.outputMemory || [],
      codeDataBytes,
      userDefinedTypes,
      {
        memory: Codec.Memory.Allocate.getMemoryAllocations(userDefinedTypes),
        abi: Codec.AbiData.Allocate.getAbiAllocations(userDefinedTypes)
      }
    )
    const newInternalCall: DecodedCallTrace = {
      type: callTrace.type,
      functionName: functionDefinitionNode?.name,
      inputs: decodedRes.decodedInputs,
      startIndex: callTrace.startIndex,
      endIndex: callTrace.endIndex,
      location: { ...location, instructionIndex: callTrace.startIndex },
      depth: level,
      calls: [],
      logs: [],
      dynamicLogs: [],
      contractName: this.contractMetadataProvider.findContractName(callTrace.from) ?? callTrace.from,
      address: callTrace.from,
      from: callTrace.from,
      gas: callTrace.gas,
      error: callTrace.error,
      revertReason: callTrace.revertReason,
      gasUsed: callTrace.gasUsed,
      returnValue: decodedRes.decodedOutputs
    }

    return newInternalCall
  }

  private async handleError(callTrace: SentioCallTrace, address: string, level: number): Promise<DecodedCallTrace> {
    await this.sourceInfoProvider.enterAsync(address)
    const location = this.sourceInfoProvider.getPCLocation(callTrace.pc)

    let decodedError = undefined
    if (callTrace.output) {
      const abiDescriptor = await this.abiProvider.findAbi({
        address,
        signature: callTrace.output.slice(0, 10),
        data: callTrace.output
      })
      if (abiDescriptor) {
        const call = decodeCall(abiDescriptor.abiItem, callTrace.output, '0x')
        decodedError = {
          name: call.functionName,
          inputs: call.inputs
        }
      }
    }

    return {
      type: callTrace.type,
      depth: level,
      startIndex: callTrace.startIndex,
      endIndex: callTrace.endIndex,
      location: { ...location, instructionIndex: callTrace.startIndex },
      calls: [],
      logs: [],
      dynamicLogs: [],
      error: callTrace.error,
      decodedError,
      gas: callTrace.gas,
      gasUsed: callTrace.gasUsed,
      revertReason: callTrace.revertReason
    }
  }
}
