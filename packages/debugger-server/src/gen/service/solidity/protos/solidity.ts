/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal";
import { Transaction, TransactionReceipt } from "../../../chain/evm/protos/evm";
import { HttpBody } from "../../../google/api/httpbody";
import { Empty } from "../../../google/protobuf/empty";
import { ListValue, Struct } from "../../../google/protobuf/struct";
import { Timestamp } from "../../../google/protobuf/timestamp";
import { Index } from "../../../scip";
import { ChainIdentifier, Simulation, SourceSpec, StorageSummaryResult, TxIdentifier, Verification } from "./common";

export enum SignatureType {
  FUNCTION = 0,
  EVENT = 1,
  ERROR = 2,
  UNRECOGNIZED = -1,
}

export function signatureTypeFromJSON(object: any): SignatureType {
  switch (object) {
    case 0:
    case "FUNCTION":
      return SignatureType.FUNCTION;
    case 1:
    case "EVENT":
      return SignatureType.EVENT;
    case 2:
    case "ERROR":
      return SignatureType.ERROR;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SignatureType.UNRECOGNIZED;
  }
}

export function signatureTypeToJSON(object: SignatureType): string {
  switch (object) {
    case SignatureType.FUNCTION:
      return "FUNCTION";
    case SignatureType.EVENT:
      return "EVENT";
    case SignatureType.ERROR:
      return "ERROR";
    case SignatureType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MEVTxType {
  NONE = 0,
  SANDWICH = 1,
  ARBITRAGE = 2,
  VICTIM = 3,
  UNRECOGNIZED = -1,
}

export function mEVTxTypeFromJSON(object: any): MEVTxType {
  switch (object) {
    case 0:
    case "NONE":
      return MEVTxType.NONE;
    case 1:
    case "SANDWICH":
      return MEVTxType.SANDWICH;
    case 2:
    case "ARBITRAGE":
      return MEVTxType.ARBITRAGE;
    case 3:
    case "VICTIM":
      return MEVTxType.VICTIM;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MEVTxType.UNRECOGNIZED;
  }
}

export function mEVTxTypeToJSON(object: MEVTxType): string {
  switch (object) {
    case MEVTxType.NONE:
      return "NONE";
    case MEVTxType.SANDWICH:
      return "SANDWICH";
    case MEVTxType.ARBITRAGE:
      return "ARBITRAGE";
    case MEVTxType.VICTIM:
      return "VICTIM";
    case MEVTxType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface GetStorageSummaryRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  address: string;
  blockNumber?: string | undefined;
  variablePath?: string | undefined;
}

export interface GetStorageSummaryResponse {
  address: string;
  implAddress?: string | undefined;
  results: StorageSummaryResult[];
  blockNumber: string;
}

export interface FetchAndCompileRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  shareId?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId: TxIdentifier | undefined;
  addresses: string[];
  disableOptimizer: boolean;
  sourceOnly: boolean;
}

export interface GetTransactionInfoRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId: TxIdentifier | undefined;
}

export interface GetTransactionInfoResponse {
  chainSpec: ChainIdentifier | undefined;
  block: { [key: string]: any } | undefined;
  transaction: Transaction | undefined;
  transactionReceipt: TransactionReceipt | undefined;
  latestBlockNumber: string;
  transactions: Transaction[];
  transactionReceipts: TransactionReceipt[];
}

export interface GetTransactionsRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txHash: string[];
}

export interface GetTransactionsResponse {
  transactions: { [key: string]: Transaction };
}

export interface GetTransactionsResponse_TransactionsEntry {
  key: string;
  value: Transaction | undefined;
}

export interface GetLatestBlockNumberRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
}

export interface GetLatestBlockNumberResponse {
  blockNumber: string;
}

export interface GetBlockSummaryRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  blockNumber: string;
}

export interface GetBlockSummaryResponse {
  blockNumber: string;
  transactionCount: number;
  baseFeePerGas: string;
}

export interface GetStorageInfoRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  blockHash: string;
  txIdx: bigint;
  contractAddress: string;
  keyStart: string;
  maxResult: bigint;
}

export interface GetStorageInfoResponse {
  result: { [key: string]: any } | undefined;
}

export interface GetCodeRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  contractAddress: string;
  blockNumber: string;
}

export interface GetCodeResponse {
  code: string;
}

export interface GetCallTraceRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  shareId?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId: TxIdentifier | undefined;
  disableOptimizer: boolean;
  withInternalCalls: boolean;
  ignoreGasCost: boolean;
}

export interface SentioTraceTransactionRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId: TxIdentifier | undefined;
  disableOptimizer: boolean;
  withInternalCalls: boolean;
  debug: boolean;
  ignoreGasCost: boolean;
}

export interface GetCallTraceResponse {
  result: Array<any> | undefined;
  outputs: Array<any> | undefined;
}

export interface GetAffectedContractRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  shareId?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId: TxIdentifier | undefined;
}

export interface GetAffectedContractResponse {
  addresses: string[];
}

export interface SimulateTransactionRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  simulation: Simulation | undefined;
}

export interface SimulateTransactionResponse {
  simulation: Simulation | undefined;
}

export interface SimulateTransactionBundleRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  simulations: Simulation[];
}

export interface SimulateTransactionBundleResponse {
  bundleId: string;
  simulations: Simulation[];
  error: string;
}

export interface GetSimulationBundleRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  bundleId: string;
}

export interface GetSimulationBundleResponse {
  simulations: Simulation[];
  error: string;
}

export interface GetSimulationsRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  labelContains?: string | undefined;
  page: number;
  pageSize: number;
}

export interface GetSimulationsResponse {
  simulations: Simulation[];
  count: bigint;
  page: number;
  pageSize: number;
}

export interface GetSimulationRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  simulationId: string;
}

export interface GetSimulationResponse {
  simulation: Simulation | undefined;
}

export interface SyncContractsRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  addresses: string[];
  disableOptimizer: boolean;
}

export interface UniversalSearchRequest {
  q: string;
  limit: number;
}

export interface UniversalSearchResultTransaction {
  hash: string;
  blockNumber: bigint;
  blockHash: string;
  from: string;
  to: string;
  methodId: string;
  status: number;
  isTrace: boolean;
}

export interface UniversalSearchResultContract {
  address: string;
  contractName: string;
}

export interface UniversalSearchResult {
  chainId: string;
  timestampMs: bigint;
  transaction?: UniversalSearchResultTransaction | undefined;
  contract?: UniversalSearchResultContract | undefined;
}

export interface UniversalSearchResponse {
  results: UniversalSearchResult[];
}

export interface GetContractIndexRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId?: TxIdentifier | undefined;
  userCompilationId?: string | undefined;
  address: string;
}

export interface GetContractIndexResponse {
  index: Index | undefined;
}

export interface GetDebugTraceRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  shareId?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId: TxIdentifier | undefined;
  memoryCompressionWindow: number;
  disableOptimizer: boolean;
  ignoreGasCost: boolean;
}

export interface LookupSignatureRequest {
  chainSpec?: ChainIdentifier | undefined;
  address?: string | undefined;
  hexSignature: string;
  type: SignatureType;
  data?: string | undefined;
  output?: string | undefined;
  topics: string[];
}

export interface LookupSignatureResponse {
  textSignature: string;
  abiItem?: string | undefined;
}

export interface GetABIRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId?: TxIdentifier | undefined;
  address: string;
}

export interface GetABIResponse {
  ABI: string;
}

export interface GetContractNameRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId?: TxIdentifier | undefined;
  address: string;
}

export interface GetContractNameResponse {
  contractName: string;
}

export interface StateDiffRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  shareId?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txId: TxIdentifier | undefined;
  decode?: boolean | undefined;
}

export interface UploadUserCompilationRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  compileSpec: SourceSpec | undefined;
  fromContract?: UploadUserCompilationRequest_FromContract | undefined;
  fromUserCompilation?: UploadUserCompilationRequest_FromUserCompilation | undefined;
  verifySpec?: Verification | undefined;
  name?: string | undefined;
}

export interface UploadUserCompilationRequest_FromContract {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  address: string;
  overrideSource: { [key: string]: string };
}

export interface UploadUserCompilationRequest_FromContract_OverrideSourceEntry {
  key: string;
  value: string;
}

export interface UploadUserCompilationRequest_FromUserCompilation {
  userCompilationId: string;
  overrideSource: { [key: string]: string };
}

export interface UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry {
  key: string;
  value: string;
}

export interface UploadUserCompilationResponse {
  userCompilationId: string;
  verified: boolean;
}

export interface GetUserCompilationsRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  name?: string | undefined;
  page: number;
  pageSize: number;
}

export interface GetUserCompilationsResponse {
  results: UserCompilation[];
  total: bigint;
  page: number;
  pageSize: number;
}

export interface GetUserCompilationRequest {
  userCompilationId: string;
  brief: boolean;
}

export interface GetDeployedCodeResponse {
  deployedCode: string;
}

export interface UpdateUserCompilationRequest {
  userCompilationId: string;
  name: string;
}

export interface DeleteUserCompilationRequest {
  userCompilationId: string;
}

export interface GetUserCompilationResponse {
  result: UserCompilation | undefined;
  projectOwner: string;
  projectSlug: string;
}

export interface GetDeployedCodeRequest {
  chainSpec: ChainIdentifier | undefined;
  userCompilationId: string;
  address: string;
}

export interface VerifyContractRequest {
  verifySpec: Verification | undefined;
}

export interface VerifyContractResponse {
  verified: boolean;
}

export interface GetVerificationsRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId?: string | undefined;
  chainSpec: ChainIdentifier | undefined;
  page: number;
  pageSize: number;
}

export interface GetVerificationsResponse {
  results: Verification[];
  total: bigint;
  page: number;
  pageSize: number;
}

export interface GetVerificationByContractRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  addresses: string[];
  checkInternal: boolean;
}

export interface GetVerificationByContractResponse {
  result: { [key: string]: GetVerificationByContractResponse_Contract };
}

export interface GetVerificationByContractResponse_Contract {
  internal: boolean;
  userVerification: Verification | undefined;
}

export interface GetVerificationByContractResponse_ResultEntry {
  key: string;
  value: GetVerificationByContractResponse_Contract | undefined;
}

export interface DeleteVerificationRequest {
  verificationId: string;
}

export interface ParseContractsRequest {
  compileSpec: SourceSpec | undefined;
}

export interface ParseContractsResponse {
  contracts: string[];
}

export interface UserCompilation {
  id: string;
  name: string;
  contractName: string;
  ABI: string;
  source: SourceSpec | undefined;
  compilation: { [key: string]: any } | undefined;
  verifications: Verification[];
  createTime: Date | undefined;
  origin?: UserCompilation_Origin | undefined;
}

export interface UserCompilation_Origin {
  chainId: string;
  address: string;
  creationTx: string;
}

export interface GetMEVInfoRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txHash: string;
}

export interface GetMEVInfoResponse {
  txHash: string;
  txIndex: number;
  blockNumber: string;
  type: MEVTxType;
  sandwich?: SandwichResult | undefined;
  arbitrage?: ArbitrageResult | undefined;
  blockSandwiches: SandwichResult[];
  blockArbitrages: ArbitrageResult[];
}

export interface BatchGetMEVInfoRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  txHash: string[];
}

export interface BatchGetMEVInfoResponse {
  results: GetMEVInfoResponse[];
}

export interface SandwichResult {
  /** @deprecated */
  frontTxHash: string;
  /** @deprecated */
  backTxHash: string;
  /** @deprecated */
  frontTxIndex: number;
  /** @deprecated */
  backTxIndex: number;
  txs: TxHashAndIndex[];
  victims: TxHashAndIndex[];
  mevContract: string;
  revenues: Assets | undefined;
  costs: Assets | undefined;
  profits: Assets | undefined;
  tokens: TokenInfo[];
  traders: Pool[];
  tags: string[];
}

export interface TxHashAndIndex {
  txHash: string;
  txIndex: number;
}

export interface ArbitrageResult {
  txHash: string;
  txIndex: number;
  mevContract: string;
  revenues: Assets | undefined;
  costs: Assets | undefined;
  profits: Assets | undefined;
  tokens: TokenInfo[];
  traders: Pool[];
  tags: string[];
}

export interface Assets {
  tokens: Assets_Token[];
  totalUsd: number;
}

export interface Assets_Token {
  address: string;
  symbol: string;
  value: number;
  valueUsd?: number | undefined;
}

export interface TokenInfo {
  address: string;
  symbol?: string | undefined;
  timestamp?: Date | undefined;
  price?: number | undefined;
}

export interface Pool {
  address: string;
  protocol?: string | undefined;
  tokens: TokenInfo[];
}

export interface DumpSimulationRequest {
  simulationId: string;
}

export interface DumpSimulationResponse {
  simulationReq: SimulateTransactionRequest | undefined;
  compilationReq: { [key: string]: UploadUserCompilationRequest };
}

export interface DumpSimulationResponse_CompilationReqEntry {
  key: string;
  value: UploadUserCompilationRequest | undefined;
}

export interface DumpUserCompilationRequest {
  userCompilationId: string;
}

export interface DumpUserCompilationResponse {
  compilationReq: UploadUserCompilationRequest | undefined;
}

export interface SimulateDeploymentRequest {
  projectOwner?: string | undefined;
  projectSlug?:
    | string
    | undefined;
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  address: string;
  userCompilationId: string;
  blockNumber?: string | undefined;
}

export interface SimulateDeploymentResponse {
  simulation: Simulation | undefined;
}

export interface DownloadContractRequest {
  /** @deprecated */
  networkId: string;
  chainSpec: ChainIdentifier | undefined;
  address: string;
}

export interface GetForkStateRequestDeprecated {
  /** @deprecated */
  networkId: string;
}

export interface GetRecentTransactionsRequest {
  projectOwner?: string | undefined;
  projectSlug?: string | undefined;
  chainSpec: ChainIdentifier | undefined;
  limit?: number | undefined;
}

export interface GetRecentTransactionsResponse {
  txHashes: string[];
}

export interface GetEstimatedGasPriceRequest {
  chainId: string;
}

export interface GetEstimatedGasPriceResponse {
  system: string;
  network: string;
  unit: string;
  maxPrice: number;
  currentBlockNumber: number;
  msSinceLastBlock: number;
  blockPrices: BlockPrice[];
}

export interface BlockPrice {
  blockNumber: number;
  estimatedTransactionCount: number;
  baseFeePerGas: number;
  blobBaseFeePerGas: number;
  estimatedPrices: EstimatedPrice[];
}

export interface EstimatedPrice {
  confidence: number;
  price: number;
  maxPriorityFeePerGas: number;
  maxFeePerGas: number;
}

export interface EvmSearchTransactionsRequest {
  chainId: string[];
  address: string[];
  includeDirect: boolean;
  includeTrace: boolean;
  includeIn: boolean;
  includeOut: boolean;
  startBlock?: bigint | undefined;
  endBlock?: bigint | undefined;
  startTimestamp?: bigint | undefined;
  endTimestamp?: bigint | undefined;
  transactionStatus: number[];
  methodSignature?: string | undefined;
  limit: number;
  pageToken: Uint8Array;
}

export interface EvmRawTransaction {
  hash: string;
  blockNumber: bigint;
  isIn: boolean;
  trace: boolean;
  tx: Transaction | undefined;
  json: string;
  timestamp: bigint;
  transactionStatus: number;
  methodSignature: string;
  methodSignatureText?: string | undefined;
  abiItem?: string | undefined;
}

export interface EvmSearchTransactionsResponse {
  transactions: EvmRawTransaction[];
  nextPageToken: Uint8Array;
}

export interface CreateShareSimulationRequest {
  simulationId: string;
  public: boolean;
}

export interface CreateShareSimulationResponse {
  id: string;
  public: boolean;
}

export interface GetShareSimulationRequest {
  id: string;
}

export interface GetShareSimulationResponse {
  simulation: Simulation | undefined;
}

function createBaseGetStorageSummaryRequest(): GetStorageSummaryRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: "",
    chainSpec: undefined,
    address: "",
    blockNumber: undefined,
    variablePath: undefined,
  };
}

export const GetStorageSummaryRequest = {
  encode(message: GetStorageSummaryRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(58).fork()).ldelim();
    }
    if (message.address !== "") {
      writer.uint32(34).string(message.address);
    }
    if (message.blockNumber !== undefined) {
      writer.uint32(42).string(message.blockNumber);
    }
    if (message.variablePath !== undefined) {
      writer.uint32(50).string(message.variablePath);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetStorageSummaryRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetStorageSummaryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.address = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.variablePath = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetStorageSummaryRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : undefined,
      variablePath: isSet(object.variablePath) ? globalThis.String(object.variablePath) : undefined,
    };
  },

  toJSON(message: GetStorageSummaryRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.blockNumber !== undefined) {
      obj.blockNumber = message.blockNumber;
    }
    if (message.variablePath !== undefined) {
      obj.variablePath = message.variablePath;
    }
    return obj;
  },

  create(base?: DeepPartial<GetStorageSummaryRequest>): GetStorageSummaryRequest {
    return GetStorageSummaryRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetStorageSummaryRequest>): GetStorageSummaryRequest {
    const message = createBaseGetStorageSummaryRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.address = object.address ?? "";
    message.blockNumber = object.blockNumber ?? undefined;
    message.variablePath = object.variablePath ?? undefined;
    return message;
  },
};

function createBaseGetStorageSummaryResponse(): GetStorageSummaryResponse {
  return { address: "", implAddress: undefined, results: [], blockNumber: "" };
}

export const GetStorageSummaryResponse = {
  encode(message: GetStorageSummaryResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.address !== "") {
      writer.uint32(10).string(message.address);
    }
    if (message.implAddress !== undefined) {
      writer.uint32(18).string(message.implAddress);
    }
    for (const v of message.results) {
      StorageSummaryResult.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    if (message.blockNumber !== "") {
      writer.uint32(34).string(message.blockNumber);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetStorageSummaryResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetStorageSummaryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.address = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.implAddress = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.results.push(StorageSummaryResult.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetStorageSummaryResponse {
    return {
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      implAddress: isSet(object.implAddress) ? globalThis.String(object.implAddress) : undefined,
      results: globalThis.Array.isArray(object?.results)
        ? object.results.map((e: any) => StorageSummaryResult.fromJSON(e))
        : [],
      blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : "",
    };
  },

  toJSON(message: GetStorageSummaryResponse): unknown {
    const obj: any = {};
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.implAddress !== undefined) {
      obj.implAddress = message.implAddress;
    }
    if (message.results?.length) {
      obj.results = message.results.map((e) => StorageSummaryResult.toJSON(e));
    }
    if (message.blockNumber !== "") {
      obj.blockNumber = message.blockNumber;
    }
    return obj;
  },

  create(base?: DeepPartial<GetStorageSummaryResponse>): GetStorageSummaryResponse {
    return GetStorageSummaryResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetStorageSummaryResponse>): GetStorageSummaryResponse {
    const message = createBaseGetStorageSummaryResponse();
    message.address = object.address ?? "";
    message.implAddress = object.implAddress ?? undefined;
    message.results = object.results?.map((e) => StorageSummaryResult.fromPartial(e)) || [];
    message.blockNumber = object.blockNumber ?? "";
    return message;
  },
};

function createBaseFetchAndCompileRequest(): FetchAndCompileRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    shareId: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    addresses: [],
    disableOptimizer: false,
    sourceOnly: false,
  };
}

export const FetchAndCompileRequest = {
  encode(message: FetchAndCompileRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(42).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(50).string(message.projectSlug);
    }
    if (message.shareId !== undefined) {
      writer.uint32(74).string(message.shareId);
    }
    if (message.networkId !== "") {
      writer.uint32(34).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(66).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(10).fork()).ldelim();
    }
    for (const v of message.addresses) {
      writer.uint32(58).string(v!);
    }
    if (message.disableOptimizer !== false) {
      writer.uint32(16).bool(message.disableOptimizer);
    }
    if (message.sourceOnly !== false) {
      writer.uint32(24).bool(message.sourceOnly);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FetchAndCompileRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFetchAndCompileRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 5:
          if (tag !== 42) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.shareId = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.addresses.push(reader.string());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.disableOptimizer = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.sourceOnly = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FetchAndCompileRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      shareId: isSet(object.shareId) ? globalThis.String(object.shareId) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      addresses: globalThis.Array.isArray(object?.addresses)
        ? object.addresses.map((e: any) => globalThis.String(e))
        : [],
      disableOptimizer: isSet(object.disableOptimizer) ? globalThis.Boolean(object.disableOptimizer) : false,
      sourceOnly: isSet(object.sourceOnly) ? globalThis.Boolean(object.sourceOnly) : false,
    };
  },

  toJSON(message: FetchAndCompileRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.shareId !== undefined) {
      obj.shareId = message.shareId;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.addresses?.length) {
      obj.addresses = message.addresses;
    }
    if (message.disableOptimizer !== false) {
      obj.disableOptimizer = message.disableOptimizer;
    }
    if (message.sourceOnly !== false) {
      obj.sourceOnly = message.sourceOnly;
    }
    return obj;
  },

  create(base?: DeepPartial<FetchAndCompileRequest>): FetchAndCompileRequest {
    return FetchAndCompileRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<FetchAndCompileRequest>): FetchAndCompileRequest {
    const message = createBaseFetchAndCompileRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.shareId = object.shareId ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.addresses = object.addresses?.map((e) => e) || [];
    message.disableOptimizer = object.disableOptimizer ?? false;
    message.sourceOnly = object.sourceOnly ?? false;
    return message;
  },
};

function createBaseGetTransactionInfoRequest(): GetTransactionInfoRequest {
  return { projectOwner: undefined, projectSlug: undefined, networkId: "", chainSpec: undefined, txId: undefined };
}

export const GetTransactionInfoRequest = {
  encode(message: GetTransactionInfoRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(50).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(58).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(42).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(66).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetTransactionInfoRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTransactionInfoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 6:
          if (tag !== 50) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetTransactionInfoRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
    };
  },

  toJSON(message: GetTransactionInfoRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    return obj;
  },

  create(base?: DeepPartial<GetTransactionInfoRequest>): GetTransactionInfoRequest {
    return GetTransactionInfoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetTransactionInfoRequest>): GetTransactionInfoRequest {
    const message = createBaseGetTransactionInfoRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    return message;
  },
};

function createBaseGetTransactionInfoResponse(): GetTransactionInfoResponse {
  return {
    chainSpec: undefined,
    block: undefined,
    transaction: undefined,
    transactionReceipt: undefined,
    latestBlockNumber: "",
    transactions: [],
    transactionReceipts: [],
  };
}

export const GetTransactionInfoResponse = {
  encode(message: GetTransactionInfoResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(74).fork()).ldelim();
    }
    if (message.block !== undefined) {
      Struct.encode(Struct.wrap(message.block), writer.uint32(10).fork()).ldelim();
    }
    if (message.transaction !== undefined) {
      Transaction.encode(message.transaction, writer.uint32(18).fork()).ldelim();
    }
    if (message.transactionReceipt !== undefined) {
      TransactionReceipt.encode(message.transactionReceipt, writer.uint32(26).fork()).ldelim();
    }
    if (message.latestBlockNumber !== "") {
      writer.uint32(50).string(message.latestBlockNumber);
    }
    for (const v of message.transactions) {
      Transaction.encode(v!, writer.uint32(58).fork()).ldelim();
    }
    for (const v of message.transactionReceipts) {
      TransactionReceipt.encode(v!, writer.uint32(66).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetTransactionInfoResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTransactionInfoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 9:
          if (tag !== 74) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.block = Struct.unwrap(Struct.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.transaction = Transaction.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.transactionReceipt = TransactionReceipt.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.latestBlockNumber = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.transactions.push(Transaction.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.transactionReceipts.push(TransactionReceipt.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetTransactionInfoResponse {
    return {
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      block: isObject(object.block) ? object.block : undefined,
      transaction: isSet(object.transaction) ? Transaction.fromJSON(object.transaction) : undefined,
      transactionReceipt: isSet(object.transactionReceipt)
        ? TransactionReceipt.fromJSON(object.transactionReceipt)
        : undefined,
      latestBlockNumber: isSet(object.latestBlockNumber) ? globalThis.String(object.latestBlockNumber) : "",
      transactions: globalThis.Array.isArray(object?.transactions)
        ? object.transactions.map((e: any) => Transaction.fromJSON(e))
        : [],
      transactionReceipts: globalThis.Array.isArray(object?.transactionReceipts)
        ? object.transactionReceipts.map((e: any) => TransactionReceipt.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetTransactionInfoResponse): unknown {
    const obj: any = {};
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.block !== undefined) {
      obj.block = message.block;
    }
    if (message.transaction !== undefined) {
      obj.transaction = Transaction.toJSON(message.transaction);
    }
    if (message.transactionReceipt !== undefined) {
      obj.transactionReceipt = TransactionReceipt.toJSON(message.transactionReceipt);
    }
    if (message.latestBlockNumber !== "") {
      obj.latestBlockNumber = message.latestBlockNumber;
    }
    if (message.transactions?.length) {
      obj.transactions = message.transactions.map((e) => Transaction.toJSON(e));
    }
    if (message.transactionReceipts?.length) {
      obj.transactionReceipts = message.transactionReceipts.map((e) => TransactionReceipt.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<GetTransactionInfoResponse>): GetTransactionInfoResponse {
    return GetTransactionInfoResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetTransactionInfoResponse>): GetTransactionInfoResponse {
    const message = createBaseGetTransactionInfoResponse();
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.block = object.block ?? undefined;
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? Transaction.fromPartial(object.transaction)
      : undefined;
    message.transactionReceipt = (object.transactionReceipt !== undefined && object.transactionReceipt !== null)
      ? TransactionReceipt.fromPartial(object.transactionReceipt)
      : undefined;
    message.latestBlockNumber = object.latestBlockNumber ?? "";
    message.transactions = object.transactions?.map((e) => Transaction.fromPartial(e)) || [];
    message.transactionReceipts = object.transactionReceipts?.map((e) => TransactionReceipt.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetTransactionsRequest(): GetTransactionsRequest {
  return { networkId: "", chainSpec: undefined, txHash: [] };
}

export const GetTransactionsRequest = {
  encode(message: GetTransactionsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(26).fork()).ldelim();
    }
    for (const v of message.txHash) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetTransactionsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTransactionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.txHash.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetTransactionsRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txHash: globalThis.Array.isArray(object?.txHash) ? object.txHash.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: GetTransactionsRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txHash?.length) {
      obj.txHash = message.txHash;
    }
    return obj;
  },

  create(base?: DeepPartial<GetTransactionsRequest>): GetTransactionsRequest {
    return GetTransactionsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetTransactionsRequest>): GetTransactionsRequest {
    const message = createBaseGetTransactionsRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txHash = object.txHash?.map((e) => e) || [];
    return message;
  },
};

function createBaseGetTransactionsResponse(): GetTransactionsResponse {
  return { transactions: {} };
}

export const GetTransactionsResponse = {
  encode(message: GetTransactionsResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    Object.entries(message.transactions).forEach(([key, value]) => {
      GetTransactionsResponse_TransactionsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetTransactionsResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTransactionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          const entry1 = GetTransactionsResponse_TransactionsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.transactions[entry1.key] = entry1.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetTransactionsResponse {
    return {
      transactions: isObject(object.transactions)
        ? Object.entries(object.transactions).reduce<{ [key: string]: Transaction }>((acc, [key, value]) => {
          acc[key] = Transaction.fromJSON(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: GetTransactionsResponse): unknown {
    const obj: any = {};
    if (message.transactions) {
      const entries = Object.entries(message.transactions);
      if (entries.length > 0) {
        obj.transactions = {};
        entries.forEach(([k, v]) => {
          obj.transactions[k] = Transaction.toJSON(v);
        });
      }
    }
    return obj;
  },

  create(base?: DeepPartial<GetTransactionsResponse>): GetTransactionsResponse {
    return GetTransactionsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetTransactionsResponse>): GetTransactionsResponse {
    const message = createBaseGetTransactionsResponse();
    message.transactions = Object.entries(object.transactions ?? {}).reduce<{ [key: string]: Transaction }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Transaction.fromPartial(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseGetTransactionsResponse_TransactionsEntry(): GetTransactionsResponse_TransactionsEntry {
  return { key: "", value: undefined };
}

export const GetTransactionsResponse_TransactionsEntry = {
  encode(message: GetTransactionsResponse_TransactionsEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      Transaction.encode(message.value, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetTransactionsResponse_TransactionsEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetTransactionsResponse_TransactionsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = Transaction.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetTransactionsResponse_TransactionsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? Transaction.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: GetTransactionsResponse_TransactionsEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = Transaction.toJSON(message.value);
    }
    return obj;
  },

  create(base?: DeepPartial<GetTransactionsResponse_TransactionsEntry>): GetTransactionsResponse_TransactionsEntry {
    return GetTransactionsResponse_TransactionsEntry.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<GetTransactionsResponse_TransactionsEntry>,
  ): GetTransactionsResponse_TransactionsEntry {
    const message = createBaseGetTransactionsResponse_TransactionsEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? Transaction.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseGetLatestBlockNumberRequest(): GetLatestBlockNumberRequest {
  return { networkId: "", chainSpec: undefined };
}

export const GetLatestBlockNumberRequest = {
  encode(message: GetLatestBlockNumberRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetLatestBlockNumberRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetLatestBlockNumberRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetLatestBlockNumberRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
    };
  },

  toJSON(message: GetLatestBlockNumberRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    return obj;
  },

  create(base?: DeepPartial<GetLatestBlockNumberRequest>): GetLatestBlockNumberRequest {
    return GetLatestBlockNumberRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetLatestBlockNumberRequest>): GetLatestBlockNumberRequest {
    const message = createBaseGetLatestBlockNumberRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    return message;
  },
};

function createBaseGetLatestBlockNumberResponse(): GetLatestBlockNumberResponse {
  return { blockNumber: "" };
}

export const GetLatestBlockNumberResponse = {
  encode(message: GetLatestBlockNumberResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.blockNumber !== "") {
      writer.uint32(10).string(message.blockNumber);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetLatestBlockNumberResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetLatestBlockNumberResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetLatestBlockNumberResponse {
    return { blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : "" };
  },

  toJSON(message: GetLatestBlockNumberResponse): unknown {
    const obj: any = {};
    if (message.blockNumber !== "") {
      obj.blockNumber = message.blockNumber;
    }
    return obj;
  },

  create(base?: DeepPartial<GetLatestBlockNumberResponse>): GetLatestBlockNumberResponse {
    return GetLatestBlockNumberResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetLatestBlockNumberResponse>): GetLatestBlockNumberResponse {
    const message = createBaseGetLatestBlockNumberResponse();
    message.blockNumber = object.blockNumber ?? "";
    return message;
  },
};

function createBaseGetBlockSummaryRequest(): GetBlockSummaryRequest {
  return { networkId: "", chainSpec: undefined, blockNumber: "" };
}

export const GetBlockSummaryRequest = {
  encode(message: GetBlockSummaryRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(26).fork()).ldelim();
    }
    if (message.blockNumber !== "") {
      writer.uint32(18).string(message.blockNumber);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockSummaryRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBlockSummaryRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBlockSummaryRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : "",
    };
  },

  toJSON(message: GetBlockSummaryRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.blockNumber !== "") {
      obj.blockNumber = message.blockNumber;
    }
    return obj;
  },

  create(base?: DeepPartial<GetBlockSummaryRequest>): GetBlockSummaryRequest {
    return GetBlockSummaryRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetBlockSummaryRequest>): GetBlockSummaryRequest {
    const message = createBaseGetBlockSummaryRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.blockNumber = object.blockNumber ?? "";
    return message;
  },
};

function createBaseGetBlockSummaryResponse(): GetBlockSummaryResponse {
  return { blockNumber: "", transactionCount: 0, baseFeePerGas: "" };
}

export const GetBlockSummaryResponse = {
  encode(message: GetBlockSummaryResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.blockNumber !== "") {
      writer.uint32(10).string(message.blockNumber);
    }
    if (message.transactionCount !== 0) {
      writer.uint32(16).uint32(message.transactionCount);
    }
    if (message.baseFeePerGas !== "") {
      writer.uint32(26).string(message.baseFeePerGas);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetBlockSummaryResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetBlockSummaryResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.transactionCount = reader.uint32();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.baseFeePerGas = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetBlockSummaryResponse {
    return {
      blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : "",
      transactionCount: isSet(object.transactionCount) ? globalThis.Number(object.transactionCount) : 0,
      baseFeePerGas: isSet(object.baseFeePerGas) ? globalThis.String(object.baseFeePerGas) : "",
    };
  },

  toJSON(message: GetBlockSummaryResponse): unknown {
    const obj: any = {};
    if (message.blockNumber !== "") {
      obj.blockNumber = message.blockNumber;
    }
    if (message.transactionCount !== 0) {
      obj.transactionCount = Math.round(message.transactionCount);
    }
    if (message.baseFeePerGas !== "") {
      obj.baseFeePerGas = message.baseFeePerGas;
    }
    return obj;
  },

  create(base?: DeepPartial<GetBlockSummaryResponse>): GetBlockSummaryResponse {
    return GetBlockSummaryResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetBlockSummaryResponse>): GetBlockSummaryResponse {
    const message = createBaseGetBlockSummaryResponse();
    message.blockNumber = object.blockNumber ?? "";
    message.transactionCount = object.transactionCount ?? 0;
    message.baseFeePerGas = object.baseFeePerGas ?? "";
    return message;
  },
};

function createBaseGetStorageInfoRequest(): GetStorageInfoRequest {
  return {
    networkId: "",
    chainSpec: undefined,
    blockHash: "",
    txIdx: BigInt("0"),
    contractAddress: "",
    keyStart: "",
    maxResult: BigInt("0"),
  };
}

export const GetStorageInfoRequest = {
  encode(message: GetStorageInfoRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(50).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(58).fork()).ldelim();
    }
    if (message.blockHash !== "") {
      writer.uint32(10).string(message.blockHash);
    }
    if (message.txIdx !== BigInt("0")) {
      if (BigInt.asIntN(64, message.txIdx) !== message.txIdx) {
        throw new globalThis.Error("value provided for field message.txIdx of type int64 too large");
      }
      writer.uint32(16).int64(message.txIdx.toString());
    }
    if (message.contractAddress !== "") {
      writer.uint32(26).string(message.contractAddress);
    }
    if (message.keyStart !== "") {
      writer.uint32(34).string(message.keyStart);
    }
    if (message.maxResult !== BigInt("0")) {
      if (BigInt.asIntN(64, message.maxResult) !== message.maxResult) {
        throw new globalThis.Error("value provided for field message.maxResult of type int64 too large");
      }
      writer.uint32(40).int64(message.maxResult.toString());
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetStorageInfoRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetStorageInfoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 6:
          if (tag !== 50) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.blockHash = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.txIdx = longToBigint(reader.int64() as Long);
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.contractAddress = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.keyStart = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.maxResult = longToBigint(reader.int64() as Long);
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetStorageInfoRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      blockHash: isSet(object.blockHash) ? globalThis.String(object.blockHash) : "",
      txIdx: isSet(object.txIdx) ? BigInt(object.txIdx) : BigInt("0"),
      contractAddress: isSet(object.contractAddress) ? globalThis.String(object.contractAddress) : "",
      keyStart: isSet(object.keyStart) ? globalThis.String(object.keyStart) : "",
      maxResult: isSet(object.maxResult) ? BigInt(object.maxResult) : BigInt("0"),
    };
  },

  toJSON(message: GetStorageInfoRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.blockHash !== "") {
      obj.blockHash = message.blockHash;
    }
    if (message.txIdx !== BigInt("0")) {
      obj.txIdx = message.txIdx.toString();
    }
    if (message.contractAddress !== "") {
      obj.contractAddress = message.contractAddress;
    }
    if (message.keyStart !== "") {
      obj.keyStart = message.keyStart;
    }
    if (message.maxResult !== BigInt("0")) {
      obj.maxResult = message.maxResult.toString();
    }
    return obj;
  },

  create(base?: DeepPartial<GetStorageInfoRequest>): GetStorageInfoRequest {
    return GetStorageInfoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetStorageInfoRequest>): GetStorageInfoRequest {
    const message = createBaseGetStorageInfoRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.blockHash = object.blockHash ?? "";
    message.txIdx = object.txIdx ?? BigInt("0");
    message.contractAddress = object.contractAddress ?? "";
    message.keyStart = object.keyStart ?? "";
    message.maxResult = object.maxResult ?? BigInt("0");
    return message;
  },
};

function createBaseGetStorageInfoResponse(): GetStorageInfoResponse {
  return { result: undefined };
}

export const GetStorageInfoResponse = {
  encode(message: GetStorageInfoResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      Struct.encode(Struct.wrap(message.result), writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetStorageInfoResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetStorageInfoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = Struct.unwrap(Struct.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetStorageInfoResponse {
    return { result: isObject(object.result) ? object.result : undefined };
  },

  toJSON(message: GetStorageInfoResponse): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = message.result;
    }
    return obj;
  },

  create(base?: DeepPartial<GetStorageInfoResponse>): GetStorageInfoResponse {
    return GetStorageInfoResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetStorageInfoResponse>): GetStorageInfoResponse {
    const message = createBaseGetStorageInfoResponse();
    message.result = object.result ?? undefined;
    return message;
  },
};

function createBaseGetCodeRequest(): GetCodeRequest {
  return { networkId: "", chainSpec: undefined, contractAddress: "", blockNumber: "" };
}

export const GetCodeRequest = {
  encode(message: GetCodeRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(34).fork()).ldelim();
    }
    if (message.contractAddress !== "") {
      writer.uint32(10).string(message.contractAddress);
    }
    if (message.blockNumber !== "") {
      writer.uint32(18).string(message.blockNumber);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetCodeRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCodeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.contractAddress = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCodeRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      contractAddress: isSet(object.contractAddress) ? globalThis.String(object.contractAddress) : "",
      blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : "",
    };
  },

  toJSON(message: GetCodeRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.contractAddress !== "") {
      obj.contractAddress = message.contractAddress;
    }
    if (message.blockNumber !== "") {
      obj.blockNumber = message.blockNumber;
    }
    return obj;
  },

  create(base?: DeepPartial<GetCodeRequest>): GetCodeRequest {
    return GetCodeRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetCodeRequest>): GetCodeRequest {
    const message = createBaseGetCodeRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.contractAddress = object.contractAddress ?? "";
    message.blockNumber = object.blockNumber ?? "";
    return message;
  },
};

function createBaseGetCodeResponse(): GetCodeResponse {
  return { code: "" };
}

export const GetCodeResponse = {
  encode(message: GetCodeResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.code !== "") {
      writer.uint32(42).string(message.code);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetCodeResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCodeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 5:
          if (tag !== 42) {
            break;
          }

          message.code = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCodeResponse {
    return { code: isSet(object.code) ? globalThis.String(object.code) : "" };
  },

  toJSON(message: GetCodeResponse): unknown {
    const obj: any = {};
    if (message.code !== "") {
      obj.code = message.code;
    }
    return obj;
  },

  create(base?: DeepPartial<GetCodeResponse>): GetCodeResponse {
    return GetCodeResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetCodeResponse>): GetCodeResponse {
    const message = createBaseGetCodeResponse();
    message.code = object.code ?? "";
    return message;
  },
};

function createBaseGetCallTraceRequest(): GetCallTraceRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    shareId: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    disableOptimizer: false,
    withInternalCalls: false,
    ignoreGasCost: false,
  };
}

export const GetCallTraceRequest = {
  encode(message: GetCallTraceRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(50).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(58).string(message.projectSlug);
    }
    if (message.shareId !== undefined) {
      writer.uint32(74).string(message.shareId);
    }
    if (message.networkId !== "") {
      writer.uint32(42).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(66).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(10).fork()).ldelim();
    }
    if (message.disableOptimizer !== false) {
      writer.uint32(16).bool(message.disableOptimizer);
    }
    if (message.withInternalCalls !== false) {
      writer.uint32(24).bool(message.withInternalCalls);
    }
    if (message.ignoreGasCost !== false) {
      writer.uint32(32).bool(message.ignoreGasCost);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetCallTraceRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCallTraceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 6:
          if (tag !== 50) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.shareId = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.disableOptimizer = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.withInternalCalls = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.ignoreGasCost = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCallTraceRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      shareId: isSet(object.shareId) ? globalThis.String(object.shareId) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      disableOptimizer: isSet(object.disableOptimizer) ? globalThis.Boolean(object.disableOptimizer) : false,
      withInternalCalls: isSet(object.withInternalCalls) ? globalThis.Boolean(object.withInternalCalls) : false,
      ignoreGasCost: isSet(object.ignoreGasCost) ? globalThis.Boolean(object.ignoreGasCost) : false,
    };
  },

  toJSON(message: GetCallTraceRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.shareId !== undefined) {
      obj.shareId = message.shareId;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.disableOptimizer !== false) {
      obj.disableOptimizer = message.disableOptimizer;
    }
    if (message.withInternalCalls !== false) {
      obj.withInternalCalls = message.withInternalCalls;
    }
    if (message.ignoreGasCost !== false) {
      obj.ignoreGasCost = message.ignoreGasCost;
    }
    return obj;
  },

  create(base?: DeepPartial<GetCallTraceRequest>): GetCallTraceRequest {
    return GetCallTraceRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetCallTraceRequest>): GetCallTraceRequest {
    const message = createBaseGetCallTraceRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.shareId = object.shareId ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.disableOptimizer = object.disableOptimizer ?? false;
    message.withInternalCalls = object.withInternalCalls ?? false;
    message.ignoreGasCost = object.ignoreGasCost ?? false;
    return message;
  },
};

function createBaseSentioTraceTransactionRequest(): SentioTraceTransactionRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    disableOptimizer: false,
    withInternalCalls: false,
    debug: false,
    ignoreGasCost: false,
  };
}

export const SentioTraceTransactionRequest = {
  encode(message: SentioTraceTransactionRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(58).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(66).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(50).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(74).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(10).fork()).ldelim();
    }
    if (message.disableOptimizer !== false) {
      writer.uint32(16).bool(message.disableOptimizer);
    }
    if (message.withInternalCalls !== false) {
      writer.uint32(24).bool(message.withInternalCalls);
    }
    if (message.debug !== false) {
      writer.uint32(32).bool(message.debug);
    }
    if (message.ignoreGasCost !== false) {
      writer.uint32(40).bool(message.ignoreGasCost);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SentioTraceTransactionRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSentioTraceTransactionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 7:
          if (tag !== 58) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.disableOptimizer = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.withInternalCalls = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.debug = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.ignoreGasCost = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SentioTraceTransactionRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      disableOptimizer: isSet(object.disableOptimizer) ? globalThis.Boolean(object.disableOptimizer) : false,
      withInternalCalls: isSet(object.withInternalCalls) ? globalThis.Boolean(object.withInternalCalls) : false,
      debug: isSet(object.debug) ? globalThis.Boolean(object.debug) : false,
      ignoreGasCost: isSet(object.ignoreGasCost) ? globalThis.Boolean(object.ignoreGasCost) : false,
    };
  },

  toJSON(message: SentioTraceTransactionRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.disableOptimizer !== false) {
      obj.disableOptimizer = message.disableOptimizer;
    }
    if (message.withInternalCalls !== false) {
      obj.withInternalCalls = message.withInternalCalls;
    }
    if (message.debug !== false) {
      obj.debug = message.debug;
    }
    if (message.ignoreGasCost !== false) {
      obj.ignoreGasCost = message.ignoreGasCost;
    }
    return obj;
  },

  create(base?: DeepPartial<SentioTraceTransactionRequest>): SentioTraceTransactionRequest {
    return SentioTraceTransactionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SentioTraceTransactionRequest>): SentioTraceTransactionRequest {
    const message = createBaseSentioTraceTransactionRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.disableOptimizer = object.disableOptimizer ?? false;
    message.withInternalCalls = object.withInternalCalls ?? false;
    message.debug = object.debug ?? false;
    message.ignoreGasCost = object.ignoreGasCost ?? false;
    return message;
  },
};

function createBaseGetCallTraceResponse(): GetCallTraceResponse {
  return { result: undefined, outputs: undefined };
}

export const GetCallTraceResponse = {
  encode(message: GetCallTraceResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      ListValue.encode(ListValue.wrap(message.result), writer.uint32(10).fork()).ldelim();
    }
    if (message.outputs !== undefined) {
      ListValue.encode(ListValue.wrap(message.outputs), writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetCallTraceResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCallTraceResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = ListValue.unwrap(ListValue.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.outputs = ListValue.unwrap(ListValue.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCallTraceResponse {
    return {
      result: globalThis.Array.isArray(object.result) ? [...object.result] : undefined,
      outputs: globalThis.Array.isArray(object.outputs) ? [...object.outputs] : undefined,
    };
  },

  toJSON(message: GetCallTraceResponse): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = message.result;
    }
    if (message.outputs !== undefined) {
      obj.outputs = message.outputs;
    }
    return obj;
  },

  create(base?: DeepPartial<GetCallTraceResponse>): GetCallTraceResponse {
    return GetCallTraceResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetCallTraceResponse>): GetCallTraceResponse {
    const message = createBaseGetCallTraceResponse();
    message.result = object.result ?? undefined;
    message.outputs = object.outputs ?? undefined;
    return message;
  },
};

function createBaseGetAffectedContractRequest(): GetAffectedContractRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    shareId: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
  };
}

export const GetAffectedContractRequest = {
  encode(message: GetAffectedContractRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(26).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(34).string(message.projectSlug);
    }
    if (message.shareId !== undefined) {
      writer.uint32(50).string(message.shareId);
    }
    if (message.networkId !== "") {
      writer.uint32(18).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(42).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetAffectedContractRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAffectedContractRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3:
          if (tag !== 26) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.shareId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAffectedContractRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      shareId: isSet(object.shareId) ? globalThis.String(object.shareId) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
    };
  },

  toJSON(message: GetAffectedContractRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.shareId !== undefined) {
      obj.shareId = message.shareId;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    return obj;
  },

  create(base?: DeepPartial<GetAffectedContractRequest>): GetAffectedContractRequest {
    return GetAffectedContractRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetAffectedContractRequest>): GetAffectedContractRequest {
    const message = createBaseGetAffectedContractRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.shareId = object.shareId ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    return message;
  },
};

function createBaseGetAffectedContractResponse(): GetAffectedContractResponse {
  return { addresses: [] };
}

export const GetAffectedContractResponse = {
  encode(message: GetAffectedContractResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.addresses) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetAffectedContractResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetAffectedContractResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.addresses.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetAffectedContractResponse {
    return {
      addresses: globalThis.Array.isArray(object?.addresses)
        ? object.addresses.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: GetAffectedContractResponse): unknown {
    const obj: any = {};
    if (message.addresses?.length) {
      obj.addresses = message.addresses;
    }
    return obj;
  },

  create(base?: DeepPartial<GetAffectedContractResponse>): GetAffectedContractResponse {
    return GetAffectedContractResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetAffectedContractResponse>): GetAffectedContractResponse {
    const message = createBaseGetAffectedContractResponse();
    message.addresses = object.addresses?.map((e) => e) || [];
    return message;
  },
};

function createBaseSimulateTransactionRequest(): SimulateTransactionRequest {
  return { projectOwner: undefined, projectSlug: undefined, simulation: undefined };
}

export const SimulateTransactionRequest = {
  encode(message: SimulateTransactionRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.simulation !== undefined) {
      Simulation.encode(message.simulation, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SimulateTransactionRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimulateTransactionRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.simulation = Simulation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimulateTransactionRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      simulation: isSet(object.simulation) ? Simulation.fromJSON(object.simulation) : undefined,
    };
  },

  toJSON(message: SimulateTransactionRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.simulation !== undefined) {
      obj.simulation = Simulation.toJSON(message.simulation);
    }
    return obj;
  },

  create(base?: DeepPartial<SimulateTransactionRequest>): SimulateTransactionRequest {
    return SimulateTransactionRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SimulateTransactionRequest>): SimulateTransactionRequest {
    const message = createBaseSimulateTransactionRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.simulation = (object.simulation !== undefined && object.simulation !== null)
      ? Simulation.fromPartial(object.simulation)
      : undefined;
    return message;
  },
};

function createBaseSimulateTransactionResponse(): SimulateTransactionResponse {
  return { simulation: undefined };
}

export const SimulateTransactionResponse = {
  encode(message: SimulateTransactionResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.simulation !== undefined) {
      Simulation.encode(message.simulation, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SimulateTransactionResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimulateTransactionResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulation = Simulation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimulateTransactionResponse {
    return { simulation: isSet(object.simulation) ? Simulation.fromJSON(object.simulation) : undefined };
  },

  toJSON(message: SimulateTransactionResponse): unknown {
    const obj: any = {};
    if (message.simulation !== undefined) {
      obj.simulation = Simulation.toJSON(message.simulation);
    }
    return obj;
  },

  create(base?: DeepPartial<SimulateTransactionResponse>): SimulateTransactionResponse {
    return SimulateTransactionResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SimulateTransactionResponse>): SimulateTransactionResponse {
    const message = createBaseSimulateTransactionResponse();
    message.simulation = (object.simulation !== undefined && object.simulation !== null)
      ? Simulation.fromPartial(object.simulation)
      : undefined;
    return message;
  },
};

function createBaseSimulateTransactionBundleRequest(): SimulateTransactionBundleRequest {
  return { projectOwner: undefined, projectSlug: undefined, simulations: [] };
}

export const SimulateTransactionBundleRequest = {
  encode(message: SimulateTransactionBundleRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    for (const v of message.simulations) {
      Simulation.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SimulateTransactionBundleRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimulateTransactionBundleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.simulations.push(Simulation.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimulateTransactionBundleRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      simulations: globalThis.Array.isArray(object?.simulations)
        ? object.simulations.map((e: any) => Simulation.fromJSON(e))
        : [],
    };
  },

  toJSON(message: SimulateTransactionBundleRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.simulations?.length) {
      obj.simulations = message.simulations.map((e) => Simulation.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<SimulateTransactionBundleRequest>): SimulateTransactionBundleRequest {
    return SimulateTransactionBundleRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SimulateTransactionBundleRequest>): SimulateTransactionBundleRequest {
    const message = createBaseSimulateTransactionBundleRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.simulations = object.simulations?.map((e) => Simulation.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSimulateTransactionBundleResponse(): SimulateTransactionBundleResponse {
  return { bundleId: "", simulations: [], error: "" };
}

export const SimulateTransactionBundleResponse = {
  encode(message: SimulateTransactionBundleResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.bundleId !== "") {
      writer.uint32(10).string(message.bundleId);
    }
    for (const v of message.simulations) {
      Simulation.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    if (message.error !== "") {
      writer.uint32(26).string(message.error);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SimulateTransactionBundleResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimulateTransactionBundleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.bundleId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.simulations.push(Simulation.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.error = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimulateTransactionBundleResponse {
    return {
      bundleId: isSet(object.bundleId) ? globalThis.String(object.bundleId) : "",
      simulations: globalThis.Array.isArray(object?.simulations)
        ? object.simulations.map((e: any) => Simulation.fromJSON(e))
        : [],
      error: isSet(object.error) ? globalThis.String(object.error) : "",
    };
  },

  toJSON(message: SimulateTransactionBundleResponse): unknown {
    const obj: any = {};
    if (message.bundleId !== "") {
      obj.bundleId = message.bundleId;
    }
    if (message.simulations?.length) {
      obj.simulations = message.simulations.map((e) => Simulation.toJSON(e));
    }
    if (message.error !== "") {
      obj.error = message.error;
    }
    return obj;
  },

  create(base?: DeepPartial<SimulateTransactionBundleResponse>): SimulateTransactionBundleResponse {
    return SimulateTransactionBundleResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SimulateTransactionBundleResponse>): SimulateTransactionBundleResponse {
    const message = createBaseSimulateTransactionBundleResponse();
    message.bundleId = object.bundleId ?? "";
    message.simulations = object.simulations?.map((e) => Simulation.fromPartial(e)) || [];
    message.error = object.error ?? "";
    return message;
  },
};

function createBaseGetSimulationBundleRequest(): GetSimulationBundleRequest {
  return { projectOwner: undefined, projectSlug: undefined, bundleId: "" };
}

export const GetSimulationBundleRequest = {
  encode(message: GetSimulationBundleRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.bundleId !== "") {
      writer.uint32(26).string(message.bundleId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetSimulationBundleRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSimulationBundleRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.bundleId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSimulationBundleRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      bundleId: isSet(object.bundleId) ? globalThis.String(object.bundleId) : "",
    };
  },

  toJSON(message: GetSimulationBundleRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.bundleId !== "") {
      obj.bundleId = message.bundleId;
    }
    return obj;
  },

  create(base?: DeepPartial<GetSimulationBundleRequest>): GetSimulationBundleRequest {
    return GetSimulationBundleRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetSimulationBundleRequest>): GetSimulationBundleRequest {
    const message = createBaseGetSimulationBundleRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.bundleId = object.bundleId ?? "";
    return message;
  },
};

function createBaseGetSimulationBundleResponse(): GetSimulationBundleResponse {
  return { simulations: [], error: "" };
}

export const GetSimulationBundleResponse = {
  encode(message: GetSimulationBundleResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.simulations) {
      Simulation.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    if (message.error !== "") {
      writer.uint32(18).string(message.error);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetSimulationBundleResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSimulationBundleResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulations.push(Simulation.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.error = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSimulationBundleResponse {
    return {
      simulations: globalThis.Array.isArray(object?.simulations)
        ? object.simulations.map((e: any) => Simulation.fromJSON(e))
        : [],
      error: isSet(object.error) ? globalThis.String(object.error) : "",
    };
  },

  toJSON(message: GetSimulationBundleResponse): unknown {
    const obj: any = {};
    if (message.simulations?.length) {
      obj.simulations = message.simulations.map((e) => Simulation.toJSON(e));
    }
    if (message.error !== "") {
      obj.error = message.error;
    }
    return obj;
  },

  create(base?: DeepPartial<GetSimulationBundleResponse>): GetSimulationBundleResponse {
    return GetSimulationBundleResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetSimulationBundleResponse>): GetSimulationBundleResponse {
    const message = createBaseGetSimulationBundleResponse();
    message.simulations = object.simulations?.map((e) => Simulation.fromPartial(e)) || [];
    message.error = object.error ?? "";
    return message;
  },
};

function createBaseGetSimulationsRequest(): GetSimulationsRequest {
  return { projectOwner: undefined, projectSlug: undefined, labelContains: undefined, page: 0, pageSize: 0 };
}

export const GetSimulationsRequest = {
  encode(message: GetSimulationsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.labelContains !== undefined) {
      writer.uint32(42).string(message.labelContains);
    }
    if (message.page !== 0) {
      writer.uint32(24).int32(message.page);
    }
    if (message.pageSize !== 0) {
      writer.uint32(32).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetSimulationsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSimulationsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.labelContains = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.page = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSimulationsRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      labelContains: isSet(object.labelContains) ? globalThis.String(object.labelContains) : undefined,
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetSimulationsRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.labelContains !== undefined) {
      obj.labelContains = message.labelContains;
    }
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create(base?: DeepPartial<GetSimulationsRequest>): GetSimulationsRequest {
    return GetSimulationsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetSimulationsRequest>): GetSimulationsRequest {
    const message = createBaseGetSimulationsRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.labelContains = object.labelContains ?? undefined;
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseGetSimulationsResponse(): GetSimulationsResponse {
  return { simulations: [], count: BigInt("0"), page: 0, pageSize: 0 };
}

export const GetSimulationsResponse = {
  encode(message: GetSimulationsResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.simulations) {
      Simulation.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    if (message.count !== BigInt("0")) {
      if (BigInt.asIntN(64, message.count) !== message.count) {
        throw new globalThis.Error("value provided for field message.count of type int64 too large");
      }
      writer.uint32(16).int64(message.count.toString());
    }
    if (message.page !== 0) {
      writer.uint32(24).int32(message.page);
    }
    if (message.pageSize !== 0) {
      writer.uint32(32).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetSimulationsResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSimulationsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulations.push(Simulation.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.count = longToBigint(reader.int64() as Long);
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.page = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSimulationsResponse {
    return {
      simulations: globalThis.Array.isArray(object?.simulations)
        ? object.simulations.map((e: any) => Simulation.fromJSON(e))
        : [],
      count: isSet(object.count) ? BigInt(object.count) : BigInt("0"),
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetSimulationsResponse): unknown {
    const obj: any = {};
    if (message.simulations?.length) {
      obj.simulations = message.simulations.map((e) => Simulation.toJSON(e));
    }
    if (message.count !== BigInt("0")) {
      obj.count = message.count.toString();
    }
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create(base?: DeepPartial<GetSimulationsResponse>): GetSimulationsResponse {
    return GetSimulationsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetSimulationsResponse>): GetSimulationsResponse {
    const message = createBaseGetSimulationsResponse();
    message.simulations = object.simulations?.map((e) => Simulation.fromPartial(e)) || [];
    message.count = object.count ?? BigInt("0");
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseGetSimulationRequest(): GetSimulationRequest {
  return { projectOwner: undefined, projectSlug: undefined, simulationId: "" };
}

export const GetSimulationRequest = {
  encode(message: GetSimulationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.simulationId !== "") {
      writer.uint32(26).string(message.simulationId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetSimulationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSimulationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.simulationId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSimulationRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      simulationId: isSet(object.simulationId) ? globalThis.String(object.simulationId) : "",
    };
  },

  toJSON(message: GetSimulationRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.simulationId !== "") {
      obj.simulationId = message.simulationId;
    }
    return obj;
  },

  create(base?: DeepPartial<GetSimulationRequest>): GetSimulationRequest {
    return GetSimulationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetSimulationRequest>): GetSimulationRequest {
    const message = createBaseGetSimulationRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.simulationId = object.simulationId ?? "";
    return message;
  },
};

function createBaseGetSimulationResponse(): GetSimulationResponse {
  return { simulation: undefined };
}

export const GetSimulationResponse = {
  encode(message: GetSimulationResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.simulation !== undefined) {
      Simulation.encode(message.simulation, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetSimulationResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetSimulationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulation = Simulation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetSimulationResponse {
    return { simulation: isSet(object.simulation) ? Simulation.fromJSON(object.simulation) : undefined };
  },

  toJSON(message: GetSimulationResponse): unknown {
    const obj: any = {};
    if (message.simulation !== undefined) {
      obj.simulation = Simulation.toJSON(message.simulation);
    }
    return obj;
  },

  create(base?: DeepPartial<GetSimulationResponse>): GetSimulationResponse {
    return GetSimulationResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetSimulationResponse>): GetSimulationResponse {
    const message = createBaseGetSimulationResponse();
    message.simulation = (object.simulation !== undefined && object.simulation !== null)
      ? Simulation.fromPartial(object.simulation)
      : undefined;
    return message;
  },
};

function createBaseSyncContractsRequest(): SyncContractsRequest {
  return { networkId: "", chainSpec: undefined, addresses: [], disableOptimizer: false };
}

export const SyncContractsRequest = {
  encode(message: SyncContractsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(34).fork()).ldelim();
    }
    for (const v of message.addresses) {
      writer.uint32(10).string(v!);
    }
    if (message.disableOptimizer !== false) {
      writer.uint32(16).bool(message.disableOptimizer);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SyncContractsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSyncContractsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.addresses.push(reader.string());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.disableOptimizer = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SyncContractsRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      addresses: globalThis.Array.isArray(object?.addresses)
        ? object.addresses.map((e: any) => globalThis.String(e))
        : [],
      disableOptimizer: isSet(object.disableOptimizer) ? globalThis.Boolean(object.disableOptimizer) : false,
    };
  },

  toJSON(message: SyncContractsRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.addresses?.length) {
      obj.addresses = message.addresses;
    }
    if (message.disableOptimizer !== false) {
      obj.disableOptimizer = message.disableOptimizer;
    }
    return obj;
  },

  create(base?: DeepPartial<SyncContractsRequest>): SyncContractsRequest {
    return SyncContractsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SyncContractsRequest>): SyncContractsRequest {
    const message = createBaseSyncContractsRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.addresses = object.addresses?.map((e) => e) || [];
    message.disableOptimizer = object.disableOptimizer ?? false;
    return message;
  },
};

function createBaseUniversalSearchRequest(): UniversalSearchRequest {
  return { q: "", limit: 0 };
}

export const UniversalSearchRequest = {
  encode(message: UniversalSearchRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.q !== "") {
      writer.uint32(10).string(message.q);
    }
    if (message.limit !== 0) {
      writer.uint32(16).int32(message.limit);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UniversalSearchRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUniversalSearchRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.q = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.limit = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UniversalSearchRequest {
    return {
      q: isSet(object.q) ? globalThis.String(object.q) : "",
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : 0,
    };
  },

  toJSON(message: UniversalSearchRequest): unknown {
    const obj: any = {};
    if (message.q !== "") {
      obj.q = message.q;
    }
    if (message.limit !== 0) {
      obj.limit = Math.round(message.limit);
    }
    return obj;
  },

  create(base?: DeepPartial<UniversalSearchRequest>): UniversalSearchRequest {
    return UniversalSearchRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UniversalSearchRequest>): UniversalSearchRequest {
    const message = createBaseUniversalSearchRequest();
    message.q = object.q ?? "";
    message.limit = object.limit ?? 0;
    return message;
  },
};

function createBaseUniversalSearchResultTransaction(): UniversalSearchResultTransaction {
  return {
    hash: "",
    blockNumber: BigInt("0"),
    blockHash: "",
    from: "",
    to: "",
    methodId: "",
    status: 0,
    isTrace: false,
  };
}

export const UniversalSearchResultTransaction = {
  encode(message: UniversalSearchResultTransaction, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.hash !== "") {
      writer.uint32(10).string(message.hash);
    }
    if (message.blockNumber !== BigInt("0")) {
      if (BigInt.asUintN(64, message.blockNumber) !== message.blockNumber) {
        throw new globalThis.Error("value provided for field message.blockNumber of type uint64 too large");
      }
      writer.uint32(16).uint64(message.blockNumber.toString());
    }
    if (message.blockHash !== "") {
      writer.uint32(26).string(message.blockHash);
    }
    if (message.from !== "") {
      writer.uint32(34).string(message.from);
    }
    if (message.to !== "") {
      writer.uint32(42).string(message.to);
    }
    if (message.methodId !== "") {
      writer.uint32(50).string(message.methodId);
    }
    if (message.status !== 0) {
      writer.uint32(56).int32(message.status);
    }
    if (message.isTrace !== false) {
      writer.uint32(64).bool(message.isTrace);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UniversalSearchResultTransaction {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUniversalSearchResultTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.hash = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.blockNumber = longToBigint(reader.uint64() as Long);
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.blockHash = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.from = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.to = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.methodId = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.status = reader.int32();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.isTrace = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UniversalSearchResultTransaction {
    return {
      hash: isSet(object.hash) ? globalThis.String(object.hash) : "",
      blockNumber: isSet(object.blockNumber) ? BigInt(object.blockNumber) : BigInt("0"),
      blockHash: isSet(object.blockHash) ? globalThis.String(object.blockHash) : "",
      from: isSet(object.from) ? globalThis.String(object.from) : "",
      to: isSet(object.to) ? globalThis.String(object.to) : "",
      methodId: isSet(object.methodId) ? globalThis.String(object.methodId) : "",
      status: isSet(object.status) ? globalThis.Number(object.status) : 0,
      isTrace: isSet(object.isTrace) ? globalThis.Boolean(object.isTrace) : false,
    };
  },

  toJSON(message: UniversalSearchResultTransaction): unknown {
    const obj: any = {};
    if (message.hash !== "") {
      obj.hash = message.hash;
    }
    if (message.blockNumber !== BigInt("0")) {
      obj.blockNumber = message.blockNumber.toString();
    }
    if (message.blockHash !== "") {
      obj.blockHash = message.blockHash;
    }
    if (message.from !== "") {
      obj.from = message.from;
    }
    if (message.to !== "") {
      obj.to = message.to;
    }
    if (message.methodId !== "") {
      obj.methodId = message.methodId;
    }
    if (message.status !== 0) {
      obj.status = Math.round(message.status);
    }
    if (message.isTrace !== false) {
      obj.isTrace = message.isTrace;
    }
    return obj;
  },

  create(base?: DeepPartial<UniversalSearchResultTransaction>): UniversalSearchResultTransaction {
    return UniversalSearchResultTransaction.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UniversalSearchResultTransaction>): UniversalSearchResultTransaction {
    const message = createBaseUniversalSearchResultTransaction();
    message.hash = object.hash ?? "";
    message.blockNumber = object.blockNumber ?? BigInt("0");
    message.blockHash = object.blockHash ?? "";
    message.from = object.from ?? "";
    message.to = object.to ?? "";
    message.methodId = object.methodId ?? "";
    message.status = object.status ?? 0;
    message.isTrace = object.isTrace ?? false;
    return message;
  },
};

function createBaseUniversalSearchResultContract(): UniversalSearchResultContract {
  return { address: "", contractName: "" };
}

export const UniversalSearchResultContract = {
  encode(message: UniversalSearchResultContract, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.address !== "") {
      writer.uint32(10).string(message.address);
    }
    if (message.contractName !== "") {
      writer.uint32(18).string(message.contractName);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UniversalSearchResultContract {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUniversalSearchResultContract();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.address = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.contractName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UniversalSearchResultContract {
    return {
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      contractName: isSet(object.contractName) ? globalThis.String(object.contractName) : "",
    };
  },

  toJSON(message: UniversalSearchResultContract): unknown {
    const obj: any = {};
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.contractName !== "") {
      obj.contractName = message.contractName;
    }
    return obj;
  },

  create(base?: DeepPartial<UniversalSearchResultContract>): UniversalSearchResultContract {
    return UniversalSearchResultContract.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UniversalSearchResultContract>): UniversalSearchResultContract {
    const message = createBaseUniversalSearchResultContract();
    message.address = object.address ?? "";
    message.contractName = object.contractName ?? "";
    return message;
  },
};

function createBaseUniversalSearchResult(): UniversalSearchResult {
  return { chainId: "", timestampMs: BigInt("0"), transaction: undefined, contract: undefined };
}

export const UniversalSearchResult = {
  encode(message: UniversalSearchResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.chainId !== "") {
      writer.uint32(10).string(message.chainId);
    }
    if (message.timestampMs !== BigInt("0")) {
      if (BigInt.asUintN(64, message.timestampMs) !== message.timestampMs) {
        throw new globalThis.Error("value provided for field message.timestampMs of type uint64 too large");
      }
      writer.uint32(16).uint64(message.timestampMs.toString());
    }
    if (message.transaction !== undefined) {
      UniversalSearchResultTransaction.encode(message.transaction, writer.uint32(26).fork()).ldelim();
    }
    if (message.contract !== undefined) {
      UniversalSearchResultContract.encode(message.contract, writer.uint32(34).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UniversalSearchResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUniversalSearchResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.chainId = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.timestampMs = longToBigint(reader.uint64() as Long);
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.transaction = UniversalSearchResultTransaction.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.contract = UniversalSearchResultContract.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UniversalSearchResult {
    return {
      chainId: isSet(object.chainId) ? globalThis.String(object.chainId) : "",
      timestampMs: isSet(object.timestampMs) ? BigInt(object.timestampMs) : BigInt("0"),
      transaction: isSet(object.transaction)
        ? UniversalSearchResultTransaction.fromJSON(object.transaction)
        : undefined,
      contract: isSet(object.contract) ? UniversalSearchResultContract.fromJSON(object.contract) : undefined,
    };
  },

  toJSON(message: UniversalSearchResult): unknown {
    const obj: any = {};
    if (message.chainId !== "") {
      obj.chainId = message.chainId;
    }
    if (message.timestampMs !== BigInt("0")) {
      obj.timestampMs = message.timestampMs.toString();
    }
    if (message.transaction !== undefined) {
      obj.transaction = UniversalSearchResultTransaction.toJSON(message.transaction);
    }
    if (message.contract !== undefined) {
      obj.contract = UniversalSearchResultContract.toJSON(message.contract);
    }
    return obj;
  },

  create(base?: DeepPartial<UniversalSearchResult>): UniversalSearchResult {
    return UniversalSearchResult.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UniversalSearchResult>): UniversalSearchResult {
    const message = createBaseUniversalSearchResult();
    message.chainId = object.chainId ?? "";
    message.timestampMs = object.timestampMs ?? BigInt("0");
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? UniversalSearchResultTransaction.fromPartial(object.transaction)
      : undefined;
    message.contract = (object.contract !== undefined && object.contract !== null)
      ? UniversalSearchResultContract.fromPartial(object.contract)
      : undefined;
    return message;
  },
};

function createBaseUniversalSearchResponse(): UniversalSearchResponse {
  return { results: [] };
}

export const UniversalSearchResponse = {
  encode(message: UniversalSearchResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.results) {
      UniversalSearchResult.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UniversalSearchResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUniversalSearchResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.results.push(UniversalSearchResult.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UniversalSearchResponse {
    return {
      results: globalThis.Array.isArray(object?.results)
        ? object.results.map((e: any) => UniversalSearchResult.fromJSON(e))
        : [],
    };
  },

  toJSON(message: UniversalSearchResponse): unknown {
    const obj: any = {};
    if (message.results?.length) {
      obj.results = message.results.map((e) => UniversalSearchResult.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<UniversalSearchResponse>): UniversalSearchResponse {
    return UniversalSearchResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UniversalSearchResponse>): UniversalSearchResponse {
    const message = createBaseUniversalSearchResponse();
    message.results = object.results?.map((e) => UniversalSearchResult.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetContractIndexRequest(): GetContractIndexRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    userCompilationId: undefined,
    address: "",
  };
}

export const GetContractIndexRequest = {
  encode(message: GetContractIndexRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(26).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(34).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(58).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(42).fork()).ldelim();
    }
    if (message.userCompilationId !== undefined) {
      writer.uint32(50).string(message.userCompilationId);
    }
    if (message.address !== "") {
      writer.uint32(18).string(message.address);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetContractIndexRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetContractIndexRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3:
          if (tag !== 26) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.address = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetContractIndexRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : "",
    };
  },

  toJSON(message: GetContractIndexRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.userCompilationId !== undefined) {
      obj.userCompilationId = message.userCompilationId;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    return obj;
  },

  create(base?: DeepPartial<GetContractIndexRequest>): GetContractIndexRequest {
    return GetContractIndexRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetContractIndexRequest>): GetContractIndexRequest {
    const message = createBaseGetContractIndexRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.userCompilationId = object.userCompilationId ?? undefined;
    message.address = object.address ?? "";
    return message;
  },
};

function createBaseGetContractIndexResponse(): GetContractIndexResponse {
  return { index: undefined };
}

export const GetContractIndexResponse = {
  encode(message: GetContractIndexResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.index !== undefined) {
      Index.encode(message.index, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetContractIndexResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetContractIndexResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.index = Index.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetContractIndexResponse {
    return { index: isSet(object.index) ? Index.fromJSON(object.index) : undefined };
  },

  toJSON(message: GetContractIndexResponse): unknown {
    const obj: any = {};
    if (message.index !== undefined) {
      obj.index = Index.toJSON(message.index);
    }
    return obj;
  },

  create(base?: DeepPartial<GetContractIndexResponse>): GetContractIndexResponse {
    return GetContractIndexResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetContractIndexResponse>): GetContractIndexResponse {
    const message = createBaseGetContractIndexResponse();
    message.index = (object.index !== undefined && object.index !== null) ? Index.fromPartial(object.index) : undefined;
    return message;
  },
};

function createBaseGetDebugTraceRequest(): GetDebugTraceRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    shareId: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    memoryCompressionWindow: 0,
    disableOptimizer: false,
    ignoreGasCost: false,
  };
}

export const GetDebugTraceRequest = {
  encode(message: GetDebugTraceRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(50).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(58).string(message.projectSlug);
    }
    if (message.shareId !== undefined) {
      writer.uint32(74).string(message.shareId);
    }
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(66).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(18).fork()).ldelim();
    }
    if (message.memoryCompressionWindow !== 0) {
      writer.uint32(24).int32(message.memoryCompressionWindow);
    }
    if (message.disableOptimizer !== false) {
      writer.uint32(32).bool(message.disableOptimizer);
    }
    if (message.ignoreGasCost !== false) {
      writer.uint32(40).bool(message.ignoreGasCost);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetDebugTraceRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetDebugTraceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 6:
          if (tag !== 50) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.shareId = reader.string();
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.memoryCompressionWindow = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.disableOptimizer = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.ignoreGasCost = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetDebugTraceRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      shareId: isSet(object.shareId) ? globalThis.String(object.shareId) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      memoryCompressionWindow: isSet(object.memoryCompressionWindow)
        ? globalThis.Number(object.memoryCompressionWindow)
        : 0,
      disableOptimizer: isSet(object.disableOptimizer) ? globalThis.Boolean(object.disableOptimizer) : false,
      ignoreGasCost: isSet(object.ignoreGasCost) ? globalThis.Boolean(object.ignoreGasCost) : false,
    };
  },

  toJSON(message: GetDebugTraceRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.shareId !== undefined) {
      obj.shareId = message.shareId;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.memoryCompressionWindow !== 0) {
      obj.memoryCompressionWindow = Math.round(message.memoryCompressionWindow);
    }
    if (message.disableOptimizer !== false) {
      obj.disableOptimizer = message.disableOptimizer;
    }
    if (message.ignoreGasCost !== false) {
      obj.ignoreGasCost = message.ignoreGasCost;
    }
    return obj;
  },

  create(base?: DeepPartial<GetDebugTraceRequest>): GetDebugTraceRequest {
    return GetDebugTraceRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetDebugTraceRequest>): GetDebugTraceRequest {
    const message = createBaseGetDebugTraceRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.shareId = object.shareId ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.memoryCompressionWindow = object.memoryCompressionWindow ?? 0;
    message.disableOptimizer = object.disableOptimizer ?? false;
    message.ignoreGasCost = object.ignoreGasCost ?? false;
    return message;
  },
};

function createBaseLookupSignatureRequest(): LookupSignatureRequest {
  return {
    chainSpec: undefined,
    address: undefined,
    hexSignature: "",
    type: 0,
    data: undefined,
    output: undefined,
    topics: [],
  };
}

export const LookupSignatureRequest = {
  encode(message: LookupSignatureRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(50).fork()).ldelim();
    }
    if (message.address !== undefined) {
      writer.uint32(58).string(message.address);
    }
    if (message.hexSignature !== "") {
      writer.uint32(10).string(message.hexSignature);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.data !== undefined) {
      writer.uint32(26).string(message.data);
    }
    if (message.output !== undefined) {
      writer.uint32(34).string(message.output);
    }
    for (const v of message.topics) {
      writer.uint32(42).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LookupSignatureRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLookupSignatureRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 6:
          if (tag !== 50) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.address = reader.string();
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.hexSignature = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.output = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.topics.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LookupSignatureRequest {
    return {
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : undefined,
      hexSignature: isSet(object.hexSignature) ? globalThis.String(object.hexSignature) : "",
      type: isSet(object.type) ? signatureTypeFromJSON(object.type) : 0,
      data: isSet(object.data) ? globalThis.String(object.data) : undefined,
      output: isSet(object.output) ? globalThis.String(object.output) : undefined,
      topics: globalThis.Array.isArray(object?.topics) ? object.topics.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: LookupSignatureRequest): unknown {
    const obj: any = {};
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.address !== undefined) {
      obj.address = message.address;
    }
    if (message.hexSignature !== "") {
      obj.hexSignature = message.hexSignature;
    }
    if (message.type !== 0) {
      obj.type = signatureTypeToJSON(message.type);
    }
    if (message.data !== undefined) {
      obj.data = message.data;
    }
    if (message.output !== undefined) {
      obj.output = message.output;
    }
    if (message.topics?.length) {
      obj.topics = message.topics;
    }
    return obj;
  },

  create(base?: DeepPartial<LookupSignatureRequest>): LookupSignatureRequest {
    return LookupSignatureRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<LookupSignatureRequest>): LookupSignatureRequest {
    const message = createBaseLookupSignatureRequest();
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.address = object.address ?? undefined;
    message.hexSignature = object.hexSignature ?? "";
    message.type = object.type ?? 0;
    message.data = object.data ?? undefined;
    message.output = object.output ?? undefined;
    message.topics = object.topics?.map((e) => e) || [];
    return message;
  },
};

function createBaseLookupSignatureResponse(): LookupSignatureResponse {
  return { textSignature: "", abiItem: undefined };
}

export const LookupSignatureResponse = {
  encode(message: LookupSignatureResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.textSignature !== "") {
      writer.uint32(10).string(message.textSignature);
    }
    if (message.abiItem !== undefined) {
      writer.uint32(18).string(message.abiItem);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LookupSignatureResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLookupSignatureResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.textSignature = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.abiItem = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LookupSignatureResponse {
    return {
      textSignature: isSet(object.textSignature) ? globalThis.String(object.textSignature) : "",
      abiItem: isSet(object.abiItem) ? globalThis.String(object.abiItem) : undefined,
    };
  },

  toJSON(message: LookupSignatureResponse): unknown {
    const obj: any = {};
    if (message.textSignature !== "") {
      obj.textSignature = message.textSignature;
    }
    if (message.abiItem !== undefined) {
      obj.abiItem = message.abiItem;
    }
    return obj;
  },

  create(base?: DeepPartial<LookupSignatureResponse>): LookupSignatureResponse {
    return LookupSignatureResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<LookupSignatureResponse>): LookupSignatureResponse {
    const message = createBaseLookupSignatureResponse();
    message.textSignature = object.textSignature ?? "";
    message.abiItem = object.abiItem ?? undefined;
    return message;
  },
};

function createBaseGetABIRequest(): GetABIRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    address: "",
  };
}

export const GetABIRequest = {
  encode(message: GetABIRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(50).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(34).fork()).ldelim();
    }
    if (message.address !== "") {
      writer.uint32(42).string(message.address);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetABIRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetABIRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.address = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetABIRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : "",
    };
  },

  toJSON(message: GetABIRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    return obj;
  },

  create(base?: DeepPartial<GetABIRequest>): GetABIRequest {
    return GetABIRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetABIRequest>): GetABIRequest {
    const message = createBaseGetABIRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.address = object.address ?? "";
    return message;
  },
};

function createBaseGetABIResponse(): GetABIResponse {
  return { ABI: "" };
}

export const GetABIResponse = {
  encode(message: GetABIResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.ABI !== "") {
      writer.uint32(10).string(message.ABI);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetABIResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetABIResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ABI = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetABIResponse {
    return { ABI: isSet(object.ABI) ? globalThis.String(object.ABI) : "" };
  },

  toJSON(message: GetABIResponse): unknown {
    const obj: any = {};
    if (message.ABI !== "") {
      obj.ABI = message.ABI;
    }
    return obj;
  },

  create(base?: DeepPartial<GetABIResponse>): GetABIResponse {
    return GetABIResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetABIResponse>): GetABIResponse {
    const message = createBaseGetABIResponse();
    message.ABI = object.ABI ?? "";
    return message;
  },
};

function createBaseGetContractNameRequest(): GetContractNameRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    address: "",
  };
}

export const GetContractNameRequest = {
  encode(message: GetContractNameRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(50).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(34).fork()).ldelim();
    }
    if (message.address !== "") {
      writer.uint32(42).string(message.address);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetContractNameRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetContractNameRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.address = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetContractNameRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : "",
    };
  },

  toJSON(message: GetContractNameRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    return obj;
  },

  create(base?: DeepPartial<GetContractNameRequest>): GetContractNameRequest {
    return GetContractNameRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetContractNameRequest>): GetContractNameRequest {
    const message = createBaseGetContractNameRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.address = object.address ?? "";
    return message;
  },
};

function createBaseGetContractNameResponse(): GetContractNameResponse {
  return { contractName: "" };
}

export const GetContractNameResponse = {
  encode(message: GetContractNameResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.contractName !== "") {
      writer.uint32(10).string(message.contractName);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetContractNameResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetContractNameResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.contractName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetContractNameResponse {
    return { contractName: isSet(object.contractName) ? globalThis.String(object.contractName) : "" };
  },

  toJSON(message: GetContractNameResponse): unknown {
    const obj: any = {};
    if (message.contractName !== "") {
      obj.contractName = message.contractName;
    }
    return obj;
  },

  create(base?: DeepPartial<GetContractNameResponse>): GetContractNameResponse {
    return GetContractNameResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetContractNameResponse>): GetContractNameResponse {
    const message = createBaseGetContractNameResponse();
    message.contractName = object.contractName ?? "";
    return message;
  },
};

function createBaseStateDiffRequest(): StateDiffRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    shareId: undefined,
    networkId: "",
    chainSpec: undefined,
    txId: undefined,
    decode: undefined,
  };
}

export const StateDiffRequest = {
  encode(message: StateDiffRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(26).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(34).string(message.projectSlug);
    }
    if (message.shareId !== undefined) {
      writer.uint32(58).string(message.shareId);
    }
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(50).fork()).ldelim();
    }
    if (message.txId !== undefined) {
      TxIdentifier.encode(message.txId, writer.uint32(18).fork()).ldelim();
    }
    if (message.decode !== undefined) {
      writer.uint32(40).bool(message.decode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): StateDiffRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseStateDiffRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3:
          if (tag !== 26) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.shareId = reader.string();
          continue;
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.txId = TxIdentifier.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.decode = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): StateDiffRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      shareId: isSet(object.shareId) ? globalThis.String(object.shareId) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txId: isSet(object.txId) ? TxIdentifier.fromJSON(object.txId) : undefined,
      decode: isSet(object.decode) ? globalThis.Boolean(object.decode) : undefined,
    };
  },

  toJSON(message: StateDiffRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.shareId !== undefined) {
      obj.shareId = message.shareId;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txId !== undefined) {
      obj.txId = TxIdentifier.toJSON(message.txId);
    }
    if (message.decode !== undefined) {
      obj.decode = message.decode;
    }
    return obj;
  },

  create(base?: DeepPartial<StateDiffRequest>): StateDiffRequest {
    return StateDiffRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<StateDiffRequest>): StateDiffRequest {
    const message = createBaseStateDiffRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.shareId = object.shareId ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txId = (object.txId !== undefined && object.txId !== null)
      ? TxIdentifier.fromPartial(object.txId)
      : undefined;
    message.decode = object.decode ?? undefined;
    return message;
  },
};

function createBaseUploadUserCompilationRequest(): UploadUserCompilationRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    compileSpec: undefined,
    fromContract: undefined,
    fromUserCompilation: undefined,
    verifySpec: undefined,
    name: undefined,
  };
}

export const UploadUserCompilationRequest = {
  encode(message: UploadUserCompilationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.compileSpec !== undefined) {
      SourceSpec.encode(message.compileSpec, writer.uint32(26).fork()).ldelim();
    }
    if (message.fromContract !== undefined) {
      UploadUserCompilationRequest_FromContract.encode(message.fromContract, writer.uint32(50).fork()).ldelim();
    }
    if (message.fromUserCompilation !== undefined) {
      UploadUserCompilationRequest_FromUserCompilation.encode(message.fromUserCompilation, writer.uint32(58).fork())
        .ldelim();
    }
    if (message.verifySpec !== undefined) {
      Verification.encode(message.verifySpec, writer.uint32(34).fork()).ldelim();
    }
    if (message.name !== undefined) {
      writer.uint32(42).string(message.name);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UploadUserCompilationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUploadUserCompilationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.compileSpec = SourceSpec.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.fromContract = UploadUserCompilationRequest_FromContract.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.fromUserCompilation = UploadUserCompilationRequest_FromUserCompilation.decode(
            reader,
            reader.uint32(),
          );
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.verifySpec = Verification.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UploadUserCompilationRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      compileSpec: isSet(object.compileSpec) ? SourceSpec.fromJSON(object.compileSpec) : undefined,
      fromContract: isSet(object.fromContract)
        ? UploadUserCompilationRequest_FromContract.fromJSON(object.fromContract)
        : undefined,
      fromUserCompilation: isSet(object.fromUserCompilation)
        ? UploadUserCompilationRequest_FromUserCompilation.fromJSON(object.fromUserCompilation)
        : undefined,
      verifySpec: isSet(object.verifySpec) ? Verification.fromJSON(object.verifySpec) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
    };
  },

  toJSON(message: UploadUserCompilationRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.compileSpec !== undefined) {
      obj.compileSpec = SourceSpec.toJSON(message.compileSpec);
    }
    if (message.fromContract !== undefined) {
      obj.fromContract = UploadUserCompilationRequest_FromContract.toJSON(message.fromContract);
    }
    if (message.fromUserCompilation !== undefined) {
      obj.fromUserCompilation = UploadUserCompilationRequest_FromUserCompilation.toJSON(message.fromUserCompilation);
    }
    if (message.verifySpec !== undefined) {
      obj.verifySpec = Verification.toJSON(message.verifySpec);
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    return obj;
  },

  create(base?: DeepPartial<UploadUserCompilationRequest>): UploadUserCompilationRequest {
    return UploadUserCompilationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UploadUserCompilationRequest>): UploadUserCompilationRequest {
    const message = createBaseUploadUserCompilationRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.compileSpec = (object.compileSpec !== undefined && object.compileSpec !== null)
      ? SourceSpec.fromPartial(object.compileSpec)
      : undefined;
    message.fromContract = (object.fromContract !== undefined && object.fromContract !== null)
      ? UploadUserCompilationRequest_FromContract.fromPartial(object.fromContract)
      : undefined;
    message.fromUserCompilation = (object.fromUserCompilation !== undefined && object.fromUserCompilation !== null)
      ? UploadUserCompilationRequest_FromUserCompilation.fromPartial(object.fromUserCompilation)
      : undefined;
    message.verifySpec = (object.verifySpec !== undefined && object.verifySpec !== null)
      ? Verification.fromPartial(object.verifySpec)
      : undefined;
    message.name = object.name ?? undefined;
    return message;
  },
};

function createBaseUploadUserCompilationRequest_FromContract(): UploadUserCompilationRequest_FromContract {
  return { networkId: "", chainSpec: undefined, address: "", overrideSource: {} };
}

export const UploadUserCompilationRequest_FromContract = {
  encode(message: UploadUserCompilationRequest_FromContract, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(34).fork()).ldelim();
    }
    if (message.address !== "") {
      writer.uint32(18).string(message.address);
    }
    Object.entries(message.overrideSource).forEach(([key, value]) => {
      UploadUserCompilationRequest_FromContract_OverrideSourceEntry.encode(
        { key: key as any, value },
        writer.uint32(26).fork(),
      ).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UploadUserCompilationRequest_FromContract {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUploadUserCompilationRequest_FromContract();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.address = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          const entry3 = UploadUserCompilationRequest_FromContract_OverrideSourceEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.overrideSource[entry3.key] = entry3.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UploadUserCompilationRequest_FromContract {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      overrideSource: isObject(object.overrideSource)
        ? Object.entries(object.overrideSource).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: UploadUserCompilationRequest_FromContract): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.overrideSource) {
      const entries = Object.entries(message.overrideSource);
      if (entries.length > 0) {
        obj.overrideSource = {};
        entries.forEach(([k, v]) => {
          obj.overrideSource[k] = v;
        });
      }
    }
    return obj;
  },

  create(base?: DeepPartial<UploadUserCompilationRequest_FromContract>): UploadUserCompilationRequest_FromContract {
    return UploadUserCompilationRequest_FromContract.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<UploadUserCompilationRequest_FromContract>,
  ): UploadUserCompilationRequest_FromContract {
    const message = createBaseUploadUserCompilationRequest_FromContract();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.address = object.address ?? "";
    message.overrideSource = Object.entries(object.overrideSource ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseUploadUserCompilationRequest_FromContract_OverrideSourceEntry(): UploadUserCompilationRequest_FromContract_OverrideSourceEntry {
  return { key: "", value: "" };
}

export const UploadUserCompilationRequest_FromContract_OverrideSourceEntry = {
  encode(
    message: UploadUserCompilationRequest_FromContract_OverrideSourceEntry,
    writer: _m0.Writer = _m0.Writer.create(),
  ): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number,
  ): UploadUserCompilationRequest_FromContract_OverrideSourceEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUploadUserCompilationRequest_FromContract_OverrideSourceEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UploadUserCompilationRequest_FromContract_OverrideSourceEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: UploadUserCompilationRequest_FromContract_OverrideSourceEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create(
    base?: DeepPartial<UploadUserCompilationRequest_FromContract_OverrideSourceEntry>,
  ): UploadUserCompilationRequest_FromContract_OverrideSourceEntry {
    return UploadUserCompilationRequest_FromContract_OverrideSourceEntry.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<UploadUserCompilationRequest_FromContract_OverrideSourceEntry>,
  ): UploadUserCompilationRequest_FromContract_OverrideSourceEntry {
    const message = createBaseUploadUserCompilationRequest_FromContract_OverrideSourceEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseUploadUserCompilationRequest_FromUserCompilation(): UploadUserCompilationRequest_FromUserCompilation {
  return { userCompilationId: "", overrideSource: {} };
}

export const UploadUserCompilationRequest_FromUserCompilation = {
  encode(
    message: UploadUserCompilationRequest_FromUserCompilation,
    writer: _m0.Writer = _m0.Writer.create(),
  ): _m0.Writer {
    if (message.userCompilationId !== "") {
      writer.uint32(10).string(message.userCompilationId);
    }
    Object.entries(message.overrideSource).forEach(([key, value]) => {
      UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry.encode(
        { key: key as any, value },
        writer.uint32(18).fork(),
      ).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UploadUserCompilationRequest_FromUserCompilation {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUploadUserCompilationRequest_FromUserCompilation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          const entry2 = UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry.decode(
            reader,
            reader.uint32(),
          );
          if (entry2.value !== undefined) {
            message.overrideSource[entry2.key] = entry2.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UploadUserCompilationRequest_FromUserCompilation {
    return {
      userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "",
      overrideSource: isObject(object.overrideSource)
        ? Object.entries(object.overrideSource).reduce<{ [key: string]: string }>((acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        }, {})
        : {},
    };
  },

  toJSON(message: UploadUserCompilationRequest_FromUserCompilation): unknown {
    const obj: any = {};
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    if (message.overrideSource) {
      const entries = Object.entries(message.overrideSource);
      if (entries.length > 0) {
        obj.overrideSource = {};
        entries.forEach(([k, v]) => {
          obj.overrideSource[k] = v;
        });
      }
    }
    return obj;
  },

  create(
    base?: DeepPartial<UploadUserCompilationRequest_FromUserCompilation>,
  ): UploadUserCompilationRequest_FromUserCompilation {
    return UploadUserCompilationRequest_FromUserCompilation.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<UploadUserCompilationRequest_FromUserCompilation>,
  ): UploadUserCompilationRequest_FromUserCompilation {
    const message = createBaseUploadUserCompilationRequest_FromUserCompilation();
    message.userCompilationId = object.userCompilationId ?? "";
    message.overrideSource = Object.entries(object.overrideSource ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {},
    );
    return message;
  },
};

function createBaseUploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry(): UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry {
  return { key: "", value: "" };
}

export const UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry = {
  encode(
    message: UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry,
    writer: _m0.Writer = _m0.Writer.create(),
  ): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== "") {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(
    input: _m0.Reader | Uint8Array,
    length?: number,
  ): UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? globalThis.String(object.value) : "",
    };
  },

  toJSON(message: UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== "") {
      obj.value = message.value;
    }
    return obj;
  },

  create(
    base?: DeepPartial<UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry>,
  ): UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry {
    return UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry>,
  ): UploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry {
    const message = createBaseUploadUserCompilationRequest_FromUserCompilation_OverrideSourceEntry();
    message.key = object.key ?? "";
    message.value = object.value ?? "";
    return message;
  },
};

function createBaseUploadUserCompilationResponse(): UploadUserCompilationResponse {
  return { userCompilationId: "", verified: false };
}

export const UploadUserCompilationResponse = {
  encode(message: UploadUserCompilationResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userCompilationId !== "") {
      writer.uint32(10).string(message.userCompilationId);
    }
    if (message.verified !== false) {
      writer.uint32(16).bool(message.verified);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UploadUserCompilationResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUploadUserCompilationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.verified = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UploadUserCompilationResponse {
    return {
      userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "",
      verified: isSet(object.verified) ? globalThis.Boolean(object.verified) : false,
    };
  },

  toJSON(message: UploadUserCompilationResponse): unknown {
    const obj: any = {};
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    if (message.verified !== false) {
      obj.verified = message.verified;
    }
    return obj;
  },

  create(base?: DeepPartial<UploadUserCompilationResponse>): UploadUserCompilationResponse {
    return UploadUserCompilationResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UploadUserCompilationResponse>): UploadUserCompilationResponse {
    const message = createBaseUploadUserCompilationResponse();
    message.userCompilationId = object.userCompilationId ?? "";
    message.verified = object.verified ?? false;
    return message;
  },
};

function createBaseGetUserCompilationsRequest(): GetUserCompilationsRequest {
  return { projectOwner: undefined, projectSlug: undefined, name: undefined, page: 0, pageSize: 0 };
}

export const GetUserCompilationsRequest = {
  encode(message: GetUserCompilationsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.name !== undefined) {
      writer.uint32(42).string(message.name);
    }
    if (message.page !== 0) {
      writer.uint32(24).int32(message.page);
    }
    if (message.pageSize !== 0) {
      writer.uint32(32).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetUserCompilationsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserCompilationsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.page = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserCompilationsRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetUserCompilationsRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create(base?: DeepPartial<GetUserCompilationsRequest>): GetUserCompilationsRequest {
    return GetUserCompilationsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserCompilationsRequest>): GetUserCompilationsRequest {
    const message = createBaseGetUserCompilationsRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.name = object.name ?? undefined;
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseGetUserCompilationsResponse(): GetUserCompilationsResponse {
  return { results: [], total: BigInt("0"), page: 0, pageSize: 0 };
}

export const GetUserCompilationsResponse = {
  encode(message: GetUserCompilationsResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.results) {
      UserCompilation.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    if (message.total !== BigInt("0")) {
      if (BigInt.asIntN(64, message.total) !== message.total) {
        throw new globalThis.Error("value provided for field message.total of type int64 too large");
      }
      writer.uint32(16).int64(message.total.toString());
    }
    if (message.page !== 0) {
      writer.uint32(24).int32(message.page);
    }
    if (message.pageSize !== 0) {
      writer.uint32(32).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetUserCompilationsResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserCompilationsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.results.push(UserCompilation.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.total = longToBigint(reader.int64() as Long);
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.page = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserCompilationsResponse {
    return {
      results: globalThis.Array.isArray(object?.results)
        ? object.results.map((e: any) => UserCompilation.fromJSON(e))
        : [],
      total: isSet(object.total) ? BigInt(object.total) : BigInt("0"),
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetUserCompilationsResponse): unknown {
    const obj: any = {};
    if (message.results?.length) {
      obj.results = message.results.map((e) => UserCompilation.toJSON(e));
    }
    if (message.total !== BigInt("0")) {
      obj.total = message.total.toString();
    }
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create(base?: DeepPartial<GetUserCompilationsResponse>): GetUserCompilationsResponse {
    return GetUserCompilationsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserCompilationsResponse>): GetUserCompilationsResponse {
    const message = createBaseGetUserCompilationsResponse();
    message.results = object.results?.map((e) => UserCompilation.fromPartial(e)) || [];
    message.total = object.total ?? BigInt("0");
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseGetUserCompilationRequest(): GetUserCompilationRequest {
  return { userCompilationId: "", brief: false };
}

export const GetUserCompilationRequest = {
  encode(message: GetUserCompilationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userCompilationId !== "") {
      writer.uint32(10).string(message.userCompilationId);
    }
    if (message.brief !== false) {
      writer.uint32(16).bool(message.brief);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetUserCompilationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserCompilationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.brief = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserCompilationRequest {
    return {
      userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "",
      brief: isSet(object.brief) ? globalThis.Boolean(object.brief) : false,
    };
  },

  toJSON(message: GetUserCompilationRequest): unknown {
    const obj: any = {};
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    if (message.brief !== false) {
      obj.brief = message.brief;
    }
    return obj;
  },

  create(base?: DeepPartial<GetUserCompilationRequest>): GetUserCompilationRequest {
    return GetUserCompilationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserCompilationRequest>): GetUserCompilationRequest {
    const message = createBaseGetUserCompilationRequest();
    message.userCompilationId = object.userCompilationId ?? "";
    message.brief = object.brief ?? false;
    return message;
  },
};

function createBaseGetDeployedCodeResponse(): GetDeployedCodeResponse {
  return { deployedCode: "" };
}

export const GetDeployedCodeResponse = {
  encode(message: GetDeployedCodeResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.deployedCode !== "") {
      writer.uint32(10).string(message.deployedCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetDeployedCodeResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetDeployedCodeResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.deployedCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetDeployedCodeResponse {
    return { deployedCode: isSet(object.deployedCode) ? globalThis.String(object.deployedCode) : "" };
  },

  toJSON(message: GetDeployedCodeResponse): unknown {
    const obj: any = {};
    if (message.deployedCode !== "") {
      obj.deployedCode = message.deployedCode;
    }
    return obj;
  },

  create(base?: DeepPartial<GetDeployedCodeResponse>): GetDeployedCodeResponse {
    return GetDeployedCodeResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetDeployedCodeResponse>): GetDeployedCodeResponse {
    const message = createBaseGetDeployedCodeResponse();
    message.deployedCode = object.deployedCode ?? "";
    return message;
  },
};

function createBaseUpdateUserCompilationRequest(): UpdateUserCompilationRequest {
  return { userCompilationId: "", name: "" };
}

export const UpdateUserCompilationRequest = {
  encode(message: UpdateUserCompilationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userCompilationId !== "") {
      writer.uint32(10).string(message.userCompilationId);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UpdateUserCompilationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateUserCompilationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateUserCompilationRequest {
    return {
      userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
    };
  },

  toJSON(message: UpdateUserCompilationRequest): unknown {
    const obj: any = {};
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    return obj;
  },

  create(base?: DeepPartial<UpdateUserCompilationRequest>): UpdateUserCompilationRequest {
    return UpdateUserCompilationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UpdateUserCompilationRequest>): UpdateUserCompilationRequest {
    const message = createBaseUpdateUserCompilationRequest();
    message.userCompilationId = object.userCompilationId ?? "";
    message.name = object.name ?? "";
    return message;
  },
};

function createBaseDeleteUserCompilationRequest(): DeleteUserCompilationRequest {
  return { userCompilationId: "" };
}

export const DeleteUserCompilationRequest = {
  encode(message: DeleteUserCompilationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userCompilationId !== "") {
      writer.uint32(10).string(message.userCompilationId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DeleteUserCompilationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteUserCompilationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteUserCompilationRequest {
    return { userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "" };
  },

  toJSON(message: DeleteUserCompilationRequest): unknown {
    const obj: any = {};
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    return obj;
  },

  create(base?: DeepPartial<DeleteUserCompilationRequest>): DeleteUserCompilationRequest {
    return DeleteUserCompilationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteUserCompilationRequest>): DeleteUserCompilationRequest {
    const message = createBaseDeleteUserCompilationRequest();
    message.userCompilationId = object.userCompilationId ?? "";
    return message;
  },
};

function createBaseGetUserCompilationResponse(): GetUserCompilationResponse {
  return { result: undefined, projectOwner: "", projectSlug: "" };
}

export const GetUserCompilationResponse = {
  encode(message: GetUserCompilationResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.result !== undefined) {
      UserCompilation.encode(message.result, writer.uint32(10).fork()).ldelim();
    }
    if (message.projectOwner !== "") {
      writer.uint32(18).string(message.projectOwner);
    }
    if (message.projectSlug !== "") {
      writer.uint32(26).string(message.projectSlug);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetUserCompilationResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetUserCompilationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.result = UserCompilation.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetUserCompilationResponse {
    return {
      result: isSet(object.result) ? UserCompilation.fromJSON(object.result) : undefined,
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : "",
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : "",
    };
  },

  toJSON(message: GetUserCompilationResponse): unknown {
    const obj: any = {};
    if (message.result !== undefined) {
      obj.result = UserCompilation.toJSON(message.result);
    }
    if (message.projectOwner !== "") {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== "") {
      obj.projectSlug = message.projectSlug;
    }
    return obj;
  },

  create(base?: DeepPartial<GetUserCompilationResponse>): GetUserCompilationResponse {
    return GetUserCompilationResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetUserCompilationResponse>): GetUserCompilationResponse {
    const message = createBaseGetUserCompilationResponse();
    message.result = (object.result !== undefined && object.result !== null)
      ? UserCompilation.fromPartial(object.result)
      : undefined;
    message.projectOwner = object.projectOwner ?? "";
    message.projectSlug = object.projectSlug ?? "";
    return message;
  },
};

function createBaseGetDeployedCodeRequest(): GetDeployedCodeRequest {
  return { chainSpec: undefined, userCompilationId: "", address: "" };
}

export const GetDeployedCodeRequest = {
  encode(message: GetDeployedCodeRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(10).fork()).ldelim();
    }
    if (message.userCompilationId !== "") {
      writer.uint32(18).string(message.userCompilationId);
    }
    if (message.address !== "") {
      writer.uint32(26).string(message.address);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetDeployedCodeRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetDeployedCodeRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.address = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetDeployedCodeRequest {
    return {
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
    };
  },

  toJSON(message: GetDeployedCodeRequest): unknown {
    const obj: any = {};
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    return obj;
  },

  create(base?: DeepPartial<GetDeployedCodeRequest>): GetDeployedCodeRequest {
    return GetDeployedCodeRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetDeployedCodeRequest>): GetDeployedCodeRequest {
    const message = createBaseGetDeployedCodeRequest();
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.userCompilationId = object.userCompilationId ?? "";
    message.address = object.address ?? "";
    return message;
  },
};

function createBaseVerifyContractRequest(): VerifyContractRequest {
  return { verifySpec: undefined };
}

export const VerifyContractRequest = {
  encode(message: VerifyContractRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.verifySpec !== undefined) {
      Verification.encode(message.verifySpec, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): VerifyContractRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVerifyContractRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.verifySpec = Verification.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VerifyContractRequest {
    return { verifySpec: isSet(object.verifySpec) ? Verification.fromJSON(object.verifySpec) : undefined };
  },

  toJSON(message: VerifyContractRequest): unknown {
    const obj: any = {};
    if (message.verifySpec !== undefined) {
      obj.verifySpec = Verification.toJSON(message.verifySpec);
    }
    return obj;
  },

  create(base?: DeepPartial<VerifyContractRequest>): VerifyContractRequest {
    return VerifyContractRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<VerifyContractRequest>): VerifyContractRequest {
    const message = createBaseVerifyContractRequest();
    message.verifySpec = (object.verifySpec !== undefined && object.verifySpec !== null)
      ? Verification.fromPartial(object.verifySpec)
      : undefined;
    return message;
  },
};

function createBaseVerifyContractResponse(): VerifyContractResponse {
  return { verified: false };
}

export const VerifyContractResponse = {
  encode(message: VerifyContractResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.verified !== false) {
      writer.uint32(8).bool(message.verified);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): VerifyContractResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVerifyContractResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.verified = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): VerifyContractResponse {
    return { verified: isSet(object.verified) ? globalThis.Boolean(object.verified) : false };
  },

  toJSON(message: VerifyContractResponse): unknown {
    const obj: any = {};
    if (message.verified !== false) {
      obj.verified = message.verified;
    }
    return obj;
  },

  create(base?: DeepPartial<VerifyContractResponse>): VerifyContractResponse {
    return VerifyContractResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<VerifyContractResponse>): VerifyContractResponse {
    const message = createBaseVerifyContractResponse();
    message.verified = object.verified ?? false;
    return message;
  },
};

function createBaseGetVerificationsRequest(): GetVerificationsRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: undefined,
    chainSpec: undefined,
    page: 0,
    pageSize: 0,
  };
}

export const GetVerificationsRequest = {
  encode(message: GetVerificationsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.networkId !== undefined) {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(50).fork()).ldelim();
    }
    if (message.page !== 0) {
      writer.uint32(32).int32(message.page);
    }
    if (message.pageSize !== 0) {
      writer.uint32(40).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetVerificationsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVerificationsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.page = reader.int32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVerificationsRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : undefined,
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetVerificationsRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== undefined) {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create(base?: DeepPartial<GetVerificationsRequest>): GetVerificationsRequest {
    return GetVerificationsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetVerificationsRequest>): GetVerificationsRequest {
    const message = createBaseGetVerificationsRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? undefined;
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseGetVerificationsResponse(): GetVerificationsResponse {
  return { results: [], total: BigInt("0"), page: 0, pageSize: 0 };
}

export const GetVerificationsResponse = {
  encode(message: GetVerificationsResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.results) {
      Verification.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    if (message.total !== BigInt("0")) {
      if (BigInt.asIntN(64, message.total) !== message.total) {
        throw new globalThis.Error("value provided for field message.total of type int64 too large");
      }
      writer.uint32(16).int64(message.total.toString());
    }
    if (message.page !== 0) {
      writer.uint32(24).int32(message.page);
    }
    if (message.pageSize !== 0) {
      writer.uint32(32).int32(message.pageSize);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetVerificationsResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVerificationsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.results.push(Verification.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.total = longToBigint(reader.int64() as Long);
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.page = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.pageSize = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVerificationsResponse {
    return {
      results: globalThis.Array.isArray(object?.results)
        ? object.results.map((e: any) => Verification.fromJSON(e))
        : [],
      total: isSet(object.total) ? BigInt(object.total) : BigInt("0"),
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
    };
  },

  toJSON(message: GetVerificationsResponse): unknown {
    const obj: any = {};
    if (message.results?.length) {
      obj.results = message.results.map((e) => Verification.toJSON(e));
    }
    if (message.total !== BigInt("0")) {
      obj.total = message.total.toString();
    }
    if (message.page !== 0) {
      obj.page = Math.round(message.page);
    }
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    return obj;
  },

  create(base?: DeepPartial<GetVerificationsResponse>): GetVerificationsResponse {
    return GetVerificationsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetVerificationsResponse>): GetVerificationsResponse {
    const message = createBaseGetVerificationsResponse();
    message.results = object.results?.map((e) => Verification.fromPartial(e)) || [];
    message.total = object.total ?? BigInt("0");
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  },
};

function createBaseGetVerificationByContractRequest(): GetVerificationByContractRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: "",
    chainSpec: undefined,
    addresses: [],
    checkInternal: false,
  };
}

export const GetVerificationByContractRequest = {
  encode(message: GetVerificationByContractRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(50).fork()).ldelim();
    }
    for (const v of message.addresses) {
      writer.uint32(34).string(v!);
    }
    if (message.checkInternal !== false) {
      writer.uint32(40).bool(message.checkInternal);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetVerificationByContractRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVerificationByContractRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.addresses.push(reader.string());
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.checkInternal = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVerificationByContractRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      addresses: globalThis.Array.isArray(object?.addresses)
        ? object.addresses.map((e: any) => globalThis.String(e))
        : [],
      checkInternal: isSet(object.checkInternal) ? globalThis.Boolean(object.checkInternal) : false,
    };
  },

  toJSON(message: GetVerificationByContractRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.addresses?.length) {
      obj.addresses = message.addresses;
    }
    if (message.checkInternal !== false) {
      obj.checkInternal = message.checkInternal;
    }
    return obj;
  },

  create(base?: DeepPartial<GetVerificationByContractRequest>): GetVerificationByContractRequest {
    return GetVerificationByContractRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetVerificationByContractRequest>): GetVerificationByContractRequest {
    const message = createBaseGetVerificationByContractRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.addresses = object.addresses?.map((e) => e) || [];
    message.checkInternal = object.checkInternal ?? false;
    return message;
  },
};

function createBaseGetVerificationByContractResponse(): GetVerificationByContractResponse {
  return { result: {} };
}

export const GetVerificationByContractResponse = {
  encode(message: GetVerificationByContractResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    Object.entries(message.result).forEach(([key, value]) => {
      GetVerificationByContractResponse_ResultEntry.encode({ key: key as any, value }, writer.uint32(10).fork())
        .ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetVerificationByContractResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVerificationByContractResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          const entry1 = GetVerificationByContractResponse_ResultEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.result[entry1.key] = entry1.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVerificationByContractResponse {
    return {
      result: isObject(object.result)
        ? Object.entries(object.result).reduce<{ [key: string]: GetVerificationByContractResponse_Contract }>(
          (acc, [key, value]) => {
            acc[key] = GetVerificationByContractResponse_Contract.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
    };
  },

  toJSON(message: GetVerificationByContractResponse): unknown {
    const obj: any = {};
    if (message.result) {
      const entries = Object.entries(message.result);
      if (entries.length > 0) {
        obj.result = {};
        entries.forEach(([k, v]) => {
          obj.result[k] = GetVerificationByContractResponse_Contract.toJSON(v);
        });
      }
    }
    return obj;
  },

  create(base?: DeepPartial<GetVerificationByContractResponse>): GetVerificationByContractResponse {
    return GetVerificationByContractResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetVerificationByContractResponse>): GetVerificationByContractResponse {
    const message = createBaseGetVerificationByContractResponse();
    message.result = Object.entries(object.result ?? {}).reduce<
      { [key: string]: GetVerificationByContractResponse_Contract }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = GetVerificationByContractResponse_Contract.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseGetVerificationByContractResponse_Contract(): GetVerificationByContractResponse_Contract {
  return { internal: false, userVerification: undefined };
}

export const GetVerificationByContractResponse_Contract = {
  encode(message: GetVerificationByContractResponse_Contract, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.internal !== false) {
      writer.uint32(8).bool(message.internal);
    }
    if (message.userVerification !== undefined) {
      Verification.encode(message.userVerification, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetVerificationByContractResponse_Contract {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVerificationByContractResponse_Contract();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.internal = reader.bool();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.userVerification = Verification.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVerificationByContractResponse_Contract {
    return {
      internal: isSet(object.internal) ? globalThis.Boolean(object.internal) : false,
      userVerification: isSet(object.userVerification) ? Verification.fromJSON(object.userVerification) : undefined,
    };
  },

  toJSON(message: GetVerificationByContractResponse_Contract): unknown {
    const obj: any = {};
    if (message.internal !== false) {
      obj.internal = message.internal;
    }
    if (message.userVerification !== undefined) {
      obj.userVerification = Verification.toJSON(message.userVerification);
    }
    return obj;
  },

  create(base?: DeepPartial<GetVerificationByContractResponse_Contract>): GetVerificationByContractResponse_Contract {
    return GetVerificationByContractResponse_Contract.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<GetVerificationByContractResponse_Contract>,
  ): GetVerificationByContractResponse_Contract {
    const message = createBaseGetVerificationByContractResponse_Contract();
    message.internal = object.internal ?? false;
    message.userVerification = (object.userVerification !== undefined && object.userVerification !== null)
      ? Verification.fromPartial(object.userVerification)
      : undefined;
    return message;
  },
};

function createBaseGetVerificationByContractResponse_ResultEntry(): GetVerificationByContractResponse_ResultEntry {
  return { key: "", value: undefined };
}

export const GetVerificationByContractResponse_ResultEntry = {
  encode(message: GetVerificationByContractResponse_ResultEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      GetVerificationByContractResponse_Contract.encode(message.value, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetVerificationByContractResponse_ResultEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetVerificationByContractResponse_ResultEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = GetVerificationByContractResponse_Contract.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetVerificationByContractResponse_ResultEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? GetVerificationByContractResponse_Contract.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: GetVerificationByContractResponse_ResultEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = GetVerificationByContractResponse_Contract.toJSON(message.value);
    }
    return obj;
  },

  create(
    base?: DeepPartial<GetVerificationByContractResponse_ResultEntry>,
  ): GetVerificationByContractResponse_ResultEntry {
    return GetVerificationByContractResponse_ResultEntry.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<GetVerificationByContractResponse_ResultEntry>,
  ): GetVerificationByContractResponse_ResultEntry {
    const message = createBaseGetVerificationByContractResponse_ResultEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? GetVerificationByContractResponse_Contract.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDeleteVerificationRequest(): DeleteVerificationRequest {
  return { verificationId: "" };
}

export const DeleteVerificationRequest = {
  encode(message: DeleteVerificationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.verificationId !== "") {
      writer.uint32(10).string(message.verificationId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DeleteVerificationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteVerificationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.verificationId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteVerificationRequest {
    return { verificationId: isSet(object.verificationId) ? globalThis.String(object.verificationId) : "" };
  },

  toJSON(message: DeleteVerificationRequest): unknown {
    const obj: any = {};
    if (message.verificationId !== "") {
      obj.verificationId = message.verificationId;
    }
    return obj;
  },

  create(base?: DeepPartial<DeleteVerificationRequest>): DeleteVerificationRequest {
    return DeleteVerificationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DeleteVerificationRequest>): DeleteVerificationRequest {
    const message = createBaseDeleteVerificationRequest();
    message.verificationId = object.verificationId ?? "";
    return message;
  },
};

function createBaseParseContractsRequest(): ParseContractsRequest {
  return { compileSpec: undefined };
}

export const ParseContractsRequest = {
  encode(message: ParseContractsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.compileSpec !== undefined) {
      SourceSpec.encode(message.compileSpec, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ParseContractsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseParseContractsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.compileSpec = SourceSpec.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ParseContractsRequest {
    return { compileSpec: isSet(object.compileSpec) ? SourceSpec.fromJSON(object.compileSpec) : undefined };
  },

  toJSON(message: ParseContractsRequest): unknown {
    const obj: any = {};
    if (message.compileSpec !== undefined) {
      obj.compileSpec = SourceSpec.toJSON(message.compileSpec);
    }
    return obj;
  },

  create(base?: DeepPartial<ParseContractsRequest>): ParseContractsRequest {
    return ParseContractsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ParseContractsRequest>): ParseContractsRequest {
    const message = createBaseParseContractsRequest();
    message.compileSpec = (object.compileSpec !== undefined && object.compileSpec !== null)
      ? SourceSpec.fromPartial(object.compileSpec)
      : undefined;
    return message;
  },
};

function createBaseParseContractsResponse(): ParseContractsResponse {
  return { contracts: [] };
}

export const ParseContractsResponse = {
  encode(message: ParseContractsResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.contracts) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ParseContractsResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseParseContractsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.contracts.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ParseContractsResponse {
    return {
      contracts: globalThis.Array.isArray(object?.contracts)
        ? object.contracts.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: ParseContractsResponse): unknown {
    const obj: any = {};
    if (message.contracts?.length) {
      obj.contracts = message.contracts;
    }
    return obj;
  },

  create(base?: DeepPartial<ParseContractsResponse>): ParseContractsResponse {
    return ParseContractsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ParseContractsResponse>): ParseContractsResponse {
    const message = createBaseParseContractsResponse();
    message.contracts = object.contracts?.map((e) => e) || [];
    return message;
  },
};

function createBaseUserCompilation(): UserCompilation {
  return {
    id: "",
    name: "",
    contractName: "",
    ABI: "",
    source: undefined,
    compilation: undefined,
    verifications: [],
    createTime: undefined,
    origin: undefined,
  };
}

export const UserCompilation = {
  encode(message: UserCompilation, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.contractName !== "") {
      writer.uint32(26).string(message.contractName);
    }
    if (message.ABI !== "") {
      writer.uint32(34).string(message.ABI);
    }
    if (message.source !== undefined) {
      SourceSpec.encode(message.source, writer.uint32(42).fork()).ldelim();
    }
    if (message.compilation !== undefined) {
      Struct.encode(Struct.wrap(message.compilation), writer.uint32(50).fork()).ldelim();
    }
    for (const v of message.verifications) {
      Verification.encode(v!, writer.uint32(58).fork()).ldelim();
    }
    if (message.createTime !== undefined) {
      Timestamp.encode(toTimestamp(message.createTime), writer.uint32(66).fork()).ldelim();
    }
    if (message.origin !== undefined) {
      UserCompilation_Origin.encode(message.origin, writer.uint32(74).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserCompilation {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserCompilation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.contractName = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.ABI = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.source = SourceSpec.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.compilation = Struct.unwrap(Struct.decode(reader, reader.uint32()));
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.verifications.push(Verification.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.createTime = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.origin = UserCompilation_Origin.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserCompilation {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      contractName: isSet(object.contractName) ? globalThis.String(object.contractName) : "",
      ABI: isSet(object.ABI) ? globalThis.String(object.ABI) : "",
      source: isSet(object.source) ? SourceSpec.fromJSON(object.source) : undefined,
      compilation: isObject(object.compilation) ? object.compilation : undefined,
      verifications: globalThis.Array.isArray(object?.verifications)
        ? object.verifications.map((e: any) => Verification.fromJSON(e))
        : [],
      createTime: isSet(object.createTime) ? fromJsonTimestamp(object.createTime) : undefined,
      origin: isSet(object.origin) ? UserCompilation_Origin.fromJSON(object.origin) : undefined,
    };
  },

  toJSON(message: UserCompilation): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.contractName !== "") {
      obj.contractName = message.contractName;
    }
    if (message.ABI !== "") {
      obj.ABI = message.ABI;
    }
    if (message.source !== undefined) {
      obj.source = SourceSpec.toJSON(message.source);
    }
    if (message.compilation !== undefined) {
      obj.compilation = message.compilation;
    }
    if (message.verifications?.length) {
      obj.verifications = message.verifications.map((e) => Verification.toJSON(e));
    }
    if (message.createTime !== undefined) {
      obj.createTime = message.createTime.toISOString();
    }
    if (message.origin !== undefined) {
      obj.origin = UserCompilation_Origin.toJSON(message.origin);
    }
    return obj;
  },

  create(base?: DeepPartial<UserCompilation>): UserCompilation {
    return UserCompilation.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserCompilation>): UserCompilation {
    const message = createBaseUserCompilation();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.contractName = object.contractName ?? "";
    message.ABI = object.ABI ?? "";
    message.source = (object.source !== undefined && object.source !== null)
      ? SourceSpec.fromPartial(object.source)
      : undefined;
    message.compilation = object.compilation ?? undefined;
    message.verifications = object.verifications?.map((e) => Verification.fromPartial(e)) || [];
    message.createTime = object.createTime ?? undefined;
    message.origin = (object.origin !== undefined && object.origin !== null)
      ? UserCompilation_Origin.fromPartial(object.origin)
      : undefined;
    return message;
  },
};

function createBaseUserCompilation_Origin(): UserCompilation_Origin {
  return { chainId: "", address: "", creationTx: "" };
}

export const UserCompilation_Origin = {
  encode(message: UserCompilation_Origin, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.chainId !== "") {
      writer.uint32(10).string(message.chainId);
    }
    if (message.address !== "") {
      writer.uint32(18).string(message.address);
    }
    if (message.creationTx !== "") {
      writer.uint32(26).string(message.creationTx);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserCompilation_Origin {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserCompilation_Origin();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.chainId = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.address = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.creationTx = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserCompilation_Origin {
    return {
      chainId: isSet(object.chainId) ? globalThis.String(object.chainId) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      creationTx: isSet(object.creationTx) ? globalThis.String(object.creationTx) : "",
    };
  },

  toJSON(message: UserCompilation_Origin): unknown {
    const obj: any = {};
    if (message.chainId !== "") {
      obj.chainId = message.chainId;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.creationTx !== "") {
      obj.creationTx = message.creationTx;
    }
    return obj;
  },

  create(base?: DeepPartial<UserCompilation_Origin>): UserCompilation_Origin {
    return UserCompilation_Origin.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<UserCompilation_Origin>): UserCompilation_Origin {
    const message = createBaseUserCompilation_Origin();
    message.chainId = object.chainId ?? "";
    message.address = object.address ?? "";
    message.creationTx = object.creationTx ?? "";
    return message;
  },
};

function createBaseGetMEVInfoRequest(): GetMEVInfoRequest {
  return { networkId: "", chainSpec: undefined, txHash: "" };
}

export const GetMEVInfoRequest = {
  encode(message: GetMEVInfoRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(26).fork()).ldelim();
    }
    if (message.txHash !== "") {
      writer.uint32(18).string(message.txHash);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetMEVInfoRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMEVInfoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.txHash = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetMEVInfoRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txHash: isSet(object.txHash) ? globalThis.String(object.txHash) : "",
    };
  },

  toJSON(message: GetMEVInfoRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txHash !== "") {
      obj.txHash = message.txHash;
    }
    return obj;
  },

  create(base?: DeepPartial<GetMEVInfoRequest>): GetMEVInfoRequest {
    return GetMEVInfoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetMEVInfoRequest>): GetMEVInfoRequest {
    const message = createBaseGetMEVInfoRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txHash = object.txHash ?? "";
    return message;
  },
};

function createBaseGetMEVInfoResponse(): GetMEVInfoResponse {
  return {
    txHash: "",
    txIndex: 0,
    blockNumber: "",
    type: 0,
    sandwich: undefined,
    arbitrage: undefined,
    blockSandwiches: [],
    blockArbitrages: [],
  };
}

export const GetMEVInfoResponse = {
  encode(message: GetMEVInfoResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.txHash !== "") {
      writer.uint32(10).string(message.txHash);
    }
    if (message.txIndex !== 0) {
      writer.uint32(16).int32(message.txIndex);
    }
    if (message.blockNumber !== "") {
      writer.uint32(26).string(message.blockNumber);
    }
    if (message.type !== 0) {
      writer.uint32(32).int32(message.type);
    }
    if (message.sandwich !== undefined) {
      SandwichResult.encode(message.sandwich, writer.uint32(42).fork()).ldelim();
    }
    if (message.arbitrage !== undefined) {
      ArbitrageResult.encode(message.arbitrage, writer.uint32(50).fork()).ldelim();
    }
    for (const v of message.blockSandwiches) {
      SandwichResult.encode(v!, writer.uint32(58).fork()).ldelim();
    }
    for (const v of message.blockArbitrages) {
      ArbitrageResult.encode(v!, writer.uint32(66).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetMEVInfoResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetMEVInfoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txHash = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.txIndex = reader.int32();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.sandwich = SandwichResult.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.arbitrage = ArbitrageResult.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.blockSandwiches.push(SandwichResult.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.blockArbitrages.push(ArbitrageResult.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetMEVInfoResponse {
    return {
      txHash: isSet(object.txHash) ? globalThis.String(object.txHash) : "",
      txIndex: isSet(object.txIndex) ? globalThis.Number(object.txIndex) : 0,
      blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : "",
      type: isSet(object.type) ? mEVTxTypeFromJSON(object.type) : 0,
      sandwich: isSet(object.sandwich) ? SandwichResult.fromJSON(object.sandwich) : undefined,
      arbitrage: isSet(object.arbitrage) ? ArbitrageResult.fromJSON(object.arbitrage) : undefined,
      blockSandwiches: globalThis.Array.isArray(object?.blockSandwiches)
        ? object.blockSandwiches.map((e: any) => SandwichResult.fromJSON(e))
        : [],
      blockArbitrages: globalThis.Array.isArray(object?.blockArbitrages)
        ? object.blockArbitrages.map((e: any) => ArbitrageResult.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetMEVInfoResponse): unknown {
    const obj: any = {};
    if (message.txHash !== "") {
      obj.txHash = message.txHash;
    }
    if (message.txIndex !== 0) {
      obj.txIndex = Math.round(message.txIndex);
    }
    if (message.blockNumber !== "") {
      obj.blockNumber = message.blockNumber;
    }
    if (message.type !== 0) {
      obj.type = mEVTxTypeToJSON(message.type);
    }
    if (message.sandwich !== undefined) {
      obj.sandwich = SandwichResult.toJSON(message.sandwich);
    }
    if (message.arbitrage !== undefined) {
      obj.arbitrage = ArbitrageResult.toJSON(message.arbitrage);
    }
    if (message.blockSandwiches?.length) {
      obj.blockSandwiches = message.blockSandwiches.map((e) => SandwichResult.toJSON(e));
    }
    if (message.blockArbitrages?.length) {
      obj.blockArbitrages = message.blockArbitrages.map((e) => ArbitrageResult.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<GetMEVInfoResponse>): GetMEVInfoResponse {
    return GetMEVInfoResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetMEVInfoResponse>): GetMEVInfoResponse {
    const message = createBaseGetMEVInfoResponse();
    message.txHash = object.txHash ?? "";
    message.txIndex = object.txIndex ?? 0;
    message.blockNumber = object.blockNumber ?? "";
    message.type = object.type ?? 0;
    message.sandwich = (object.sandwich !== undefined && object.sandwich !== null)
      ? SandwichResult.fromPartial(object.sandwich)
      : undefined;
    message.arbitrage = (object.arbitrage !== undefined && object.arbitrage !== null)
      ? ArbitrageResult.fromPartial(object.arbitrage)
      : undefined;
    message.blockSandwiches = object.blockSandwiches?.map((e) => SandwichResult.fromPartial(e)) || [];
    message.blockArbitrages = object.blockArbitrages?.map((e) => ArbitrageResult.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBatchGetMEVInfoRequest(): BatchGetMEVInfoRequest {
  return { networkId: "", chainSpec: undefined, txHash: [] };
}

export const BatchGetMEVInfoRequest = {
  encode(message: BatchGetMEVInfoRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(26).fork()).ldelim();
    }
    for (const v of message.txHash) {
      writer.uint32(18).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BatchGetMEVInfoRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchGetMEVInfoRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.txHash.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BatchGetMEVInfoRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      txHash: globalThis.Array.isArray(object?.txHash) ? object.txHash.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: BatchGetMEVInfoRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.txHash?.length) {
      obj.txHash = message.txHash;
    }
    return obj;
  },

  create(base?: DeepPartial<BatchGetMEVInfoRequest>): BatchGetMEVInfoRequest {
    return BatchGetMEVInfoRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<BatchGetMEVInfoRequest>): BatchGetMEVInfoRequest {
    const message = createBaseBatchGetMEVInfoRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.txHash = object.txHash?.map((e) => e) || [];
    return message;
  },
};

function createBaseBatchGetMEVInfoResponse(): BatchGetMEVInfoResponse {
  return { results: [] };
}

export const BatchGetMEVInfoResponse = {
  encode(message: BatchGetMEVInfoResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.results) {
      GetMEVInfoResponse.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BatchGetMEVInfoResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchGetMEVInfoResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.results.push(GetMEVInfoResponse.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BatchGetMEVInfoResponse {
    return {
      results: globalThis.Array.isArray(object?.results)
        ? object.results.map((e: any) => GetMEVInfoResponse.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BatchGetMEVInfoResponse): unknown {
    const obj: any = {};
    if (message.results?.length) {
      obj.results = message.results.map((e) => GetMEVInfoResponse.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<BatchGetMEVInfoResponse>): BatchGetMEVInfoResponse {
    return BatchGetMEVInfoResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<BatchGetMEVInfoResponse>): BatchGetMEVInfoResponse {
    const message = createBaseBatchGetMEVInfoResponse();
    message.results = object.results?.map((e) => GetMEVInfoResponse.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSandwichResult(): SandwichResult {
  return {
    frontTxHash: "",
    backTxHash: "",
    frontTxIndex: 0,
    backTxIndex: 0,
    txs: [],
    victims: [],
    mevContract: "",
    revenues: undefined,
    costs: undefined,
    profits: undefined,
    tokens: [],
    traders: [],
    tags: [],
  };
}

export const SandwichResult = {
  encode(message: SandwichResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.frontTxHash !== "") {
      writer.uint32(10).string(message.frontTxHash);
    }
    if (message.backTxHash !== "") {
      writer.uint32(18).string(message.backTxHash);
    }
    if (message.frontTxIndex !== 0) {
      writer.uint32(24).int32(message.frontTxIndex);
    }
    if (message.backTxIndex !== 0) {
      writer.uint32(32).int32(message.backTxIndex);
    }
    for (const v of message.txs) {
      TxHashAndIndex.encode(v!, writer.uint32(98).fork()).ldelim();
    }
    for (const v of message.victims) {
      TxHashAndIndex.encode(v!, writer.uint32(106).fork()).ldelim();
    }
    if (message.mevContract !== "") {
      writer.uint32(42).string(message.mevContract);
    }
    if (message.revenues !== undefined) {
      Assets.encode(message.revenues, writer.uint32(50).fork()).ldelim();
    }
    if (message.costs !== undefined) {
      Assets.encode(message.costs, writer.uint32(58).fork()).ldelim();
    }
    if (message.profits !== undefined) {
      Assets.encode(message.profits, writer.uint32(66).fork()).ldelim();
    }
    for (const v of message.tokens) {
      TokenInfo.encode(v!, writer.uint32(74).fork()).ldelim();
    }
    for (const v of message.traders) {
      Pool.encode(v!, writer.uint32(82).fork()).ldelim();
    }
    for (const v of message.tags) {
      writer.uint32(90).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SandwichResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSandwichResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.frontTxHash = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.backTxHash = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.frontTxIndex = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.backTxIndex = reader.int32();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.txs.push(TxHashAndIndex.decode(reader, reader.uint32()));
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.victims.push(TxHashAndIndex.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.mevContract = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.revenues = Assets.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.costs = Assets.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.profits = Assets.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.tokens.push(TokenInfo.decode(reader, reader.uint32()));
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.traders.push(Pool.decode(reader, reader.uint32()));
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.tags.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SandwichResult {
    return {
      frontTxHash: isSet(object.frontTxHash) ? globalThis.String(object.frontTxHash) : "",
      backTxHash: isSet(object.backTxHash) ? globalThis.String(object.backTxHash) : "",
      frontTxIndex: isSet(object.frontTxIndex) ? globalThis.Number(object.frontTxIndex) : 0,
      backTxIndex: isSet(object.backTxIndex) ? globalThis.Number(object.backTxIndex) : 0,
      txs: globalThis.Array.isArray(object?.txs) ? object.txs.map((e: any) => TxHashAndIndex.fromJSON(e)) : [],
      victims: globalThis.Array.isArray(object?.victims)
        ? object.victims.map((e: any) => TxHashAndIndex.fromJSON(e))
        : [],
      mevContract: isSet(object.mevContract) ? globalThis.String(object.mevContract) : "",
      revenues: isSet(object.revenues) ? Assets.fromJSON(object.revenues) : undefined,
      costs: isSet(object.costs) ? Assets.fromJSON(object.costs) : undefined,
      profits: isSet(object.profits) ? Assets.fromJSON(object.profits) : undefined,
      tokens: globalThis.Array.isArray(object?.tokens) ? object.tokens.map((e: any) => TokenInfo.fromJSON(e)) : [],
      traders: globalThis.Array.isArray(object?.traders) ? object.traders.map((e: any) => Pool.fromJSON(e)) : [],
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: SandwichResult): unknown {
    const obj: any = {};
    if (message.frontTxHash !== "") {
      obj.frontTxHash = message.frontTxHash;
    }
    if (message.backTxHash !== "") {
      obj.backTxHash = message.backTxHash;
    }
    if (message.frontTxIndex !== 0) {
      obj.frontTxIndex = Math.round(message.frontTxIndex);
    }
    if (message.backTxIndex !== 0) {
      obj.backTxIndex = Math.round(message.backTxIndex);
    }
    if (message.txs?.length) {
      obj.txs = message.txs.map((e) => TxHashAndIndex.toJSON(e));
    }
    if (message.victims?.length) {
      obj.victims = message.victims.map((e) => TxHashAndIndex.toJSON(e));
    }
    if (message.mevContract !== "") {
      obj.mevContract = message.mevContract;
    }
    if (message.revenues !== undefined) {
      obj.revenues = Assets.toJSON(message.revenues);
    }
    if (message.costs !== undefined) {
      obj.costs = Assets.toJSON(message.costs);
    }
    if (message.profits !== undefined) {
      obj.profits = Assets.toJSON(message.profits);
    }
    if (message.tokens?.length) {
      obj.tokens = message.tokens.map((e) => TokenInfo.toJSON(e));
    }
    if (message.traders?.length) {
      obj.traders = message.traders.map((e) => Pool.toJSON(e));
    }
    if (message.tags?.length) {
      obj.tags = message.tags;
    }
    return obj;
  },

  create(base?: DeepPartial<SandwichResult>): SandwichResult {
    return SandwichResult.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SandwichResult>): SandwichResult {
    const message = createBaseSandwichResult();
    message.frontTxHash = object.frontTxHash ?? "";
    message.backTxHash = object.backTxHash ?? "";
    message.frontTxIndex = object.frontTxIndex ?? 0;
    message.backTxIndex = object.backTxIndex ?? 0;
    message.txs = object.txs?.map((e) => TxHashAndIndex.fromPartial(e)) || [];
    message.victims = object.victims?.map((e) => TxHashAndIndex.fromPartial(e)) || [];
    message.mevContract = object.mevContract ?? "";
    message.revenues = (object.revenues !== undefined && object.revenues !== null)
      ? Assets.fromPartial(object.revenues)
      : undefined;
    message.costs = (object.costs !== undefined && object.costs !== null)
      ? Assets.fromPartial(object.costs)
      : undefined;
    message.profits = (object.profits !== undefined && object.profits !== null)
      ? Assets.fromPartial(object.profits)
      : undefined;
    message.tokens = object.tokens?.map((e) => TokenInfo.fromPartial(e)) || [];
    message.traders = object.traders?.map((e) => Pool.fromPartial(e)) || [];
    message.tags = object.tags?.map((e) => e) || [];
    return message;
  },
};

function createBaseTxHashAndIndex(): TxHashAndIndex {
  return { txHash: "", txIndex: 0 };
}

export const TxHashAndIndex = {
  encode(message: TxHashAndIndex, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.txHash !== "") {
      writer.uint32(10).string(message.txHash);
    }
    if (message.txIndex !== 0) {
      writer.uint32(16).int32(message.txIndex);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): TxHashAndIndex {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxHashAndIndex();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txHash = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.txIndex = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxHashAndIndex {
    return {
      txHash: isSet(object.txHash) ? globalThis.String(object.txHash) : "",
      txIndex: isSet(object.txIndex) ? globalThis.Number(object.txIndex) : 0,
    };
  },

  toJSON(message: TxHashAndIndex): unknown {
    const obj: any = {};
    if (message.txHash !== "") {
      obj.txHash = message.txHash;
    }
    if (message.txIndex !== 0) {
      obj.txIndex = Math.round(message.txIndex);
    }
    return obj;
  },

  create(base?: DeepPartial<TxHashAndIndex>): TxHashAndIndex {
    return TxHashAndIndex.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TxHashAndIndex>): TxHashAndIndex {
    const message = createBaseTxHashAndIndex();
    message.txHash = object.txHash ?? "";
    message.txIndex = object.txIndex ?? 0;
    return message;
  },
};

function createBaseArbitrageResult(): ArbitrageResult {
  return {
    txHash: "",
    txIndex: 0,
    mevContract: "",
    revenues: undefined,
    costs: undefined,
    profits: undefined,
    tokens: [],
    traders: [],
    tags: [],
  };
}

export const ArbitrageResult = {
  encode(message: ArbitrageResult, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.txHash !== "") {
      writer.uint32(10).string(message.txHash);
    }
    if (message.txIndex !== 0) {
      writer.uint32(16).int32(message.txIndex);
    }
    if (message.mevContract !== "") {
      writer.uint32(26).string(message.mevContract);
    }
    if (message.revenues !== undefined) {
      Assets.encode(message.revenues, writer.uint32(34).fork()).ldelim();
    }
    if (message.costs !== undefined) {
      Assets.encode(message.costs, writer.uint32(42).fork()).ldelim();
    }
    if (message.profits !== undefined) {
      Assets.encode(message.profits, writer.uint32(50).fork()).ldelim();
    }
    for (const v of message.tokens) {
      TokenInfo.encode(v!, writer.uint32(58).fork()).ldelim();
    }
    for (const v of message.traders) {
      Pool.encode(v!, writer.uint32(66).fork()).ldelim();
    }
    for (const v of message.tags) {
      writer.uint32(74).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ArbitrageResult {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseArbitrageResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txHash = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.txIndex = reader.int32();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.mevContract = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.revenues = Assets.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.costs = Assets.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.profits = Assets.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.tokens.push(TokenInfo.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.traders.push(Pool.decode(reader, reader.uint32()));
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.tags.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ArbitrageResult {
    return {
      txHash: isSet(object.txHash) ? globalThis.String(object.txHash) : "",
      txIndex: isSet(object.txIndex) ? globalThis.Number(object.txIndex) : 0,
      mevContract: isSet(object.mevContract) ? globalThis.String(object.mevContract) : "",
      revenues: isSet(object.revenues) ? Assets.fromJSON(object.revenues) : undefined,
      costs: isSet(object.costs) ? Assets.fromJSON(object.costs) : undefined,
      profits: isSet(object.profits) ? Assets.fromJSON(object.profits) : undefined,
      tokens: globalThis.Array.isArray(object?.tokens) ? object.tokens.map((e: any) => TokenInfo.fromJSON(e)) : [],
      traders: globalThis.Array.isArray(object?.traders) ? object.traders.map((e: any) => Pool.fromJSON(e)) : [],
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: ArbitrageResult): unknown {
    const obj: any = {};
    if (message.txHash !== "") {
      obj.txHash = message.txHash;
    }
    if (message.txIndex !== 0) {
      obj.txIndex = Math.round(message.txIndex);
    }
    if (message.mevContract !== "") {
      obj.mevContract = message.mevContract;
    }
    if (message.revenues !== undefined) {
      obj.revenues = Assets.toJSON(message.revenues);
    }
    if (message.costs !== undefined) {
      obj.costs = Assets.toJSON(message.costs);
    }
    if (message.profits !== undefined) {
      obj.profits = Assets.toJSON(message.profits);
    }
    if (message.tokens?.length) {
      obj.tokens = message.tokens.map((e) => TokenInfo.toJSON(e));
    }
    if (message.traders?.length) {
      obj.traders = message.traders.map((e) => Pool.toJSON(e));
    }
    if (message.tags?.length) {
      obj.tags = message.tags;
    }
    return obj;
  },

  create(base?: DeepPartial<ArbitrageResult>): ArbitrageResult {
    return ArbitrageResult.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<ArbitrageResult>): ArbitrageResult {
    const message = createBaseArbitrageResult();
    message.txHash = object.txHash ?? "";
    message.txIndex = object.txIndex ?? 0;
    message.mevContract = object.mevContract ?? "";
    message.revenues = (object.revenues !== undefined && object.revenues !== null)
      ? Assets.fromPartial(object.revenues)
      : undefined;
    message.costs = (object.costs !== undefined && object.costs !== null)
      ? Assets.fromPartial(object.costs)
      : undefined;
    message.profits = (object.profits !== undefined && object.profits !== null)
      ? Assets.fromPartial(object.profits)
      : undefined;
    message.tokens = object.tokens?.map((e) => TokenInfo.fromPartial(e)) || [];
    message.traders = object.traders?.map((e) => Pool.fromPartial(e)) || [];
    message.tags = object.tags?.map((e) => e) || [];
    return message;
  },
};

function createBaseAssets(): Assets {
  return { tokens: [], totalUsd: 0 };
}

export const Assets = {
  encode(message: Assets, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.tokens) {
      Assets_Token.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    if (message.totalUsd !== 0) {
      writer.uint32(17).double(message.totalUsd);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Assets {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssets();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.tokens.push(Assets_Token.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.totalUsd = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Assets {
    return {
      tokens: globalThis.Array.isArray(object?.tokens) ? object.tokens.map((e: any) => Assets_Token.fromJSON(e)) : [],
      totalUsd: isSet(object.totalUsd) ? globalThis.Number(object.totalUsd) : 0,
    };
  },

  toJSON(message: Assets): unknown {
    const obj: any = {};
    if (message.tokens?.length) {
      obj.tokens = message.tokens.map((e) => Assets_Token.toJSON(e));
    }
    if (message.totalUsd !== 0) {
      obj.totalUsd = message.totalUsd;
    }
    return obj;
  },

  create(base?: DeepPartial<Assets>): Assets {
    return Assets.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Assets>): Assets {
    const message = createBaseAssets();
    message.tokens = object.tokens?.map((e) => Assets_Token.fromPartial(e)) || [];
    message.totalUsd = object.totalUsd ?? 0;
    return message;
  },
};

function createBaseAssets_Token(): Assets_Token {
  return { address: "", symbol: "", value: 0, valueUsd: undefined };
}

export const Assets_Token = {
  encode(message: Assets_Token, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.address !== "") {
      writer.uint32(10).string(message.address);
    }
    if (message.symbol !== "") {
      writer.uint32(18).string(message.symbol);
    }
    if (message.value !== 0) {
      writer.uint32(25).double(message.value);
    }
    if (message.valueUsd !== undefined) {
      writer.uint32(33).double(message.valueUsd);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Assets_Token {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAssets_Token();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.address = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.symbol = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.value = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.valueUsd = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Assets_Token {
    return {
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      symbol: isSet(object.symbol) ? globalThis.String(object.symbol) : "",
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
      valueUsd: isSet(object.valueUsd) ? globalThis.Number(object.valueUsd) : undefined,
    };
  },

  toJSON(message: Assets_Token): unknown {
    const obj: any = {};
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.symbol !== "") {
      obj.symbol = message.symbol;
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    if (message.valueUsd !== undefined) {
      obj.valueUsd = message.valueUsd;
    }
    return obj;
  },

  create(base?: DeepPartial<Assets_Token>): Assets_Token {
    return Assets_Token.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Assets_Token>): Assets_Token {
    const message = createBaseAssets_Token();
    message.address = object.address ?? "";
    message.symbol = object.symbol ?? "";
    message.value = object.value ?? 0;
    message.valueUsd = object.valueUsd ?? undefined;
    return message;
  },
};

function createBaseTokenInfo(): TokenInfo {
  return { address: "", symbol: undefined, timestamp: undefined, price: undefined };
}

export const TokenInfo = {
  encode(message: TokenInfo, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.address !== "") {
      writer.uint32(10).string(message.address);
    }
    if (message.symbol !== undefined) {
      writer.uint32(18).string(message.symbol);
    }
    if (message.timestamp !== undefined) {
      Timestamp.encode(toTimestamp(message.timestamp), writer.uint32(26).fork()).ldelim();
    }
    if (message.price !== undefined) {
      writer.uint32(33).double(message.price);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): TokenInfo {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTokenInfo();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.address = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.symbol = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.timestamp = fromTimestamp(Timestamp.decode(reader, reader.uint32()));
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.price = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TokenInfo {
    return {
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      symbol: isSet(object.symbol) ? globalThis.String(object.symbol) : undefined,
      timestamp: isSet(object.timestamp) ? fromJsonTimestamp(object.timestamp) : undefined,
      price: isSet(object.price) ? globalThis.Number(object.price) : undefined,
    };
  },

  toJSON(message: TokenInfo): unknown {
    const obj: any = {};
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.symbol !== undefined) {
      obj.symbol = message.symbol;
    }
    if (message.timestamp !== undefined) {
      obj.timestamp = message.timestamp.toISOString();
    }
    if (message.price !== undefined) {
      obj.price = message.price;
    }
    return obj;
  },

  create(base?: DeepPartial<TokenInfo>): TokenInfo {
    return TokenInfo.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<TokenInfo>): TokenInfo {
    const message = createBaseTokenInfo();
    message.address = object.address ?? "";
    message.symbol = object.symbol ?? undefined;
    message.timestamp = object.timestamp ?? undefined;
    message.price = object.price ?? undefined;
    return message;
  },
};

function createBasePool(): Pool {
  return { address: "", protocol: undefined, tokens: [] };
}

export const Pool = {
  encode(message: Pool, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.address !== "") {
      writer.uint32(10).string(message.address);
    }
    if (message.protocol !== undefined) {
      writer.uint32(18).string(message.protocol);
    }
    for (const v of message.tokens) {
      TokenInfo.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Pool {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePool();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.address = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.protocol = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.tokens.push(TokenInfo.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Pool {
    return {
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      protocol: isSet(object.protocol) ? globalThis.String(object.protocol) : undefined,
      tokens: globalThis.Array.isArray(object?.tokens) ? object.tokens.map((e: any) => TokenInfo.fromJSON(e)) : [],
    };
  },

  toJSON(message: Pool): unknown {
    const obj: any = {};
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.protocol !== undefined) {
      obj.protocol = message.protocol;
    }
    if (message.tokens?.length) {
      obj.tokens = message.tokens.map((e) => TokenInfo.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<Pool>): Pool {
    return Pool.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<Pool>): Pool {
    const message = createBasePool();
    message.address = object.address ?? "";
    message.protocol = object.protocol ?? undefined;
    message.tokens = object.tokens?.map((e) => TokenInfo.fromPartial(e)) || [];
    return message;
  },
};

function createBaseDumpSimulationRequest(): DumpSimulationRequest {
  return { simulationId: "" };
}

export const DumpSimulationRequest = {
  encode(message: DumpSimulationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.simulationId !== "") {
      writer.uint32(10).string(message.simulationId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DumpSimulationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDumpSimulationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulationId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DumpSimulationRequest {
    return { simulationId: isSet(object.simulationId) ? globalThis.String(object.simulationId) : "" };
  },

  toJSON(message: DumpSimulationRequest): unknown {
    const obj: any = {};
    if (message.simulationId !== "") {
      obj.simulationId = message.simulationId;
    }
    return obj;
  },

  create(base?: DeepPartial<DumpSimulationRequest>): DumpSimulationRequest {
    return DumpSimulationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DumpSimulationRequest>): DumpSimulationRequest {
    const message = createBaseDumpSimulationRequest();
    message.simulationId = object.simulationId ?? "";
    return message;
  },
};

function createBaseDumpSimulationResponse(): DumpSimulationResponse {
  return { simulationReq: undefined, compilationReq: {} };
}

export const DumpSimulationResponse = {
  encode(message: DumpSimulationResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.simulationReq !== undefined) {
      SimulateTransactionRequest.encode(message.simulationReq, writer.uint32(10).fork()).ldelim();
    }
    Object.entries(message.compilationReq).forEach(([key, value]) => {
      DumpSimulationResponse_CompilationReqEntry.encode({ key: key as any, value }, writer.uint32(18).fork()).ldelim();
    });
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DumpSimulationResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDumpSimulationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulationReq = SimulateTransactionRequest.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          const entry2 = DumpSimulationResponse_CompilationReqEntry.decode(reader, reader.uint32());
          if (entry2.value !== undefined) {
            message.compilationReq[entry2.key] = entry2.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DumpSimulationResponse {
    return {
      simulationReq: isSet(object.simulationReq)
        ? SimulateTransactionRequest.fromJSON(object.simulationReq)
        : undefined,
      compilationReq: isObject(object.compilationReq)
        ? Object.entries(object.compilationReq).reduce<{ [key: string]: UploadUserCompilationRequest }>(
          (acc, [key, value]) => {
            acc[key] = UploadUserCompilationRequest.fromJSON(value);
            return acc;
          },
          {},
        )
        : {},
    };
  },

  toJSON(message: DumpSimulationResponse): unknown {
    const obj: any = {};
    if (message.simulationReq !== undefined) {
      obj.simulationReq = SimulateTransactionRequest.toJSON(message.simulationReq);
    }
    if (message.compilationReq) {
      const entries = Object.entries(message.compilationReq);
      if (entries.length > 0) {
        obj.compilationReq = {};
        entries.forEach(([k, v]) => {
          obj.compilationReq[k] = UploadUserCompilationRequest.toJSON(v);
        });
      }
    }
    return obj;
  },

  create(base?: DeepPartial<DumpSimulationResponse>): DumpSimulationResponse {
    return DumpSimulationResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DumpSimulationResponse>): DumpSimulationResponse {
    const message = createBaseDumpSimulationResponse();
    message.simulationReq = (object.simulationReq !== undefined && object.simulationReq !== null)
      ? SimulateTransactionRequest.fromPartial(object.simulationReq)
      : undefined;
    message.compilationReq = Object.entries(object.compilationReq ?? {}).reduce<
      { [key: string]: UploadUserCompilationRequest }
    >((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = UploadUserCompilationRequest.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  },
};

function createBaseDumpSimulationResponse_CompilationReqEntry(): DumpSimulationResponse_CompilationReqEntry {
  return { key: "", value: undefined };
}

export const DumpSimulationResponse_CompilationReqEntry = {
  encode(message: DumpSimulationResponse_CompilationReqEntry, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      UploadUserCompilationRequest.encode(message.value, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DumpSimulationResponse_CompilationReqEntry {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDumpSimulationResponse_CompilationReqEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = UploadUserCompilationRequest.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DumpSimulationResponse_CompilationReqEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : "",
      value: isSet(object.value) ? UploadUserCompilationRequest.fromJSON(object.value) : undefined,
    };
  },

  toJSON(message: DumpSimulationResponse_CompilationReqEntry): unknown {
    const obj: any = {};
    if (message.key !== "") {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = UploadUserCompilationRequest.toJSON(message.value);
    }
    return obj;
  },

  create(base?: DeepPartial<DumpSimulationResponse_CompilationReqEntry>): DumpSimulationResponse_CompilationReqEntry {
    return DumpSimulationResponse_CompilationReqEntry.fromPartial(base ?? {});
  },
  fromPartial(
    object: DeepPartial<DumpSimulationResponse_CompilationReqEntry>,
  ): DumpSimulationResponse_CompilationReqEntry {
    const message = createBaseDumpSimulationResponse_CompilationReqEntry();
    message.key = object.key ?? "";
    message.value = (object.value !== undefined && object.value !== null)
      ? UploadUserCompilationRequest.fromPartial(object.value)
      : undefined;
    return message;
  },
};

function createBaseDumpUserCompilationRequest(): DumpUserCompilationRequest {
  return { userCompilationId: "" };
}

export const DumpUserCompilationRequest = {
  encode(message: DumpUserCompilationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userCompilationId !== "") {
      writer.uint32(10).string(message.userCompilationId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DumpUserCompilationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDumpUserCompilationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DumpUserCompilationRequest {
    return { userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "" };
  },

  toJSON(message: DumpUserCompilationRequest): unknown {
    const obj: any = {};
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    return obj;
  },

  create(base?: DeepPartial<DumpUserCompilationRequest>): DumpUserCompilationRequest {
    return DumpUserCompilationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DumpUserCompilationRequest>): DumpUserCompilationRequest {
    const message = createBaseDumpUserCompilationRequest();
    message.userCompilationId = object.userCompilationId ?? "";
    return message;
  },
};

function createBaseDumpUserCompilationResponse(): DumpUserCompilationResponse {
  return { compilationReq: undefined };
}

export const DumpUserCompilationResponse = {
  encode(message: DumpUserCompilationResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.compilationReq !== undefined) {
      UploadUserCompilationRequest.encode(message.compilationReq, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DumpUserCompilationResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDumpUserCompilationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.compilationReq = UploadUserCompilationRequest.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DumpUserCompilationResponse {
    return {
      compilationReq: isSet(object.compilationReq)
        ? UploadUserCompilationRequest.fromJSON(object.compilationReq)
        : undefined,
    };
  },

  toJSON(message: DumpUserCompilationResponse): unknown {
    const obj: any = {};
    if (message.compilationReq !== undefined) {
      obj.compilationReq = UploadUserCompilationRequest.toJSON(message.compilationReq);
    }
    return obj;
  },

  create(base?: DeepPartial<DumpUserCompilationResponse>): DumpUserCompilationResponse {
    return DumpUserCompilationResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DumpUserCompilationResponse>): DumpUserCompilationResponse {
    const message = createBaseDumpUserCompilationResponse();
    message.compilationReq = (object.compilationReq !== undefined && object.compilationReq !== null)
      ? UploadUserCompilationRequest.fromPartial(object.compilationReq)
      : undefined;
    return message;
  },
};

function createBaseSimulateDeploymentRequest(): SimulateDeploymentRequest {
  return {
    projectOwner: undefined,
    projectSlug: undefined,
    networkId: "",
    chainSpec: undefined,
    address: "",
    userCompilationId: "",
    blockNumber: undefined,
  };
}

export const SimulateDeploymentRequest = {
  encode(message: SimulateDeploymentRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.networkId !== "") {
      writer.uint32(26).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(58).fork()).ldelim();
    }
    if (message.address !== "") {
      writer.uint32(34).string(message.address);
    }
    if (message.userCompilationId !== "") {
      writer.uint32(42).string(message.userCompilationId);
    }
    if (message.blockNumber !== undefined) {
      writer.uint32(50).string(message.blockNumber);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SimulateDeploymentRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimulateDeploymentRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.address = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.userCompilationId = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.blockNumber = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimulateDeploymentRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      userCompilationId: isSet(object.userCompilationId) ? globalThis.String(object.userCompilationId) : "",
      blockNumber: isSet(object.blockNumber) ? globalThis.String(object.blockNumber) : undefined,
    };
  },

  toJSON(message: SimulateDeploymentRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.userCompilationId !== "") {
      obj.userCompilationId = message.userCompilationId;
    }
    if (message.blockNumber !== undefined) {
      obj.blockNumber = message.blockNumber;
    }
    return obj;
  },

  create(base?: DeepPartial<SimulateDeploymentRequest>): SimulateDeploymentRequest {
    return SimulateDeploymentRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SimulateDeploymentRequest>): SimulateDeploymentRequest {
    const message = createBaseSimulateDeploymentRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.address = object.address ?? "";
    message.userCompilationId = object.userCompilationId ?? "";
    message.blockNumber = object.blockNumber ?? undefined;
    return message;
  },
};

function createBaseSimulateDeploymentResponse(): SimulateDeploymentResponse {
  return { simulation: undefined };
}

export const SimulateDeploymentResponse = {
  encode(message: SimulateDeploymentResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.simulation !== undefined) {
      Simulation.encode(message.simulation, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SimulateDeploymentResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimulateDeploymentResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulation = Simulation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimulateDeploymentResponse {
    return { simulation: isSet(object.simulation) ? Simulation.fromJSON(object.simulation) : undefined };
  },

  toJSON(message: SimulateDeploymentResponse): unknown {
    const obj: any = {};
    if (message.simulation !== undefined) {
      obj.simulation = Simulation.toJSON(message.simulation);
    }
    return obj;
  },

  create(base?: DeepPartial<SimulateDeploymentResponse>): SimulateDeploymentResponse {
    return SimulateDeploymentResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<SimulateDeploymentResponse>): SimulateDeploymentResponse {
    const message = createBaseSimulateDeploymentResponse();
    message.simulation = (object.simulation !== undefined && object.simulation !== null)
      ? Simulation.fromPartial(object.simulation)
      : undefined;
    return message;
  },
};

function createBaseDownloadContractRequest(): DownloadContractRequest {
  return { networkId: "", chainSpec: undefined, address: "" };
}

export const DownloadContractRequest = {
  encode(message: DownloadContractRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(26).fork()).ldelim();
    }
    if (message.address !== "") {
      writer.uint32(18).string(message.address);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): DownloadContractRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDownloadContractRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.address = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DownloadContractRequest {
    return {
      networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "",
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      address: isSet(object.address) ? globalThis.String(object.address) : "",
    };
  },

  toJSON(message: DownloadContractRequest): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    return obj;
  },

  create(base?: DeepPartial<DownloadContractRequest>): DownloadContractRequest {
    return DownloadContractRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<DownloadContractRequest>): DownloadContractRequest {
    const message = createBaseDownloadContractRequest();
    message.networkId = object.networkId ?? "";
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.address = object.address ?? "";
    return message;
  },
};

function createBaseGetForkStateRequestDeprecated(): GetForkStateRequestDeprecated {
  return { networkId: "" };
}

export const GetForkStateRequestDeprecated = {
  encode(message: GetForkStateRequestDeprecated, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.networkId !== "") {
      writer.uint32(10).string(message.networkId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetForkStateRequestDeprecated {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetForkStateRequestDeprecated();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.networkId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetForkStateRequestDeprecated {
    return { networkId: isSet(object.networkId) ? globalThis.String(object.networkId) : "" };
  },

  toJSON(message: GetForkStateRequestDeprecated): unknown {
    const obj: any = {};
    if (message.networkId !== "") {
      obj.networkId = message.networkId;
    }
    return obj;
  },

  create(base?: DeepPartial<GetForkStateRequestDeprecated>): GetForkStateRequestDeprecated {
    return GetForkStateRequestDeprecated.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetForkStateRequestDeprecated>): GetForkStateRequestDeprecated {
    const message = createBaseGetForkStateRequestDeprecated();
    message.networkId = object.networkId ?? "";
    return message;
  },
};

function createBaseGetRecentTransactionsRequest(): GetRecentTransactionsRequest {
  return { projectOwner: undefined, projectSlug: undefined, chainSpec: undefined, limit: undefined };
}

export const GetRecentTransactionsRequest = {
  encode(message: GetRecentTransactionsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.projectOwner !== undefined) {
      writer.uint32(10).string(message.projectOwner);
    }
    if (message.projectSlug !== undefined) {
      writer.uint32(18).string(message.projectSlug);
    }
    if (message.chainSpec !== undefined) {
      ChainIdentifier.encode(message.chainSpec, writer.uint32(26).fork()).ldelim();
    }
    if (message.limit !== undefined) {
      writer.uint32(32).int32(message.limit);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetRecentTransactionsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRecentTransactionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.projectOwner = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.projectSlug = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.chainSpec = ChainIdentifier.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.limit = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRecentTransactionsRequest {
    return {
      projectOwner: isSet(object.projectOwner) ? globalThis.String(object.projectOwner) : undefined,
      projectSlug: isSet(object.projectSlug) ? globalThis.String(object.projectSlug) : undefined,
      chainSpec: isSet(object.chainSpec) ? ChainIdentifier.fromJSON(object.chainSpec) : undefined,
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : undefined,
    };
  },

  toJSON(message: GetRecentTransactionsRequest): unknown {
    const obj: any = {};
    if (message.projectOwner !== undefined) {
      obj.projectOwner = message.projectOwner;
    }
    if (message.projectSlug !== undefined) {
      obj.projectSlug = message.projectSlug;
    }
    if (message.chainSpec !== undefined) {
      obj.chainSpec = ChainIdentifier.toJSON(message.chainSpec);
    }
    if (message.limit !== undefined) {
      obj.limit = Math.round(message.limit);
    }
    return obj;
  },

  create(base?: DeepPartial<GetRecentTransactionsRequest>): GetRecentTransactionsRequest {
    return GetRecentTransactionsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetRecentTransactionsRequest>): GetRecentTransactionsRequest {
    const message = createBaseGetRecentTransactionsRequest();
    message.projectOwner = object.projectOwner ?? undefined;
    message.projectSlug = object.projectSlug ?? undefined;
    message.chainSpec = (object.chainSpec !== undefined && object.chainSpec !== null)
      ? ChainIdentifier.fromPartial(object.chainSpec)
      : undefined;
    message.limit = object.limit ?? undefined;
    return message;
  },
};

function createBaseGetRecentTransactionsResponse(): GetRecentTransactionsResponse {
  return { txHashes: [] };
}

export const GetRecentTransactionsResponse = {
  encode(message: GetRecentTransactionsResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.txHashes) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetRecentTransactionsResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRecentTransactionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.txHashes.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRecentTransactionsResponse {
    return {
      txHashes: globalThis.Array.isArray(object?.txHashes) ? object.txHashes.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: GetRecentTransactionsResponse): unknown {
    const obj: any = {};
    if (message.txHashes?.length) {
      obj.txHashes = message.txHashes;
    }
    return obj;
  },

  create(base?: DeepPartial<GetRecentTransactionsResponse>): GetRecentTransactionsResponse {
    return GetRecentTransactionsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetRecentTransactionsResponse>): GetRecentTransactionsResponse {
    const message = createBaseGetRecentTransactionsResponse();
    message.txHashes = object.txHashes?.map((e) => e) || [];
    return message;
  },
};

function createBaseGetEstimatedGasPriceRequest(): GetEstimatedGasPriceRequest {
  return { chainId: "" };
}

export const GetEstimatedGasPriceRequest = {
  encode(message: GetEstimatedGasPriceRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.chainId !== "") {
      writer.uint32(10).string(message.chainId);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetEstimatedGasPriceRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEstimatedGasPriceRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.chainId = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetEstimatedGasPriceRequest {
    return { chainId: isSet(object.chainId) ? globalThis.String(object.chainId) : "" };
  },

  toJSON(message: GetEstimatedGasPriceRequest): unknown {
    const obj: any = {};
    if (message.chainId !== "") {
      obj.chainId = message.chainId;
    }
    return obj;
  },

  create(base?: DeepPartial<GetEstimatedGasPriceRequest>): GetEstimatedGasPriceRequest {
    return GetEstimatedGasPriceRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetEstimatedGasPriceRequest>): GetEstimatedGasPriceRequest {
    const message = createBaseGetEstimatedGasPriceRequest();
    message.chainId = object.chainId ?? "";
    return message;
  },
};

function createBaseGetEstimatedGasPriceResponse(): GetEstimatedGasPriceResponse {
  return {
    system: "",
    network: "",
    unit: "",
    maxPrice: 0,
    currentBlockNumber: 0,
    msSinceLastBlock: 0,
    blockPrices: [],
  };
}

export const GetEstimatedGasPriceResponse = {
  encode(message: GetEstimatedGasPriceResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.system !== "") {
      writer.uint32(10).string(message.system);
    }
    if (message.network !== "") {
      writer.uint32(18).string(message.network);
    }
    if (message.unit !== "") {
      writer.uint32(26).string(message.unit);
    }
    if (message.maxPrice !== 0) {
      writer.uint32(33).double(message.maxPrice);
    }
    if (message.currentBlockNumber !== 0) {
      writer.uint32(40).int32(message.currentBlockNumber);
    }
    if (message.msSinceLastBlock !== 0) {
      writer.uint32(48).int32(message.msSinceLastBlock);
    }
    for (const v of message.blockPrices) {
      BlockPrice.encode(v!, writer.uint32(58).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetEstimatedGasPriceResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetEstimatedGasPriceResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.system = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.network = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.unit = reader.string();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.maxPrice = reader.double();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.currentBlockNumber = reader.int32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.msSinceLastBlock = reader.int32();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.blockPrices.push(BlockPrice.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetEstimatedGasPriceResponse {
    return {
      system: isSet(object.system) ? globalThis.String(object.system) : "",
      network: isSet(object.network) ? globalThis.String(object.network) : "",
      unit: isSet(object.unit) ? globalThis.String(object.unit) : "",
      maxPrice: isSet(object.maxPrice) ? globalThis.Number(object.maxPrice) : 0,
      currentBlockNumber: isSet(object.currentBlockNumber) ? globalThis.Number(object.currentBlockNumber) : 0,
      msSinceLastBlock: isSet(object.msSinceLastBlock) ? globalThis.Number(object.msSinceLastBlock) : 0,
      blockPrices: globalThis.Array.isArray(object?.blockPrices)
        ? object.blockPrices.map((e: any) => BlockPrice.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetEstimatedGasPriceResponse): unknown {
    const obj: any = {};
    if (message.system !== "") {
      obj.system = message.system;
    }
    if (message.network !== "") {
      obj.network = message.network;
    }
    if (message.unit !== "") {
      obj.unit = message.unit;
    }
    if (message.maxPrice !== 0) {
      obj.maxPrice = message.maxPrice;
    }
    if (message.currentBlockNumber !== 0) {
      obj.currentBlockNumber = Math.round(message.currentBlockNumber);
    }
    if (message.msSinceLastBlock !== 0) {
      obj.msSinceLastBlock = Math.round(message.msSinceLastBlock);
    }
    if (message.blockPrices?.length) {
      obj.blockPrices = message.blockPrices.map((e) => BlockPrice.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<GetEstimatedGasPriceResponse>): GetEstimatedGasPriceResponse {
    return GetEstimatedGasPriceResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetEstimatedGasPriceResponse>): GetEstimatedGasPriceResponse {
    const message = createBaseGetEstimatedGasPriceResponse();
    message.system = object.system ?? "";
    message.network = object.network ?? "";
    message.unit = object.unit ?? "";
    message.maxPrice = object.maxPrice ?? 0;
    message.currentBlockNumber = object.currentBlockNumber ?? 0;
    message.msSinceLastBlock = object.msSinceLastBlock ?? 0;
    message.blockPrices = object.blockPrices?.map((e) => BlockPrice.fromPartial(e)) || [];
    return message;
  },
};

function createBaseBlockPrice(): BlockPrice {
  return { blockNumber: 0, estimatedTransactionCount: 0, baseFeePerGas: 0, blobBaseFeePerGas: 0, estimatedPrices: [] };
}

export const BlockPrice = {
  encode(message: BlockPrice, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.blockNumber !== 0) {
      writer.uint32(8).int32(message.blockNumber);
    }
    if (message.estimatedTransactionCount !== 0) {
      writer.uint32(16).int32(message.estimatedTransactionCount);
    }
    if (message.baseFeePerGas !== 0) {
      writer.uint32(25).double(message.baseFeePerGas);
    }
    if (message.blobBaseFeePerGas !== 0) {
      writer.uint32(33).double(message.blobBaseFeePerGas);
    }
    for (const v of message.estimatedPrices) {
      EstimatedPrice.encode(v!, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BlockPrice {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBlockPrice();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.blockNumber = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.estimatedTransactionCount = reader.int32();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.baseFeePerGas = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.blobBaseFeePerGas = reader.double();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.estimatedPrices.push(EstimatedPrice.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BlockPrice {
    return {
      blockNumber: isSet(object.blockNumber) ? globalThis.Number(object.blockNumber) : 0,
      estimatedTransactionCount: isSet(object.estimatedTransactionCount)
        ? globalThis.Number(object.estimatedTransactionCount)
        : 0,
      baseFeePerGas: isSet(object.baseFeePerGas) ? globalThis.Number(object.baseFeePerGas) : 0,
      blobBaseFeePerGas: isSet(object.blobBaseFeePerGas) ? globalThis.Number(object.blobBaseFeePerGas) : 0,
      estimatedPrices: globalThis.Array.isArray(object?.estimatedPrices)
        ? object.estimatedPrices.map((e: any) => EstimatedPrice.fromJSON(e))
        : [],
    };
  },

  toJSON(message: BlockPrice): unknown {
    const obj: any = {};
    if (message.blockNumber !== 0) {
      obj.blockNumber = Math.round(message.blockNumber);
    }
    if (message.estimatedTransactionCount !== 0) {
      obj.estimatedTransactionCount = Math.round(message.estimatedTransactionCount);
    }
    if (message.baseFeePerGas !== 0) {
      obj.baseFeePerGas = message.baseFeePerGas;
    }
    if (message.blobBaseFeePerGas !== 0) {
      obj.blobBaseFeePerGas = message.blobBaseFeePerGas;
    }
    if (message.estimatedPrices?.length) {
      obj.estimatedPrices = message.estimatedPrices.map((e) => EstimatedPrice.toJSON(e));
    }
    return obj;
  },

  create(base?: DeepPartial<BlockPrice>): BlockPrice {
    return BlockPrice.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<BlockPrice>): BlockPrice {
    const message = createBaseBlockPrice();
    message.blockNumber = object.blockNumber ?? 0;
    message.estimatedTransactionCount = object.estimatedTransactionCount ?? 0;
    message.baseFeePerGas = object.baseFeePerGas ?? 0;
    message.blobBaseFeePerGas = object.blobBaseFeePerGas ?? 0;
    message.estimatedPrices = object.estimatedPrices?.map((e) => EstimatedPrice.fromPartial(e)) || [];
    return message;
  },
};

function createBaseEstimatedPrice(): EstimatedPrice {
  return { confidence: 0, price: 0, maxPriorityFeePerGas: 0, maxFeePerGas: 0 };
}

export const EstimatedPrice = {
  encode(message: EstimatedPrice, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.confidence !== 0) {
      writer.uint32(8).int32(message.confidence);
    }
    if (message.price !== 0) {
      writer.uint32(17).double(message.price);
    }
    if (message.maxPriorityFeePerGas !== 0) {
      writer.uint32(25).double(message.maxPriorityFeePerGas);
    }
    if (message.maxFeePerGas !== 0) {
      writer.uint32(33).double(message.maxFeePerGas);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): EstimatedPrice {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEstimatedPrice();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.confidence = reader.int32();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.price = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.maxPriorityFeePerGas = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }

          message.maxFeePerGas = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EstimatedPrice {
    return {
      confidence: isSet(object.confidence) ? globalThis.Number(object.confidence) : 0,
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      maxPriorityFeePerGas: isSet(object.maxPriorityFeePerGas) ? globalThis.Number(object.maxPriorityFeePerGas) : 0,
      maxFeePerGas: isSet(object.maxFeePerGas) ? globalThis.Number(object.maxFeePerGas) : 0,
    };
  },

  toJSON(message: EstimatedPrice): unknown {
    const obj: any = {};
    if (message.confidence !== 0) {
      obj.confidence = Math.round(message.confidence);
    }
    if (message.price !== 0) {
      obj.price = message.price;
    }
    if (message.maxPriorityFeePerGas !== 0) {
      obj.maxPriorityFeePerGas = message.maxPriorityFeePerGas;
    }
    if (message.maxFeePerGas !== 0) {
      obj.maxFeePerGas = message.maxFeePerGas;
    }
    return obj;
  },

  create(base?: DeepPartial<EstimatedPrice>): EstimatedPrice {
    return EstimatedPrice.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<EstimatedPrice>): EstimatedPrice {
    const message = createBaseEstimatedPrice();
    message.confidence = object.confidence ?? 0;
    message.price = object.price ?? 0;
    message.maxPriorityFeePerGas = object.maxPriorityFeePerGas ?? 0;
    message.maxFeePerGas = object.maxFeePerGas ?? 0;
    return message;
  },
};

function createBaseEvmSearchTransactionsRequest(): EvmSearchTransactionsRequest {
  return {
    chainId: [],
    address: [],
    includeDirect: false,
    includeTrace: false,
    includeIn: false,
    includeOut: false,
    startBlock: undefined,
    endBlock: undefined,
    startTimestamp: undefined,
    endTimestamp: undefined,
    transactionStatus: [],
    methodSignature: undefined,
    limit: 0,
    pageToken: new Uint8Array(0),
  };
}

export const EvmSearchTransactionsRequest = {
  encode(message: EvmSearchTransactionsRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.chainId) {
      writer.uint32(10).string(v!);
    }
    for (const v of message.address) {
      writer.uint32(18).string(v!);
    }
    if (message.includeDirect !== false) {
      writer.uint32(24).bool(message.includeDirect);
    }
    if (message.includeTrace !== false) {
      writer.uint32(32).bool(message.includeTrace);
    }
    if (message.includeIn !== false) {
      writer.uint32(40).bool(message.includeIn);
    }
    if (message.includeOut !== false) {
      writer.uint32(48).bool(message.includeOut);
    }
    if (message.startBlock !== undefined) {
      if (BigInt.asIntN(64, message.startBlock) !== message.startBlock) {
        throw new globalThis.Error("value provided for field message.startBlock of type int64 too large");
      }
      writer.uint32(56).int64(message.startBlock.toString());
    }
    if (message.endBlock !== undefined) {
      if (BigInt.asIntN(64, message.endBlock) !== message.endBlock) {
        throw new globalThis.Error("value provided for field message.endBlock of type int64 too large");
      }
      writer.uint32(64).int64(message.endBlock.toString());
    }
    if (message.startTimestamp !== undefined) {
      if (BigInt.asIntN(64, message.startTimestamp) !== message.startTimestamp) {
        throw new globalThis.Error("value provided for field message.startTimestamp of type int64 too large");
      }
      writer.uint32(72).int64(message.startTimestamp.toString());
    }
    if (message.endTimestamp !== undefined) {
      if (BigInt.asIntN(64, message.endTimestamp) !== message.endTimestamp) {
        throw new globalThis.Error("value provided for field message.endTimestamp of type int64 too large");
      }
      writer.uint32(80).int64(message.endTimestamp.toString());
    }
    writer.uint32(90).fork();
    for (const v of message.transactionStatus) {
      writer.int32(v);
    }
    writer.ldelim();
    if (message.methodSignature !== undefined) {
      writer.uint32(98).string(message.methodSignature);
    }
    if (message.limit !== 0) {
      writer.uint32(256).int32(message.limit);
    }
    if (message.pageToken.length !== 0) {
      writer.uint32(266).bytes(message.pageToken);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): EvmSearchTransactionsRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEvmSearchTransactionsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.chainId.push(reader.string());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.address.push(reader.string());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.includeDirect = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.includeTrace = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.includeIn = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.includeOut = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.startBlock = longToBigint(reader.int64() as Long);
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.endBlock = longToBigint(reader.int64() as Long);
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.startTimestamp = longToBigint(reader.int64() as Long);
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.endTimestamp = longToBigint(reader.int64() as Long);
          continue;
        case 11:
          if (tag === 88) {
            message.transactionStatus.push(reader.int32());

            continue;
          }

          if (tag === 90) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.transactionStatus.push(reader.int32());
            }

            continue;
          }

          break;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.methodSignature = reader.string();
          continue;
        case 32:
          if (tag !== 256) {
            break;
          }

          message.limit = reader.int32();
          continue;
        case 33:
          if (tag !== 266) {
            break;
          }

          message.pageToken = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EvmSearchTransactionsRequest {
    return {
      chainId: globalThis.Array.isArray(object?.chainId) ? object.chainId.map((e: any) => globalThis.String(e)) : [],
      address: globalThis.Array.isArray(object?.address) ? object.address.map((e: any) => globalThis.String(e)) : [],
      includeDirect: isSet(object.includeDirect) ? globalThis.Boolean(object.includeDirect) : false,
      includeTrace: isSet(object.includeTrace) ? globalThis.Boolean(object.includeTrace) : false,
      includeIn: isSet(object.includeIn) ? globalThis.Boolean(object.includeIn) : false,
      includeOut: isSet(object.includeOut) ? globalThis.Boolean(object.includeOut) : false,
      startBlock: isSet(object.startBlock) ? BigInt(object.startBlock) : undefined,
      endBlock: isSet(object.endBlock) ? BigInt(object.endBlock) : undefined,
      startTimestamp: isSet(object.startTimestamp) ? BigInt(object.startTimestamp) : undefined,
      endTimestamp: isSet(object.endTimestamp) ? BigInt(object.endTimestamp) : undefined,
      transactionStatus: globalThis.Array.isArray(object?.transactionStatus)
        ? object.transactionStatus.map((e: any) => globalThis.Number(e))
        : [],
      methodSignature: isSet(object.methodSignature) ? globalThis.String(object.methodSignature) : undefined,
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : 0,
      pageToken: isSet(object.pageToken) ? bytesFromBase64(object.pageToken) : new Uint8Array(0),
    };
  },

  toJSON(message: EvmSearchTransactionsRequest): unknown {
    const obj: any = {};
    if (message.chainId?.length) {
      obj.chainId = message.chainId;
    }
    if (message.address?.length) {
      obj.address = message.address;
    }
    if (message.includeDirect !== false) {
      obj.includeDirect = message.includeDirect;
    }
    if (message.includeTrace !== false) {
      obj.includeTrace = message.includeTrace;
    }
    if (message.includeIn !== false) {
      obj.includeIn = message.includeIn;
    }
    if (message.includeOut !== false) {
      obj.includeOut = message.includeOut;
    }
    if (message.startBlock !== undefined) {
      obj.startBlock = message.startBlock.toString();
    }
    if (message.endBlock !== undefined) {
      obj.endBlock = message.endBlock.toString();
    }
    if (message.startTimestamp !== undefined) {
      obj.startTimestamp = message.startTimestamp.toString();
    }
    if (message.endTimestamp !== undefined) {
      obj.endTimestamp = message.endTimestamp.toString();
    }
    if (message.transactionStatus?.length) {
      obj.transactionStatus = message.transactionStatus.map((e) => Math.round(e));
    }
    if (message.methodSignature !== undefined) {
      obj.methodSignature = message.methodSignature;
    }
    if (message.limit !== 0) {
      obj.limit = Math.round(message.limit);
    }
    if (message.pageToken.length !== 0) {
      obj.pageToken = base64FromBytes(message.pageToken);
    }
    return obj;
  },

  create(base?: DeepPartial<EvmSearchTransactionsRequest>): EvmSearchTransactionsRequest {
    return EvmSearchTransactionsRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<EvmSearchTransactionsRequest>): EvmSearchTransactionsRequest {
    const message = createBaseEvmSearchTransactionsRequest();
    message.chainId = object.chainId?.map((e) => e) || [];
    message.address = object.address?.map((e) => e) || [];
    message.includeDirect = object.includeDirect ?? false;
    message.includeTrace = object.includeTrace ?? false;
    message.includeIn = object.includeIn ?? false;
    message.includeOut = object.includeOut ?? false;
    message.startBlock = object.startBlock ?? undefined;
    message.endBlock = object.endBlock ?? undefined;
    message.startTimestamp = object.startTimestamp ?? undefined;
    message.endTimestamp = object.endTimestamp ?? undefined;
    message.transactionStatus = object.transactionStatus?.map((e) => e) || [];
    message.methodSignature = object.methodSignature ?? undefined;
    message.limit = object.limit ?? 0;
    message.pageToken = object.pageToken ?? new Uint8Array(0);
    return message;
  },
};

function createBaseEvmRawTransaction(): EvmRawTransaction {
  return {
    hash: "",
    blockNumber: BigInt("0"),
    isIn: false,
    trace: false,
    tx: undefined,
    json: "",
    timestamp: BigInt("0"),
    transactionStatus: 0,
    methodSignature: "",
    methodSignatureText: undefined,
    abiItem: undefined,
  };
}

export const EvmRawTransaction = {
  encode(message: EvmRawTransaction, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.hash !== "") {
      writer.uint32(10).string(message.hash);
    }
    if (message.blockNumber !== BigInt("0")) {
      if (BigInt.asIntN(64, message.blockNumber) !== message.blockNumber) {
        throw new globalThis.Error("value provided for field message.blockNumber of type int64 too large");
      }
      writer.uint32(16).int64(message.blockNumber.toString());
    }
    if (message.isIn !== false) {
      writer.uint32(24).bool(message.isIn);
    }
    if (message.trace !== false) {
      writer.uint32(32).bool(message.trace);
    }
    if (message.tx !== undefined) {
      Transaction.encode(message.tx, writer.uint32(42).fork()).ldelim();
    }
    if (message.json !== "") {
      writer.uint32(50).string(message.json);
    }
    if (message.timestamp !== BigInt("0")) {
      if (BigInt.asIntN(64, message.timestamp) !== message.timestamp) {
        throw new globalThis.Error("value provided for field message.timestamp of type int64 too large");
      }
      writer.uint32(56).int64(message.timestamp.toString());
    }
    if (message.transactionStatus !== 0) {
      writer.uint32(64).int32(message.transactionStatus);
    }
    if (message.methodSignature !== "") {
      writer.uint32(74).string(message.methodSignature);
    }
    if (message.methodSignatureText !== undefined) {
      writer.uint32(82).string(message.methodSignatureText);
    }
    if (message.abiItem !== undefined) {
      writer.uint32(90).string(message.abiItem);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): EvmRawTransaction {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEvmRawTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.hash = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.blockNumber = longToBigint(reader.int64() as Long);
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.isIn = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.trace = reader.bool();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.tx = Transaction.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.json = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.timestamp = longToBigint(reader.int64() as Long);
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.transactionStatus = reader.int32();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.methodSignature = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.methodSignatureText = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.abiItem = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EvmRawTransaction {
    return {
      hash: isSet(object.hash) ? globalThis.String(object.hash) : "",
      blockNumber: isSet(object.blockNumber) ? BigInt(object.blockNumber) : BigInt("0"),
      isIn: isSet(object.isIn) ? globalThis.Boolean(object.isIn) : false,
      trace: isSet(object.trace) ? globalThis.Boolean(object.trace) : false,
      tx: isSet(object.tx) ? Transaction.fromJSON(object.tx) : undefined,
      json: isSet(object.json) ? globalThis.String(object.json) : "",
      timestamp: isSet(object.timestamp) ? BigInt(object.timestamp) : BigInt("0"),
      transactionStatus: isSet(object.transactionStatus) ? globalThis.Number(object.transactionStatus) : 0,
      methodSignature: isSet(object.methodSignature) ? globalThis.String(object.methodSignature) : "",
      methodSignatureText: isSet(object.methodSignatureText)
        ? globalThis.String(object.methodSignatureText)
        : undefined,
      abiItem: isSet(object.abiItem) ? globalThis.String(object.abiItem) : undefined,
    };
  },

  toJSON(message: EvmRawTransaction): unknown {
    const obj: any = {};
    if (message.hash !== "") {
      obj.hash = message.hash;
    }
    if (message.blockNumber !== BigInt("0")) {
      obj.blockNumber = message.blockNumber.toString();
    }
    if (message.isIn !== false) {
      obj.isIn = message.isIn;
    }
    if (message.trace !== false) {
      obj.trace = message.trace;
    }
    if (message.tx !== undefined) {
      obj.tx = Transaction.toJSON(message.tx);
    }
    if (message.json !== "") {
      obj.json = message.json;
    }
    if (message.timestamp !== BigInt("0")) {
      obj.timestamp = message.timestamp.toString();
    }
    if (message.transactionStatus !== 0) {
      obj.transactionStatus = Math.round(message.transactionStatus);
    }
    if (message.methodSignature !== "") {
      obj.methodSignature = message.methodSignature;
    }
    if (message.methodSignatureText !== undefined) {
      obj.methodSignatureText = message.methodSignatureText;
    }
    if (message.abiItem !== undefined) {
      obj.abiItem = message.abiItem;
    }
    return obj;
  },

  create(base?: DeepPartial<EvmRawTransaction>): EvmRawTransaction {
    return EvmRawTransaction.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<EvmRawTransaction>): EvmRawTransaction {
    const message = createBaseEvmRawTransaction();
    message.hash = object.hash ?? "";
    message.blockNumber = object.blockNumber ?? BigInt("0");
    message.isIn = object.isIn ?? false;
    message.trace = object.trace ?? false;
    message.tx = (object.tx !== undefined && object.tx !== null) ? Transaction.fromPartial(object.tx) : undefined;
    message.json = object.json ?? "";
    message.timestamp = object.timestamp ?? BigInt("0");
    message.transactionStatus = object.transactionStatus ?? 0;
    message.methodSignature = object.methodSignature ?? "";
    message.methodSignatureText = object.methodSignatureText ?? undefined;
    message.abiItem = object.abiItem ?? undefined;
    return message;
  },
};

function createBaseEvmSearchTransactionsResponse(): EvmSearchTransactionsResponse {
  return { transactions: [], nextPageToken: new Uint8Array(0) };
}

export const EvmSearchTransactionsResponse = {
  encode(message: EvmSearchTransactionsResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.transactions) {
      EvmRawTransaction.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    if (message.nextPageToken.length !== 0) {
      writer.uint32(18).bytes(message.nextPageToken);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): EvmSearchTransactionsResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseEvmSearchTransactionsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.transactions.push(EvmRawTransaction.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.nextPageToken = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): EvmSearchTransactionsResponse {
    return {
      transactions: globalThis.Array.isArray(object?.transactions)
        ? object.transactions.map((e: any) => EvmRawTransaction.fromJSON(e))
        : [],
      nextPageToken: isSet(object.nextPageToken) ? bytesFromBase64(object.nextPageToken) : new Uint8Array(0),
    };
  },

  toJSON(message: EvmSearchTransactionsResponse): unknown {
    const obj: any = {};
    if (message.transactions?.length) {
      obj.transactions = message.transactions.map((e) => EvmRawTransaction.toJSON(e));
    }
    if (message.nextPageToken.length !== 0) {
      obj.nextPageToken = base64FromBytes(message.nextPageToken);
    }
    return obj;
  },

  create(base?: DeepPartial<EvmSearchTransactionsResponse>): EvmSearchTransactionsResponse {
    return EvmSearchTransactionsResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<EvmSearchTransactionsResponse>): EvmSearchTransactionsResponse {
    const message = createBaseEvmSearchTransactionsResponse();
    message.transactions = object.transactions?.map((e) => EvmRawTransaction.fromPartial(e)) || [];
    message.nextPageToken = object.nextPageToken ?? new Uint8Array(0);
    return message;
  },
};

function createBaseCreateShareSimulationRequest(): CreateShareSimulationRequest {
  return { simulationId: "", public: false };
}

export const CreateShareSimulationRequest = {
  encode(message: CreateShareSimulationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.simulationId !== "") {
      writer.uint32(10).string(message.simulationId);
    }
    if (message.public !== false) {
      writer.uint32(16).bool(message.public);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CreateShareSimulationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateShareSimulationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulationId = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.public = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateShareSimulationRequest {
    return {
      simulationId: isSet(object.simulationId) ? globalThis.String(object.simulationId) : "",
      public: isSet(object.public) ? globalThis.Boolean(object.public) : false,
    };
  },

  toJSON(message: CreateShareSimulationRequest): unknown {
    const obj: any = {};
    if (message.simulationId !== "") {
      obj.simulationId = message.simulationId;
    }
    if (message.public !== false) {
      obj.public = message.public;
    }
    return obj;
  },

  create(base?: DeepPartial<CreateShareSimulationRequest>): CreateShareSimulationRequest {
    return CreateShareSimulationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateShareSimulationRequest>): CreateShareSimulationRequest {
    const message = createBaseCreateShareSimulationRequest();
    message.simulationId = object.simulationId ?? "";
    message.public = object.public ?? false;
    return message;
  },
};

function createBaseCreateShareSimulationResponse(): CreateShareSimulationResponse {
  return { id: "", public: false };
}

export const CreateShareSimulationResponse = {
  encode(message: CreateShareSimulationResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.public !== false) {
      writer.uint32(16).bool(message.public);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CreateShareSimulationResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateShareSimulationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.public = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateShareSimulationResponse {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      public: isSet(object.public) ? globalThis.Boolean(object.public) : false,
    };
  },

  toJSON(message: CreateShareSimulationResponse): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.public !== false) {
      obj.public = message.public;
    }
    return obj;
  },

  create(base?: DeepPartial<CreateShareSimulationResponse>): CreateShareSimulationResponse {
    return CreateShareSimulationResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<CreateShareSimulationResponse>): CreateShareSimulationResponse {
    const message = createBaseCreateShareSimulationResponse();
    message.id = object.id ?? "";
    message.public = object.public ?? false;
    return message;
  },
};

function createBaseGetShareSimulationRequest(): GetShareSimulationRequest {
  return { id: "" };
}

export const GetShareSimulationRequest = {
  encode(message: GetShareSimulationRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetShareSimulationRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetShareSimulationRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetShareSimulationRequest {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: GetShareSimulationRequest): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create(base?: DeepPartial<GetShareSimulationRequest>): GetShareSimulationRequest {
    return GetShareSimulationRequest.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetShareSimulationRequest>): GetShareSimulationRequest {
    const message = createBaseGetShareSimulationRequest();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseGetShareSimulationResponse(): GetShareSimulationResponse {
  return { simulation: undefined };
}

export const GetShareSimulationResponse = {
  encode(message: GetShareSimulationResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.simulation !== undefined) {
      Simulation.encode(message.simulation, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): GetShareSimulationResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetShareSimulationResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.simulation = Simulation.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetShareSimulationResponse {
    return { simulation: isSet(object.simulation) ? Simulation.fromJSON(object.simulation) : undefined };
  },

  toJSON(message: GetShareSimulationResponse): unknown {
    const obj: any = {};
    if (message.simulation !== undefined) {
      obj.simulation = Simulation.toJSON(message.simulation);
    }
    return obj;
  },

  create(base?: DeepPartial<GetShareSimulationResponse>): GetShareSimulationResponse {
    return GetShareSimulationResponse.fromPartial(base ?? {});
  },
  fromPartial(object: DeepPartial<GetShareSimulationResponse>): GetShareSimulationResponse {
    const message = createBaseGetShareSimulationResponse();
    message.simulation = (object.simulation !== undefined && object.simulation !== null)
      ? Simulation.fromPartial(object.simulation)
      : undefined;
    return message;
  },
};

export type SolidityServiceDefinition = typeof SolidityServiceDefinition;
export const SolidityServiceDefinition = {
  name: "SolidityService",
  fullName: "solidity_service.SolidityService",
  methods: {
    fetchAndCompile: {
      name: "FetchAndCompile",
      requestType: FetchAndCompileRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              36,
              18,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              102,
              101,
              116,
              99,
              104,
              95,
              97,
              110,
              100,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              101,
            ]),
          ],
        },
      },
    },
    getTransactionInfo: {
      name: "GetTransactionInfo",
      requestType: GetTransactionInfoRequest,
      requestStream: false,
      responseType: GetTransactionInfoResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              35,
              18,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              116,
              114,
              97,
              110,
              115,
              97,
              99,
              116,
              105,
              111,
              110,
              95,
              105,
              110,
              102,
              111,
            ]),
          ],
        },
      },
    },
    getLatestBlockNumber: {
      name: "GetLatestBlockNumber",
      requestType: GetLatestBlockNumberRequest,
      requestStream: false,
      responseType: GetLatestBlockNumberResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              31,
              18,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              98,
              108,
              111,
              99,
              107,
              95,
              110,
              117,
              109,
              98,
              101,
              114,
            ]),
          ],
        },
      },
    },
    getBlockSummary: {
      name: "GetBlockSummary",
      requestType: GetBlockSummaryRequest,
      requestStream: false,
      responseType: GetBlockSummaryResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              32,
              18,
              30,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              98,
              108,
              111,
              99,
              107,
              95,
              115,
              117,
              109,
              109,
              97,
              114,
              121,
            ]),
          ],
        },
      },
    },
    getStorageInfo: {
      name: "GetStorageInfo",
      requestType: GetStorageInfoRequest,
      requestStream: false,
      responseType: GetStorageInfoResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              31,
              18,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              116,
              111,
              114,
              97,
              103,
              101,
              95,
              105,
              110,
              102,
              111,
            ]),
          ],
        },
      },
    },
    getCode: {
      name: "GetCode",
      requestType: GetCodeRequest,
      requestStream: false,
      responseType: GetCodeResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              23,
              18,
              21,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              99,
              111,
              100,
              101,
            ]),
          ],
        },
      },
    },
    getCallTrace: {
      name: "GetCallTrace",
      requestType: GetCallTraceRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          400002: [
            new Uint8Array([
              30,
              10,
              12,
              97,
              112,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
              18,
              14,
              119,
              101,
              98,
              117,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
            ]),
          ],
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              29,
              18,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              99,
              97,
              108,
              108,
              95,
              116,
              114,
              97,
              99,
              101,
            ]),
          ],
        },
      },
    },
    sentioTraceTransaction: {
      name: "SentioTraceTransaction",
      requestType: SentioTraceTransactionRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              43,
              18,
              41,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              101,
              110,
              116,
              105,
              111,
              95,
              116,
              114,
              97,
              99,
              101,
              95,
              116,
              114,
              97,
              110,
              115,
              97,
              99,
              116,
              105,
              111,
              110,
            ]),
          ],
        },
      },
    },
    getAffectedContract: {
      name: "GetAffectedContract",
      requestType: GetAffectedContractRequest,
      requestStream: false,
      responseType: GetAffectedContractResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              36,
              18,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              97,
              102,
              102,
              101,
              99,
              116,
              101,
              100,
              95,
              99,
              111,
              110,
              116,
              114,
              97,
              99,
              116,
            ]),
          ],
        },
      },
    },
    simulateTransaction: {
      name: "SimulateTransaction",
      requestType: SimulateTransactionRequest,
      requestStream: false,
      responseType: SimulateTransactionResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          400002: [
            new Uint8Array([
              30,
              10,
              12,
              97,
              112,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
              18,
              14,
              119,
              101,
              98,
              117,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
            ]),
          ],
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              30,
              34,
              25,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              101,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    simulateTransactionBundle: {
      name: "SimulateTransactionBundle",
      requestType: SimulateTransactionBundleRequest,
      requestStream: false,
      responseType: SimulateTransactionBundleResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          400002: [
            new Uint8Array([
              30,
              10,
              12,
              97,
              112,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
              18,
              14,
              119,
              101,
              98,
              117,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
            ]),
          ],
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              37,
              34,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              101,
              95,
              98,
              117,
              110,
              100,
              108,
              101,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    getSimulationBundle: {
      name: "GetSimulationBundle",
      requestType: GetSimulationBundleRequest,
      requestStream: false,
      responseType: GetSimulationBundleResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          400002: [
            new Uint8Array([
              30,
              10,
              12,
              97,
              112,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
              18,
              14,
              119,
              101,
              98,
              117,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
            ]),
          ],
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              46,
              18,
              44,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              101,
              95,
              98,
              117,
              110,
              100,
              108,
              101,
              47,
              123,
              98,
              117,
              110,
              100,
              108,
              101,
              95,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
    getSimulations: {
      name: "GetSimulations",
      requestType: GetSimulationsRequest,
      requestStream: false,
      responseType: GetSimulationsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          400002: [
            new Uint8Array([
              30,
              10,
              12,
              97,
              112,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
              18,
              14,
              119,
              101,
              98,
              117,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
            ]),
          ],
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              27,
              18,
              25,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              101,
            ]),
          ],
        },
      },
    },
    getSimulation: {
      name: "GetSimulation",
      requestType: GetSimulationRequest,
      requestStream: false,
      responseType: GetSimulationResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          400002: [
            new Uint8Array([
              30,
              10,
              12,
              97,
              112,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
              18,
              14,
              119,
              101,
              98,
              117,
              105,
              95,
              100,
              101,
              98,
              117,
              103,
              103,
              101,
              114,
            ]),
          ],
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              43,
              18,
              41,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              101,
              47,
              123,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              105,
              111,
              110,
              95,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
    searchTransactions: {
      name: "SearchTransactions",
      requestType: EvmSearchTransactionsRequest,
      requestStream: false,
      responseType: EvmSearchTransactionsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              81,
              34,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              101,
              97,
              114,
              99,
              104,
              95,
              116,
              114,
              97,
              110,
              115,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
              58,
              1,
              42,
              90,
              38,
              18,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              101,
              97,
              114,
              99,
              104,
              95,
              116,
              114,
              97,
              110,
              115,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    syncContracts: {
      name: "SyncContracts",
      requestType: SyncContractsRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              36,
              34,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              121,
              110,
              99,
              95,
              99,
              111,
              110,
              116,
              114,
              97,
              99,
              116,
              115,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    universalSearch: {
      name: "UniversalSearch",
      requestType: UniversalSearchRequest,
      requestStream: false,
      responseType: UniversalSearchResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              35,
              18,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              117,
              110,
              105,
              118,
              101,
              114,
              115,
              97,
              108,
              95,
              115,
              101,
              97,
              114,
              99,
              104,
            ]),
          ],
        },
      },
    },
    getContractIndex: {
      name: "GetContractIndex",
      requestType: GetContractIndexRequest,
      requestStream: false,
      responseType: GetContractIndexResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              24,
              18,
              22,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              105,
              110,
              100,
              101,
              120,
            ]),
          ],
        },
      },
    },
    getDebugTrace: {
      name: "GetDebugTrace",
      requestType: GetDebugTraceRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              30,
              18,
              28,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              100,
              101,
              98,
              117,
              103,
              95,
              116,
              114,
              97,
              99,
              101,
            ]),
          ],
        },
      },
    },
    lookupSignature: {
      name: "LookupSignature",
      requestType: LookupSignatureRequest,
      requestStream: false,
      responseType: LookupSignatureResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              28,
              18,
              26,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              105,
              103,
              110,
              97,
              116,
              117,
              114,
              101,
            ]),
          ],
        },
      },
    },
    getABI: {
      name: "GetABI",
      requestType: GetABIRequest,
      requestStream: false,
      responseType: GetABIResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              22,
              18,
              20,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              97,
              98,
              105,
            ]),
          ],
        },
      },
    },
    getContractName: {
      name: "GetContractName",
      requestType: GetContractNameRequest,
      requestStream: false,
      responseType: GetContractNameResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              32,
              18,
              30,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              99,
              111,
              110,
              116,
              114,
              97,
              99,
              116,
              95,
              110,
              97,
              109,
              101,
            ]),
          ],
        },
      },
    },
    stateDiff: {
      name: "StateDiff",
      requestType: StateDiffRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              29,
              18,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              116,
              97,
              116,
              101,
              95,
              100,
              105,
              102,
              102,
            ]),
          ],
        },
      },
    },
    uploadUserCompilation: {
      name: "UploadUserCompilation",
      requestType: UploadUserCompilationRequest,
      requestStream: false,
      responseType: UploadUserCompilationResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              38,
              34,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    getUserCompilations: {
      name: "GetUserCompilations",
      requestType: GetUserCompilationsRequest,
      requestStream: false,
      responseType: GetUserCompilationsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              35,
              18,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
            ]),
          ],
        },
      },
    },
    getUserCompilation: {
      name: "GetUserCompilation",
      requestType: GetUserCompilationRequest,
      requestStream: false,
      responseType: GetUserCompilationResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              57,
              18,
              55,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              47,
              123,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              95,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
    updateUserCompilation: {
      name: "UpdateUserCompilation",
      requestType: UpdateUserCompilationRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              60,
              34,
              55,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              47,
              123,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              95,
              105,
              100,
              125,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    deleteUserCompilation: {
      name: "DeleteUserCompilation",
      requestType: DeleteUserCompilationRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              57,
              42,
              55,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              47,
              123,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              95,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
    getDeployedCode: {
      name: "GetDeployedCode",
      requestType: GetDeployedCodeRequest,
      requestStream: false,
      responseType: GetDeployedCodeResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              71,
              18,
              69,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              47,
              123,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
              95,
              105,
              100,
              125,
              47,
              100,
              101,
              112,
              108,
              111,
              121,
              101,
              100,
              95,
              99,
              111,
              100,
              101,
            ]),
          ],
        },
      },
    },
    verifyContract: {
      name: "VerifyContract",
      requestType: VerifyContractRequest,
      requestStream: false,
      responseType: VerifyContractResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              34,
              34,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              118,
              101,
              114,
              105,
              102,
              105,
              99,
              97,
              116,
              105,
              111,
              110,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    getVerifications: {
      name: "GetVerifications",
      requestType: GetVerificationsRequest,
      requestStream: false,
      responseType: GetVerificationsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              31,
              18,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              118,
              101,
              114,
              105,
              102,
              105,
              99,
              97,
              116,
              105,
              111,
              110,
            ]),
          ],
        },
      },
    },
    getVerificationByContract: {
      name: "GetVerificationByContract",
      requestType: GetVerificationByContractRequest,
      requestStream: false,
      responseType: GetVerificationByContractResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              43,
              18,
              41,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              118,
              101,
              114,
              105,
              102,
              105,
              99,
              97,
              116,
              105,
              111,
              110,
              95,
              98,
              121,
              95,
              99,
              111,
              110,
              116,
              114,
              97,
              99,
              116,
            ]),
          ],
        },
      },
    },
    deleteVerification: {
      name: "DeleteVerification",
      requestType: DeleteVerificationRequest,
      requestStream: false,
      responseType: Empty,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              49,
              42,
              47,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              118,
              101,
              114,
              105,
              102,
              105,
              99,
              97,
              116,
              105,
              111,
              110,
              47,
              123,
              118,
              101,
              114,
              105,
              102,
              105,
              99,
              97,
              116,
              105,
              111,
              110,
              95,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
    parseContracts: {
      name: "ParseContracts",
      requestType: ParseContractsRequest,
      requestStream: false,
      responseType: ParseContractsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              37,
              34,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              112,
              97,
              114,
              115,
              101,
              95,
              99,
              111,
              110,
              116,
              114,
              97,
              99,
              116,
              115,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    getTransactions: {
      name: "GetTransactions",
      requestType: GetTransactionsRequest,
      requestStream: false,
      responseType: GetTransactionsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              31,
              18,
              29,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              116,
              114,
              97,
              110,
              115,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    getMEVInfo: {
      name: "GetMEVInfo",
      requestType: GetMEVInfoRequest,
      requestStream: false,
      responseType: GetMEVInfoResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              27,
              18,
              25,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              109,
              101,
              118,
              95,
              105,
              110,
              102,
              111,
            ]),
          ],
        },
      },
    },
    batchGetMEVInfo: {
      name: "BatchGetMEVInfo",
      requestType: BatchGetMEVInfoRequest,
      requestStream: false,
      responseType: BatchGetMEVInfoResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              33,
              18,
              31,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              109,
              101,
              118,
              95,
              105,
              110,
              102,
              111,
              47,
              98,
              97,
              116,
              99,
              104,
            ]),
          ],
        },
      },
    },
    getStorageSummary: {
      name: "GetStorageSummary",
      requestType: GetStorageSummaryRequest,
      requestStream: false,
      responseType: GetStorageSummaryResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              34,
              18,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              116,
              111,
              114,
              97,
              103,
              101,
              95,
              115,
              117,
              109,
              109,
              97,
              114,
              121,
            ]),
          ],
        },
      },
    },
    dumpSimulation: {
      name: "DumpSimulation",
      requestType: DumpSimulationRequest,
      requestStream: false,
      responseType: DumpSimulationResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              34,
              18,
              32,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              100,
              117,
              109,
              112,
              95,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              105,
              111,
              110,
            ]),
          ],
        },
      },
    },
    dumpUserCompilation: {
      name: "DumpUserCompilation",
      requestType: DumpUserCompilationRequest,
      requestStream: false,
      responseType: DumpUserCompilationResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              40,
              18,
              38,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              100,
              117,
              109,
              112,
              95,
              117,
              115,
              101,
              114,
              95,
              99,
              111,
              109,
              112,
              105,
              108,
              97,
              116,
              105,
              111,
              110,
            ]),
          ],
        },
      },
    },
    simulateDeployment: {
      name: "SimulateDeployment",
      requestType: SimulateDeploymentRequest,
      requestStream: false,
      responseType: SimulateDeploymentResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              38,
              34,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              101,
              95,
              100,
              101,
              112,
              108,
              111,
              121,
              109,
              101,
              110,
              116,
            ]),
          ],
        },
      },
    },
    createShareSimulation: {
      name: "CreateShareSimulation",
      requestType: CreateShareSimulationRequest,
      requestStream: false,
      responseType: CreateShareSimulationResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              38,
              34,
              33,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              104,
              97,
              114,
              101,
              95,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              105,
              111,
              110,
              58,
              1,
              42,
            ]),
          ],
        },
      },
    },
    getSharedSimulation: {
      name: "GetSharedSimulation",
      requestType: GetShareSimulationRequest,
      requestStream: false,
      responseType: GetShareSimulationResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              40,
              18,
              38,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              115,
              104,
              97,
              114,
              101,
              95,
              115,
              105,
              109,
              117,
              108,
              97,
              116,
              105,
              111,
              110,
              47,
              123,
              105,
              100,
              125,
            ]),
          ],
        },
      },
    },
    downloadContract: {
      name: "DownloadContract",
      requestType: DownloadContractRequest,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              36,
              18,
              34,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              100,
              111,
              119,
              110,
              108,
              111,
              97,
              100,
              95,
              99,
              111,
              110,
              116,
              114,
              97,
              99,
              116,
            ]),
          ],
        },
      },
    },
    getForkState: {
      name: "GetForkState",
      requestType: GetForkStateRequestDeprecated,
      requestStream: false,
      responseType: HttpBody,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              29,
              18,
              27,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              102,
              111,
              114,
              107,
              95,
              115,
              116,
              97,
              116,
              101,
            ]),
          ],
        },
      },
    },
    getRecentTransactions: {
      name: "GetRecentTransactions",
      requestType: GetRecentTransactionsRequest,
      requestStream: false,
      responseType: GetRecentTransactionsResponse,
      responseStream: false,
      options: {
        _unknownFields: {
          578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])],
          578365826: [
            new Uint8Array([
              38,
              18,
              36,
              47,
              97,
              112,
              105,
              47,
              118,
              49,
              47,
              115,
              111,
              108,
              105,
              100,
              105,
              116,
              121,
              47,
              114,
              101,
              99,
              101,
              110,
              116,
              95,
              116,
              114,
              97,
              110,
              115,
              97,
              99,
              116,
              105,
              111,
              110,
              115,
            ]),
          ],
        },
      },
    },
    getEstimatedGasPrice: {
      name: "GetEstimatedGasPrice",
      requestType: GetEstimatedGasPriceRequest,
      requestStream: false,
      responseType: GetEstimatedGasPriceResponse,
      responseStream: false,
      options: { _unknownFields: { 578365818: [new Uint8Array([10, 18, 8, 73, 78, 84, 69, 82, 78, 65, 76])] } },
    },
  },
} as const;

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

function base64FromBytes(arr: Uint8Array): string {
  if ((globalThis as any).Buffer) {
    return globalThis.Buffer.from(arr).toString("base64");
  } else {
    const bin: string[] = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(""));
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | bigint | undefined;

type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

function toTimestamp(date: Date): Timestamp {
  const seconds = BigInt(Math.trunc(date.getTime() / 1_000));
  const nanos = (date.getTime() % 1_000) * 1_000_000;
  return { seconds, nanos };
}

function fromTimestamp(t: Timestamp): Date {
  let millis = (globalThis.Number(t.seconds.toString()) || 0) * 1_000;
  millis += (t.nanos || 0) / 1_000_000;
  return new globalThis.Date(millis);
}

function fromJsonTimestamp(o: any): Date {
  if (o instanceof globalThis.Date) {
    return o;
  } else if (typeof o === "string") {
    return new globalThis.Date(o);
  } else {
    return fromTimestamp(Timestamp.fromJSON(o));
  }
}

function longToBigint(long: Long) {
  return BigInt(long.toString());
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isObject(value: any): boolean {
  return typeof value === "object" && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
