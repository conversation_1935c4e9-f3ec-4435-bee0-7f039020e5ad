// Customized sentio trace results

export interface SentioLogTrace {
  address: string
  codeAddress?: string
  topics: string[]
  data: string

  type: string
  pc: number
  index: number
  startIndex: number
  endIndex: number
  gas: string
  gasUsed: string
  error?: string
}

export interface SentioCallTrace {
  type: string
  from: string
  to?: string
  value?: string
  gas: string
  gasUsed: string
  input?: string
  output?: string
  error?: string
  revertReason?: string
  codeAddress?: string

  pc: number
  functionPc: number
  startIndex: number
  endIndex: number

  inputStack?: string[]
  inputMemory?: string[]
  outputStack?: string[]
  outputMemory?: string[]
  traces: (SentioCallTrace | SentioLogTrace)[]
}

export interface SentioExternalCallTrace extends SentioCallTrace {
  to: string
  value: string
  input: string
  output: string
}

export interface TraceReceipt {
  nonce: number
  transactionHash: string
  blockNumber: string
}

export interface SentioRootCallTrace extends SentioExternalCallTrace {
  receipt?: TraceReceipt
  refund?: string
}
