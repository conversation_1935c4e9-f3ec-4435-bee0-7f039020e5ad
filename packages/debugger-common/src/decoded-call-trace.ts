import { AbiDescriptor_AbiDescriptorItem, DecodedLog, Location, LocationWithInstructionIndex } from './types'

// Decoded Results
export interface DecodedCall {
  functionName?: string
  inputs?: any
  returnValue?: any
}

export interface DecodedError {
  name?: string
  inputs?: any
}

export interface DecodedDynamicLog {
  functionName: string
  inputs?: any
  instructionIndex?: number
  location?: LocationWithInstructionIndex
}

export interface FormattedDynamicLog {
  message: string
  instructionIndex?: number
  location?: LocationWithInstructionIndex
}

export interface DecodedCallTrace extends DecodedCall {
  depth: number
  type: string
  calls: DecodedCallTrace[]
  contractName?: string
  address?: string
  fromContractName?: string
  toContractName?: string
  from?: string
  to?: string
  pc?: number
  value?: string
  rawInput?: string
  rawOutput?: string
  logs?: DecodedLog[]
  location?: LocationWithInstructionIndex
  defLocation?: Location
  startIndex: number
  endIndex: number
  error?: string
  decodedError?: DecodedError
  gas?: string
  gasUsed?: string
  revertReason?: string
  logsAndInternalCalls?: any[]
  // will be deleted at last
  abiDescriptor?: AbiDescriptor_AbiDescriptorItem
  gasCost?: string
  astNode?: any
  returnPC?: number
  dynamicLogs?: DecodedDynamicLog[]
  refund?: string
}

export interface DecodedExternalCallTrace extends DecodedCallTrace {
  from: string
  to: string
}
