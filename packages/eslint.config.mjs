import { defineConfig, globalIgnores } from "eslint/config";
import { fixupConfigRules, fixupPluginRules } from "@eslint/compat";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import unusedImports from "eslint-plugin-unused-imports";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default defineConfig([
    globalIgnores(["**/dist", "**/gen", "**/.*", "**/*.test.ts", "**/node_modules"]),
    {
        extends: fixupConfigRules(compat.extends(
            "plugin:@typescript-eslint/recommended",
            "plugin:import/recommended",
            "plugin:import/typescript",
            "prettier",
        )),

        plugins: {
            "@typescript-eslint": fixupPluginRules(typescriptEslint),
            "unused-imports": unusedImports,
        },

        rules: {
            "@typescript-eslint/no-inferrable-types": ["off"],
            "@typescript-eslint/no-empty-function": ["off"],
            "@typescript-eslint/no-unused-vars": ["off"],
            "@typescript-eslint/no-this-alias": ["off"],
            "@typescript-eslint/no-non-null-assertion": ["off"],
            "@typescript-eslint/no-explicit-any": ["off"],

            "@typescript-eslint/ban-ts-comment": ["error", {
                "ts-ignore": "allow-with-description",
                "ts-expect-error": "allow-with-description",
            }],

            "import/no-extraneous-dependencies": ["off"],
            "import/no-named-as-default": ["off"],
            "import/no-unresolved": ["off"],
            "unused-imports/no-unused-imports": "error",
        },
    },
]);