load("@aspect_rules_js//js:defs.bzl", "js_test")
load("@aspect_rules_js//npm:defs.bzl", "npm_package")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config", "ts_project")
load("@npm//:defs.bzl", "npm_link_all_packages")

npm_link_all_packages(name = "node_modules")

ts_project(
    name = "chain-ts",
    srcs = glob(["src/**"]),
    declaration = True,
    declaration_map = True,
    out_dir = "dist",
    resolve_json_module = True,
    source_map = True,
    tsconfig = ":tsconfig",
    deps = [
        ":node_modules",
    ],
)

ts_config(
    name = "tsconfig",
    src = ":tsconfig.json",
    deps = [
        "//packages:common_tsconfig",
    ],
)

js_test(
    name = "test",
    data = [":chain-ts"],
    entry_point = "dist/src/chain.test.js",
)

# make this library available via node_modules
npm_package(
    name = "pkg",
    srcs = [
        "package.json",
        ":chain-ts",
    ],
    include_runfiles = False,
    replace_prefixes = {
        "dist/src": "dist",
    },
    visibility = ["//visibility:public"],
)
