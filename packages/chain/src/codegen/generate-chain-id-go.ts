import {
  AptosChainId,
  BTCChainId,
  ChainId,
  ChainType,
  ChainIdToType,
  CosmosChainId,
  EthChainId,
  FuelChainId,
  SolanaChainId,
  StarknetChainId,
  SuiChainId
} from '../chain-id'
import { writeFileSync } from 'node:fs'
import { getGoVariableName } from './generate-go'
import * as console from 'node:console'

const chainTypeToChainIdString: Record<ChainType, string> = {
  [ChainType.SOLANA]: 'SolanaChainId',
  [ChainType.SUI]: 'SuiChainId',
  [ChainType.APTOS]: 'AptosChainId',
  [ChainType.ETH]: 'EthChainId',
  [ChainType.BTC]: 'BTCChainId',
  [ChainType.COSMOS]: 'CosmosChainId',
  [ChainType.STARKNET]: 'StarknetChainId',
  [ChainType.FUEL]: 'FuelChainId'
}

const chainTypeToChainId: Record<ChainType, object> = {
  [ChainType.SOLANA]: SolanaChainId,
  [ChainType.SUI]: SuiChainId,
  [ChainType.APTOS]: AptosChainId,
  [ChainType.ETH]: EthChainId,
  [ChainType.BTC]: BTCChainId,
  [ChainType.COSMOS]: CosmosChainId,
  [ChainType.STARKNET]: StarknetChainId,
  [ChainType.FUEL]: FuelChainId
}

const chainTypeToTypeName = new Map<string, string>()
for (const [key, value] of Object.entries(ChainType)) {
  chainTypeToTypeName.set(value, key)
}

const chainIdToChainKey = new Map<string, string>()
for (const [key, value] of Object.entries(ChainId)) {
  chainIdToChainKey.set(value, key)
}

let generated = `// This code is generated by packages/chain/src/codegen/generate-chain-id-go.ts, please do not modify directly
package chains
`

generated += `
type ChainType string
const (
  ${Object.entries(ChainType)
    .map(([key, value]) => `${getGoVariableName(key)}ChainType ChainType = "${value}"`)
    .join('\n\t')}
)
`

function generateChainId(chainIdString: string, chainId: object) {
  return `
type ${chainIdString} string
const (
  ${Object.entries(chainId)
    .map(
      ([chainIdKey, chainIdValue]: [string, string]) =>
        `${getGoVariableName(chainIdKey)} ${chainIdString} = "${chainIdValue}"`
    )
    .join('\n  ')}
)
var ${chainIdString}s = []${chainIdString}{${Object.keys(chainId).map(getGoVariableName).join(', ')}}
`
}

for (const [chainType, chainId] of Object.entries(chainTypeToChainId)) {
  const chainIdString = getGoVariableName(chainTypeToChainIdString[chainType as ChainType])
  generated += generateChainId(chainIdString, chainId)
}

generated += `
type ChainID string
var ChainIDs = []ChainID{${Object.keys(ChainId)
  .map((chainId) => `ChainID(${getGoVariableName(chainId)})`)
  .join(', ')}}
`

generated += `
var ChainIDToInfo = map[ChainID]ChainType{
  ${[...ChainIdToType.entries()]
    .map(
      ([chainId, chainType]) =>
        `ChainID(${getGoVariableName(chainIdToChainKey.get(chainId)!)}): ${getGoVariableName(chainTypeToTypeName.get(chainType)!)}ChainType,`
    )
    .join('\n\t')}
}
`

console.log(generated)

writeFileSync('../../common/chains/chain_id.go', generated)
