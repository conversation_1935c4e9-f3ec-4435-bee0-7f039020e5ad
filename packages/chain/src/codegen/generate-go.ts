import { EthChainInfo, EthVariation, ExplorerApiType } from '../chain-info'
import { EthChainId } from '../chain-id'
import { writeFileSync } from 'node:fs'
import { camelCase, upperFirst } from 'lodash'
import * as process from 'node:process'

const EXPLORER_API_TYPE_TO_KEY = new Map<ExplorerApiType, string>()
for (const [key, value] of Object.entries(ExplorerApiType)) {
  EXPLORER_API_TYPE_TO_KEY.set(value as ExplorerApiType, key)
}

function getGoExplorerAPIType(apiType?: ExplorerApiType) {
  if (!apiType) {
    return 'ExplorerAPITypeUnknown'
  }
  return `ExplorerAPIType${upperFirst(camelCase(EXPLORER_API_TYPE_TO_KEY.get(apiType) || ''))}`
}

export function getGoVariableName(name: string) {
  return upperFirst(camelCase(name)).replace('Id', 'ID').replace('Btc', 'BTC')
}

const VARIATION_VALUE_TO_KEY = new Map<EthVariation, string>()
for (const [key, value] of Object.entries(EthVariation)) {
  VARIATION_VALUE_TO_KEY.set(value as EthVariation, key)
}

function getGoVariationName(variation: EthVariation) {
  if (variation === EthVariation.POLYGON_ZKEVM) {
    return 'EthVariationPolygonZkEVM'
  }
  if (variation === EthVariation.ZKSYNC) {
    return 'EthVariationZkSync'
  }
  const key = VARIATION_VALUE_TO_KEY.get(variation)
  if (!key) {
    console.log(key)
    process.exit(1)
  }
  return 'EthVariation' + upperFirst(camelCase(VARIATION_VALUE_TO_KEY.get(variation)))
}

let generated = `// This code is generated by packages/chain/src/codegen/generate-go.ts, please do not modify directly
package chaininfo

import "github.com/ethereum/go-ethereum/common"

func init() {
	for _, chain := range Chains {
		ChainIDToInfo[chain.ChainID] = chain
		SlugToInfo[chain.Slug] = chain
	}
}

var ChainIDToInfo = map[string]*EthChainInfo{}
var SlugToInfo = map[string]*EthChainInfo{}
`
generated += `
var Chains = []*EthChainInfo{${Object.keys(EthChainId)
  .map((k) => `&${getGoVariableName(k)}`)
  .join(', ')}}
`

for (const [chainIdKey, chainId] of Object.entries(EthChainId)) {
  const chainInfo = EthChainInfo[chainId]
  generated += `
var ${getGoVariableName(chainIdKey)} = EthChainInfo{
  Name: "${chainInfo.name}",
  Slug: "${chainInfo.slug}",
  AdditionalSlugs: []string{${chainInfo.additionalSlugs ? chainInfo.additionalSlugs.map((s) => `"${s}"`).join(', ') : ''}},
  ChainID: "${chainInfo.chainId}",
  MainnetChainID: "${chainInfo.mainnetChainId || ''}",
  Variation: ${getGoVariationName(chainInfo.variation)},
  TokenSymbol: "${chainInfo.tokenSymbol}",
  TokenDecimals: ${chainInfo.tokenDecimals},
  TokenAddress: common.HexToAddress("${chainInfo.tokenAddress}"),
  PriceTokenAddress: common.HexToAddress("${chainInfo.priceTokenAddress}"),
  WrappedTokenAddress: common.HexToAddress("${chainInfo.wrappedTokenAddress}"),
  ExplorerURL: "${chainInfo.explorerUrl}",
  ExplorerAPI: "${chainInfo.explorerApi || ''}",
  ExplorerAPIType: ${getGoExplorerAPIType(chainInfo.explorerApiType)},
}
`
}

console.log(generated)

writeFileSync('../../chain/evm/chaininfo/chains.go', generated)
