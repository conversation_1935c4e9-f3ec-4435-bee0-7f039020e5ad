import {
  AptosChainId,
  BTCChainId,
  ChainId,
  CosmosChainId,
  EthChainId,
  FuelChainId,
  SolanaChainId,
  StarknetChainId,
  SuiChainId
} from './chain-id'

export enum ExplorerApiType {
  ETHERSCAN = 'etherscan',
  ETHERSCAN_V2 = 'etherscan_v2',
  BLOCKSCOUT = 'blockscout',
  L2_SCAN = 'l2scan',
  OK_LINK = 'oklink',
  UNKNOWN = 'unknown'
}

export type ChainInfo = {
  name: string
  slug: string
  additionalSlugs?: string[]
  chainId: ChainId
  explorerUrl: string
  lightIcon?: string // icon used in light mode, default icon
  darkIcon?: string // icon used in dark mode
}

export enum EthVariation {
  DEFAULT = 0,
  ARBITRUM = 1,
  OPTIMISM = 2,
  ZKSYNC = 3,
  POLYGON_ZKEVM = 4,
  SUBSTRATE
}

export type EthChainInfo = ChainInfo & {
  mainnetChainId?: EthChainId // if it is a testnet, this is the mainnet chain id
  chainId: EthChainId
  variation: EthVariation

  tokenAddress: string // native token address
  tokenSymbol: string // native token symbol
  tokenDecimals: number // native token decimals

  priceTokenAddress: string // token address for price
  wrappedTokenAddress: string // wrapped token address with contract, normally Wxxx (Wrapped xxx)

  explorerApiType?: ExplorerApiType
  explorerApi?: string
  blockscoutUrl?: string
}

/**
 * EVM chains
 */
export const EthChainInfo: Record<EthChainId | string, EthChainInfo> = {
  [EthChainId.ETHEREUM]: {
    name: 'Ethereum',
    slug: 'mainnet',
    additionalSlugs: ['ethereum'],
    chainId: EthChainId.ETHEREUM,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://etherscan.io',
    explorerApi: 'https://api.etherscan.io/v2',
    blockscoutUrl: 'https://eth.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/eth.svg',
    darkIcon: 'https://sentio.xyz/chains/eth-dark.svg'
  },
  [EthChainId.SEPOLIA]: {
    name: 'Sepolia',
    slug: 'sepolia',
    chainId: EthChainId.SEPOLIA,
    mainnetChainId: EthChainId.ETHEREUM,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://sepolia.etherscan.io',
    explorerApi: 'https://api.etherscan.io/v2',
    blockscoutUrl: 'https://eth-sepolia.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/eth.svg',
    darkIcon: 'https://sentio.xyz/chains/eth-dark.svg'
  },
  [EthChainId.HOLESKY]: {
    name: 'Holesky',
    slug: 'holesky',
    chainId: EthChainId.HOLESKY,
    mainnetChainId: EthChainId.ETHEREUM,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://holesky.etherscan.io',
    explorerApi: 'https://api.etherscan.io/v2',
    blockscoutUrl: 'https://eth-holesky.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/eth.svg',
    darkIcon: 'https://sentio.xyz/chains/eth-dark.svg'
  },
  [EthChainId.HOODI]: {
    name: 'Hoodi',
    slug: 'hoodi',
    chainId: EthChainId.HOODI,
    mainnetChainId: EthChainId.ETHEREUM,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://hoodi.etherscan.io',
    explorerApi: 'https://api.etherscan.io/v2',
    blockscoutUrl: 'https://light-hoodi.beaconcha.in',
    lightIcon: 'https://sentio.xyz/chains/eth.svg',
    darkIcon: 'https://sentio.xyz/chains/eth-dark.svg'
  },
  [EthChainId.BSC]: {
    name: 'Binance Smart Chain',
    slug: 'bsc',
    chainId: EthChainId.BSC,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BNB',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://bscscan.com',
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/bnb-chain.svg'
  },
  [EthChainId.BSC_TESTNET]: {
    name: 'Binance Smart Chain Testnet',
    slug: 'bsc-testnet',
    chainId: EthChainId.BSC_TESTNET,
    mainnetChainId: EthChainId.BSC,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'tBNB',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://testnet.bscscan.com',
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/bnb-chain.svg'
  },
  [EthChainId.OP_BNB_MAINNET]: {
    name: 'opBNB Mainnet',
    slug: 'opbnb',
    chainId: EthChainId.OP_BNB_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BNB',
    tokenDecimals: 18,
    explorerUrl: 'https://opbnb.bscscan.com',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/bnb-chain.svg'
  },
  [EthChainId.POLYGON]: {
    name: 'Polygon',
    slug: 'matic',
    additionalSlugs: ['polygon'],
    chainId: EthChainId.POLYGON,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WMATIC
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************', // WMATIC
    tokenSymbol: 'MATIC',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://polygonscan.com',
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/polygon.svg'
    // blockscoutBlockPrefix: 'https://polygon.blockscout.com/block/',
  },
  [EthChainId.ARBITRUM]: {
    name: 'Arbitrum',
    slug: 'arbitrum-one',
    additionalSlugs: ['arbitrum'],
    chainId: EthChainId.ARBITRUM,
    variation: EthVariation.ARBITRUM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://arbiscan.io',
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/arbitrum.svg'
  },
  [EthChainId.AVALANCHE]: {
    name: 'Avalanche',
    slug: 'avalanche',
    chainId: EthChainId.AVALANCHE,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerUrl: 'https://snowtrace.io',
    explorerApi: 'https://api.routescan.io/v2/network/mainnet/evm/43114/etherscan',
    lightIcon: 'https://sentio.xyz/chains/avalanche.svg'
  },
  [EthChainId.POLYGON_ZKEVM]: {
    name: 'Polygon zkEVM',
    chainId: EthChainId.POLYGON_ZKEVM,
    slug: 'polygon-zkevm',
    variation: EthVariation.POLYGON_ZKEVM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerUrl: 'https://polygon.blockscout.com',
    explorerApi: 'https://polygon.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/polygon.svg'
  },
  [EthChainId.MOONBEAM]: {
    name: 'Moonbeam',
    slug: 'moonbeam',
    chainId: EthChainId.MOONBEAM,
    variation: EthVariation.SUBSTRATE,
    priceTokenAddress: '******************************************', // WGLMR
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'GLMR',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerUrl: 'https://moonscan.io',
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/moonbeam.svg'
  },
  [EthChainId.ASTAR]: {
    name: 'Astar',
    slug: 'astar',
    chainId: EthChainId.ASTAR,
    variation: EthVariation.SUBSTRATE,
    priceTokenAddress: '******************************************', // WASTR
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ASTR',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerUrl: 'https://astar.blockscout.com',
    explorerApi: 'https://astar.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/astar.svg'
  },
  [EthChainId.LINEA]: {
    name: 'Linea',
    slug: 'linea',
    chainId: EthChainId.LINEA,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WETH
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://lineascan.build',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/linea.svg',
    darkIcon: 'https://sentio.xyz/chains/linea-dark.svg'
  },
  [EthChainId.SCROLL]: {
    name: 'Scroll',
    slug: 'scroll',
    chainId: EthChainId.SCROLL,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************', // TODO questionable
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://scrollscan.com',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/scroll.svg'
  },
  [EthChainId.TAIKO]: {
    name: 'Taiko Mainnet',
    slug: 'taiko',
    chainId: EthChainId.TAIKO,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://taikoscan.io',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/taiko.svg'
  },
  // [EthChainId.TAIKO_TESTNET]: {
  //   name: 'Taiko Testnet',
  //   slug: 'taiko-hekla-testnet',
  //   chainId: EthChainId.TAIKO_TESTNET,
  //   mainnetChainId: EthChainId.TAIKO,
  //   variation: EthVariation.DEFAULT,
  //   priceTokenAddress: '******************************************',
  //   tokenAddress: '******************************************',
  //   wrappedTokenAddress: '******************************************',
  //   tokenSymbol: 'ETH',
  //   tokenDecimals: 18,
  //   explorerUrl: 'https://hekla.taikoscan.io',
  //   explorerApiType: ExplorerApiType.ETHERSCAN,
  //   explorerApi: 'https://api.etherscan.io/v2',
  //   lightIcon: 'https://sentio.xyz/chains/taiko.svg'
  // },
  [EthChainId.XLAYER_TESTNET]: {
    name: 'X Layer Testnet',
    slug: 'xlayer-sepolia',
    chainId: EthChainId.XLAYER_TESTNET,
    mainnetChainId: EthChainId.XLAYER_MAINNET,
    variation: EthVariation.POLYGON_ZKEVM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://www.oklink.com/xlayer-test',
    explorerApiType: ExplorerApiType.OK_LINK,
    explorerApi: 'https://www.oklink.com/api/v5/explorer',
    lightIcon: 'https://sentio.xyz/chains/x1-logo.png'
  },
  [EthChainId.CORE_MAINNET]: {
    name: 'Core',
    slug: 'core-mainnet',
    chainId: EthChainId.CORE_MAINNET,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'CORE',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.UNKNOWN,
    explorerUrl: 'https://scan.coredao.org',
    // explorerApi: 'https://openapi.coredao.org',
    lightIcon: 'https://sentio.xyz/chains/core.svg'
  },
  [EthChainId.XLAYER_MAINNET]: {
    name: 'X Layer Mainnet',
    slug: 'xlayer-mainnet',
    chainId: EthChainId.XLAYER_MAINNET,
    variation: EthVariation.POLYGON_ZKEVM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://www.oklink.com/xlayer',
    explorerApiType: ExplorerApiType.OK_LINK,
    explorerApi: 'https://www.oklink.com/api/v5/explorer',
    lightIcon: 'https://sentio.xyz/chains/x1-logo.png'
  },
  [EthChainId.BLAST]: {
    name: 'Blast Mainnet',
    slug: 'blast-mainnet',
    chainId: EthChainId.BLAST,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://blastscan.io',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/blast-logo.png'
  },
  [EthChainId.BLAST_SEPOLIA]: {
    name: 'Blast Testnet',
    slug: 'blast-testnet',
    chainId: EthChainId.BLAST_SEPOLIA,
    mainnetChainId: EthChainId.BLAST,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://sepolia.blastscan.io',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/blast-logo.png'
  },
  [EthChainId.BASE]: {
    name: 'Base',
    slug: 'base',
    chainId: EthChainId.BASE,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://basescan.org',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/base.svg',
    darkIcon: 'https://sentio.xyz/chains/base_blue.svg'
  },
  [EthChainId.ZKSYNC_ERA]: {
    name: 'zkSync Era',
    slug: 'zksync-era',
    chainId: EthChainId.ZKSYNC_ERA,
    variation: EthVariation.ZKSYNC,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.zksync.io',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://block-explorer-api.mainnet.zksync.io',
    lightIcon: 'https://sentio.xyz/chains/zksync.svg'
  },
  [EthChainId.ZIRCUIT_TESTNET]: {
    name: 'Zircuit Testnet',
    slug: 'zircuit-testnet',
    chainId: EthChainId.ZIRCUIT_TESTNET,
    mainnetChainId: EthChainId.ZIRCUIT_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.testnet.zircuit.com',
    lightIcon: 'https://sentio.xyz/chains/zircuit-inverted-icon.svg',
    darkIcon: 'https://sentio.xyz/chains/zircuit-green-icon.svg'
  },
  [EthChainId.ZIRCUIT_MAINNET]: {
    name: 'Zircuit Mainnet',
    slug: 'zircuit',
    chainId: EthChainId.ZIRCUIT_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.zircuit.com',
    lightIcon: 'https://sentio.xyz/chains/zircuit-inverted-icon.svg',
    darkIcon: 'https://sentio.xyz/chains/zircuit-green-icon.svg'
  },
  // [EthChainId.FANTOM]: {
  //   name: 'Fantom Opera',
  //   slug: 'fantom',
  //   chainId: EthChainId.FANTOM,
  //   variation: EthVariation.DEFAULT,
  //   priceTokenAddress: '******************************************', // WFTM
  //   tokenAddress: '******************************************',
  //   wrappedTokenAddress: '******************************************',
  //   tokenSymbol: 'WFTM',
  //   tokenDecimals: 18,
  //   explorerUrl: 'https://ftmscan.com',
  //   explorerApiType: ExplorerApiType.ETHERSCAN,
  //   explorerApi: 'https://api.ftmscan.com',
  //   lightIcon: 'https://sentio.xyz/chains/fantom.svg'
  // },
  [EthChainId.OPTIMISM]: {
    name: 'Optimism Mainnet',
    slug: 'optimism',
    chainId: EthChainId.OPTIMISM,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://optimistic.etherscan.io',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/optimism.svg'
  },
  [EthChainId.CRONOS]: {
    name: 'Cronos Mainnet',
    slug: 'cronos',
    chainId: EthChainId.CRONOS,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WCRO
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'CRO',
    tokenDecimals: 18,
    explorerUrl: 'https://cronoscan.com',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/cronos.svg',
    darkIcon: 'https://sentio.xyz/chains/cronos_light.svg'
  },
  [EthChainId.CRONOS_TESTNET]: {
    name: 'Cronos Testnet',
    slug: 'cronos-testnet',
    chainId: EthChainId.CRONOS_TESTNET,
    mainnetChainId: EthChainId.CRONOS,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WCRO
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'CRO',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.cronos.org/testnet',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/cronos.svg',
    darkIcon: 'https://sentio.xyz/chains/cronos_light.svg'
  },
  [EthChainId.BITLAYER]: {
    name: 'Bitlayer Mainnet',
    slug: 'bitlayer',
    chainId: EthChainId.BITLAYER,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WBTC
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BTC',
    tokenDecimals: 18,
    explorerUrl: 'https://www.btrscan.com',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://api.btrscan.com/scan',
    lightIcon: 'https://sentio.xyz/chains/bitlayer.svg'
  },
  [EthChainId.MANTA_PACIFIC]: {
    name: 'Manta Pacific',
    slug: 'manta-pacific-mainnet',
    chainId: EthChainId.MANTA_PACIFIC,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://pacific-explorer.manta.network',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://pacific-explorer.manta.network',
    lightIcon: 'https://sentio.xyz/chains/manta.png'
  },
  [EthChainId.MANTLE]: {
    name: 'Mantle',
    slug: 'mantle',
    chainId: EthChainId.MANTLE,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************', // WMNT
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'MNT',
    tokenDecimals: 18,
    explorerUrl: 'https://mantlescan.xyz',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/mantle.svg',
    darkIcon: 'https://sentio.xyz/chains/mantle-white.svg'
  },
  [EthChainId.B2_MAINNET]: {
    name: 'B2 Mainnet',
    slug: 'b2-mainnet',
    chainId: EthChainId.B2_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************', // WBTC
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BTC',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.bsquared.network',
    explorerApiType: ExplorerApiType.L2_SCAN,
    explorerApi: 'https://explorer.bsquared.network/api',
    lightIcon: 'https://sentio.xyz/chains/b2.svg'
  },
  [EthChainId.MODE]: {
    name: 'Mode Mainnet',
    slug: 'mode-mainnet',
    chainId: EthChainId.MODE,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://modescan.io',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://api.routescan.io/v2/network/mainnet/evm/34443/etherscan',
    lightIcon: 'https://sentio.xyz/chains/mode.svg',
    darkIcon: 'https://sentio.xyz/chains/mode-dark.svg'
  },
  [EthChainId.BOB]: {
    name: 'Bob Mainnet',
    slug: 'bob',
    chainId: EthChainId.BOB,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.gobob.xyz',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://explorer.gobob.xyz',
    lightIcon: 'https://sentio.xyz/chains/bob.svg'
  },
  [EthChainId.FRAXTAL]: {
    name: 'Fraxtal Mainnet',
    slug: 'frax-mainnet',
    chainId: EthChainId.FRAXTAL,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************', // wfrxETH
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://fraxscan.com',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/fraxtal.svg'
  },
  [EthChainId.KUCOIN]: {
    name: 'KCC Mainnet',
    slug: 'kucoin',
    chainId: EthChainId.KUCOIN,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WCCS
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'KCS',
    tokenDecimals: 18,
    explorerUrl: 'https://scan.kcc.io',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://scan.kcc.io',
    lightIcon: 'https://sentio.xyz/chains/kcc.svg'
  },
  [EthChainId.CONFLUX]: {
    name: 'Conflux eSpace',
    slug: 'conflux-espace',
    chainId: EthChainId.CONFLUX,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WCFX
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'CFX',
    tokenDecimals: 18,
    explorerUrl: 'https://evm.confluxscan.io',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://evmapi.confluxscan.io',
    lightIcon: 'https://sentio.xyz/chains/conflux.svg',
    darkIcon: 'https://sentio.xyz/chains/conflux-white.svg'
  },
  [EthChainId.METIS]: {
    name: 'Metis',
    slug: 'metis',
    chainId: EthChainId.METIS,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************', // WMETIS
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'METIS',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.metis.io',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://api.routescan.io/v2/network/mainnet/evm/1088/etherscan/',
    lightIcon: 'https://sentio.xyz/chains/metis.svg'
  },
  [EthChainId.BEVM]: {
    name: 'BEVM',
    slug: 'bevm',
    chainId: EthChainId.BEVM,
    variation: EthVariation.SUBSTRATE,
    priceTokenAddress: '******************************************', // WBTC
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BTC',
    tokenDecimals: 18,
    explorerUrl: 'https://scan.bevm.io',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://scan.bevm.io',
    lightIcon: 'https://sentio.xyz/chains/bevm.svg'
  },
  [EthChainId.MERLIN_MAINNET]: {
    name: 'Merlin Mainnet',
    slug: 'merlin',
    chainId: EthChainId.MERLIN_MAINNET,
    variation: EthVariation.POLYGON_ZKEVM,
    priceTokenAddress: '******************************************', // WBTC
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BTC',
    tokenDecimals: 18,
    explorerUrl: 'https://scan.merlinchain.io',
    explorerApiType: ExplorerApiType.L2_SCAN,
    explorerApi: 'https://scan.merlinchain.io/api',
    lightIcon: 'https://sentio.xyz/chains/merlin.png'
  },
  [EthChainId.CHILIZ]: {
    name: 'Chiliz',
    slug: 'chiliz',
    chainId: EthChainId.CHILIZ,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************', // WCHZ
    tokenSymbol: 'CHZ',
    tokenDecimals: 18,
    explorerUrl: 'https://chiliscan.com',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://api.routescan.io/v2/network/mainnet/evm/88888/etherscan',
    lightIcon: 'https://sentio.xyz/chains/chiliz.svg'
  },
  [EthChainId.ZKLINK_NOVA]: {
    name: 'zkLink Nova',
    slug: 'zklink-nova',
    chainId: EthChainId.ZKLINK_NOVA,
    variation: EthVariation.ZKSYNC,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************', //special
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.zklink.io',
    explorerApiType: ExplorerApiType.ETHERSCAN,
    explorerApi: 'https://explorer-api.zklink.io',
    lightIcon: 'https://sentio.xyz/chains/zklink.svg'
  },
  [EthChainId.AURORA]: {
    name: 'Aurora',
    slug: 'aurora',
    chainId: EthChainId.AURORA,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.aurora.dev',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://explorer.aurora.dev',
    lightIcon: 'https://sentio.xyz/chains/aurora.svg'
  },
  [EthChainId.SONIC_MAINNET]: {
    name: 'Sonic Mainnet',
    slug: 'sonic-mainnet',
    chainId: EthChainId.SONIC_MAINNET,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'S',
    tokenDecimals: 18,
    explorerUrl: 'https://sonicscan.org',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/sonic.svg',
    darkIcon: 'https://sentio.xyz/chains/sonic-dark.svg'
  },
  [EthChainId.SONIC_TESTNET]: {
    name: 'Sonic Testnet',
    slug: 'sonic-testnet',
    chainId: EthChainId.SONIC_TESTNET,
    mainnetChainId: EthChainId.SONIC_MAINNET,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'S',
    tokenDecimals: 18,
    explorerUrl: 'https://testnet.sonicscan.org',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    explorerApi: 'https://api.etherscan.io/v2',
    lightIcon: 'https://sentio.xyz/chains/sonic.svg',
    darkIcon: 'https://sentio.xyz/chains/sonic-dark.svg'
  },
  [EthChainId.SONEIUM_MAINNET]: {
    name: 'Soneium Mainnet',
    slug: 'soneium-mainnet',
    chainId: EthChainId.SONEIUM_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://soneium.blockscout.com',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://soneium.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/soneium.svg'
  },
  [EthChainId.SONEIUM_TESTNET]: {
    name: 'Soneium Testnet',
    slug: 'soneium-minato',
    chainId: EthChainId.SONEIUM_TESTNET,
    mainnetChainId: EthChainId.SONEIUM_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://soneium-minato.blockscout.com',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://soneium-minato.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/soneium.svg'
  },
  [EthChainId.CRONOS_ZKEVM]: {
    name: 'Cronos zkEVM',
    slug: 'cronos-zkevm',
    chainId: EthChainId.CRONOS_ZKEVM,
    variation: EthVariation.ZKSYNC,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'zkCRO',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.zkevm.cronos.org',
    explorerApiType: ExplorerApiType.UNKNOWN,
    // explorerApi: 'https://explorer.zkevm.cronos.org',
    lightIcon: 'https://sentio.xyz/chains/cronos.svg',
    darkIcon: 'https://sentio.xyz/chains/cronos_light.svg'
  },
  [EthChainId.DERIVE]: {
    name: 'Derive Mainnet',
    slug: 'derive-mainnet',
    chainId: EthChainId.DERIVE,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.lyra.finance',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://explorer.lyra.finance',
    lightIcon: 'https://sentio.xyz/chains/derive.svg'
  },
  [EthChainId.UNICHAIN_SEPOLIA]: {
    name: 'Unichain Sepolia',
    slug: 'unichain-sepolia',
    chainId: EthChainId.UNICHAIN_SEPOLIA,
    mainnetChainId: EthChainId.UNICHAIN,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://unichain-sepolia.blockscout.com',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://unichain-sepolia.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/unichain-testnet.svg'
  },
  [EthChainId.UNICHAIN]: {
    name: 'Unichain',
    slug: 'unichain-mainnet',
    chainId: EthChainId.UNICHAIN,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://unichain.blockscout.com',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://unichain.blockscout.com',
    lightIcon: 'https://sentio.xyz/chains/unichain.svg'
  },
  [EthChainId.CORN_MAIZENET]: {
    name: 'Corn Maizenet',
    slug: 'corn-maizenet',
    chainId: EthChainId.CORN_MAIZENET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BTCN',
    tokenDecimals: 18,
    explorerUrl: 'https://maizenet-explorer.usecorn.com',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://maizenet-explorer.usecorn.com',
    lightIcon: 'https://sentio.xyz/chains/corn.svg'
  },
  [EthChainId.KARAK]: {
    name: 'Karak Mainnet',
    slug: 'karak-mainnet',
    chainId: EthChainId.KARAK,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.karak.network',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://explorer.karak.network',
    lightIcon: 'https://sentio.xyz/chains/karak.svg'
  },
  [EthChainId.SEI]: {
    name: 'Sei Mainnet',
    slug: 'sei',
    chainId: EthChainId.SEI,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://seistream.app',
    lightIcon: 'https://sentio.xyz/chains/sei.svg'
  },
  [EthChainId.SWELL_MAINNET]: {
    name: 'Swell Mainnet',
    slug: 'swell-mainnet',
    chainId: EthChainId.SWELL_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.swellnetwork.io',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://explorer.swellnetwork.io',
    lightIcon: 'https://sentio.xyz/chains/swell.svg'
  },
  [EthChainId.SWELL_TESTNET]: {
    name: 'Swell Testnet',
    slug: 'swell-testnet',
    chainId: EthChainId.SWELL_TESTNET,
    mainnetChainId: EthChainId.SWELL_MAINNET,
    variation: EthVariation.OPTIMISM,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://swell-testnet-explorer.alt.technology',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://swell-testnet-explorer.alt.technology',
    lightIcon: 'https://sentio.xyz/chains/swell.svg'
  },
  [EthChainId.TAC_TESTNET]: {
    name: 'TAC Testnet',
    slug: 'tac-testnet',
    chainId: EthChainId.TAC_TESTNET,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'TAC',
    tokenDecimals: 18,
    explorerUrl: 'https://turin.explorer.tac.build',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://turin.explorer.tac.build',
    lightIcon: 'https://sentio.xyz/chains/tac.svg'
  },
  [EthChainId.MONAD_TESTNET]: {
    name: 'Monad Testnet',
    slug: 'monad-testnet',
    chainId: EthChainId.MONAD_TESTNET,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'MON',
    tokenDecimals: 18,
    explorerUrl: 'https://testnet.monadexplorer.com',
    explorerApiType: ExplorerApiType.UNKNOWN,
    lightIcon: 'https://sentio.xyz/chains/monad.svg'
  },
  [EthChainId.BERACHAIN]: {
    name: 'Berachain',
    slug: 'berachain',
    chainId: EthChainId.BERACHAIN,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'BERA',
    tokenDecimals: 18,
    explorerUrl: 'https://berascan.com',
    explorerApi: 'https://api.etherscan.io/v2',
    explorerApiType: ExplorerApiType.ETHERSCAN_V2,
    lightIcon: 'https://sentio.xyz/chains/berachain.svg'
  },
  [EthChainId.HYPER_EVM]: {
    name: 'HyperEVM',
    slug: 'hyperevm',
    additionalSlugs: ['hyper-evm'],
    chainId: EthChainId.HYPER_EVM,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'HYPE',
    tokenDecimals: 18,
    explorerUrl: 'https://liquidscan.xyz/hyper-evm/mainnet',
    lightIcon: 'https://sentio.xyz/chains/hype.svg',
    darkIcon: 'https://sentio.xyz/chains/hype-dark.svg'
  },
  [EthChainId.ETHERLINK]: {
    name: 'Etherlink',
    slug: 'etherlink',
    chainId: EthChainId.ETHERLINK,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'XTZ',
    tokenDecimals: 18,
    explorerUrl: 'https://explorer.etherlink.com',
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerApi: 'https://explorer.etherlink.com',
    lightIcon: 'https://sentio.xyz/chains/etherlink.svg'
  },
  [EthChainId.MEV_COMMIT]: {
    name: 'MEV Commit',
    slug: 'mev-commit',
    chainId: EthChainId.MEV_COMMIT,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerUrl: 'https://www.mev-commit.xyz/',
    lightIcon: 'https://sentio.xyz/chains/mev-commit-dark.svg',
    darkIcon: 'https://sentio.xyz/chains/mev-commit.svg'
  },
  [EthChainId.HEMI]: {
    name: 'Hemi',
    slug: 'hemi',
    chainId: EthChainId.HEMI,
    variation: EthVariation.DEFAULT,
    priceTokenAddress: '******************************************',
    tokenAddress: '******************************************',
    wrappedTokenAddress: '******************************************',
    tokenSymbol: 'ETH',
    tokenDecimals: 18,
    explorerApiType: ExplorerApiType.BLOCKSCOUT,
    explorerUrl: 'https://explorer.hemi.xyz',
    explorerApi: 'https://explorer.hemi.xyz',
    lightIcon: 'https://sentio.xyz/chains/hemi.svg',
    darkIcon: 'https://sentio.xyz/chains/hemi.svg'
  }
}

type ScanUrlSubType = 'block' | 'address' | 'tx' | 'token' | 'object'
type ScanSubPath = Partial<Record<ScanUrlSubType, string | undefined>>

function getEVMChainScanUrl(chainId: string | number, hash: string, subtype: ScanUrlSubType) {
  // TODO l2scan address might be different
  let subtypeStr: string = subtype
  const supportedChain = EthChainInfo[chainId as ChainId]
  if (!supportedChain) {
    return
  }
  if (supportedChain.explorerApiType === ExplorerApiType.L2_SCAN) {
    if (subtype === 'block') {
      subtypeStr = 'blocks'
    }
  }
  return `${supportedChain.explorerUrl}/${subtypeStr}/${hash}`
}

/**
 * BTC chains
 */
export const BTCChainInfo: Record<BTCChainId | string, ChainInfo> = {
  [BTCChainId.BTC_MAINNET]: {
    name: 'Bitcoin Mainnet',
    slug: 'btc',
    chainId: BTCChainId.BTC_MAINNET,
    explorerUrl: 'https://mempool.space',
    lightIcon: 'https://sentio.xyz/chains/bitcoin.svg'
  },
  [BTCChainId.BTC_TESTNET]: {
    name: 'Bitcoin Mainnet',
    slug: 'btc-signet',
    chainId: BTCChainId.BTC_TESTNET,
    explorerUrl: 'https://mempool.space/testnet4',
    lightIcon: 'https://sentio.xyz/chains/bitcoin-testnet.svg'
  }
}

const BtcSubTypePaths: ScanSubPath = {
  block: 'block',
  address: 'address',
  tx: 'tx',
  token: undefined
}

function getBtcChainScanUrl(chainId: string | number, hash: string, subtype: ScanUrlSubType) {
  const hostName = BTCChainInfo[chainId]?.explorerUrl
  const subPath = BtcSubTypePaths[subtype]
  if (!hostName || !subPath) {
    return
  }
  return `${hostName}/${subPath}/${hash}`
}

/**
 * Aptos chains
 */
export const AptosChainInfo: Record<
  AptosChainId | string,
  ChainInfo & {
    suffix: string
  }
> = {
  [AptosChainId.APTOS_MAINNET]: {
    name: 'Aptos Mainnet',
    slug: 'aptos',
    chainId: AptosChainId.APTOS_MAINNET,
    explorerUrl: 'https://explorer.aptoslabs.com',
    suffix: '?network=mainnet',
    lightIcon: 'https://sentio.xyz/chains/aptos.svg',
    darkIcon: 'https://sentio.xyz/chains/aptos-dark.svg'
  },
  [AptosChainId.APTOS_TESTNET]: {
    name: 'Aptos Testnet',
    chainId: AptosChainId.APTOS_TESTNET,
    slug: 'aptos-testnet',
    explorerUrl: 'https://explorer.aptoslabs.com',
    suffix: '?network=testnet',
    lightIcon: 'https://sentio.xyz/chains/aptos.svg',
    darkIcon: 'https://sentio.xyz/chains/aptos-dark.svg'
  },
  [AptosChainId.APTOS_MOVEMENT_MAINNET]: {
    name: 'Movement Mainnet',
    slug: 'movement',
    chainId: AptosChainId.APTOS_MOVEMENT_MAINNET,
    explorerUrl: 'https://explorer.movementnetwork.xyz',
    suffix: '?network=mainnet',
    lightIcon: 'https://sentio.xyz/chains/movement.svg',
    darkIcon: 'https://sentio.xyz/chains/movement-dark.svg'
  },
  [AptosChainId.APTOS_MOVEMENT_TESTNET]: {
    name: 'Movement Testnet',
    slug: 'movement-testnet',
    chainId: AptosChainId.APTOS_MOVEMENT_TESTNET,
    explorerUrl: 'https://explorer.movementnetwork.xyz',
    suffix: '?network=testnet',
    lightIcon: 'https://sentio.xyz/chains/movement.svg',
    darkIcon: 'https://sentio.xyz/chains/movement-dark.svg'
  },
  [AptosChainId.INITIA_ECHELON]: {
    name: 'Initia Echelon',
    slug: 'initia-echelon',
    chainId: AptosChainId.INITIA_ECHELON,
    explorerUrl: 'https://scan.initia.xyz/echelon-1',
    suffix: '',
    lightIcon: 'https://sentio.xyz/chains/initia-echelon.svg'
  }
}
const AptosSubTypePaths: ScanSubPath = {
  block: 'block',
  address: 'account',
  tx: 'txn',
  token: undefined
}
function getAptosChainScanUrl(chainId: string | number, hash: string, subtype: ScanUrlSubType) {
  const { explorerUrl, suffix } = AptosChainInfo[chainId]
  const subPath = AptosSubTypePaths[subtype]
  if (!subPath) {
    return
  }
  return `${explorerUrl}/${subPath}/${hash}${suffix}`
}

/**
 * Solana
 */
export const SolanaChainInfo: Record<
  SolanaChainId | string,
  ChainInfo & {
    suffix: string
  }
> = {
  [SolanaChainId.SOLANA_MAINNET]: {
    name: 'Solana Mainnet',
    slug: 'solana',
    chainId: SolanaChainId.SOLANA_MAINNET,
    explorerUrl: 'https://solscan.io/',
    suffix: '',
    lightIcon: 'https://sentio.xyz/solana.svg'
  },
  [SolanaChainId.SOLANA_TESTNET]: {
    name: 'Solana Testnet',
    slug: 'solana-testnet',
    chainId: SolanaChainId.SOLANA_TESTNET,
    explorerUrl: 'https://solscan.io/',
    suffix: '?cluster=testnet',
    lightIcon: 'https://sentio.xyz/solana.svg'
  },
  [SolanaChainId.SOLANA_PYTH]: {
    name: 'Pyth',
    slug: 'pyth',
    chainId: SolanaChainId.SOLANA_PYTH,
    explorerUrl: 'https://solscan.io/',
    suffix: '?cluster=custom&customUrl=https://pythnet.rpcpool.com',
    lightIcon: 'https://sentio.xyz/pyth.svg'
  }
}

const SolanaSubTypePaths: ScanSubPath = {
  block: 'block',
  address: 'address',
  tx: 'tx',
  token: 'token'
}

function getSolanaChainScanUrl(chainId: string | number, hash: string, subtype: ScanUrlSubType) {
  const { explorerUrl, suffix } = SolanaChainInfo[chainId]
  const subPath = SolanaSubTypePaths[subtype]
  if (!subPath) {
    return
  }
  return `${explorerUrl}${subPath}/${hash}${suffix}`
}

/**
 * Sui
 */
export const SuiChainInfo: Record<
  SuiChainId | string,
  ChainInfo & {
    suiscanUrl?: string
    suivisionUrl?: string
  }
> = {
  [SuiChainId.SUI_MAINNET]: {
    name: 'Sui Mainnet',
    slug: 'sui',
    chainId: SuiChainId.SUI_MAINNET,
    suivisionUrl: 'https://suivision.xyz',
    explorerUrl: 'https://suiscan.xyz/mainnet',
    lightIcon: 'https://sentio.xyz/chains/sui.svg',
    darkIcon: 'https://sentio.xyz/chains/sui-dark.svg'
  },
  [SuiChainId.SUI_TESTNET]: {
    name: 'Sui Testnet',
    slug: 'sui-testnet',
    chainId: SuiChainId.SUI_TESTNET,
    suivisionUrl: 'https://testnet.suivision.xyz',
    explorerUrl: 'https://suiscan.xyz/testnet',
    lightIcon: 'https://sentio.xyz/chains/sui.svg',
    darkIcon: 'https://sentio.xyz/chains/sui-dark.svg'
  },
  [SuiChainId.IOTA_MAINNET]: {
    name: 'IOTA Mainnet',
    slug: 'iota',
    chainId: SuiChainId.IOTA_MAINNET,
    suivisionUrl: '',
    explorerUrl: 'https://iotascan.com/mainnet',
    lightIcon: 'https://sentio.xyz/chains/iota.svg',
    darkIcon: 'https://sentio.xyz/chains/iota-dark.svg'
  },
  [SuiChainId.IOTA_TESTNET]: {
    name: 'IOTA Testnet',
    slug: 'iota-testnet',
    chainId: SuiChainId.IOTA_TESTNET,
    suivisionUrl: '',
    explorerUrl: 'https://iotascan.com/testnet',
    lightIcon: 'https://sentio.xyz/chains/iota.svg',
    darkIcon: 'https://sentio.xyz/chains/iota-dark.svg'
  }
}

const SuiScanSubTypePaths: ScanSubPath = {
  block: 'checkpoint',
  address: 'account',
  tx: 'tx',
  token: 'coin',
  object: 'object'
}

const SuiVisionSubTypePaths: ScanSubPath = {
  block: 'checkpoint',
  address: 'account',
  tx: 'txblock',
  token: 'coin',
  object: 'object'
}

function getSuiChainScanUrl(chainId: string | number, hash: string, subtype: ScanUrlSubType) {
  const suiChain = SuiChainInfo[chainId]
  if (!suiChain) {
    return
  }
  if (!suiChain.explorerUrl) {
    return
  }
  const subPath = SuiScanSubTypePaths[subtype]
  if (!subPath) {
    return
  }
  return `${suiChain.explorerUrl}/${subPath}/${hash}`
}

function getSuiChainVisionUrl(chainId: string | number, hash: string, subtype: ScanUrlSubType) {
  const suiChain = SuiChainInfo[chainId]
  if (!suiChain) {
    return
  }
  if (!suiChain.suivisionUrl) {
    return
  }
  const subPath = SuiVisionSubTypePaths[subtype]
  if (!subPath) {
    return
  }
  return `${suiChain.suivisionUrl}/${subPath}/${hash}`
}

/**
 * Fuel
 */
export const FuelChainInfo: Record<FuelChainId | string, ChainInfo> = {
  [FuelChainId.FUEL_MAINNET]: {
    name: 'Fuel Mainnet',
    slug: 'fuel',
    chainId: FuelChainId.FUEL_MAINNET,
    explorerUrl: 'https://app.fuel.network',
    lightIcon: 'https://sentio.xyz/chains/fuel.svg'
  },
  [FuelChainId.FUEL_TESTNET]: {
    name: 'Fuel Testnet',
    slug: 'fuel-testnet',
    chainId: FuelChainId.FUEL_TESTNET,
    explorerUrl: 'https://app-testnet.fuel.network',
    lightIcon: 'https://sentio.xyz/chains/fuel.svg'
  }
}

export const StarknetChainInfo: Record<FuelChainId | string, ChainInfo> = {
  [StarknetChainId.STARKNET_MAINNET]: {
    name: 'Starknet',
    slug: 'starknet',
    chainId: StarknetChainId.STARKNET_MAINNET,
    explorerUrl: 'https://starkscan.co',
    lightIcon: 'https://sentio.xyz/chains/starknet.svg'
  },
  [StarknetChainId.STARKNET_SEPOLIA]: {
    name: 'Starknet Sepolia',
    slug: 'starknet-sepolia',
    chainId: StarknetChainId.STARKNET_SEPOLIA,
    explorerUrl: 'https://sepolia.starkscan.co',
    lightIcon: 'https://sentio.xyz/chains/starknet.svg'
  }
}

export const CosmosChainInfo: Record<CosmosChainId | string, ChainInfo> = {
  [CosmosChainId.INJECTIVE_MAINNET]: {
    name: 'Injective',
    slug: 'injective',
    chainId: CosmosChainId.INJECTIVE_MAINNET,
    explorerUrl: 'https://injscan.com/',
    lightIcon: 'https://sentio.xyz/chains/injective.svg'
  },
  [CosmosChainId.INJECTIVE_TESTNET]: {
    name: 'Injective Testnet',
    slug: 'injective-testnet',
    chainId: CosmosChainId.INJECTIVE_TESTNET,
    explorerUrl: 'https://testnet.explorer.injective.network',
    lightIcon: 'https://sentio.xyz/chains/injective.svg'
  }
}

export const ChainInfo: Record<ChainId | string, ChainInfo> = {
  ...EthChainInfo,
  ...BTCChainInfo,
  ...AptosChainInfo,
  ...SolanaChainInfo,
  ...SuiChainInfo,
  ...FuelChainInfo,
  ...StarknetChainInfo,
  ...CosmosChainInfo
}

const FuelSubTypePaths: ScanSubPath = {
  block: 'block',
  address: 'account',
  tx: 'tx',
  token: undefined
}

function getFuelChainScanUrl(chainId: string | number, hash: string, subtype: ScanUrlSubType) {
  const { explorerUrl } = FuelChainInfo[chainId]
  const subPath = FuelSubTypePaths[subtype]
  if (!subPath) {
    return
  }
  return `${explorerUrl}/${subPath}/${hash}`
}

/**
 * Generate scan url of target chain and sub types.
 * @param chainId
 * @param hash
 * @param subtype
 * @returns
 */
export function getChainExternalUrl(
  chainId?: string | number,
  hash?: string,
  subtype?: ScanUrlSubType
): string | undefined {
  if (!chainId || !hash || !subtype) {
    return
  }
  const chainIdStr = chainId.toString()
  if (Object.keys(EthChainInfo).includes(chainIdStr)) {
    // EVM
    return getEVMChainScanUrl(chainIdStr, hash, subtype)
  } else if (Object.keys(BTCChainInfo).includes(chainIdStr)) {
    // BTC
    return getBtcChainScanUrl(chainIdStr, hash, subtype)
  } else if (Object.keys(AptosChainInfo).includes(chainIdStr)) {
    // Aptos
    return getAptosChainScanUrl(chainIdStr, hash, subtype)
  } else if (Object.keys(SolanaChainInfo).includes(chainIdStr)) {
    // Solana
    return getSolanaChainScanUrl(chainIdStr, hash, subtype)
  } else if (Object.keys(SuiChainInfo).includes(chainIdStr)) {
    // Sui
    return getSuiChainVisionUrl(chainIdStr, hash, subtype) || getSuiChainScanUrl(chainIdStr, hash, subtype)
  } else if (Object.keys(FuelChainInfo).includes(chainIdStr)) {
    // Fuel
    return getFuelChainScanUrl(chainIdStr, hash, subtype)
  }
  return
}

export function getChainBlockscoutUrl(
  chainId?: string | number,
  hash?: string,
  subtype?: ScanUrlSubType
): string | undefined {
  if (!chainId || !hash || !subtype) {
    return
  }
  const supportedChain = EthChainInfo[chainId as ChainId]
  if (!supportedChain) {
    return
  }
  if (!supportedChain.blockscoutUrl) {
    return
  }
  return `${supportedChain.blockscoutUrl}/${subtype}/${hash}`
}

export function getSuiscanUrl(chainId?: string | number, hash?: string, subtype?: ScanUrlSubType) {
  if (!chainId || !hash || !subtype) {
    return
  }
  return getSuiChainScanUrl(chainId, hash, subtype)
}

function getLogoUrl(info?: ChainInfo, dark?: boolean) {
  const defaultUrl = 'https://sentio.xyz/chains/chain-unknown.webp'
  if (!info) {
    return defaultUrl
  }
  if (dark && info?.darkIcon) {
    return info.darkIcon
  }
  if (info?.lightIcon) {
    return info.lightIcon
  }
  return defaultUrl
}

export function getChainLogo(chainId?: string, dark?: boolean) {
  if (!chainId) {
    return
  }
  const chainInfo = ChainInfo[chainId.toString()]
  return getLogoUrl(chainInfo, dark)
}
