package handler

import (
	"context"
	"time"

	"sentioxyz/sentio/common/functions"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/formula"
	protoscommon "sentioxyz/sentio/service/common/protos"
	cache "sentioxyz/sentio/service/common/requestcache"
	"sentioxyz/sentio/service/common/timerange"
	formulaproto "sentioxyz/sentio/service/formula/protos"
	"sentioxyz/sentio/service/insights/constants"
	"sentioxyz/sentio/service/insights/models"
	"sentioxyz/sentio/service/insights/protos"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

type FormulaHandler struct {
	*handler
	request       *protoscommon.Formula
	redisClient   *redis.Client
	formulaClient *formulaproto.FormulaServiceClient
}

func NewFormulaHandler(
	queryContext models.InsightsContext,
	f *protoscommon.Formula,
	redisClient *redis.Client,
	formulaClient *formulaproto.FormulaServiceClient,
	bypassCache bool, cacheTTLSecs, cacheRefreshIntervalSecs int32) Handler {
	h := &FormulaHandler{
		handler: &handler{
			queryContext:             queryContext,
			processed:                false,
			bypassCache:              bypassCache,
			cacheTTLSecs:             cacheTTLSecs,
			cacheRefreshIntervalSecs: cacheRefreshIntervalSecs,
		},
		request:       f,
		redisClient:   redisClient,
		formulaClient: formulaClient,
	}
	return h
}

func (f *FormulaHandler) SetQuery(_ context.Context, _ *protos.QueryRequest_Query, limit int32) error {
	f.limit = limit
	f.id = f.request.GetId()
	return nil
}

func CalculateFormula(ctx context.Context, _ *formula.Request, argv ...any) (result *protos.QueryResponse_Result, err error) {
	if len(argv) != 1 {
		return nil, errors.Errorf("invalid number of argv")
	}
	f := argv[0].(*FormulaHandler)

	result = initInsightsResult(f.ID(), f.queryContext.GetQueryAlias(f.ID()), f.queryContext.GetQueryColor(f.ID()), protos.DataSource_FORMULA)
	if err := f.queryContext.FormulaPrepareContext(f.ID()); err != nil {
		return insightsResultSetError(result, err), err
	}
	expression, err := f.queryContext.GetFormulaExpression(f.ID())
	if err != nil {
		return insightsResultSetError(result, err), err
	}
	request := &formulaproto.FormulaRequest{
		ProjectOwner: f.queryContext.GetProjectOwner(),
		ProjectSlug:  f.queryContext.GetProjectSlug(),
		ProjectId:    f.queryContext.GetProjectID(),
		Version:      f.queryContext.GetVersion(),
		Expression:   expression.ToString(),
		Parameters:   f.queryContext.GetFormulaParameters(f.ID()),
	}
	data, err := (*f.formulaClient).Evaluate(ctx, request)
	if err != nil {
		return insightsResultSetError(result, err), err
	}
	// NOTE: formula service return scalar value with timestamp 0,
	// we need to set it to the end of the time range
	if data.GetResult().GetMatrix() != nil {
		if len(data.GetResult().GetMatrix().GetSamples()) == 1 &&
			len(data.GetResult().GetMatrix().Samples[0].GetValues()) == 1 &&
			data.GetResult().GetMatrix().Samples[0].Values[0].Timestamp == 0 {
			tz, err := time.LoadLocation(f.queryContext.GetTimeRange().GetTimezone())
			if err != nil {
				return nil, err
			}
			ts, _ := timerange.ResolveTimeStrWithAlign(f.queryContext.GetTimeRange().GetEnd(), false, tz)
			data.GetResult().GetMatrix().Samples[0].Values[0].Timestamp = ts.Unix()
		}
	}
	result = &protos.QueryResponse_Result{
		Id:           f.ID(),
		Alias:        f.queryContext.GetFormulaAlias(f.ID()),
		Color:        f.queryContext.GetFormulaColor(f.ID()),
		ComputeStats: data.GetResult().GetStats(),
		DataSource:   protos.DataSource_FORMULA,
		ResultType: &protos.QueryResponse_Result_Matrix{
			Matrix: data.GetResult().GetMatrix(),
		},
	}
	return result, nil
}

func (f *FormulaHandler) Process(ctx context.Context) (resp *protos.QueryResponse, err error) {
	defer func() {
		f.queryContext.SetFormulaQueryFinished(f.ID())
		if len(resp.GetResults()) > 0 {
			f.queryContext.SetFormulaResponse(f.ID(), resp.GetResults()[0])
		}
	}()
	resp = initInsightsResponse(f.ID(), f.queryContext.GetQueryAlias(f.ID()), f.queryContext.GetQueryColor(f.ID()), protos.DataSource_FORMULA)
	if err := f.queryContext.CheckFormulaError(f.ID()); err != nil {
		return insightsResponseSetError(resp, err), err
	}
	if f.queryContext.GetFormulaDisabled(f.ID()) {
		return nil, nil
	}
	ready, err := f.formulaDependencyReady(ctx)
	if err != nil {
		return insightsResponseSetError(resp, err), err
	}
	if !ready {
		return insightsResponseSetError(resp, ErrDependencyNotReady), ErrDependencyNotReady
	}

	f.processed = true
	cacheOptions := []*cache.Option{
		cache.WithLoaderArgv([]any{f}),
		cache.WithRefreshBackground(),
		cache.WithConcurrencyControl(),
	}
	if f.bypassCache {
		cacheOptions = append(cacheOptions, cache.WithBypassCache())
	}
	if f.cacheTTLSecs > 0 {
		cacheOptions = append(cacheOptions, cache.WithSpecifiedTTL(time.Duration(f.cacheTTLSecs)*time.Second))
	}
	if f.cacheRefreshIntervalSecs > 0 {
		cacheOptions = append(cacheOptions, cache.WithSpecifiedRefreshInterval(time.Duration(f.cacheRefreshIntervalSecs)*time.Second))
	}
	c := cache.NewRequestCache[*formula.Request, *protos.QueryResponse_Result](f.redisClient)
	var result *protos.QueryResponse_Result
	resp = &protos.QueryResponse{}
	result, err = c.Query(ctx, formula.NewRequest(
		f.request, f.queryContext.GetFormulaHash(f.ID()),
		"project_id", f.queryContext.GetProjectID(),
		"project_slug", f.queryContext.GetProjectSlug(),
		"project_owner", f.queryContext.GetProjectOwner(),
		"version", f.queryContext.GetVersion()),
		CalculateFormula, cacheOptions...)
	if result != nil {
		resp.Results = append(resp.Results, result)
	} else {
		resp.Results = append(resp.Results, initInsightsResult(f.ID(), f.queryContext.GetQueryAlias(f.ID()), f.queryContext.GetQueryColor(f.ID()), protos.DataSource_FORMULA))
	}
	return resp, err
}

func (f *FormulaHandler) ExecuteFunctions(ctx context.Context) error {
	defer func() {
		f.queryContext.SetFormulaExecuteFunctionsFinished(f.ID())
	}()

	var result *protos.QueryResponse_Result
	var ok bool
	result, ok = f.queryContext.GetResult(f.ID())
	if !ok {
		return ErrSkipFunctions
	}
	if result == nil {
		f.queryContext.CleanResult(f.ID())
		return ErrSkipFunctions
	}
	if result.GetMatrix() == nil {
		return ErrSkipFunctions
	}
	if !f.waitFormulaExecuteFunction(ctx) {
		log.Warnf("will execute function force, maybe race condition happened, err: %v", ErrDependencyNotReady)
	}

	var limit, offset int32 = constants.AfterFormulaDefaultLimit, 0
	var funcs []*protoscommon.Function
	if f.limit > 0 {
		limit = f.limit
	}
	funcs = f.queryContext.GetFormulaFunctions(f.ID())
	funcs = append(funcs, &protoscommon.Function{
		Name: "truncate",
		Arguments: []*protoscommon.Argument{
			{ArgumentValue: &protoscommon.Argument_IntValue{IntValue: limit}},
			{ArgumentValue: &protoscommon.Argument_IntValue{IntValue: offset}},
		},
	})

	defer func() {
		f.queryContext.SetQueryResponse(f.ID(), result)
	}()
	matrix, err := functions.Process(ctx, result.GetMatrix(), funcs)
	if err != nil {
		result.ResultType = &protos.QueryResponse_Result_Error{
			Error: err.Error(),
		}
		return err
	}
	result.ResultType = &protos.QueryResponse_Result_Matrix{
		Matrix: matrix,
	}
	return nil
}

func (f *FormulaHandler) Type() Type {
	return FormulaHandlerType
}
