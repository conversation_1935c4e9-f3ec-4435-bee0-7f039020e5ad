package handler

import (
	"encoding/json"
	"fmt"
	"time"

	"sentioxyz/sentio/common/identifier"
	protoscommon "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/common/timerange"
	"sentioxyz/sentio/service/insights/constants"
	insightsproto "sentioxyz/sentio/service/insights/protos"
	"sentioxyz/sentio/service/price/protos"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	Year   int32 = 31536000
	Qtr    int32 = 7776000
	Month  int32 = 2592000
	Week   int32 = 604800
	Day    int32 = 86400
	Hour   int32 = 3600
	Minute int32 = 60
)

func timeTruncateWithLocation(t time.Time, step int32, tz *time.Location) time.Time {
	t = t.In(tz)
	switch step {
	case Year:
		return time.Date(t.Year(), 1, 1, 0, 0, 0, 0, tz)
	case Qtr:
		month := int32(t.Month())
		month = (month-1)/3*3 + 1
		return time.Date(t.Year(), time.Month(month), 1, 0, 0, 0, 0, tz)
	case Month:
		return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, tz)
	case Week:
		return time.Date(t.Year(), t.Month(), t.Day()-int(t.Weekday())+1, 0, 0, 0, 0, tz) // Monday as the start of the week
	case Day:
		return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, tz)
	case Hour:
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), 0, 0, 0, tz)
	case Minute:
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, tz)
	default:
		return t.In(tz).Truncate(time.Duration(step) * time.Second)
	}
}

func Timerange2timestamppb(timeRange *protoscommon.TimeRangeLite) (results []*timestamppb.Timestamp) {
	tz := time.UTC
	if len(timeRange.GetTimezone()) > 0 {
		var err error
		tz, err = time.LoadLocation(timeRange.GetTimezone())
		if err != nil {
			tz = time.UTC
		}
	}
	start, err := timerange.ResolveTimeStrWithAlign(timeRange.GetStart(), true, tz)
	if err != nil {
		return nil
	}
	end, err := timerange.ResolveTimeStrWithAlign(timeRange.GetEnd(), false, tz)
	if err != nil {
		return nil
	}
	truncateStart := timeTruncateWithLocation(start, timeRange.GetStep(), tz)
	truncateEnd := timeTruncateWithLocation(end.Add(time.Duration(timeRange.GetStep())*time.Second), timeRange.GetStep(), tz)
	results = []*timestamppb.Timestamp{}
	for t := truncateStart; t.Before(truncateEnd); {
		if !t.Before(start) && !t.After(end) {
			results = append(results, timestamppb.New(t))
		}
		switch timeRange.GetStep() {
		case Year:
			t = t.AddDate(1, 0, 0)
		case Qtr:
			t = t.AddDate(0, 3, 0)
		case Month:
			t = t.AddDate(0, 1, 0)
		case Week:
			t = t.AddDate(0, 0, 7)
		case Day:
			t = t.AddDate(0, 0, 1)
		case Hour:
			t = t.Add(time.Hour)
		case Minute:
			t = t.Add(time.Minute)
		default:
			t = t.Add(time.Second * time.Duration(timeRange.GetStep()))
		}
	}
	return
}

func deepcopyCoin(from *protoscommon.CoinID) (to *protos.CoinID) {
	to = &protos.CoinID{}
	switch from.Id.(type) {
	case *protoscommon.CoinID_Address:
		to.Id = &protos.CoinID_Address{
			Address: &protos.CoinID_AddressIdentifier{
				Address: from.GetAddress().GetAddress(),
				Chain:   from.GetAddress().GetChain(),
			},
		}
	case *protoscommon.CoinID_Symbol:
		to.Id = &protos.CoinID_Symbol{
			Symbol: from.GetSymbol(),
		}
	}
	return to
}

func deepcopyCoin2(from *protos.CoinID) (to *protoscommon.CoinID) {
	to = &protoscommon.CoinID{}
	switch from.Id.(type) {
	case *protos.CoinID_Address:
		to.Id = &protoscommon.CoinID_Address{
			Address: &protoscommon.CoinID_AddressIdentifier{
				Address: from.GetAddress().GetAddress(),
				Chain:   from.GetAddress().GetChain(),
			},
		}
	case *protos.CoinID_Symbol:
		to.Id = &protoscommon.CoinID_Symbol{
			Symbol: from.GetSymbol(),
		}
	}
	return to
}

func coinKey(coin any) string {
	switch coin.(type) {
	case *protos.CoinID, *protoscommon.CoinID:
		key, _ := json.Marshal(coin)
		return string(key)
	default:
		return ""
	}
}

func coinName(coin any) string {
	switch coin := coin.(type) {
	case *protos.CoinID:
		if coin.GetAddress() != nil {
			return fmt.Sprintf("%s:%s", coin.GetAddress().GetChain(), coin.GetAddress().GetAddress())
		} else if coin.GetSymbol() != "" {
			return coin.GetSymbol()
		}
	case *protoscommon.CoinID:
		if coin.GetAddress() != nil {
			return fmt.Sprintf("%s:%s", coin.GetAddress().GetChain(), coin.GetAddress().GetAddress())
		} else if coin.GetSymbol() != "" {
			return coin.GetSymbol()
		}
	}
	return "<nil>"
}

func generateCoinLabel(coin any) map[string]string {
	labels := map[string]string{}
	switch coin := coin.(type) {
	case *protos.CoinID:
		if coin.GetAddress() != nil {
			labels[constants.CoinLabelChain] = coin.GetAddress().GetChain()
			labels[constants.CoinLabelAddress] = coin.GetAddress().GetAddress()
		}
		if coin.GetSymbol() != "" {
			labels[constants.CoinLabelSymbol] = coin.GetSymbol()
		}
	case *protoscommon.CoinID:
		if coin.GetAddress() != nil {
			labels[constants.CoinLabelChain] = coin.GetAddress().GetChain()
			labels[constants.CoinLabelAddress] = coin.GetAddress().GetAddress()
		}
		if coin.GetSymbol() != "" {
			labels[constants.CoinLabelSymbol] = coin.GetSymbol()
		}
	}
	return labels
}

func initInsightsResult(id, alias string, color string, source insightsproto.DataSource) *insightsproto.QueryResponse_Result {
	return &insightsproto.QueryResponse_Result{
		Id:         id,
		Alias:      alias,
		Color:      color,
		DataSource: source,
		ResultType: &insightsproto.QueryResponse_Result_Error{
			Error: "unexpected error occurred, please try again later",
		},
	}
}

func initInsightsResponse(id, alias string, color string, source insightsproto.DataSource) *insightsproto.QueryResponse {
	return &insightsproto.QueryResponse{
		Results: []*insightsproto.QueryResponse_Result{
			{
				Id:         id,
				Alias:      alias,
				Color:      color,
				DataSource: source,
				ResultType: &insightsproto.QueryResponse_Result_Error{
					Error: "unexpected error occurred, please try again later",
				},
			},
		},
	}
}

func insightsResultSetError(r *insightsproto.QueryResponse_Result, err error) *insightsproto.QueryResponse_Result {
	if r == nil {
		return &insightsproto.QueryResponse_Result{
			ResultType: &insightsproto.QueryResponse_Result_Error{
				Error: errors.Wrapf(err, "unexpected error occurred, please check arguments and try again later").Error(),
			},
		}
	} else {
		r.ResultType = &insightsproto.QueryResponse_Result_Error{
			Error: errors.Wrapf(err, "unexpected error occurred, please check arguments and try again later").Error(),
		}
		return r
	}
}

func insightsResponseSetError(r *insightsproto.QueryResponse, err error) *insightsproto.QueryResponse {
	if len(r.GetResults()) == 0 {
		r.Results = append(r.Results, &insightsproto.QueryResponse_Result{
			ResultType: &insightsproto.QueryResponse_Result_Error{
				Error: errors.Wrapf(err, "unexpected error occurred, please check arguments and try again later").Error(),
			},
		})
	} else {
		r.Results[0].ResultType = &insightsproto.QueryResponse_Result_Error{
			Error: errors.Wrapf(err, "unexpected error occurred, please check arguments and try again later").Error(),
		}
	}
	return r
}

func initComputeStats() *protoscommon.ComputeStats {
	return &protoscommon.ComputeStats{
		ComputedAt:        timestamppb.Now(),
		BinaryVersionHash: identifier.BinaryHash(),
		ComputedBy:        "sentio/" + identifier.PodName(),
	}
}
