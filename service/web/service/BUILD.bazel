load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "apikey_service.go",
        "coder_service.go",
        "dashboard_service.go",
        "notification_service.go",
        "project_service.go",
        "sitemaps.go",
        "snapshot_service.go",
        "user_service.go",
        "web_service.go",
    ],
    importpath = "sentioxyz/sentio/service/web/service",
    visibility = ["//visibility:public"],
    deps = [
        "//common/gonanoid",
        "//common/https",
        "//common/log",
        "//common/notification",
        "//common/protojson",
        "//common/utils",
        "//service/analytic/sqllib/mapper",
        "//service/common/auth",
        "//service/common/gormcache",
        "//service/common/messenger",
        "//service/common/models",
        "//service/common/preloader",
        "//service/common/protos",
        "//service/web/models",
        "//service/web/protos",
        "//service/web/repository",
        "//service/web/utils",
        "@com_github_grpc_ecosystem_grpc_gateway_v2//runtime",
        "@com_github_matoous_go_nanoid_v2//:go-nanoid",
        "@com_github_pkg_errors//:errors",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@io_opentelemetry_go_otel//:otel",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//reflect/protoreflect",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_google_protobuf//types/known/structpb",
        "@org_golang_x_exp//slices",
        "@org_golang_x_net//context",
    ],
)
