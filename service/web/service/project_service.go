package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sentioxyz/sentio/service/common/preloader"
	"sort"
	"strings"

	"sentioxyz/sentio/common/protojson"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"

	"go.opentelemetry.io/otel"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/models"
	protoscommon "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/web/protos"
)

const FreeTierProjectsLimit = 5

func (s *Service) GetProject(
	ctx context.Context,
	req *protos.ProjectOwnerAndSlug,
) (response *protos.GetProjectResponse, err error) {
	var identity *models.Identity
	tracer := otel.Tracer("GetProject")
	ctx, span := tracer.Start(ctx, "Auth Login")
	identity, err = s.auth.LoginOrAnonymous(ctx)
	if err != nil {
		return nil, err
	}
	span.End()
	response = &protos.GetProjectResponse{
		Permissions: []protoscommon.Permission{},
	}
	var p *models.Project
	db := s.db.WithContext(ctx)
	if req != nil {
		p, err = s.repository.GetProjectBySlug(ctx, req.GetOwnerName(), req.GetSlug())
	} else {
		// get default project
		var projects []*models.Project
		projects, err = s.repository.GetProjects(db, identity)
		if err == nil && projects != nil && len(projects) > 0 {
			p = projects[0]
		}
	}

	if p == nil {
		err = status.Errorf(
			codes.NotFound,
			"project '%s/%s' not found", req.OwnerName, req.Slug,
		)
	} else {
		_, span := tracer.Start(ctx, "Check Permission")
		if access, _ := s.auth.GetProjectAccess(ctx, identity, p); len(access) == 0 {
			err = status.Errorf(
				codes.PermissionDenied,
				"user %s does not have read access to project %s",
				identity.Sub,
				p.ID,
			)
		} else {
			for _, a := range access {
				response.Permissions = append(response.Permissions, a.ToPB())
			}
			response.Project = p.ToPB()
		}
		span.End()
	}
	if !identity.IsAnonymous() && response.Project != nil {
		s.repository.AddViewedProject(identity, p)
	}
	return
}

func (s *Service) GetProjectById(
	ctx context.Context,
	req *protos.GetProjectByIdRequest,
) (*protoscommon.ProjectInfo, error) {
	project, err := preloader.PreLoadedProjectByID(ctx, s.db, req.ProjectId)
	if project == nil || err != nil {
		return nil, status.Errorf(codes.NotFound, "project with ID '%s' not found", req.ProjectId)
	}
	return project.ToProjectInfo(), nil
}

func (s *Service) createProject(ctx context.Context, req *protoscommon.Project, identity *models.Identity, createDashboard bool) (*models.Project, error) {
	var err error
	p := &models.Project{}
	if identity.User.AccountStatus != protoscommon.User_ACTIVE.String() {
		return nil, status.Errorf(
			codes.PermissionDenied,
			"account status is not active yet",
		)
	}
	if !gonanoid.CheckIDMatchPattern(req.Slug, false, true) {
		return nil, status.Error(
			codes.InvalidArgument,
			"Invalid project slug",
		)
	}

	p.FromPB(req)
	if req.OwnerName == "" {
		req.OwnerName = identity.User.Username
	}
	isFreeTier := false

	if org, _ := s.repository.GetOrganization(req.OwnerName); org != nil {
		// check if user can create project in this organization
		if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.WRITE); !ok {
			return nil, status.Errorf(
				codes.PermissionDenied,
				"user %s doesn't have write permission to organization %s",
				identity.User.Username,
				org.Name,
			)
		}
		p.OwnerID = org.ID
		p.OwnerType = "organizations"
		isFreeTier = org.Tier == int32(protoscommon.Tier_FREE)
	} else if u, _ := s.repository.FindUserByUsernameOrEmail(req.OwnerName); u != nil {
		if u.ID != identity.UserID {
			return nil, status.Errorf(
				codes.PermissionDenied,
				"user %s doesn't have write permission to user %s",
				identity.User.Username,
				u.Username,
			)
		}
		p.OwnerID = identity.UserID
		p.OwnerType = "users"
		isFreeTier = identity.User.Tier == int32(protoscommon.Tier_FREE)
	} else {
		return nil, status.Errorf(
			codes.NotFound,
			"owner %s not found",
			req.OwnerName,
		)
	}

	// check free tier limit
	if isFreeTier {
		projects, err := s.repository.GetProjectsByOwnerID(s.db, p.OwnerID)
		if err != nil {
			return nil, err
		}
		if len(projects) >= FreeTierProjectsLimit {
			return nil, status.Errorf(
				codes.PermissionDenied,
				"Free tier plan can only have %d projects",
				FreeTierProjectsLimit,
			)
		}
	}

	err = s.repository.SaveProject(s.db, p)
	if err != nil {
		return nil, err
	}

	if createDashboard {
		_, err = s.repository.CreateDefaultDashboard(p.ID)
	}
	return p, err
}

func (s *Service) SaveProject(ctx context.Context, req *protoscommon.Project) (*protoscommon.Project, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	p := &models.Project{}
	if req.Id == "" { // new project
		p, err = s.createProject(ctx, req, identity, true)
		if err != nil {
			return nil, err
		}
	} else { // update project
		err := s.db.First(p, "id = ?", req.Id).Error
		if err != nil {
			return nil, err
		}
		isPublic := req.Visibility == protoscommon.Project_PUBLIC
		if p.Public != isPublic {
			err = s.requireProjectPermission(ctx, identity, p, auth.ADMIN)
			if err != nil {
				return nil, err
			}
		}
		if p.Description != req.Description || p.DisplayName != req.DisplayName {
			err = s.requireProjectPermission(ctx, identity, p, auth.WRITE)
			if err != nil {
				return nil, err
			}
		}
		p.Public = isPublic
		p.Description = req.Description
		p.DisplayName = req.DisplayName
		// disable multi version
		if p.MultiVersion && !req.MultiVersion {
			err := s.repository.DisablePendingVersions(s.db, p.ID)
			if err != nil {
				return nil, err
			}
		}
		p.MultiVersion = req.MultiVersion
		p.EnableDisk = req.EnableDisk
		if req.DefaultTimerange != nil {
			p.DefaultTimerange, err = protojson.Marshal(req.DefaultTimerange)
			if err != nil {
				return nil, err
			}
		} else {
			p.DefaultTimerange = nil
		}
		// check if slug is changed
		if p.Slug != req.Slug {
			if !gonanoid.CheckIDMatchPattern(req.Slug, false, false) {
				return nil, status.Error(
					codes.InvalidArgument,
					"Invalid project slug",
				)
			}
			p.Slug = req.Slug
		}
		err = s.repository.SaveProject(s.db, p)
		if err != nil {
			return nil, err
		}

		channels := utils.MapSliceNoError(req.NotificationChannels, func(c *protoscommon.Channel) models.Channel {
			return models.Channel{ID: c.Id}
		})
		if err = s.db.Model(&p).Association("NotificationChannels").Replace(channels); err != nil {
			return nil, err
		}
	}
	return p.ToPB(), err
}

func (s *Service) requireProjectPermission(
	ctx context.Context,
	identity *models.Identity,
	p *models.Project,
	perm auth.Action,
) error {
	access, err := s.auth.CheckProjectAccess(ctx, identity, p, perm)
	if err != nil {
		return err
	}
	if !access {
		return status.Errorf(
			codes.PermissionDenied,
			"user %s does not have %s access to project %s",
			identity.Sub,
			perm,
			p.ID,
		)
	}
	return nil
}

func (s *Service) DeleteProject(
	ctx context.Context,
	project *protos.GetProjectByIdRequest,
) (*protoscommon.Project, error) {
	p, err := preloader.PreLoadedProjectByID(ctx, s.db, project.ProjectId)
	if p == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}
	db := s.db.WithContext(ctx)
	return p.ToPB(), db.Transaction(func(tx *gorm.DB) error {
		return s.repository.DeleteProject(ctx, tx, p)
	})
}

func (s *Service) CloneProject(ctx context.Context, req *protos.CloneProjectRequest) (*protoscommon.Project, error) {
	var err error
	var identity *models.Identity
	var project *models.Project
	if req.GetProjectId() != "" {
		_, project, err = s.auth.RequiredLoginForProject(ctx, req.GetProjectId(), auth.ADMIN)
		if err != nil {
			return nil, err
		}
	} else {
		identity = preloader.PreLoadedIdentity(ctx)
		project, err = s.createProject(ctx, req.GetProject(), identity, false)

		if err != nil {
			return nil, err
		}
	}
	fromProject, err := preloader.PreLoadedProjectByID(ctx, s.db, req.FromProjectId)
	if fromProject == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Source project not found")
	}

	db := s.db.WithContext(ctx)
	err = s.repository.NewReferenceProcessor(ctx, db, project.ID, fromProject.ID)
	if err != nil {
		return nil, err
	}
	fromDashboards, err := s.repository.GetInternalDashboardsByProjectID(fromProject.ID)
	if err != nil {
		return nil, err
	}

	for _, d := range fromDashboards {
		_, err = s.cloneDashboard(ctx, d, project.ID)
		if err != nil {
			return nil, err
		}
	}
	return project.ToPB(), nil
}

func (s *Service) CheckProjectId(
	ctx context.Context,
	req *protos.ProjectOwnerAndSlug,
) (ret *protos.CheckResponse, err error) {
	var identity *models.Identity
	identity, err = s.auth.RequiredLogin(ctx)
	if err != nil {
		return
	}
	ret = &protos.CheckResponse{
		IsValid: gonanoid.CheckIDMatchPattern(req.Slug, false, true),
	}
	var ownerID string
	if org, _ := s.repository.GetOrganization(req.OwnerName); org != nil {
		if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.WRITE); !ok {
			return nil, status.Errorf(codes.PermissionDenied, "permission denied")
		}
		ownerID = org.ID
	} else {
		ownerID = identity.UserID
	}

	var project *models.Project
	result := s.db.Find(&project, "owner_id = ? and slug = ?", ownerID, req.Slug)
	if result.Error != nil {
		return nil, result.Error
	} else {
		ret.IsTaken = result.RowsAffected > 0
	}

	return
}

func (s *Service) AddProjectMember(
	ctx context.Context,
	req *protos.ProjectMemberRequest,
) (*protos.ProjectMemberResponse, error) {
	var project *models.Project
	result := s.db.Preload("Members").First(&project, "id = ?", req.ProjectId)
	if result.Error != nil {
		return nil, result.Error
	}

	member, err := s.repository.FindUserByUsernameOrEmail(req.UsernameOrEmail)
	if member == nil {
		return nil, status.Errorf(codes.NotFound, "user with email or username %s not found", req.UsernameOrEmail)
	}
	if err != nil {
		return nil, err
	}

	// check the user is already a member before adding
	if member.ID == project.OwnerID {
		return nil, status.Errorf(codes.InvalidArgument, "owner can't be added as member")
	}
	for _, v := range project.Members {
		if v.ID == member.ID {
			return nil, status.Errorf(codes.AlreadyExists, "member already exists")
		}
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		return s.repository.AddProjectMember(tx, project, member)
	})
	if err != nil {
		return nil, err
	}
	if result = s.db.Preload("Members").First(&project, "id =?", req.ProjectId); result.Error != nil {
		return nil, result.Error
	} else {
		return &protos.ProjectMemberResponse{
			Members: project.ToPB().Members,
		}, nil
	}
}

func (s *Service) RemoveProjectMember(
	ctx context.Context,
	req *protos.ProjectMemberRequest,
) (*protos.ProjectMemberResponse, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	var project *models.Project
	result := s.db.Preload("Members").First(&project, "id = ?", req.ProjectId)
	if result.Error != nil {
		return nil, result.Error
	}
	var member *models.User
	result = s.db.First(&member, "email = ? or username = ?", req.UsernameOrEmail, req.UsernameOrEmail)

	if result.Error != nil {
		return nil, result.Error
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		result, err := s.auth.CheckProjectAccess(ctx, identity, project, auth.ADMIN)
		if err != nil {
			return err
		}
		if result || member.ID == identity.UserID { // project admin or self
			return s.repository.RemoveProjectMember(project, member)
		} else {
			return status.Errorf(
				codes.PermissionDenied,
				"user %s does not have admin access to project %s",
				identity.Sub,
				project.ID,
			)
		}
	})
	if err != nil {
		return nil, err
	}
	if result = s.db.Preload("Members").First(&project, "id =?", req.ProjectId); result.Error != nil {
		return nil, result.Error
	} else {
		return &protos.ProjectMemberResponse{
			Members: project.ToPB().Members,
		}, nil
	}
}

func (s *Service) AddProjectView(
	ctx context.Context,
	req *protoscommon.ProjectView,
) (*protos.ProjectViewResponse, error) {
	fmt.Printf("request is %v\n", req)

	var project *models.Project
	result := s.db.Preload("Views").First(&project, "id = ?", req.ProjectId)
	if result.Error != nil {
		return nil, result.Error
	}
	view := &models.ProjectView{}
	err := s.db.Transaction(func(tx *gorm.DB) error {
		view.FromPB(req)
		return s.repository.SaveProjectView(tx, view)
	})
	if err != nil {
		return nil, err
	}
	if result = s.db.Preload("Views").First(&project, "id =?", req.ProjectId); result.Error != nil {
		return nil, result.Error
	} else {
		projectData := project.ToPB()
		return &protos.ProjectViewResponse{
			ProjectId: projectData.Id,
			Views:     projectData.Views,
		}, nil
	}
}

func (s *Service) RemoveProjectView(
	ctx context.Context,
	req *protoscommon.ProjectView,
) (*protos.ProjectViewResponse, error) {

	var project *models.Project
	result := s.db.Preload("Views").First(&project, "id = ?", req.ProjectId)
	if result.Error != nil {
		return nil, result.Error
	}
	var view *models.ProjectView
	result = s.db.First(&view, "id = ?", req.Id)
	if result.Error != nil {
		return nil, result.Error
	}
	err := s.db.Transaction(func(tx *gorm.DB) error {
		return s.repository.RemoveProjectView(s.db, view)
	})
	if err != nil {
		return nil, err
	}
	if result = s.db.Preload("Views").First(&project, "id =?", req.ProjectId); result.Error != nil {
		return nil, result.Error
	} else {
		projectData := project.ToPB()
		return &protos.ProjectViewResponse{
			ProjectId: projectData.Id,
			Views:     projectData.Views,
		}, nil
	}
}

func (s *Service) UpdateProjectView(
	ctx context.Context,
	req *protoscommon.ProjectView,
) (*protos.ProjectViewResponse, error) {
	var project *models.Project
	result := s.db.Preload("Views").First(&project, "id = ?", req.ProjectId)
	if result.Error != nil {
		return nil, result.Error
	}
	var view *models.ProjectView
	result = s.db.First(&view, "id = ?", req.Id)
	if result.Error != nil {
		return nil, result.Error
	}
	err := s.db.Transaction(func(tx *gorm.DB) error {
		data, err := protojson.Marshal(req.GetConfig())
		if err != nil {
			return err
		}
		view.Config = data
		view.Name = req.GetName()
		return s.repository.SaveProjectView(s.db, view)
	})
	if err != nil {
		return nil, err
	}
	if result = s.db.Preload("Views").First(&project, "id =?", req.ProjectId); result.Error != nil {
		return nil, result.Error
	} else {
		projectData := project.ToPB()
		return &protos.ProjectViewResponse{
			ProjectId: projectData.Id,
			Views:     projectData.Views,
		}, nil
	}
}

func (s *Service) GetProjectList(
	ctx context.Context,
	req *protos.GetProjectListRequest,
) (*protos.GetProjectListResponse, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	response := &protos.GetProjectListResponse{}
	db := s.db.WithContext(ctx)
	p, err := s.repository.GetProjects(db, identity)
	response.Projects = make([]*protoscommon.Project, len(p))
	for i, v := range p {
		if err != nil {
			return nil, err
		}
		response.Projects[i] = v.ToPB()
	}

	p, err = s.repository.GetSharedProjects(identity)
	if err != nil {
		return nil, err
	}
	response.SharedProjects = make([]*protoscommon.Project, len(p))

	for i, v := range p {
		response.SharedProjects[i] = v.ToPB()
	}

	p, err = s.repository.GetOrgProjects(identity)
	if err != nil {
		return nil, err
	}

	response.OrgProjects = make([]*protoscommon.Project, len(p))
	for i, v := range p {
		response.OrgProjects[i] = v.ToPB()
	}

	return response, nil
}

var (
	populateProjectList = []*protos.ProjectOwnerAndSlug{
		{OwnerName: "sentio", Slug: "pancakeswap"},
		{OwnerName: "sentio", Slug: "coinbase"},
	}
)

func (s *Service) GetPopulateProjectList(
	ctx context.Context,
	_ *protos.GetProjectListRequest,
) (*protos.GetProjectListResponse, error) {
	response := &protos.GetProjectListResponse{
		Projects: make([]*protoscommon.Project, 0),
	}
	for _, p := range populateProjectList {
		project, err := s.repository.GetProjectBySlug(ctx, p.OwnerName, p.Slug)
		if project != nil && err == nil {
			project.GetOwner(s.db)
			response.Projects = append(response.Projects, project.ToPB())
		}
	}
	return response, nil
}

func (s *Service) ImportProject(
	ctx context.Context,
	req *protos.ImportProjectRequest,
) (*protos.ImportProjectResponse, error) {
	/*if !gonanoid.CheckIDMatchPattern(req.Name, false, true) {
		return nil, status.Error(
			codes.InvalidArgument,
			"Invalid import name",
		)
	}*/
	if req.ImportProjects != nil {
		return s.ImportProjects(ctx, req)
	}
	project, err := preloader.PreLoadedProjectBySlug(ctx, s.db, req.OwnerName, req.Slug)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}
	importedProject, err := s.repository.GetProjectBySlug(ctx, req.ImportProject.OwnerName, req.ImportProject.Slug)
	if err != nil {
		return nil, err
	}

	if project.ID == importedProject.ID {
		return nil, status.Error(codes.InvalidArgument, "Cannot import a project into itself")
	}
	imports, err := s.repository.GetImportedProjects(s.db, project.ID)
	if err != nil {
		return nil, err
	}
	for _, v := range imports {
		if v.ImportProjectID == importedProject.ID {
			return nil, status.Error(codes.AlreadyExists, "Project already imported")
		}
	}

	_, err = s.repository.SaveImportedProject(s.db, req.Name, project.ID, importedProject.ID)
	if err != nil {
		return nil, err
	}

	return s.getImports(ctx, project)
}

func (s *Service) ImportProjects(
	ctx context.Context,
	req *protos.ImportProjectRequest,
) (*protos.ImportProjectResponse, error) {
	project, err := preloader.PreLoadedProjectBySlug(ctx, s.db, req.OwnerName, req.Slug)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}

	imports, err := s.repository.GetImportedProjects(s.db, project.ID)
	if err != nil {
		return nil, err
	}

	for _, p := range req.ImportProjects {
		importedProject, err := s.repository.GetProjectBySlug(ctx, p.OwnerName, p.Slug)
		if err != nil {
			return nil, err
		}
		if project.ID == importedProject.ID {
			return nil, status.Error(codes.InvalidArgument, "Cannot import a project into itself")
		}
		for _, v := range imports {
			if v.ImportProjectID == importedProject.ID {
				return nil, status.Error(codes.AlreadyExists, "Project already imported")
			}
		}
		var alias = req.Name
		if alias == "" {
			alias = p.OwnerName + "/" + p.Slug
		}
		_, err = s.repository.SaveImportedProject(s.db, alias, project.ID, importedProject.ID)
		if err != nil {
			return nil, err
		}
	}

	return s.getImports(ctx, project)
}

func (s *Service) UnImportProject(
	ctx context.Context,
	req *protos.UnImportProjectRequest,
) (*protos.ImportProjectResponse, error) {
	project, err := preloader.PreLoadedProjectBySlug(ctx, s.db, req.OwnerName, req.Slug)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}

	imported, err := s.getImports(ctx, project)
	if err != nil {
		return nil, err
	}
	for _, v := range imported.Imports {
		if v.Imported.OwnerName == req.UnimportOwner && v.Imported.Slug == req.UnimportSlug {
			err = s.repository.DeleteImportedProject(s.db, project.ID, v.Name)
			if err != nil {
				return nil, err
			}
			break
		}
	}

	return s.getImports(ctx, project)
}

func (s *Service) GetImportedProject(
	ctx context.Context,
	req *protos.ProjectOwnerAndSlug,
) (*protos.ImportProjectResponse, error) {
	project, err := preloader.PreLoadedProjectBySlug(ctx, s.db, req.OwnerName, req.Slug)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}
	return s.getImports(ctx, project)
}

func (s *Service) getImports(ctx context.Context, project *models.Project) (*protos.ImportProjectResponse, error) {
	imports, err := s.repository.GetImportedProjects(s.db, project.ID)
	if err != nil {
		return nil, err
	}
	response := &protos.ImportProjectResponse{
		Imports: utils.MapSliceNoError(imports, func(i *models.ImportedProject) *protoscommon.ImportedProject {
			return i.ToPB()
		}),
	}
	return response, nil
}

func (s *Service) SearchProject(ctx context.Context, req *protos.SearchProjectRequest) (*protos.SearchProjectResponse, error) {
	identity := preloader.PreLoadedIdentity(ctx)

	db := s.db.WithContext(ctx)
	q := req.Q
	response := &protos.SearchProjectResponse{
		Projects: make([]*protoscommon.ProjectInfo, 0),
	}
	if len(strings.TrimSpace(q)) == 0 {
		return response, nil
	}

	ownProjects, err := s.repository.GetProjects(db, identity)
	if err != nil {
		return nil, err
	}
	sharedProjects, err := s.repository.GetSharedProjects(identity)
	if err != nil {
		return nil, err
	}
	orgProjects, err := s.repository.GetOrgProjects(identity)
	if err != nil {
		return nil, err
	}
	publicProjects, err := s.repository.FindPublicProject(q)
	if err != nil {
		return nil, err
	}
	projectsMap := make(map[string]*models.Project)
	projects := make([]*models.Project, 0)
	projects = append(projects, ownProjects...)
	projects = append(projects, sharedProjects...)
	projects = append(projects, orgProjects...)
	for _, p := range projects {
		if matchProject(db, p, q) {
			projectsMap[p.ID] = p
		}
	}
	for _, p := range publicProjects {
		projectsMap[p.ID] = p
	}
	for _, p := range projectsMap {
		p.GetOwner(db)
		response.Projects = append(response.Projects, p.ToProjectInfo())
	}

	// sort by owner & slug
	sort.Slice(response.Projects, func(i, j int) bool {
		if response.Projects[i].Owner == response.Projects[j].Owner {
			return response.Projects[i].Slug < response.Projects[j].Slug
		}
		return response.Projects[i].Owner < response.Projects[j].Owner
	})

	return response, nil
}

func matchProject(db *gorm.DB, project *models.Project, query string) bool {
	if strings.Contains(strings.ToLower(project.Slug), strings.ToLower(query)) {
		return true
	}
	if strings.Contains(strings.ToLower(project.Description), strings.ToLower(query)) {
		return true
	}
	if strings.Contains(strings.ToLower(project.DisplayName), strings.ToLower(query)) {
		return true
	}
	project.GetOwner(db)
	return strings.Contains(strings.ToLower(project.GetOwnerName()), strings.ToLower(query))
}

func (s *Service) GetStarredProjects(
	ctx context.Context,
	req *emptypb.Empty,
) (*protos.ProjectsResponse, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	p, err := s.repository.GetStarredProjects(identity)
	if err != nil {
		return nil, err
	}

	response := &protos.ProjectsResponse{
		Projects: make([]*protoscommon.Project, len(p)),
	}
	for i, v := range p {
		response.Projects[i] = v.ToPB()
	}
	return response, nil
}

func (s *Service) StarProject(
	ctx context.Context,
	req *protos.GetProjectByIdRequest,
) (*emptypb.Empty, error) {
	identity := preloader.PreLoadedIdentity(ctx)
	if err := s.db.Preload("User").First(identity).Error; err != nil {
		return nil, err
	}
	user := identity.User
	project, err := preloader.PreLoadedProjectByID(ctx, s.db, req.ProjectId)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		return s.repository.StarProject(user, project)
	})
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *Service) UnstarProject(
	ctx context.Context,
	req *protos.GetProjectByIdRequest,
) (*emptypb.Empty, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	if err := s.db.Preload("User").First(identity).Error; err != nil {
		return nil, err
	}
	user := identity.User

	err = s.repository.UnstarProject(user, req.ProjectId)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (s *Service) GetViewedProjects(
	ctx context.Context,
	req *emptypb.Empty,
) (*protos.ProjectsInfoResponse, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	p, err := s.repository.GetViewedProjects(identity)
	if err != nil {
		return nil, err
	}

	response := &protos.ProjectsInfoResponse{
		Projects: make([]*protoscommon.ProjectInfo, len(p)),
	}
	for i, v := range p {
		response.Projects[i] = v.ToProjectInfo()
	}
	return response, nil
}

func (s *Service) GetProjectVariables(ctx context.Context, req *protos.GetProjectByIdRequest) (*protoscommon.ProjectVariables, error) {
	p, err := preloader.PreLoadedProjectByID(ctx, s.db, req.ProjectId)
	if p == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}
	vars, err := s.repository.GetProjectVariables(ctx, p.ID)
	if err != nil {
		return nil, err
	}
	result := &protoscommon.ProjectVariables{
		Variables: make([]*protoscommon.ProjectVariables_Variable, len(vars)),
		ProjectId: p.ID,
	}
	for i, v := range vars {
		result.Variables[i] = v.ToPB(true)
	}

	return result, nil
}

func (s *Service) DeleteProjectVariables(ctx context.Context, req *protos.ProjectVariableKey) (*emptypb.Empty, error) {
	p, err := preloader.PreLoadedProjectByID(ctx, s.db, req.ProjectId)
	if p == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		return tx.Where("project_id = ? and key = ?", p.ID, req.Key).Delete(&models.ProjectVariable{}).Error
	})
	return &emptypb.Empty{}, err
}

func (s *Service) SaveProjectVariables(ctx context.Context, req *protoscommon.ProjectVariables) (*emptypb.Empty, error) {
	p, err := preloader.PreLoadedProjectByID(ctx, s.db, req.ProjectId)
	if p == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}

	if len(req.Variables) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Variables cannot be empty")
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		for _, v := range req.Variables {
			var pv models.ProjectVariable
			err := tx.Where("project_id = ? and key = ?", p.ID, v.Key).First(&pv).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			pv.ProjectID = p.ID
			pv.Key = v.Key
			pv.Value = v.Value
			pv.IsSecret = v.IsSecret
			if errors.Is(err, gorm.ErrRecordNotFound) {
				tx.Create(&pv)
			} else {
				err = tx.Model(&pv).Where("project_id = ? and key = ?", p.ID, v.Key).
					Updates(&pv).Update("is_secret", v.IsSecret).Error
				if err != nil {
					return err
				}
			}
		}

		return nil
	})

	return &emptypb.Empty{}, err
}

func (s *Service) TransferProjectOwner(ctx context.Context, req *protos.TransferProjectRequest) (*protoscommon.Project, error) {
	p, err := preloader.PreLoadedProjectByID(ctx, s.db, req.ProjectId)
	if p == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}

	if len(req.ToOwnerName) == 0 {
		return nil, status.Error(codes.InvalidArgument, "ToOwnerName cannot be empty")
	}

	newOwner, err := s.repository.FindUserByUsernameOrEmail(req.ToOwnerName)
	if err != nil {
		return nil, err
	}

	if newOwner == nil {
		org, err := s.repository.GetOrganization(req.ToOwnerName)
		if err != nil || org == nil {
			return nil, status.Errorf(codes.NotFound, "couldn't find owner %s", req.ToOwnerName)
		}
		if p.OwnerID == org.ID {
			return nil, status.Error(codes.InvalidArgument, "owner not changed")
		}
		p.OwnerID = org.ID
		p.OwnerType = "organizations"
	} else {
		if p.OwnerID == newOwner.ID {
			return nil, status.Error(codes.InvalidArgument, "owner not changed")
		}
		p.OwnerID = newOwner.ID
		p.OwnerType = "users"
	}

	err = s.repository.SaveProject(s.db, p)
	if err != nil {
		return nil, err
	}
	return p.ToPB(), nil
}

func (s *Service) SaveCommunityProject(ctx context.Context, req *protos.SaveCommunityProjectRequest) (*emptypb.Empty, error) {

	if req.GetCurated() {
		_, err := s.auth.RequiredAdminLogin(ctx)
		if err != nil {
			return nil, status.Error(codes.PermissionDenied, "Only admin can set curated project")
		}
	}

	if req.DashAlias == "" || !gonanoid.CheckIDMatchPattern(req.DashAlias, true, true) {
		return nil, status.Error(codes.InvalidArgument, "Invalid dash alias format")
	}

	communityProject := &models.CommunityProject{}
	err := s.db.Where("project_id = ?", req.ProjectId).First(communityProject).Error
	if err == nil {
		updates := map[string]interface{}{
			"DashAlias": req.DashAlias,
			"Curated":   req.GetCurated(),
		}
		
		if req.Chain != nil && len(req.Chain) > 0 {
			chainMap := make(map[string][]string)
			for key, stringList := range req.Chain {
				if stringList != nil {
					chainMap[key] = stringList.Values
				}
			}
			chainBytes, err := json.Marshal(chainMap)
			if err != nil {
				return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to marshal chain data: %v", err))
			}
			updates["Chain"] = datatypes.JSON(chainBytes)
		} else {
			updates["Chain"] = nil
		}
		
		if err := s.db.Model(communityProject).Updates(updates).Error; err != nil {
			if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "unique constraint") {
				return nil, status.Error(codes.AlreadyExists, "Dash alias already exists")
			}
			return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to update community project: %v", err))
		}
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		communityProject = &models.CommunityProject{
			ProjectID: req.ProjectId,
			DashAlias: req.DashAlias,
			Curated:   req.Curated != nil && req.GetCurated(),
		}
		
		if req.Chain != nil && len(req.Chain) > 0 {
			chainMap := make(map[string][]string)
			for key, stringList := range req.Chain {
				if stringList != nil {
					chainMap[key] = stringList.Values
				}
			}
			chainBytes, err := json.Marshal(chainMap)
			if err != nil {
				return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to marshal chain data: %v", err))
			}
			communityProject.Chain = datatypes.JSON(chainBytes)
		}
		
		if err := s.db.Create(communityProject).Error; err != nil {
			if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "unique constraint") {
				return nil, status.Error(codes.AlreadyExists, "Dash alias already exists")
			}
			return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to create community project: %v", err))
		}
	} else {
		return nil, status.Error(codes.Internal, "Failed to query community project")
	}
	mapper.TriggerDashTableRefresh(ctx, s.redis)
	return &emptypb.Empty{}, nil
}

func (s *Service) DeleteCommunityProject(ctx context.Context, req *protos.SaveCommunityProjectRequest) (*emptypb.Empty, error) {

	result := s.db.Unscoped().Where("project_id = ?", req.ProjectId).Delete(&models.CommunityProject{})
	if result.Error != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to delete community project: %v", result.Error))
	}
	mapper.TriggerDashTableRefresh(ctx, s.redis)
	return &emptypb.Empty{}, nil
}

func (s *Service) CheckCommunityProjectAlias(ctx context.Context, req *protos.SaveCommunityProjectRequest) (ret *protos.CheckResponse, err error) {
	_, err = s.auth.RequiredLogin(ctx)
	if err != nil {
		return
	}
	ret = &protos.CheckResponse{
		IsValid: gonanoid.CheckIDMatchPattern(req.DashAlias, true, true),
	}

	var count int64
	err = s.db.Model(&models.CommunityProject{}).Where("dash_alias = ?", req.DashAlias).Count(&count).Error
	if err != nil {
		return nil, err
	}
	ret.IsTaken = count > 0

	return
}

func (s *Service) GetCommunityProjects(ctx context.Context, req *protos.GetCommunityProjectsRequest) (*protos.GetCommunityProjectsResponse, error) {
	limit := req.Limit
	if limit == 0 {
		limit = 100
	}
	offset := req.Offset

	var communityProjects []*models.CommunityProject
	err := s.db.Limit(int(limit)).Offset(int(offset)).Find(&communityProjects).Error
	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to query community projects: %v", err))
	}

	response := &protos.GetCommunityProjectsResponse{
		CommunityProjects: make([]*protos.CommunityProjectInfo, len(communityProjects)),
	}

	for i, cp := range communityProjects {
		info := &protos.CommunityProjectInfo{
			DashAlias: cp.DashAlias,
		}

		if len(cp.Chain) > 0 {
			chainMap := make(map[string][]string)
			if err := json.Unmarshal(cp.Chain, &chainMap); err == nil {
				info.Chain = make(map[string]*protoscommon.StringList)
				for key, values := range chainMap {
					info.Chain[key] = &protoscommon.StringList{
						Values: values,
					}
				}
			}
		}

		response.CommunityProjects[i] = info
	}

	return response, nil
}
