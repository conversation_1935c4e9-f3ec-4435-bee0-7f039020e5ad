package service

import (
	"context"
	"golang.org/x/exp/slices"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	modelscommon "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/common/preloader"
	"sentioxyz/sentio/service/web/repository"
	"strings"

	"google.golang.org/protobuf/types/known/emptypb"

	utils2 "sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/common/auth"
	commonProtos "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/web/models"
	"sentioxyz/sentio/service/web/protos"
	"sentioxyz/sentio/service/web/utils"

	"sentioxyz/sentio/common/protojson"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	"gorm.io/gorm"
)

func (s *Service) SaveDashboard(ctx context.Context, dashboard *protos.Dashboard) (*protos.Dashboard, error) {
	identity := preloader.PreLoadedIdentity(ctx)
	if identity == nil || identity.IsAnonymous() {
		return nil, status.Errorf(codes.Unauthenticated, "user not authenticated")
	}
	d := PreloadedDashboard(ctx)
	if d == nil {
		d = &models.Dashboard{}
		d.FromPB(dashboard)
	}
	project, err := preloader.PreLoadedProjectByID(ctx, s.db, dashboard.ProjectId)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}

	d.Project = project

	if d.Visibility != models.VisibilityInternal {
		if d.OwnerID == nil {
			d.OwnerID = &identity.UserID
		}
	}
	// check URL owner
	err = s.checkDashboardURL(d, identity)
	if err != nil {
		return nil, err
	}

	// Create sets for quick lookup
	createPanelsSet := make(map[string]bool)
	editPanelsSet := make(map[string]bool)

	for _, panelID := range dashboard.CreatePanels {
		createPanelsSet[panelID] = true
	}
	for _, panelID := range dashboard.EditPanels {
		editPanelsSet[panelID] = true
	}

	// Set creator and updater only for specified panels
	for _, panel := range d.Panels {
		if createPanelsSet[panel.ID] {
			// Panel is in create_panels - set creator
			panel.CreatorID = &identity.UserID
		}
		if editPanelsSet[panel.ID] {
			// Panel is in edit_panels - set updater
			panel.UpdaterID = &identity.UserID
		}
	}

	err = s.repository.SaveDashboard(d)
	if err != nil {
		return nil, err
	}

	if d.Visibility != models.VisibilityInternal {
		err = s.repository.SaveDashboardTags(d.ID, dashboard.Tags)
		if err != nil {
			return nil, err
		}
	}

	d, err = s.repository.GetDashboardByID(d.ID)
	return d.ToPB(), err
}

func (s *Service) checkDashboardURL(d *models.Dashboard, identity *modelscommon.Identity) error {
	if len(d.URL) > 0 {
		parts := strings.Split(d.URL, "/")
		if len(parts) != 2 {
			return status.Errorf(codes.InvalidArgument, "invalid dashboard url %s", d.URL)
		}
		ownerName := parts[0]
		if identity.User.Username == ownerName {
			return nil
		}
		o, err := s.repository.GetOrganization(ownerName)
		if err != nil {
			return err
		}
		if o != nil {
			for _, m := range o.Members {
				if m.UserID == identity.UserID {
					return nil
				}
			}
		}

		return status.Errorf(codes.PermissionDenied, "user does not have write access to the dashboard url %s", d.URL)
	}
	return nil
}

func (s *Service) ListDashboards(
	ctx context.Context,
	req *protos.GetDashboardRequest,
) (*protos.GetDashboardResponse, error) {
	project, err := preloader.PreLoadedProjectByIDAndSlug(ctx, s.db, req.ProjectId, req.OwnerName, req.Slug)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}
	access, ok := ctx.Value("project_accesses").([]auth.Action)
	if !ok {
		return nil, status.Error(codes.Internal, "Project access not found in context")
	}

	d, err := s.repository.GetInternalDashboardsByProjectID(project.ID)
	if err != nil {
		return nil, err
	}
	dashboards := make([]*protos.Dashboard, len(d))
	var permissions []commonProtos.Permission
	if len(d) > 0 {
		for _, a := range access {
			permissions = append(permissions, a.ToPB())
		}
	}
	for i, v := range d {
		dashboards[i] = v.ToPB()
	}
	return &protos.GetDashboardResponse{
		Dashboards:  dashboards,
		Permissions: permissions,
	}, err
}

func (s *Service) ListExternalDashboards(ctx context.Context, req *protos.ListExternalDashboardsRequest) (*protos.ListExternalDashboardsResponse, error) {
	project, err := preloader.PreLoadedProjectByIDAndSlug(ctx, s.db, req.ProjectId, req.OwnerName, req.Slug)
	projectAccess, ok := ctx.Value("project_accesses").([]auth.Action)
	if !ok || project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found or access not found in context")
	}
	identity := preloader.PreLoadedIdentity(ctx)
	if len(projectAccess) == 0 {
		return nil, status.Errorf(codes.PermissionDenied, "user does not have read access to project %s", project.ID)
	}
	var list []*models.Dashboard
	starredTimeRange := int64(-1)
	if req.OrderBy == protos.ListExternalDashboardsRequest_STARRED {
		starredTimeRange = req.OrderByTimeRange
	}
	offset := int(req.Offset)
	limit := int(req.Limit)
	if limit == 0 {
		limit = 10
	}
	isAdmin := slices.Contains(projectAccess, auth.ADMIN)

	if req.GetTagsFilter() != nil {
		tags := req.GetTagsFilter().Tags
		name := req.GetTagsFilter().Name
		list, err = s.repository.ListDashboardsByTagsAndName(project.ID, identity.UserID, tags, name, starredTimeRange, offset, limit, isAdmin)
	} else if req.GetOwnerFilter() != "" {
		list, err = s.repository.ListDashboardsByOwner(project.ID, req.GetOwnerFilter(), offset, limit)
	} else if req.GetStarFilter() {
		list, err = s.repository.ListStarredDashboards(project.ID, identity.UserID, offset, limit)
	} else {
		list, err = s.repository.ListExternalDashboardsByProjectID(project.ID, identity.UserID, starredTimeRange, offset, limit, isAdmin)
	}
	if err != nil {
		return nil, err
	}

	results := make([]*protos.DashboardResult, len(list))

	if len(list) > 0 {
		dashboardIDs := utils2.MapSliceNoError(list, func(d *models.Dashboard) string { return d.ID })
		startCounts, err := s.repository.GetStarCountForDashboards(dashboardIDs, starredTimeRange)
		if err != nil {
			return nil, err
		}
		starred, err := s.repository.GetUserStarredForDashboards(identity.UserID, dashboardIDs)
		if err != nil {
			return nil, err
		}
		for idx, d := range list {
			var access []auth.Action

			if d.OwnerID != nil && *d.OwnerID == identity.GetUserID() {
				access = []auth.Action{auth.READ, auth.WRITE, auth.ADMIN}
			} else if d.Visibility == models.VisibilityPrivate {
				if isAdmin {
					access = []auth.Action{auth.READ}
				}
			} else {
				access = []auth.Action{auth.READ}
			}

			results[idx] = &protos.DashboardResult{
				Dashboard: d.ToPB(),
				Starred:   starred[d.ID],
				StarCount: startCounts[d.ID],
				Permissions: utils2.MapSliceNoError(access, func(a auth.Action) commonProtos.Permission {
					return a.ToPB()
				}),
			}
		}
	}

	return &protos.ListExternalDashboardsResponse{
		Dashboards: results,
	}, err
}

func (s *Service) GetExternalDashboard(ctx context.Context, req *protos.GetExternalDashboardRequest) (*protos.GetExternalDashboardResponse, error) {
	project, err := preloader.PreLoadedProjectByIDAndSlug(ctx, s.db, req.ProjectId, req.OwnerName, req.Slug)
	if project == nil || err != nil {
		return nil, status.Error(codes.NotFound, "Project not found")
	}
	identity := preloader.PreLoadedIdentity(ctx)

	var d *models.Dashboard
	dashboardID := req.GetDashboardId()
	dashboardURL := req.GetUrl()
	if len(dashboardID) > 0 {
		d, err = s.repository.GetDashboardByID(dashboardID)
	} else if len(dashboardURL) > 0 {
		d, err = s.repository.GetDashboardByURL(dashboardURL)
	}
	if err != nil {
		return nil, err
	}
	if d == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard not found")
	}
	access, ok := ctx.Value("dashboard_access").([]auth.Action)
	if !ok {
		return nil, status.Errorf(codes.Internal, "dashboard access not found in context")
	}
	if len(access) == 0 {
		return nil, status.Errorf(codes.PermissionDenied, "user does not have read access to dashboard")
	}
	list := []*models.Dashboard{d}
	dashboardIDs := utils2.MapSliceNoError(list, func(d *models.Dashboard) string { return d.ID })
	startCounts, err := s.repository.GetStarCountForDashboards(dashboardIDs, int64(0))
	if err != nil {
		return nil, err
	}
	starred, err := s.repository.GetUserStarredForDashboards(identity.UserID, dashboardIDs)
	if err != nil {
		return nil, err
	}
	result := &protos.DashboardResult{
		Dashboard: d.ToPB(),
		Starred:   starred[d.ID],
		StarCount: startCounts[d.ID],
		Permissions: utils2.MapSliceNoError(access, func(a auth.Action) commonProtos.Permission {
			return a.ToPB()
		}),
	}
	return &protos.GetExternalDashboardResponse{
		Dashboard: result,
	}, err
}

func (s *Service) StarDashboard(ctx context.Context, req *protos.GetDashboardRequest) (*emptypb.Empty, error) {
	identity := preloader.PreLoadedIdentity(ctx)
	if identity == nil || identity.IsAnonymous() {
		return nil, status.Errorf(codes.Unauthenticated, "user not authenticated")
	}
	d := PreloadedDashboard(ctx)
	if d == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard not found")
	}
	err := s.repository.StarDashboard(d.ID, identity.UserID)
	return new(emptypb.Empty), err
}

func (s *Service) UnStarDashboard(ctx context.Context, req *protos.GetDashboardRequest) (*emptypb.Empty, error) {
	identity := preloader.PreLoadedIdentity(ctx)
	if identity == nil || identity.IsAnonymous() {
		return nil, status.Errorf(codes.Unauthenticated, "user not authenticated")
	}
	err := s.repository.UnStarDashboard(req.GetDashboardId(), identity.UserID)
	return new(emptypb.Empty), err
}

func (s *Service) ListTags(ctx context.Context, _ *emptypb.Empty) (*protos.TagsResponse, error) {
	rows, err := s.db.Table("dashboard_tags").Select("tag, count(distinct dashboard_id ) as count").
		Joins("join tags on tags.id = dashboard_tags.tag_id").
		Group("tag").Order("count desc").Limit(1000).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	ret := &protos.TagsResponse{}
	for rows.Next() {
		var tag string
		var count int32
		err = rows.Scan(&tag, &count)
		if err != nil {
			return nil, err
		}
		ret.Tags = append(ret.Tags, tag)
		ret.Counts = append(ret.Counts, count)
	}
	return ret, nil
}

func (s *Service) GetDashboard(
	ctx context.Context,
	req *protos.GetDashboardRequest,
) (*protos.GetDashboardResponse, error) {
	if req.GetDashboardId() != "" {
		d := PreloadedDashboard(ctx)
		if d == nil {
			// dashboard not found
			return nil, status.Errorf(codes.NotFound, "dashboard not found")
		}

		access := ctx.Value("dashboard_access").([]auth.Action)
		response := protos.GetDashboardResponse{
			Dashboards:  []*protos.Dashboard{},
			Permissions: []commonProtos.Permission{},
		}

		for _, a := range access {
			response.Permissions = append(response.Permissions, a.ToPB())
		}

		response.Dashboards = append(response.Dashboards, d.ToPB())
		return &response, nil
	}
	return nil, status.Errorf(codes.InvalidArgument, "dashboard id is not provided")
}

func (s *Service) DeleteDashboard(ctx context.Context, req *protos.GetDashboardRequest) (*protos.Dashboard, error) {
	d := PreloadedDashboard(ctx)
	if d == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard not found")
	}
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return s.repository.DeleteDashboard(tx, d)
	})
	if err != nil {
		return nil, err
	}
	return d.ToPB(), nil
}

func (s *Service) ExportDashboard(
	ctx context.Context,
	req *protos.ExportDashboardRequest,
) (*protos.ExportDashboardResponse, error) {
	d := PreloadedDashboard(ctx)
	if d == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard not found")
	}
	b, err := protojson.Marshal(d.ToPB())
	if err != nil {
		return nil, errors.WithStack(err)
	}
	j := &structpb.Struct{}
	err = protojson.Unmarshal(b, j)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &protos.ExportDashboardResponse{
		DashboardJson: j,
	}, nil
}

func (s *Service) ImportDashboard(
	ctx context.Context,
	req *protos.ImportDashboardRequest,
) (*protos.ImportDashboardResponse, error) {
	dst := PreloadedDashboard(ctx)
	if dst == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard not found")
	}

	identity := preloader.PreLoadedIdentity(ctx)
	if identity == nil {
		return nil, status.Errorf(codes.Unauthenticated, "user not authenticated")
	}

	src := &protos.Dashboard{}
	jsonBytes, err := req.DashboardJson.MarshalJSON()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = protojson.Unmarshal(jsonBytes, src)
	if err != nil {
		return nil, err
	}
	utils.RepopulatePanelIds(src, dst)
	var newLayouts []byte
	if src.Layouts == nil {
		newLayouts = []byte("{ \"responsiveLayouts\": { \"md\": {} } }")
	} else {
		newLayouts, err = protojson.Marshal(src.Layouts)
	}
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if req.OverrideLayouts {
		dst.Layouts = newLayouts
		dst.Panels = []*models.Panel{}
	} else {
		dst.Layouts, err = utils.MergeLayouts(dst.Layouts, newLayouts)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	for _, p := range src.Panels {
		panel := &models.Panel{}
		panel.FromPB(p)
		// Set creator and updater for imported panels
		panel.CreatorID = &identity.UserID
		panel.UpdaterID = &identity.UserID
		dst.Panels = append(dst.Panels, panel)
	}

	dst.Extra, err = protojson.Marshal(src.Extra)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = s.repository.SaveDashboard(dst)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &protos.ImportDashboardResponse{
		Dashboard: dst.ToPB(),
	}, nil
}

func (s *Service) SaveDashboardSharing(
	ctx context.Context,
	req *protos.DashboardSharing,
) (*protos.DashboardSharing, error) {
	dashboard := PreloadedDashboard(ctx)
	if dashboard == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard not found")
	}

	if dashboard.Share == nil { // create new
		dashboard.Share = &models.ShareDashboard{}
		dashboard.Share.FromPB(req)
	} else { // update existing
		dashboard.Share.Public = req.IsPublic
	}
	err := s.db.Save(dashboard).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return dashboard.Share.ToPB(), nil
}

func (s *Service) RemoveDashboardSharing(
	ctx context.Context,
	req *protos.DashboardSharing,
) (*protos.DashboardSharing, error) {
	dashboard := PreloadedDashboard(ctx)
	if dashboard == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard not found")
	}
	if dashboard.Share == nil {
		return nil, status.Errorf(codes.NotFound, "dashboard sharing not found")
	}

	err := s.db.Delete(dashboard.Share).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return dashboard.Share.ToPB(), nil
}

func (s *Service) GetDashboardSharing(
	ctx context.Context,
	req *protos.GetDashboardSharingRequest,
) (*protos.GetDashboardSharingResponse, error) {
	sharing := models.ShareDashboard{ID: req.SharingId}

	err := s.db.Preload("Dashboard").First(&sharing).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) || sharing.Dashboard == nil {
			return nil, status.Error(codes.NotFound, "sharing not found")
		}
		return nil, err
	}

	if sharing.Public { // who has the url can access the metrics
		d, _ := s.repository.GetDashboardByID(sharing.Dashboard.ID)
		if d == nil {
			return nil, status.Error(codes.NotFound, "dashboard not found")
		}
		return &protos.GetDashboardSharingResponse{
			Dashboard: d.ToPB(),
			Project:   d.Project.ToPB(),
		}, nil
	}

	return nil, status.Errorf(codes.Unauthenticated, "Target dashboard is not public")
}

func (s *Service) cloneDashboard(ctx context.Context, d *models.Dashboard, toProjectID string) (*models.Dashboard, error) {
	toDashboard, err := s.repository.CreateEmptyDashboard(toProjectID, d.Name, d.IsDefault)
	if err != nil {
		return nil, err
	}

	resp, err := s.ExportDashboard(ctx, &protos.ExportDashboardRequest{
		DashboardId: d.ID,
	})

	if err != nil {
		return nil, err
	}

	_, err = s.ImportDashboard(ctx, &protos.ImportDashboardRequest{
		DashboardId:     toDashboard.ID,
		DashboardJson:   resp.DashboardJson,
		OverrideLayouts: true,
	})
	if err != nil {
		return nil, err
	}

	return toDashboard, nil
}

func DashboardLoader(ctx context.Context, method protoreflect.MethodDescriptor, msg proto.Message, db *gorm.DB) (context.Context, error) {
	var projectID, dashboardID, dashboardURL, projectSlug, projectOwner string
	d := &models.Dashboard{}
	var err error
	switch r := msg.(type) {
	case *protos.Dashboard:
		projectID = r.ProjectId
		dashboardID = r.Id
		d.FromPB(r)
	case *protos.GetDashboardRequest:
		dashboardID = r.DashboardId
		projectID = r.ProjectId
		projectSlug = r.Slug
	case *protos.ListExternalDashboardsRequest:
		projectID = r.ProjectId
		projectSlug = r.Slug
		projectOwner = r.OwnerName
	case *protos.ExportDashboardRequest:
		dashboardID = r.DashboardId
	case *protos.ImportDashboardRequest:
		dashboardID = r.DashboardId
	case *protos.GetExternalDashboardRequest:
		dashboardID = r.DashboardId
		projectID = r.ProjectId
		projectSlug = r.Slug
		projectOwner = r.OwnerName
		dashboardURL = r.Url
	case *protos.DashboardSharing:
		dashboardID = r.DashboardId
	}
	repo := repository.NewRepository(db)

	if dashboardID != "" {
		d, err = repo.GetDashboardByID(dashboardID)
		if err != nil {
			return ctx, err
		}
		if d == nil {
			return ctx, status.Errorf(codes.NotFound, "dashboard with ID %s not found", dashboardID)
		}
		ctx = context.WithValue(ctx, "dashboard", d)
		projectID = d.ProjectID
	}

	if dashboardURL != "" {
		d, err = repo.GetDashboardByURL(dashboardURL)
		if err != nil {
			return ctx, err
		}
		if d == nil {
			return ctx, status.Errorf(codes.NotFound, "dashboard with URL %s not found", dashboardURL)
		}
		ctx = context.WithValue(ctx, "dashboard", d)
		projectID = d.ProjectID
	}

	if projectID != "" {
		project, err := repo.GetProjectByID(db, projectID)
		if err != nil {
			return nil, status.Errorf(codes.NotFound, "failed to get project by ID %s: %v", projectID, err)
		}
		ctx = context.WithValue(ctx, preloader.ProjectKeyName, project)
	}

	if projectSlug != "" && projectOwner != "" {
		project, err := repo.GetProjectBySlug(ctx, projectOwner, projectSlug)
		if err != nil {
			return nil, status.Errorf(codes.NotFound, "failed to get project by slug %s/%s: %v", projectOwner, projectSlug, err)
		}
		ctx = context.WithValue(ctx, preloader.ProjectKeyName, project)
	}

	return ctx, nil
}

func PreloadedDashboard(ctx context.Context) *models.Dashboard {
	d, ok := ctx.Value("dashboard").(*models.Dashboard)
	if !ok {
		return nil
	}
	if d == nil {
		return nil
	}
	return d
}
