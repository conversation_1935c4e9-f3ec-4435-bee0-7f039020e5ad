package service

import (
	"context"
	"fmt"
	"sentioxyz/sentio/common/notification"
	"strings"
	"time"

	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/messenger"
	modelsweb "sentioxyz/sentio/service/web/models"

	"gorm.io/gorm/clause"

	gonanoid2 "github.com/matoous/go-nanoid/v2"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/service/common/models"
	protoscommon "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/web/protos"
)

func (s *Service) CheckOwner(
	ctx context.Context,
	req *protos.CheckOwnerRequest,
) (*protos.CheckResponse, error) {
	_, err := s.auth.RequiredValidIdentity(true, ctx)
	if err != nil {
		return nil, err
	}
	ret := &protos.CheckResponse{
		IsValid: gonanoid.CheckIDMatchPattern(req.OwnerName, true, false) && gonanoid.CheckIDBlacklist(req.OwnerName),
	}
	var owner *models.Owner
	result := s.db.Find(&owner, "LOWER(name) = LOWER(?)", req.OwnerName)
	if result.Error != nil {
		return nil, result.Error
	} else {
		ret.IsTaken = result.RowsAffected > 0
		return ret, nil
	}
}

func (s *Service) CreateUser(ctx context.Context, req *protos.SignUpRequest) (*protoscommon.User, error) {
	sub, email, err := s.auth.RequiredIDToken(ctx)
	if err != nil {
		return nil, err
	}
	user := req.User
	if sub != user.Sub {
		// login user is not the same as the user to be saved
		return nil, status.Error(codes.PermissionDenied, "Permission denied")
	}
	identity := &models.Identity{Sub: sub}
	var u *models.User
	if len(user.Email) == 0 {
		u, err = s.repository.FindUserBySub(sub)
	} else {
		u, err = s.repository.FindUserByEmail(email)
	}
	if err != nil {
		return nil, err
	}
	var invitation *modelsweb.Invitation
	if req.Invitation != "" {
		// check invitation
		invitation = s.repository.GetInvitation(req.Invitation, u)

		// It's possible that the invitation is already used，just ignore it if not valid
		/*if invitation == nil {
			return nil, status.Error(codes.InvalidArgument, "The invitation is invalid")
		}*/
	}

	if u == nil {
		// create new user
		u := &models.User{}
		u.FromPB(user)
		u.Email = email // trust the email from id token

		if s.enableWaiting {
			if strings.HasSuffix(email, "@sentio.xyz") || invitation != nil {
				u.AccountStatus = protoscommon.User_SET_USERNAME.String()
			} else {
				u.AccountStatus = protoscommon.User_PENDING.String()
			}
		} else {
			u.AccountStatus = protoscommon.User_SET_USERNAME.String()
		}

		err = s.db.Transaction(func(tx *gorm.DB) error {
			if u.Username == "" {
				// generate a temporary username
				u.Username, _ = gonanoid2.Generate("abcdefghijklmnopqrstuvwxyz", 12)
			}
			err = s.repository.CreateUserWithIdentity(tx, u, identity)
			return err
		})

		return u.ToPB(), err
	} else {
		// update username after user is created, only allow when username was not set
		err = s.db.Transaction(func(tx *gorm.DB) error {
			if user.Username != "" && u.AccountStatus == protoscommon.User_SET_USERNAME.String() {
				username := strings.ToLower(user.Username)
				err = s.changeUsername(ctx, tx, u, username)
				if err != nil {
					return err
				}
			}

			// add new identity user
			if err = s.repository.AddIdentity(tx, u, identity); err != nil {
				return err
			}

			// check invitation and add the user to organization
			if invitation != nil {
				if invitation.Organization != nil {
					var role = models.OrgRole(invitation.OrgRole)
					if err = s.repository.AddOrganizationMember(tx, invitation.Organization, u, role); err != nil {
						return err
					}
				}
				if invitation.Project != nil {
					if err = s.repository.AddProjectMember(tx, invitation.Project, u); err != nil {
						return err
					}
				}
				if err = tx.Omit(clause.Associations).Model(invitation).Update("to_user_id", u.ID).Error; err != nil {
					return err
				}
			}

			// send welcome notification
			_ = s.notification.RecordUserNotification(
				notification.Web,
				notification.Info,
				u,
				protoscommon.NotificationType_GENERAL,
				"Welcome to sentio.xyz!",
			)

			return s.repository.UpdateUser(tx, u, user)
		})
		return u.ToPB(), err
	}
}

func (s *Service) changeUsername(ctx context.Context, tx *gorm.DB, user *models.User, username string) error {

	if !gonanoid.CheckIDBlacklist(username) {
		return status.Error(codes.PermissionDenied, "Username is not allowed")
	}

	if !gonanoid.CheckIDMatchPattern(username, true, true) {
		return status.Error(codes.InvalidArgument, "Invalid user name")
	}

	err := tx.Create(&models.Owner{Name: username}).Error
	if err != nil {
		return err
	}
	oldUsername := user.Username
	if err = tx.Model(&user).Updates(models.User{
		Username:      username,
		AccountStatus: protoscommon.User_ACTIVE.String()}).Error; err != nil {
		return err
	}

	if err = tx.Delete(&models.Owner{Name: oldUsername}).Error; err != nil {
		return err
	}

	user.Username = username

	// if user has projects, we need to update casbin rules
	var projects []*models.Project
	if err := tx.Where(&models.Project{OwnerID: user.ID}).Find(&projects).Error; err != nil {
		return err
	}
	for _, p := range projects {
		p.OwnerAsUser = &models.User{
			ID:       user.ID,
			Username: oldUsername,
		}

		p.OwnerAsUser = &models.User{
			ID:       user.ID,
			Username: username,
		}

	}
	return err
}

func (s *Service) GetUser(ctx context.Context, req *protos.GetUserRequest) (*protoscommon.User, error) {
	if req.GetEmail() != "" {
		sub, email, err := s.auth.RequiredIDToken(ctx)
		if err != nil {
			return nil, err
		}
		if email != req.GetEmail() {
			return nil, status.Error(codes.PermissionDenied, "Permission denied")
		}
		u, err := s.repository.FindUserByEmail(email)
		if err != nil {
			return nil, err
		}
		if u == nil {
			return nil, status.Error(codes.NotFound, "User not found")
		}
		result := u.ToPB()
		for _, identity := range u.Identities {
			if identity.Sub == sub {
				result.Sub = sub
				break
			}
		}
		return result, nil
	}

	if req.GetSubject() != "" {
		sub, _, err := s.auth.RequiredIDToken(ctx)
		if err != nil {
			return nil, err
		}

		u, err := s.repository.FindUserBySub(sub)
		if err != nil {
			return nil, err
		}
		if u == nil {
			return nil, status.Error(codes.NotFound, "User not found")
		}
		result := u.ToPB()
		result.Sub = sub
		return result, nil
	}

	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	if identity.APIKey != nil && identity.APIKey.OwnerType == models.APIKeyOwnerTypeOrg {
		// return the organization info as a user
		identity.APIKey.GetOwner(s.db)
		org := identity.APIKey.OwnerAsOrg
		_ = org.GetTier(s.db)
		accoutStatus := protoscommon.User_ACTIVE
		if org.Account.Status == models.AccountStatusSuspended {
			accoutStatus = protoscommon.User_BANNED
		}
		return &protoscommon.User{
			Id:             org.ID,
			Email:          org.Account.Contact,
			EmailVerified:  true,
			Nickname:       org.DisplayName,
			Picture:        org.LogoURL,
			Sub:            identity.Sub,
			UpdatedAt:      org.UpdatedAt.Unix(),
			CreatedAt:      org.CreatedAt.Unix(),
			Username:       org.Name,
			AccountStatus:  accoutStatus,
			Tier:           protoscommon.Tier(org.Tier),
			IsOrganization: true,
		}, nil
	}

	user, err := s.repository.FindUserByIdentity(identity)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "User not found")
	}
	ret := user.ToPB()
	ret.Sub = identity.Sub
	return ret, nil
}

func (s *Service) GetUserInfo(ctx context.Context, req *protos.GetUserInfoRequest) (*protoscommon.UserInfo, error) {
	if req.GetUserId() != "" {
		// find user by user id from DB
		user, err := s.repository.FindUserByID(req.GetUserId())
		if err != nil {
			return nil, err
		}
		if user == nil {
			return nil, status.Error(codes.NotFound, "User not found")
		}
		return user.ToUserInfo(), nil
	}

	// use username or email to find user
	userNameOrEmail := req.GetUserName()
	if userNameOrEmail == "" {
		userNameOrEmail = req.GetEmail()
	}
	user, err := s.repository.FindUserByUsernameOrEmail(userNameOrEmail)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, status.Error(codes.NotFound, "User not found")
	}
	return user.ToUserInfo(), nil
}

func (s *Service) SearchUsersInfo(ctx context.Context, req *protos.SearchUsersInfoRequest) (*protos.SearchUsersInfoResponse, error) {
	var users []*models.User
	var users2 []*models.User

	db := s.db.WithContext(ctx)
	db2 := s.db.WithContext(ctx)
	// ignore limit and offset params for security reason
	if req.GetQuery() != "" {
		db = db.Where("username = ? OR nickname = ? OR first_name = ? OR last_name = ?", req.Query, req.Query, req.Query, req.Query)
		q := "%" + req.Query + "%" // search by like
		db2 = db2.Where("username like ? OR nickname like ? OR first_name like ? OR last_name like ?", q, q, q, q)
	}
	db.Order("created_at desc").Limit(5).Find(&users)
	db2.Order("created_at desc").Limit(5).Find(&users2)

	// merge the two results and unique by user id
	users = append(users, users2...)
	seen := make(map[string]bool)
	var uniqUsers []*models.User
	for _, v := range users {
		if _, ok := seen[v.ID]; !ok {
			seen[v.ID] = true
			uniqUsers = append(uniqUsers, v)
		}
	}

	response := &protos.SearchUsersInfoResponse{
		Users: make([]*protoscommon.UserInfo, len(uniqUsers)),
	}
	for i, v := range uniqUsers {
		response.Users[i] = v.ToUserInfo()
	}
	return response, nil
}

func (s *Service) SaveOrganization(
	ctx context.Context,
	req *protoscommon.Organization,
) (*protoscommon.Organization, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		if org, err := s.repository.GetOrganization(req.Name); org == nil && err == nil {
			if !gonanoid.CheckIDMatchPattern(req.Name, true, false) {
				return status.Error(codes.InvalidArgument, "Invalid organization name")
			}
			if !gonanoid.CheckIDBlacklist(req.Name) {
				return status.Error(codes.PermissionDenied, "Organization name is not allowed")
			}
			// create
			org := &models.Organization{}
			req.Name = strings.ToLower(req.Name)
			org.FromPB(req)

			// check free-tier limit
			// remove since we open it to all users
			//if identity.User.Tier == int32(protoscommon.Tier_FREE) {
			//	return status.Error(codes.PermissionDenied, "Free tier user cannot create organization")
			//}

			err = s.repository.CreateOrganization(tx, org, identity)
			return err
		} else if err == nil { // update
			if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.WRITE); ok {
				return tx.Model(org).Omit("Members").Updates(models.Organization{LogoURL: req.LogoUrl, DisplayName: req.DisplayName}).Error
			} else {
				return status.Error(codes.PermissionDenied, "Permission denied")
			}
		} else {
			return err
		}
	})
	if err != nil {
		return nil, err
	}

	org, err := s.repository.GetOrganization(req.Name)
	return org.ToPB(), err
}

func (s *Service) GetOrganization(
	ctx context.Context,
	req *protos.GetOrganizationRequest,
) (*protos.GetOrganizationResponse, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	// if caller auth by an org api key, should return the organization only
	if identity.APIKey != nil && identity.APIKey.OwnerType == models.APIKeyOwnerTypeOrg {
		identity.APIKey.GetOwner(s.db)
		return &protos.GetOrganizationResponse{
			Organizations: []*protoscommon.Organization{
				identity.APIKey.OwnerAsOrg.ToPB(),
			},
		}, nil
	}

	if req.OrgIdOrName != "" {
		org, err := s.repository.GetOrganization(req.OrgIdOrName)
		if err != nil {
			return nil, err
		}
		if org == nil {
			return nil, status.Error(codes.NotFound, "Organization not found")
		}
		return &protos.GetOrganizationResponse{Organizations: []*protoscommon.Organization{
			org.ToPB(),
		}}, nil
	} else {
		orgs, err := s.repository.GetOrganizationsByUser(identity.UserID)
		if err != nil {
			return nil, err
		}
		return &protos.GetOrganizationResponse{
			Organizations: utils.MapSliceNoError(orgs, func(org *models.Organization) *protoscommon.Organization {
				return org.ToPB()
			}),
		}, nil
	}
}

func (s *Service) DeleteOrganization(
	ctx context.Context,
	req *protos.GetOrganizationRequest,
) (*protoscommon.Organization, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	org, err := s.repository.GetOrganization(req.OrgIdOrName)
	if err != nil {
		return nil, err
	}

	if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.ADMIN); !ok {
		return nil, status.Error(codes.PermissionDenied, "Permission denied")
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		if err = s.repository.DeleteOrganization(tx, org); err != nil {
			return err
		}
		for _, p := range org.Projects {
			if err = s.repository.DeleteProject(ctx, tx, p); err != nil {
				return err
			}
		}
		return nil
	})

	return org.ToPB(), err
}

func (s *Service) AddOrganizationMember(
	ctx context.Context,
	req *protos.OrganizationMemberRequest,
) (*protoscommon.Organization, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	org, err := s.repository.GetOrganization(req.OrgIdOrName)
	if err != nil {
		return nil, err
	}
	if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.ADMIN); !ok {
		return nil, status.Error(codes.PermissionDenied, "Permission denied")
	}

	member, err := s.repository.FindUserByUsernameOrEmail(req.UsernameOrEmail)
	if member == nil {
		return nil, status.Errorf(codes.NotFound, "user with email or username %s not found", req.UsernameOrEmail)
	}
	if err != nil {
		return nil, err
	}

	var role models.OrgRole
	if req.Role == protoscommon.OrganizationRole_ORG_ADMIN {
		role = models.OrgAdmin
	} else {
		role = models.OrgMember
	}

	for _, m := range org.Members {
		if m.UserID == member.ID {
			return nil, status.Errorf(codes.AlreadyExists, "member %s already exists ", req.UsernameOrEmail)
		}
	}

	if err = s.repository.AddOrganizationMember(s.db, org, member, role); err != nil {
		return nil, err
	}

	return org.ToPB(), nil
}

func (s *Service) RemoveOrganizationMember(
	ctx context.Context,
	req *protos.OrganizationMemberRequest,
) (*protoscommon.Organization, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	org, err := s.repository.GetOrganization(req.OrgIdOrName)
	if err != nil {
		return nil, err
	}
	if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.ADMIN); !ok {
		return nil, status.Error(codes.PermissionDenied, "Permission denied")
	}

	member, err := s.repository.FindUserByUsernameOrEmail(req.UsernameOrEmail)
	if member == nil {
		return nil, status.Errorf(codes.NotFound, "user with email or username %s not found", req.UsernameOrEmail)
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		if err = s.repository.RemoveOrganizationMember(tx, org, member); err != nil {
			return err
		}
		return nil
	})

	return org.ToPB(), err
}

func (s *Service) UpdateOrganizationMember(
	ctx context.Context,
	req *protos.OrganizationMemberRequest,
) (*protoscommon.Organization, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	org, err := s.repository.GetOrganization(req.OrgIdOrName)
	if err != nil {
		return nil, err
	}
	if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.ADMIN); !ok {
		return nil, status.Error(codes.PermissionDenied, "Permission denied")
	}

	member, err := s.repository.FindUserByUsernameOrEmail(req.UsernameOrEmail)
	if member == nil {
		return nil, status.Errorf(codes.NotFound, "user with email or username %s not found", req.UsernameOrEmail)
	}

	var role models.OrgRole
	if req.Role == protoscommon.OrganizationRole_ORG_ADMIN {
		role = models.OrgAdmin
	} else {
		role = models.OrgMember
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		if err = s.repository.UpdateOrganizationMemberRole(tx, org, member, role); err != nil {
			return err
		}
		return nil
	})

	return org.ToPB(), err
}

func (s *Service) SaveInvitation(ctx context.Context, req *protos.InvitationRequest) (*protos.Invitation, error) {
	identity, err := s.auth.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	req.ToEmail = strings.TrimSpace(req.ToEmail)
	invitation := modelsweb.Invitation{
		FromUserID: identity.UserID,
		ToEmail:    req.ToEmail,
	}

	db := s.db.WithContext(ctx)
	if req.ToOrgName != "" {
		org, err := s.repository.GetOrganization(req.ToOrgName)
		if err != nil {
			return nil, err
		}
		if ok, _ := s.auth.CheckOrganizationAccess(ctx, identity, org.Name, auth.ADMIN); !ok {
			return nil, status.Error(codes.PermissionDenied, "Permission denied")
		}
		invitation.OrganizationID = &org.ID
		if req.ToOrgRole == protoscommon.OrganizationRole_ORG_ADMIN {
			invitation.OrgRole = string(models.OrgAdmin)
		} else {
			invitation.OrgRole = string(models.OrgMember)
		}
	}
	if req.ToProject != "" {
		project, err := s.repository.GetProjectByID(db, req.ToProject)
		if err != nil {
			return nil, err
		}
		if ok, _ := s.auth.CheckProjectAccess(ctx, identity, project, auth.ADMIN); !ok {
			return nil, status.Error(codes.PermissionDenied, "Permission denied")
		}
		invitation.ProjectID = &project.ID
	}
	// find if there is an existing one
	result := db.Preload(clause.Associations).Find(&invitation,
		"from_user_id =? and to_email = ? and (organization_id = ? or project_id = ?)",
		invitation.FromUserID, invitation.ToEmail, invitation.OrganizationID, invitation.ProjectID)

	if result.Error != nil {
		return nil, result.Error
	}
	if result.RowsAffected == 0 {
		if err = db.Omit(clause.Associations).Create(&invitation).Error; err != nil {
			return nil, err
		}
	} else {
		// update the invitation created_at, so it will be valid for another 7 days
		if err = db.Model(&invitation).Update("created_at", time.Now()).Error; err != nil {
			return nil, err
		}
	}
	err = s.sendInvitationEmail(ctx, &invitation)
	return invitation.ToPB(), err
}

func (s *Service) sendInvitationEmail(ctx context.Context, invitation *modelsweb.Invitation) error {
	org := ""
	if invitation.Organization != nil {
		org = "/" + invitation.Organization.Name
	}
	from := invitation.FromUser.Name()
	subject := fmt.Sprintf("%s is inviting you to join sentio.xyz%s", from, org)
	var href string
	if s.domainName == "localhost" {
		href = fmt.Sprintf("http://localhost:10000/signup?invitation=%s", invitation.ID)
	} else {
		href = fmt.Sprintf("https://%s/signup?invitation=%s", s.domainName, invitation.ID)
	}
	var welcome string
	if invitation.OrganizationID != nil {
		welcome = fmt.Sprintf("You have been added as a teammate within %s's organization %s.", from, org)
	} else if invitation.ProjectID != nil {
		welcome = fmt.Sprintf("You have been added as a collaborator within %s's project %s.", from, invitation.Project.Slug)
	} else {
		welcome = fmt.Sprintf("You have been invited by %s to join sentio.xyz.", from)
	}

	text := fmt.Sprintf(`<!DOCTYPE html><html><head><meta charset="UTF-8"></head>
									<body> 
											<h2>Welcome!</h2>
											<p>%s
													Your invitation will expire in 7 days. 
													Please follow the link below to get started and to set up your sentio.xyz profile.
											</p>
											<a clicktracking="off" target="_blank"  style="display:inline-block;background:#054bbd;
color:#fffdf9;font-family:Inter,Arial,sans-serif;font-size:16px;
font-weight:600;line-height:22px;margin:0;text-decoration:none;text-transform:none;padding:10px 20px;border-radius:3px"
href="%s">Set up your profile</a>
											<a href="%s">%s</a>
						</body></html>`, welcome, href, href, href)

	msg := messenger.NewEmailMessenger(s.sendgridAPIKey, []string{invitation.ToEmail})
	return msg.SendText(ctx, "Sentio.xyz", fmt.Sprintf("%s@%s", "no-reply", s.emailDomain), subject, text)

}
