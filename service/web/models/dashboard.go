package models

import (
	"time"

	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/web/protos"

	"sentioxyz/sentio/common/protojson"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type DashboardVisibility int32

const (
	VisibilityInternal DashboardVisibility = 0
	VisibilityPrivate  DashboardVisibility = 1
	VisibilityPublic   DashboardVisibility = 2
)

type Dashboard struct {
	gorm.Model
	ID          string `gorm:"primaryKey"`
	Name        string
	Description string
	ProjectID   string          `gorm:"index"`
	Project     *models.Project `gorm:"constraint:OnDelete:CASCADE;"`
	Panels      []*Panel        `gorm:"constraint:OnDelete:CASCADE;"`
	Layouts     datatypes.JSON
	Extra       datatypes.JSON
	Share       *ShareDashboard
	IsDefault   bool
	IsPinned    bool
	OwnerID     *string `gorm:"index"`
	Owner       *models.User
	Visibility  DashboardVisibility `gorm:"type:integer;default:0"`
	Tags        []Tag               `gorm:"many2many:dashboard_tags;"`
	URL         string              `gorm:"index"`
}

func (d *Dashboard) BeforeCreate(tx *gorm.DB) (err error) {
	if d.ID == "" {
		d.ID, err = gonanoid.GenerateID()
	}

	return err
}

func (d *Dashboard) ToPB() *protos.Dashboard {
	ret := protos.Dashboard{
		Id:          d.ID,
		Name:        d.Name,
		ProjectId:   d.ProjectID,
		Description: d.Description,
		CreatedAt:   timestamppb.New(d.CreatedAt),
		UpdatedAt:   timestamppb.New(d.UpdatedAt),
		Default:     d.IsDefault,
		IsPinned:    d.IsPinned,
		Visibility:  protos.Dashboard_DashboardVisibility(d.Visibility),
		Url:         d.URL,
	}

	if d.OwnerID != nil {
		ret.OwnerId = *d.OwnerID
	}

	if d.Layouts != nil {
		ret.Layouts = &protos.Dashboard_ResponsiveLayouts{}
		_ = protojson.Unmarshal(d.Layouts, ret.Layouts)
	}

	if d.Panels != nil {
		ret.Panels = make(map[string]*protos.Panel, len(d.Panels))
		for _, panel := range d.Panels {
			ret.Panels[panel.ID] = panel.ToPB()
		}
	}

	if d.Extra != nil {
		ret.Extra = &protos.Dashboard_Extra{}
		_ = protojson.Unmarshal(d.Extra, ret.Extra)
	}

	if d.Share != nil {
		ret.Sharing = d.Share.ToPB()
	}

	if d.Tags != nil {
		ret.Tags = make([]string, len(d.Tags))
		for i, tag := range d.Tags {
			ret.Tags[i] = tag.Tag
		}
	}

	if d.Project != nil {
		ret.ProjectOwner = d.Project.GetOwnerName()
		ret.ProjectSlug = d.Project.Slug
	}

	return &ret
}

func (d *Dashboard) FromPB(dashboard *protos.Dashboard) {
	d.ID = dashboard.Id
	d.Name = dashboard.Name
	d.Description = dashboard.Description
	d.ProjectID = dashboard.ProjectId
	d.IsDefault = dashboard.Default
	d.IsPinned = dashboard.IsPinned
	if dashboard.OwnerId != "" {
		d.OwnerID = &dashboard.OwnerId
	}
	d.Visibility = DashboardVisibility(dashboard.Visibility)
	if dashboard.Layouts != nil {
		data, _ := protojson.Marshal(dashboard.Layouts)
		d.Layouts = datatypes.JSON(data)
	}
	if dashboard.Panels != nil {
		d.Panels = make([]*Panel, len(dashboard.Panels))
		i := 0
		for _, panel := range dashboard.Panels {
			d.Panels[i] = &Panel{}
			d.Panels[i].FromPB(panel)
			i++
		}
	}
	if dashboard.Extra != nil {
		data, _ := protojson.Marshal(dashboard.Extra)
		d.Extra = datatypes.JSON(data)
	}
	d.URL = dashboard.Url
}

type Panel struct {
	gorm.Model
	ID          string `gorm:"primaryKey"`
	Name        string
	DashboardID string `gorm:"index"`
	Dashboard   *Dashboard
	Chart       datatypes.JSON
	CreatorID   *string `gorm:"index"`
	Creator     *models.User `gorm:"foreignKey:CreatorID;constraint:OnDelete:SET NULL;"`
	UpdaterID   *string `gorm:"index"`
	Updater     *models.User `gorm:"foreignKey:UpdaterID;constraint:OnDelete:SET NULL;"`
}

func (p *Panel) FromPB(panel *protos.Panel) {
	p.ID = panel.Id
	p.Name = panel.Name
	p.DashboardID = panel.DashboardId
	if panel.Chart != nil {
		data, _ := protojson.Marshal(panel.Chart)
		p.Chart = data
	}
}

func (p *Panel) ToPB() *protos.Panel {
	ret := protos.Panel{
		Id:          p.ID,
		DashboardId: p.DashboardID,
		Name:        p.Name,
	}
	if p.Chart != nil {
		chart := &protos.Chart{}
		_ = protojson.Unmarshal(p.Chart, chart)

		// back compatibility for note
		if chart.DatasourceType == protos.Chart_METRICS && chart.Type == protos.ChartType_NOTE {
			chart.DatasourceType = protos.Chart_NOTES
		}

		ret.Chart = chart
	}
	
	// Set creator UserInfo if Creator is preloaded
	if p.Creator != nil {
		ret.Creator = p.Creator.ToUserInfo()
	}
	
	// Set updater UserInfo if Updater is preloaded
	if p.Updater != nil {
		ret.Updater = p.Updater.ToUserInfo()
	}
	
	return &ret
}

type Snapshot struct {
	gorm.Model
	ID          string `gorm:"primaryKey"`
	Name        string
	ProjectID   string `gorm:"index"`
	ProjectSlug string
	OwnerID     string `gorm:"index"`
	OwnerName   string
	Chart       datatypes.JSON
	Data        datatypes.JSON
	StartTime   time.Time
	EndTime     time.Time
}

func (s *Snapshot) FromPB(snapshot *protos.Snapshot) {
	s.ID = snapshot.Id
	s.Name = snapshot.Name
	s.ProjectID = snapshot.ProjectId
	s.ProjectSlug = snapshot.ProjectSlug
	s.OwnerID = snapshot.OwnerId
	s.OwnerName = snapshot.OwnerName
	if snapshot.Chart != nil {
		data, _ := protojson.Marshal(snapshot.Chart)
		s.Chart = data
	}
	if snapshot.Data != nil {
		data, _ := protojson.Marshal(snapshot.Data)
		s.Data = data
	}
	s.StartTime = snapshot.StartTime.AsTime()
	s.EndTime = snapshot.EndTime.AsTime()
}

func (s *Snapshot) ToPB() *protos.Snapshot {
	ret := protos.Snapshot{
		Id:          s.ID,
		Name:        s.Name,
		ProjectId:   s.ProjectID,
		ProjectSlug: s.ProjectSlug,
		OwnerId:     s.OwnerID,
		OwnerName:   s.OwnerName,
		StartTime:   timestamppb.New(s.StartTime),
		EndTime:     timestamppb.New(s.EndTime),
		CreatedAt:   timestamppb.New(s.CreatedAt),
	}

	if s.Chart != nil {
		ret.Chart = &protos.Chart{}
		_ = protojson.Unmarshal(s.Chart, ret.Chart)
	}
	if s.Data != nil {
		ret.Data = &structpb.Struct{}
		_ = protojson.Unmarshal(s.Data, ret.Data)
	}
	return &ret
}

type ShareDashboard struct {
	ID          string     `gorm:"primaryKey"`
	DashboardID string     `gorm:"index"`
	Dashboard   *Dashboard `gorm:"constraint:OnDelete:CASCADE;"`
	Public      bool
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

func (s *ShareDashboard) BeforeCreate(tx *gorm.DB) (err error) {
	if s.ID == "" {
		s.ID = gonanoid.GenerateWithAlphabet(gonanoid.IDAlphabetLowercase, 16)
	}

	return err
}

func (s *ShareDashboard) FromPB(share *protos.DashboardSharing) {
	s.Public = share.IsPublic
	s.DashboardID = share.DashboardId
}

func (s *ShareDashboard) ToPB() *protos.DashboardSharing {
	return &protos.DashboardSharing{
		IsPublic:    s.Public,
		Id:          s.ID,
		DashboardId: s.DashboardID,
	}
}

type Tag struct {
	gorm.Model
	Tag        string       `gorm:"index:,unique"`
	Dashboards []*Dashboard `gorm:"many2many:dashboard_tags;"`
}

type StarredDashboard struct {
	DashboardID string     `gorm:"primaryKey,priority=1;uniqueIndex:star_dash_id_org_id,priority=1;"`
	Dashboard   *Dashboard `gorm:"constraint:OnDelete:CASCADE;foreignKey:DashboardID"`
	UserID      string     `gorm:"primaryKey,priority=2;uniqueIndex:star_dash_id_org_id,priority=2;"`
	User        *models.User
	CreatedAt   time.Time
}
