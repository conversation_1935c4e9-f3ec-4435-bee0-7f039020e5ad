load("@rules_go//go:def.bzl", "go_binary", "go_library")
load("//bazel:images.bzl", "create_go_image")

go_library(
    name = "server_lib",
    srcs = [
        "dashboard_checker.go",
        "main.go",
    ],
    data = [
        "//common/clickhouse:test_config",
        "//common/timescale:test_config",
        "//service/common/networklimiter:config_file",
        "//service/solidity/server:test_config",
    ],
    importpath = "sentioxyz/sentio/service/web/server",
    visibility = ["//visibility:private"],
    deps = [
        "//common/clickhouse",
        "//common/event",
        "//common/flags",
        "//common/log",
        "//common/monitoring",
        "//service/ai",
        "//service/ai/protos",
        "//service/common/auth",
        "//service/common/preloader",
        "//service/common/redis",
        "//service/common/rpc",
        "//service/contract",
        "//service/contract/protos",
        "//service/solidity",
        "//service/usage/utils",
        "//service/web/models",
        "//service/web/protos",
        "//service/web/repository",
        "//service/web/service",
        "//service/web/utils",
        "@com_github_grpc_ecosystem_grpc_gateway_v2//runtime",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//reflection",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
    tags = [],
    visibility = ["//visibility:public"],
)

create_go_image(
    binary = ":server",
)
