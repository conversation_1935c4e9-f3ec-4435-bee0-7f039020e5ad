package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"sentioxyz/sentio/service/common/preloader"

	"sentioxyz/sentio/service/common/redis"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/reflection"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/flags"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/monitoring"
	"sentioxyz/sentio/service/ai"
	protoai "sentioxyz/sentio/service/ai/protos"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/rpc"
	"sentioxyz/sentio/service/contract"
	protoscontract "sentioxyz/sentio/service/contract/protos"
	"sentioxyz/sentio/service/solidity"
	usageUtils "sentioxyz/sentio/service/usage/utils"
	"sentioxyz/sentio/service/web/protos"
	"sentioxyz/sentio/service/web/repository"
	"sentioxyz/sentio/service/web/service"
	webUtils "sentioxyz/sentio/service/web/utils"
)

func main() {
	var (
		dbURL = flag.String(
			"database",
			"postgres://postgres:postgres@localhost:5432/postgres",
			"The postgres database address",
		)
		port                = flag.Int("port", 10010, "The grpc port")
		authIssuerURL       = flag.String("auth-issuer-url", "https://sentio-dev.us.auth0.com/", "The auth0 issue url")
		authAudience        = flag.String("auth-audience", "http://localhost:8080/v1", "The auth0 audience")
		authClientID        = flag.String("auth-client-id", "JREam3EysMTM49eFbAjNK02OCykpmda3", "The auth0 app clientId")
		dataPath            = flag.String("data", "./data", "The path to the data directory")
		enableWaiting       = flag.Bool("enable-waiting", false, "The waiting list service address")
		sendgridAPIKey      = flag.String("sendgrid-api-key", os.Getenv("SENDGRID_API_KEY"), "The sendgrid api key")
		emailDomainName     = flag.String("email-domain-name", "sentioxyz.com", "The email domain name")
		domainName          = flag.String("domain-name", "localhost", "Web service domainName name")
		timescaleConfigPath = flag.String(
			"timescale-db-config",
			"common/timescale/timescale_db_local_config.yml",
			"The timescale multi db config, will use default config if not set")
		enableProf           = flag.Bool("enable-pprof", false, "Enable pprof")
		usageServiceAddress  = flag.String("usage-service-address", "", "Usage service address")
		aiServerAddress      = flag.String("ai-server-address", "http://localhost:10100", "AI service address")
		clickhouseConfigPath = flag.String("clickhouse-config-path", "", "Clickhouse config path")
		chainConfig          = flag.String(
			"chain-config",
			"service/solidity/server/nodes.yaml",
			"node config file, include all endpoint of each network",
		)
		coderAPIToken          = flag.String("coder-api-token", "lDyIMB3pVR-j8V6fEGM4LO58x0M4bSITe", "The coder api token")
		coderHost              = flag.String("coder-host", "https://test-coder.sentio.xyz", "The coder host")
		coderTemplateID        = flag.String("coder-template-id", "26416e77-1b7e-4f69-ba42-8ec3ce262e65", "The coder template id")
		coderOrgID             = flag.String("coder-org-id", "3fd0a6ac-fe2d-4662-95f0-1ce35944e1fb", "The coder org id")
		analyticServiceAddress = flag.String("analytic-service-address", "localhost:10018", "The analytic service address")
		o11yServiceAddress     = flag.String("observability-service-address", "localhost:10018", "The observability service address")
	)
	flags.ParseAndInitLogFlag()

	monitoring.StartMonitoring()
	defer monitoring.StopMonitoring()

	// register the GreeterServerImpl on the gRPC server
	conn, err := repository.SetupDB(*dbURL)
	if err != nil {
		log.Fatale(err)
	}

	authConfig := auth.AuthConfig{
		IssuerURL: *authIssuerURL,
		Audience:  *authAudience,
		ClientID:  *authClientID,
	}

	authManager := auth.NewAuthManager(&authConfig, conn)

	redisCli := redis.NewClientWithDefaultOptions()
	webService := service.NewService(
		conn,
		authManager,
		*dataPath,
		*enableWaiting,
		*domainName,
		*sendgridAPIKey,
		*emailDomainName,
		redisCli,
		*coderHost,
		*coderAPIToken,
		*coderTemplateID,
		*coderOrgID,
	)

	usageClient := usageUtils.MustNewClient(*usageServiceAddress)

	var clickhouseMultiSharding event.MultiSharding
	if *clickhouseConfigPath != "" {
		clickhouseMultiSharding, err = clickhouse.NewMultiSharding(*clickhouseConfigPath)
		if err != nil {
			log.Fatale(err)
		}
	}

	ethClients, _, err := solidity.LoadChainClients(*chainConfig, conn, nil, false)
	if err != nil {
		log.Fatale(err)
	}

	// create new gRPC server
	grpcSever := rpc.NewServer(true, grpc.ChainUnaryInterceptor(
		preloader.Interceptor(conn, authManager.IdentityPreloader, preloader.ProjectLoader, service.DashboardLoader),
		authManager.GetAuthInterceptor(map[string]auth.Checker{
			"dashboard": getDashboardChecker(authManager),
		}),
		usageUtils.UsageInterceptor(authManager, usageClient),
		webUtils.ShareDashboardChecker(conn),
	))

	protos.RegisterWebServiceServer(grpcSever, webService)

	protoscontract.RegisterContractServiceServer(
		grpcSever, contract.NewService(conn,
			*timescaleConfigPath,
			authManager,
			clickhouseMultiSharding,
			ethClients),
	)
	aiService := ai.NewService(authManager, *aiServerAddress, *analyticServiceAddress, *o11yServiceAddress, usageClient, conn)
	protoai.RegisterAiServiceServer(grpcSever, aiService)

	reflection.Register(grpcSever)

	mux := runtime.NewServeMux(runtime.WithMetadata(rpc.WithAuthAndTraceMetadata),
		runtime.WithMetadata(func(ctx context.Context, request *http.Request) metadata.MD {
			return metadata.New(map[string]string{
				"grpcgateway-http-raw-query": request.URL.RawQuery,
			})
		}))

	err = mux.HandlePath("GET", "/api/v1/sitemap", service.GetSiteMaps(conn))
	if err != nil {
		log.Fatale(err)
	}

	err = mux.HandlePath("GET", "/healthz", rpc.HealthCheck(conn))

	if err != nil {
		log.Fatale(err)
	}
	err = protos.RegisterWebServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}

	err = protoscontract.RegisterContractServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}

	err = protoai.RegisterAiServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}

	if *enableProf {
		go func() {
			http.Handle("/metrics", promhttp.Handler())
			log.Fatale(http.ListenAndServe(":6060", nil))
		}()
	}

	rpc.BindAndServeWithHTTP(mux, grpcSever, *port, nil)
}
