package main

import (
	"context"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/preloader"
	"sentioxyz/sentio/service/web/models"
	"sentioxyz/sentio/service/web/protos"
	"sentioxyz/sentio/service/web/service"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

func getDashboardChecker(authManager auth.AuthManager) auth.Checker {
	return func(ctx context.Context, method string, request proto.Message, permission string, metadata map[string]string, allowAnonymous bool) (context.Context, error) {
		project := preloader.PreLoadedProject(ctx)
		dashboard := service.PreloadedDashboard(ctx)
		identity := preloader.PreLoadedIdentity(ctx)

		if project == nil {
			return ctx, status.Error(codes.NotFound, "Project not found")
		}

		var accesses []auth.Action
		var err error
		switch r := request.(type) {
		case *protos.Dashboard:
			if dashboard == nil {
				dashboard = &models.Dashboard{}
			}
			dashboard.FromPB(r)
			if project.ID != dashboard.ProjectID {
				return ctx, status.Error(codes.InvalidArgument, "Invalid project ID in request")
			}
		}

		// old dashboard access, check project access
		if dashboard.Visibility == models.VisibilityInternal {
			accesses, err = authManager.GetProjectAccess(ctx, identity, project)
			if err != nil || len(accesses) == 0 {
				return ctx, status.Errorf(codes.PermissionDenied, "user does not have read access to this project")
			}
		} else {
			accesses, err = authManager.GetProjectAccess(ctx, identity, project)
			if err != nil || len(accesses) == 0 {
				return ctx, status.Errorf(codes.PermissionDenied, "user does not have read access to this project")
			}
			if dashboard.OwnerID != nil && *dashboard.OwnerID == identity.GetUserID() {
				accesses = []auth.Action{auth.READ, auth.WRITE, auth.ADMIN}
			} else if dashboard.Visibility == models.VisibilityPrivate {
				for _, a := range accesses {
					if a == auth.ADMIN {
						// project admin can access all private dashboards
						accesses = []auth.Action{auth.READ}
						break
					}
				}
			} else {
				accesses = []auth.Action{auth.READ}
			}
		}
		for _, a := range accesses {
			if auth.Action(permission) == a {
				ctx = context.WithValue(ctx, "dashboard_access", accesses)
				return ctx, nil
			}
		}
		if allowAnonymous {
			// If anonymous access is allowed, return the context with empty accesses
			ctx = context.WithValue(ctx, "dashboard_access", []auth.Action{})
			return ctx, nil
		}
		return ctx, status.Errorf(codes.PermissionDenied, "user does not have %s access to this dashboard", permission)
	}
}
