// https://grpc-ecosystem.github.io/grpc-gateway/docs/mapping/customizing_openapi_output/
// https://github.com/grpc-ecosystem/grpc-gateway/blob/main/examples/internal/proto/examplepb/a_bit_of_everything.proto

syntax = "proto3";

package graphql_service;

option go_package = "sentioxyz/sentio/service/graphql/protos";

import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "google/protobuf/struct.proto";
import "google/api/httpbody.proto";
import "service/common/protos/common.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

message GraphQLRequest {
  string project_owner = 1;
  string project_slug = 2;
  string query = 3;
  google.protobuf.Struct variables = 4;
  string operation_name = 5;
  int32 version = 6;
}

message MetaRequest {
  string processor_id = 1;
}

message MetaResponse {
  string block_hash = 1;
  int64 block_number = 2;
  int64 block_timestamp = 3;
  string parent_hash = 4;
  string deployment = 5;
  bool hasIndexingErrors = 6;
}

service GraphQLService {
  rpc Query(GraphQLRequest) returns (google.api.HttpBody) {
    option (common.auth) = {
      permission: "project:read"
    };

    option (common.track_usage) = {
      api_sku: "api_graphql"
      webui_sku: "webui_graphql"
      project_slug_field: "project_slug"
      project_owner_field: "project_owner"
      version_field: "version"
    };

    option (common.access_metric) = {
      project_slug_field: "project_slug"
      owner_name_field: "project_owner"
      processor_version_field: "version"
    };

    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/graphql/{project_owner}/{project_slug}"
      body: "*"
      additional_bindings: {
        get: "/api/v1/graphql/{project_owner}/{project_slug}"
      }
    };
  };

  rpc Meta(MetaRequest) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/graphql/meta/{processor_id}"
    };
  };

  rpc SaveQuery(SaveQueryRequest) returns (SaveQueryResponse) {
    option (common.auth) = {
      permission: "project:write"
    };

    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/graphql/saved_query"
      body: "*"
    };
  };

  rpc GetSavedQueries(GetSavedQueriesRequest) returns (GetSavedQueriesResponse) {
    option (common.auth) = {
      permission: "project:read"
    };

    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/graphql/saved_query"
    };
  };

  rpc DeleteSavedQuery(DeleteSavedQueryRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };

    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/graphql/saved_query/{id}"
    };
  };
}

message SaveQueryRequest {
  string project_id = 1;
  string name = 2;
  string query = 3;
  google.protobuf.Struct variables = 4;
  string id = 5;
 }

message SaveQueryResponse {
  SavedQuery saved_query = 1;
}

message SavedQuery {
  string id = 1;
  string project_id = 2;
  string name = 3;
  string query = 4;
  google.protobuf.Struct variables = 5;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

message GetSavedQueriesRequest {
  string project_id = 1;
}

message GetSavedQueriesResponse {
  repeated SavedQuery saved_queries = 1;
}

message DeleteSavedQueryRequest {
  string id = 1;
}

