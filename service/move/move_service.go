package move

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	aptossdk "github.com/aptos-labs/aptos-go-sdk"
	"github.com/aptos-labs/aptos-go-sdk/api"
	"github.com/block-vision/sui-go-sdk/models"
	"github.com/block-vision/sui-go-sdk/sui"
	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"google.golang.org/genproto/googleapis/api/httpbody"

	"sentioxyz/sentio/common/monitoring"

	"cloud.google.com/go/storage"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/structpb"
	"gorm.io/gorm"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/protojson"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/move/aptos"
	"sentioxyz/sentio/service/move/common"
	"sentioxyz/sentio/service/move/protos"
	"sentioxyz/sentio/service/move/repository"
)

const (
	ChainTypeAptos = "aptos"
	ChainTypeSui   = "sui"
	ChainTypeIota  = "iota"

	GcsCompilationVersion = 2
	GcsCallTraceVersion   = 1
	GcsTxInfoVersion      = 1
)

type Service struct {
	protos.UnimplementedMoveServiceServer

	gcsBucket    string
	authManager  auth.AuthManager
	redisClient  *redis.Client
	gcsClient    *storage.Client
	aptos        aptos.Aptos
	chainConfigs map[string]common.ChainConfig

	syncRepository *repository.SyncRepository
	envRepository  *repository.EnvRepository
}

type TransactionInfoWithBlockInfo struct {
	TransactionInfo *api.Transaction `json:"transaction_info"`
	BlockInfo       *api.Block       `json:"block_info"`
}

func NewService(
	syncDB *gorm.DB,
	envDB *gorm.DB,
	redisClient *redis.Client,
	gcsBucket string,
	gcsClient *storage.Client,
	authManager auth.AuthManager,
	aptosPath string,
	cacheDir string,
	chainConfigs map[string]common.ChainConfig,
) (*Service, error) {
	syncRepo := repository.NewSyncRepository(syncDB)
	envRepo := repository.NewEnvRepository(envDB)

	aptos := aptos.New(chainConfigs, aptosPath, cacheDir)

	// we want to download core dependencies at start
	// TODO more elegant way to do this
	for {
		_, err := aptos.DownloadAndCompile(context.Background(),
			"1", "0x8d2d7bcde13b2513617df3f98cdd5d0e4b9f714c6308b9204fe18ad900d92609", "TokenMint", true)
		if err == nil {
			break
		}
		time.Sleep(5 * time.Second)
		log.Warne(err)
	}

	return &Service{
		authManager:    authManager,
		gcsBucket:      gcsBucket,
		gcsClient:      gcsClient,
		syncRepository: syncRepo,
		envRepository:  envRepo,
		redisClient:    redisClient,
		aptos:          aptos,
		chainConfigs:   chainConfigs,
	}, nil
}

func (s *Service) FetchAndCompile(
	ctx context.Context,
	req *protos.FetchAndCompileRequest,
) (*protos.FetchAndCompileResponse, error) {
	req.Account = "0x" + strings.TrimPrefix(req.Account, "0x")
	obj := s.getGcsObjectHandle(req.NetworkId, req.Account, req.Package)
	_, err := obj.Attrs(ctx)
	if err == nil {
		rd, err := obj.NewReader(ctx)
		if err != nil {
			return nil, err
		}
		defer rd.Close()
		data, err := io.ReadAll(rd)
		if err != nil {
			return nil, err
		}
		var ret aptos.CompileResult
		err = json.Unmarshal(data, &ret)
		if err != nil {
			return nil, err
		}
		ret.Trim(req.QueryAbi, req.QuerySource, req.QuerySourceMap, req.QueryBytecode)
		data, _ = json.Marshal(ret)

		var st structpb.Struct
		err = protojson.Unmarshal(data, &st)
		if err != nil {
			return nil, err
		}
		return &protos.FetchAndCompileResponse{
			Result: &st,
		}, nil
	}
	ret, err := s.aptos.DownloadAndCompile(ctx, req.NetworkId, req.Account, req.Package, req.CheckLatestDeps)
	if err != nil {
		return nil, err
	}

	var st structpb.Struct
	data, _ := json.Marshal(ret)

	w := obj.NewWriter(ctx)
	gz := gzip.NewWriter(w)
	w.ContentType = "application/json"
	w.ContentEncoding = "gzip"
	_, err = gz.Write(data)
	if err != nil {
		return nil, err
	}
	_ = gz.Close()
	_ = w.Close()

	ret.Trim(req.QueryAbi, req.QuerySource, req.QuerySourceMap, req.QueryBytecode)
	data, _ = json.Marshal(ret)
	err = protojson.Unmarshal(data, &st)
	if err != nil {
		return nil, err
	}
	return &protos.FetchAndCompileResponse{
		Result: &st,
	}, err
}

func (s *Service) GetCallTrace(
	ctx context.Context,
	req *protos.GetCallTraceRequest,
) (ret *httpbody.HttpBody, err error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetCallTrace, networkId: %s, txhash: %s", req.GetNetworkId(), req.GetTxHash())
	config, err := s.getChainConfig([]string{ChainTypeAptos}, req.NetworkId)
	if err != nil {
		return nil, err
	}

	obj := s.getGcsCallTraceHandle(req.NetworkId, req.TxHash)
	data, err := s.loadGcs(ctx, obj)
	if err == nil {
		var body httpbody.HttpBody
		body.ContentType = "application/json"
		body.Data = data
		return &body, nil
	}
	defer func() {
		if err == nil && lo.Contains(config.PermanentCacheTxs, req.TxHash) {
			logger.Infof("storing aptos call trace, networkID: %s, txHash: %s", req.NetworkId, req.TxHash)
			err = s.storeGcs(ctx, obj, ret.Data)
			if err != nil {
				logger.Warne(err)
			}
		}
	}()

	client := http.Client{
		Timeout: 5 * time.Minute,
	}
	client.Transport = monitoring.NewTraceRoundTripper()
	resp, err := client.Get(fmt.Sprintf("%s/%s/call_trace/by_hash/%s", config.TracerEndpoint, req.GetNetworkId(), req.GetTxHash()))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(resp.Status)
	}
	var body httpbody.HttpBody
	body.ContentType = "application/json"
	body.Data, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return &body, nil
}

func (s *Service) GetTransactionInfo(
	ctx context.Context,
	req *protos.GetCallTraceRequest,
) (ret *httpbody.HttpBody, err error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetTransactionInfo, networkId: %s, txn hash: %s", req.GetNetworkId(), req.GetTxHash())
	config, err := s.getChainConfig([]string{ChainTypeAptos}, req.NetworkId)
	if err != nil {
		return nil, err
	}

	obj := s.getGcsTxInfoHandle(req.NetworkId, req.TxHash)
	data, err := s.loadGcs(ctx, obj)
	if err == nil {
		var body httpbody.HttpBody
		body.ContentType = "application/json"
		body.Data = data
		return &body, nil
	}
	defer func() {
		if err == nil && lo.Contains(config.PermanentCacheTxs, req.TxHash) {
			logger.Infof("storing aptos transaction info, networkID: %s, txHash: %s", req.NetworkId, req.TxHash)
			err = s.storeGcs(ctx, obj, ret.Data)
			if err != nil {
				logger.Warne(err)
			}
		}
	}()

	c, err := aptossdk.NewClient(aptossdk.NetworkConfig{
		NodeUrl: config.NodeEndpoint + "/v1",
	})
	transactionInfo, err := c.TransactionByHash(req.GetTxHash())
	if err != nil {
		return nil, err
	}
	blockInfo, err := c.BlockByVersion(*transactionInfo.Version(), false)
	if err != nil {
		return nil, err
	}
	var body httpbody.HttpBody
	body.ContentType = "application/json"
	// merge block info and transaction info
	out := TransactionInfoWithBlockInfo{
		TransactionInfo: transactionInfo,
		BlockInfo:       blockInfo,
	}
	body.Data, err = json.Marshal(out)
	if err != nil {
		return nil, err
	}
	return &body, nil
}

func (s *Service) GetAccountModule(
	ctx context.Context,
	req *protos.GetAccountModuleRequest,
) (*httpbody.HttpBody, error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetAccountModule, networkId: %s, account: %s, module: %s",
		req.GetNetworkId(),
		req.GetAccount(),
		req.GetModuleId())
	config, err := s.getChainConfig([]string{ChainTypeAptos}, req.NetworkId)
	if err != nil {
		return nil, err
	}

	httpCli := http.Client{}
	u, _ := url.Parse(fmt.Sprintf("%s/v1/accounts/%s/module/%s", config.NodeEndpoint, req.GetAccount(), req.GetModuleId()))
	resp, err := httpCli.Do(&http.Request{
		Method: http.MethodGet,
		URL:    u,
		Header: map[string][]string{
			"User-Agent": {"go"},
		},
	})
	if err != nil {
		return nil, err
	}
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var body httpbody.HttpBody
	body.ContentType = "application/json"
	body.Data = data
	return &body, nil
}

func (s *Service) GetAccountResource(
	ctx context.Context,
	req *protos.GetAccountResourceRequest,
) (*httpbody.HttpBody, error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetAccountResource, networkId: %s, account: %s, type: %s",
		req.GetNetworkId(),
		req.GetAccount(),
		req.GetResourceType())
	config, err := s.getChainConfig([]string{ChainTypeAptos}, req.NetworkId)
	if err != nil {
		return nil, err
	}

	httpCli := http.Client{}
	u, _ := url.Parse(fmt.Sprintf("%s/v1/accounts/%s/resource/%s", config.NodeEndpoint, req.GetAccount(), req.GetResourceType()))
	resp, err := httpCli.Do(&http.Request{
		Method: http.MethodGet,
		URL:    u,
		Header: map[string][]string{
			"User-Agent": {"go"},
		},
	})
	if err != nil {
		return nil, err
	}
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var body httpbody.HttpBody
	body.ContentType = "application/json"
	body.Data = data
	return &body, nil
}

func (s *Service) GetSuiCallTrace(
	ctx context.Context,
	req *protos.GetSuiCallTraceRequest,
) (ret *protos.GetSuiCallTraceResponse, err error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetSuiCallTrace, networkId: %s, txdigest: %s", req.GetNetworkId(), req.GetTxDigest())
	c, config, err := s.getSuiLikeConfigAndClient(req.NetworkId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	obj := s.getGcsCallTraceHandle(req.NetworkId, req.TxDigest)
	data, err := s.loadGcs(ctx, obj)
	if err == nil {
		var r protos.GetSuiCallTraceResponse
		err = protojson.Unmarshal(data, &r)
		if err == nil {
			return &r, nil
		}
	}
	defer func() {
		if err == nil && lo.Contains(config.PermanentCacheTxs, req.TxDigest) {
			logger.Infof("storing sui call trace, networkID: %s, txDigest: %s", req.NetworkId, req.TxDigest)
			data, err = protojson.Marshal(ret)
			if err != nil {
				logger.Errore(err)
				return
			}
			err = s.storeGcs(ctx, obj, data)
			if err != nil {
				logger.Warne(err)
			}
		}
	}()

	txResp, err := c.SuiGetTransactionBlock(ctx, models.SuiGetTransactionBlockRequest{
		Digest: req.TxDigest,
		Options: models.SuiTransactionBlockOptions{
			ShowInput:          true,
			ShowRawInput:       false,
			ShowEffects:        true,
			ShowEvents:         true,
			ShowObjectChanges:  true,
			ShowBalanceChanges: true,
		},
	})
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	computationCost, err := strconv.ParseUint(txResp.Effects.GasUsed.ComputationCost, 10, 64)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	client := http.Client{
		Timeout: 5 * time.Minute,
	}
	client.Transport = monitoring.NewTraceRoundTripper()
	u := fmt.Sprintf("%s/%s/call_trace/by_tx_digest/%s", config.TracerEndpoint, req.NetworkId, req.GetTxDigest())
	resp, err := client.Get(u)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, status.Errorf(codes.Internal, "get call trace failed: %d %s", resp.StatusCode, resp.Status)
	}
	data, err = io.ReadAll(resp.Body)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	t := struct {
		Result json.RawMessage `json:"result"`
	}{
		Result: json.RawMessage(data),
	}
	newData, _ := json.Marshal(t)
	ret = new(protos.GetSuiCallTraceResponse)
	err = protojson.Unmarshal(newData, ret)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	totalInternalGas := uint64(0)
	for _, call := range ret.Result {
		totalInternalGas += call.GasUsed
	}
	convertGas(ret.Result, totalInternalGas, computationCost)
	return ret, err
}

func convertGas(trace []*protos.SuiCallTrace, totalInternalGas uint64, totalComputationCost uint64) {
	for _, call := range trace {
		call.GasUsed = uint64(math.Ceil(float64(call.GasUsed) * float64(totalComputationCost) / float64(totalInternalGas)))
		convertGas(call.Calls, totalInternalGas, totalComputationCost)
	}
}

func (s *Service) GetSuiTransactionInfo(
	ctx context.Context,
	req *protos.GetSuiTransactionInfoRequest,
) (ret *protos.GetSuiTransactionInfoResponse, err error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetSuiTransactionInfo, networkId: %s, txdigest: %s", req.NetworkId, req.TxDigest)
	c, config, err := s.getSuiLikeConfigAndClient(req.NetworkId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	obj := s.getGcsTxInfoHandle(req.NetworkId, req.TxDigest)
	data, err := s.loadGcs(ctx, obj)
	if err == nil {
		var r protos.GetSuiTransactionInfoResponse
		err = protojson.Unmarshal(data, &r)
		if err == nil {
			return &r, nil
		}
	}
	defer func() {
		if err == nil && lo.Contains(config.PermanentCacheTxs, req.TxDigest) {
			logger.Infof("storing sui transaction info, networkID: %s, txDigest: %s", req.NetworkId, req.TxDigest)
			data, err = protojson.Marshal(ret)
			if err != nil {
				logger.Errore(err)
				return
			}
			err = s.storeGcs(ctx, obj, data)
			if err != nil {
				logger.Warne(err)
			}
		}
	}()

	txResp, err := c.SuiGetTransactionBlock(ctx, models.SuiGetTransactionBlockRequest{
		Digest: req.TxDigest,
		Options: models.SuiTransactionBlockOptions{
			ShowInput:          true,
			ShowRawInput:       true,
			ShowEffects:        true,
			ShowEvents:         true,
			ShowObjectChanges:  true,
			ShowBalanceChanges: true,
		},
	})
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	cpResp, err := c.SuiGetCheckpoint(ctx, models.SuiGetCheckpointRequest{
		CheckpointID: txResp.Checkpoint,
	})
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	//epResp, err := c.SuiXGetEpochs(ctx, models.SuiXGetEpochsRequest{
	//	Cursor: cpResp.Epoch,
	//	Limit:  1,
	//})
	//if err != nil {
	//	logger.Errore(err)
	//	return nil, err
	//}
	return &protos.GetSuiTransactionInfoResponse{
		TransactionBlock: mustToStruct(txResp),
		Checkpoint:       mustToStruct(cpResp),
		//Epoch:            mustToStruct(epResp),
	}, nil
}

func (s *Service) GetSuiObject(
	ctx context.Context,
	req *protos.GetSuiObjectRequest,
) (*httpbody.HttpBody, error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetSuiObject, networkId: %s, objectId: %s", req.NetworkId, req.ObjectId)
	c, _, err := s.getSuiLikeConfigAndClient(req.NetworkId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	resp, err := c.SuiCall(ctx, "sui_getObject", req.ObjectId, models.SuiObjectDataOptions{
		ShowType:                true,
		ShowContent:             true,
		ShowBcs:                 true,
		ShowOwner:               true,
		ShowPreviousTransaction: true,
		ShowStorageRebate:       true,
		ShowDisplay:             true,
	})
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	var ret struct {
		Result struct {
			Data json.RawMessage
		}
	}
	err = json.Unmarshal([]byte(resp.(string)), &ret)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &httpbody.HttpBody{
		ContentType: "application/json",
		Data:        ret.Result.Data,
	}, nil
}

func (s *Service) GetSuiPastObjects(
	ctx context.Context,
	req *protos.GetSuiPastObjectsRequest,
) (*httpbody.HttpBody, error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetSuiPastObjects, networkId: %s, objects: %+v", req.NetworkId, req.Objects)
	c, _, err := s.getSuiLikeConfigAndClient(req.NetworkId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	var ret []*models.PastObjectResponse
	// sui_tryMultiGetPastObjects max req size is 50
	for _, objects := range lo.Chunk(req.Objects, 50) {
		creq := models.SuiTryMultiGetPastObjectsRequest{
			MultiGetPastObjects: []*models.PastObject{},
			Options: models.SuiObjectDataOptions{
				ShowType:                true,
				ShowContent:             true,
				ShowBcs:                 false,
				ShowOwner:               true,
				ShowPreviousTransaction: false,
				ShowStorageRebate:       false,
				ShowDisplay:             true,
			},
		}
		for _, obj := range objects {
			creq.MultiGetPastObjects = append(creq.MultiGetPastObjects, &models.PastObject{
				ObjectId: obj.ObjectId,
				Version:  obj.Version,
			})
		}
		cresps, err := c.SuiTryMultiGetPastObjects(ctx, creq)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		ret = append(ret, cresps...)
	}
	data, err := json.Marshal(ret)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &httpbody.HttpBody{
		ContentType: "application/json",
		Data:        data,
	}, nil
}

func (s *Service) getChainConfig(allowedChainTypes []string, networkID string) (*common.ChainConfig, error) {
	config, ok := s.chainConfigs[networkID]
	if !ok {
		return nil, status.Errorf(codes.NotFound, "network not found")
	}
	if !lo.Contains(allowedChainTypes, config.Type) {
		return nil, status.Errorf(codes.InvalidArgument, "not sui chain")
	}
	return &config, nil
}

func (s *Service) getSuiLikeConfigAndClient(network string) (sui.ISuiAPI, *common.ChainConfig, error) {
	config, err := s.getChainConfig([]string{ChainTypeSui, ChainTypeIota}, network)
	if err != nil {
		return nil, nil, err
	}
	if config.Type == ChainTypeSui {
		return sui.NewSuiClient(config.NodeEndpoint), config, nil
	}
	c := sui.NewSuiClientWithCustomClient(config.NodeEndpoint, &http.Client{
		Transport: &IotaTransport{},
	})
	return c, config, nil
}

func (s *Service) loadGcs(ctx context.Context, obj *storage.ObjectHandle) ([]byte, error) {
	rd, err := obj.NewReader(ctx)
	if err != nil {
		return nil, err
	}
	defer rd.Close()
	data, err := io.ReadAll(rd)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (s *Service) storeGcs(ctx context.Context, obj *storage.ObjectHandle, data []byte) error {
	w := obj.NewWriter(ctx)
	gz := gzip.NewWriter(w)
	w.ContentType = "application/json"
	w.ContentEncoding = "gzip"
	_, err := gz.Write(data)
	if err != nil {
		return err
	}
	_ = gz.Close()
	return w.Close()
}

type IotaTransport struct{}

func (t *IotaTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	var body map[string]any
	if err := json.NewDecoder(req.Body).Decode(&body); err != nil {
		return nil, fmt.Errorf("failed to decode request body: %w", err)
	}
	method, ok := body["method"].(string)
	if !ok {
		return nil, errors.New("method not found in request body")
	}
	method = "iota" + strings.TrimPrefix(method, "sui")
	body["method"] = method
	data, _ := json.Marshal(body)
	req.ContentLength = int64(len(data))
	req.Body = io.NopCloser(bytes.NewBuffer(data))
	return http.DefaultTransport.RoundTrip(req)
}

func (s *Service) getGcsObjectHandle(networkID string, account string, packageName string) *storage.ObjectHandle {
	name := fmt.Sprintf("chain_%s/compilation/version_%d/address_%s/package_%s",
		networkID, GcsCompilationVersion, account, packageName)
	return s.gcsClient.Bucket(s.gcsBucket).Object(name)
}

func (s *Service) getGcsCallTraceHandle(networkID string, txHash string) *storage.ObjectHandle {
	name := fmt.Sprintf("chain_%s/call_trace/version_%d/tx_%s", networkID, GcsCallTraceVersion, txHash)
	return s.gcsClient.Bucket(s.gcsBucket).Object(name)
}

func (s *Service) getGcsTxInfoHandle(networkID string, txHash string) *storage.ObjectHandle {
	name := fmt.Sprintf("chain_%s/tx_info/version_%d/tx_%s", networkID, GcsTxInfoVersion, txHash)
	return s.gcsClient.Bucket(s.gcsBucket).Object(name)
}

func mustToStruct(v any) *structpb.Struct {
	data, _ := json.Marshal(v)
	var ret structpb.Struct
	_ = protojson.Unmarshal(data, &ret)
	return &ret
}
