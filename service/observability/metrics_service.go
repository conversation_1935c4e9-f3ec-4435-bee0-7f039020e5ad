package observability

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"
	_ "time/tzdata"

	"sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/common/preloader"

	"golang.org/x/exp/slices"

	"sentioxyz/sentio/common/identifier"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/timescale"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/common"
	protoscommon "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/common/query"
	"sentioxyz/sentio/service/common/timerange"
	metricutil "sentioxyz/sentio/service/common/util"
	"sentioxyz/sentio/service/observability/protos"
	"sentioxyz/sentio/service/observability/util"

	"github.com/pkg/errors"
	prometheus "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/common/model"
	"go.opentelemetry.io/otel"
	"golang.org/x/exp/maps"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *Service) GetMetrics(
	ctx context.Context,
	req *protos.GetMetricsRequest,
) (response *protos.GetMetricsResponse, err error) {
	project := preloader.PreLoadedProject(ctx)
	filterByName := ""
	if req.GetName() != "" {
		filterByName = req.GetName()
	}
	processors, err := s.repository.GetProcessorsByProjectAndVersion(ctx, project.ID, req.Version)
	if len(processors) == 0 || err != nil {
		return nil, status.Errorf(codes.NotFound, "no active version found")
	}
	processorIDs := processors.GetProcessorIDs()
	timescaleClient, err := s.timescaleMultiClient.GetClientByProcessors(ctx, processors)
	if err != nil {
		return nil, err
	}

	metrics, err := timescaleClient.GetProjectMetrics(ctx, processorIDs, filterByName, nil)
	if err != nil {
		return nil, err
	}
	response = &protos.GetMetricsResponse{
		Metrics: []*protos.MetricInfo{},
	}
	contractMapper, err := s.contractMapperFactory.CreateMapper(project.ID)
	if err != nil {
		return nil, err
	}

	for _, m := range metrics {
		displayName := metricutil.ExtractMetricName(m.Name)
		info := &protos.MetricInfo{
			Name:        displayName,
			DisplayName: displayName,
			ProjectId:   project.ID,
			Labels:      map[string]*protos.MetricInfo_LabelValues{},
		}
		contracts := map[string]bool{}
		addresses := map[string]bool{}
		chains := map[string]bool{}
		others := map[string]map[string]bool{}

		for _, series := range m.Series {
			for key, value := range series.LabelValues {
				switch key {
				case "contract_name":
					if address, ok := series.LabelValues["contract_address"]; ok {
						if chain, ok := series.LabelValues["chain"]; ok {
							if name, ok := contractMapper.GetAliasByChainAddress(chain, address); ok {
								contracts[name] = true
								continue
							}
						}
					}
					contracts[value] = true
				case "contract_address":
					addresses[value] = true
				case "chain":
					chains[value] = true
				default:
					if _, ok := common.SystemReservedLabels[key]; ok {
						// ignore
					} else {
						if _, ok := others[key]; !ok {
							others[key] = map[string]bool{}
						}
						others[key][value] = true
					}
				}
			}
		}
		info.ContractName = maps.Keys(contracts)
		info.ContractAddress = maps.Keys(addresses)
		info.ChainId = maps.Keys(chains)
		for key, values := range others {
			info.Labels[key] = &protos.MetricInfo_LabelValues{
				Values: maps.Keys(values),
			}
		}

		if m.Metadata != nil {
			info.Metadata = &protos.MetricMetadata{
				Type:     m.Metadata.Type,
				Unit:     m.Metadata.Unit,
				Help:     m.Metadata.Help,
				LastSeen: m.Metadata.LastSeen.Unix(),
			}
		}
		response.Metrics = append(response.Metrics, info)
	}

	// mix system metrics
	if s.systemMetricClient != nil {
		chainIDs, err := s.repository.GetChainIDsByProcessor(ctx, processorIDs)
		if err != nil {
			return nil, err
		}
		systemMetrics, err := s.systemMetricClient.GetProjectMetrics(ctx, processorIDs, chainIDs, "")
		if err == nil {
			for _, m := range systemMetrics {
				displayName := "system." + m.Name
				info := &protos.MetricInfo{
					Name:        displayName,
					DisplayName: displayName,
					ProjectId:   project.ID,
					Labels:      map[string]*protos.MetricInfo_LabelValues{},
				}
				for _, s := range m.Series {
					for key, value := range s.LabelValues {
						if _, ok := common.SystemReservedLabels[key]; ok {
							continue
						}
						labelValues, ok := info.Labels[key]
						if !ok {
							labelValues = &protos.MetricInfo_LabelValues{
								Values: []string{},
							}
							info.Labels[key] = labelValues
						}
						if !slices.Contains(labelValues.Values, value) {
							labelValues.Values = append(labelValues.Values, value)
						}
					}
				}
				response.Metrics = append(response.Metrics, info)
			}
		}
	}
	return response, nil
}

func (s *Service) queryRange(
	ctx context.Context, projectID string,
	queries []*protoscommon.Query,
	formulas []*protoscommon.Formula,
	start time.Time,
	end time.Time,
	step time.Duration,
	tz *time.Location,
	samplesLimit int32,
	samplesOffset int32,
	version int32,
	cacheOptions *timescale.OverrideCacheOptions,
) (*protos.MetricsQueryResponse, error) {
	if start.After(end) {
		return nil, status.Error(
			codes.InvalidArgument, "start time is after end time",
		)
	}

	g, ctx := errgroup.WithContext(ctx)

	ret := protos.MetricsQueryResponse{
		Results: []*protos.MetricsQueryResponse_Result{},
	}

	tracer := otel.Tracer("senql")

	ctx, sp := tracer.Start(ctx, "constructQuery")
	processors, err := s.repository.GetProcessorsByProjectAndVersion(ctx, projectID, version)
	if err != nil {
		sp.End()
		return nil, status.Errorf(codes.InvalidArgument, "failed to find project processors, %v", err)
	}
	if len(processors) == 0 {
		sp.End()
		return nil, status.Errorf(codes.NotFound, "no active version found for project %s, version %d", projectID, version)
	}
	processorIDs := processors.GetProcessorIDs()
	timescaleClient, err := s.timescaleMultiClient.GetClientByProcessors(ctx, processors)
	if err != nil {
		return nil, err
	}
	chainIDs, err := s.repository.GetChainIDsByProcessor(ctx, processorIDs)
	if err != nil {
		sp.End()
		return nil, status.Errorf(codes.InvalidArgument, "failed to find chain ids, %v", err)
	}
	expandedQueries := query.ExpandQueryAndFormula(
		ctx,
		timescaleClient,
		s.systemMetricClient,
		processorIDs,
		queries,
		formulas,
		projectID,
		chainIDs,
	)

	limit := samplesLimit
	if limit == 0 {
		limit = 20
	}

	var lock sync.Mutex
	for _, q := range expandedQueries {
		if q.Error != nil {
			log.Errore(q.Error, "error found in query")
			ret.Results = append(ret.Results, handleError(q, &protoscommon.ComputeStats{
				ComputedAt:        timestamppb.Now(),
				BinaryVersionHash: identifier.BinaryHash(),
				ComputedBy:        "sentio/" + identifier.PodName(),
			}, q.Error))
			continue
		}
		q := q
		g.Go(
			func() error {
				var queryStep = &step
				computeStats := &protoscommon.ComputeStats{
					ComputedAt:        timestamppb.Now(),
					BinaryVersionHash: identifier.BinaryHash(),
					ComputedBy:        "sentio/" + identifier.PodName(),
				}
				// align step to rollup function duration
				if q.Step != nil {
					queryStep = q.Step
				}

				if tr := ctx.Value(timeRangeKey); cacheOptions != nil && tr != nil {
					if tr, ok := tr.(*protoscommon.TimeRangeLite); ok && tr != nil {
						cacheOptions.CacheKey = fmt.Sprintf("ts_gz:gob:%s:%s", q.Query.String(), hashTimeRange(tr))
					}
				}

				result, err := s.doQueryRange(ctx, timescaleClient, q, start, end, *queryStep, tz, cacheOptions)

				lock.Lock()
				defer lock.Unlock()
				if err != nil {
					ret.Results = append(ret.Results, handleError(q, computeStats, err))
				} else {
					if (result.Type()) == model.ValMatrix {
						result := handleResult(result.(model.Matrix), computeStats, q, /*contractMapper,*/
							int(limit), int(samplesOffset), q.TopK, q.BottomK)
						if cacheOptions != nil && !cacheOptions.ComputeAt.IsZero() {
							result.ComputeStats.ComputedAt = timestamppb.New(cacheOptions.ComputeAt)
							result.ComputeStats.IsRefreshing = cacheOptions.IsRefreshing
							result.ComputeStats.IsCached = true
						}
						ret.Results = append(ret.Results, result)

					} else {
						ret.Results = append(ret.Results, handleError(q, computeStats, errors.Errorf("unexpected result type")))
					}
				}
				return nil
			},
		)
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}
	return &ret, nil
}

func hashTimeRange(tr *protoscommon.TimeRangeLite) string {
	return fmt.Sprintf("%s:%s:%d:%s", tr.Start, tr.End, tr.Step, tr.Timezone)
}

func handleError(q *query.ExpandedQuery, computeStats *protoscommon.ComputeStats, err error) *protos.MetricsQueryResponse_Result {
	computeStats.ComputeCostMs = time.Since(computeStats.ComputedAt.AsTime()).Milliseconds()
	return &protos.MetricsQueryResponse_Result{
		MetricsQueryResponseType: &protos.MetricsQueryResponse_Result_Error{
			Error: err.Error(),
		},
		Alias:        q.Alias,
		Color:        q.Color,
		Id:           q.ID,
		ComputeStats: computeStats,
	}
}

func handleResult(
	result model.Matrix,
	computeStats *protoscommon.ComputeStats,
	q *query.ExpandedQuery,
	limit int,
	offset int,
	topK *int,
	bottomK *int,
) *protos.MetricsQueryResponse_Result {
	totalSamples := len(result)

	sortOrderFn := func(i, j int) bool {
		ilen := len(result[i].Values)
		jlen := len(result[j].Values)
		var ilast, jlast model.SampleValue
		if ilen > 0 {
			ilast = result[i].Values[ilen-1].Value
		} else {
			ilast = 0
		}
		if jlen > 0 {
			jlast = result[j].Values[jlen-1].Value
		} else {
			jlast = 0
		}
		return ilast > jlast
	}
	sortOrder := sortOrderFn
	if topK != nil && *topK > 0 {
		// when topK is set, we ignore limit and offset
		limit = totalSamples
		offset = 0
	} else if bottomK != nil && *bottomK > 0 {
		// when bottomK is set, we ignore limit and offset
		limit = totalSamples
		offset = 0
		sortOrder = func(i, j int) bool {
			return !sortOrderFn(i, j)
		}
	}
	sort.Slice(result, sortOrder)
	if totalSamples > (offset + limit) {
		result = result[offset:(offset + limit)]
	} else if totalSamples > offset {
		result = result[offset:]
	} else {
		result = model.Matrix{}
	}

	// Filter out any future data points and convert the matrix to a protobuf object
	filteredResult := util.FilterOutFutureDataPoints(result)
	matrix := util.ConvertMatrixToPb(filteredResult)
	matrix.TotalSamples = int32(totalSamples)

	// Set metric name and display name for aggregated metrics
	if q.RequestQuery != nil {
		for _, sample := range matrix.Samples {
			if sample.Metric.Name == "" {
				sample.Metric.DisplayName = q.DisplayName()
				sample.Metric.Name = q.RequestQuery.Query
			}
		}
	}

	// Return the MetricsQueryResponse_Result object
	computeStats.ComputeCostMs = time.Since(computeStats.ComputedAt.AsTime()).Milliseconds()
	return &protos.MetricsQueryResponse_Result{
		MetricsQueryResponseType: &protos.MetricsQueryResponse_Result_Matrix{
			Matrix: matrix,
		},
		Alias:        q.Alias,
		Color:        q.Color,
		Id:           q.ID,
		ComputeStats: computeStats,
	}
}

func (s *Service) doQueryRange(
	ctx context.Context,
	timescaleClient timescale.Client,
	query *query.ExpandedQuery,
	start time.Time,
	end time.Time,
	step time.Duration,
	tz *time.Location,
	cacheOptions *timescale.OverrideCacheOptions,
) (model.Value, error) {

	q := query.Query.String()
	from := timerange.AlignStartTime(start, step, tz)

	// skip align end time.
	to := timerange.AlignEndTime(end, step, tz)
	if query.RollupDelta {
		// add extra step to end time to get the last delta value
		to = to.Add(step)
	}
	var result model.Value
	var err error

	if query.IsSystemMetric {
		result, _, err = s.systemMetricClient.QueryRange(ctx, q, prometheus.Range{
			Start: from,
			End:   to,
			Step:  step,
		})
	} else {

		batches := timerange.SplitBatch(from, to, step, tz)
		if len(batches) > 1 {
			result, err = s.queryInBatches(ctx, timescaleClient, q, batches, cacheOptions)
		} else {
			result, _, err = timescaleClient.QueryRange(
				ctx,
				q,
				prometheus.Range{
					Start: from,
					End:   to,
					Step:  step,
				},
				cacheOptions,
			)
		}
	}
	if err != nil {
		return nil, err
	}
	if result.Type() == model.ValMatrix {
		if query.RollupDelta {
			result = query.HandleDeltaResult(result.(model.Matrix))
		}
	}

	return result, nil
}

func (s *Service) queryInBatches(
	ctx context.Context,
	timescaleClient timescale.Client,
	query string,
	batches []prometheus.Range,
	cacheOptions *timescale.OverrideCacheOptions,
) (model.Value, error) {

	g, ctx := errgroup.WithContext(ctx)
	var lock sync.Mutex
	var result model.Matrix
	for idx, batch := range batches {
		batch := batch
		idx := idx
		g.Go(
			func() error {
				log.Debugf("querying batch %v", batch)
				cacheOptions := *cacheOptions
				if len(cacheOptions.CacheKey) > 0 {
					cacheOptions.CacheKey = fmt.Sprintf("%s:batch_%d", cacheOptions.CacheKey, idx)
				}
				res, _, err := timescaleClient.QueryRange(ctx, query, batch, &cacheOptions)
				if err != nil {
					log.Errore(err, "batch query failed due to error")
					return err
				}
				lock.Lock()
				defer lock.Unlock()
				if res == nil {
					log.Warnf("query returned empty result %s", query)
				} else if res.Type() == model.ValMatrix {
					result = append(result, res.(model.Matrix)...)
				} else {
					err = errors.Errorf("unexpected result type %v", res.Type())
					log.Errore(err)
					return err
				}
				return nil
			},
		)
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}

	return mergeMatrix(result), nil
}

func mergeMatrix(result model.Matrix) model.Matrix {
	samples := map[model.Fingerprint]*model.SampleStream{}

	for _, stream := range result {
		key := stream.Metric.Fingerprint()
		if s, ok := samples[key]; !ok {
			samples[key] = &model.SampleStream{
				Metric:     stream.Metric.Clone(),
				Values:     stream.Values,
				Histograms: stream.Histograms,
			}
		} else {
			s.Values = append(s.Values, stream.Values...)
			s.Histograms = append(s.Histograms, stream.Histograms...)
			log.Debugf("merged %s, new length %d", s.Metric.String(), len(s.Values))
		}
	}

	ret := maps.Values(samples)
	for _, s := range ret {
		sort.Slice(s.Values, func(i, j int) bool {
			return s.Values[i].Timestamp.Before(s.Values[j].Timestamp)
		})
	}
	return ret
}

func (s *Service) QueryRange(
	ctx context.Context,
	req *protos.QueryRangeRequest,
) (resp *protos.MetricsQueryResponse, err error) {
	return s.QueryRangeWithOptions(ctx, req, MetricsOptions{BypassCache: false})
}

type MetricsOptions struct {
	BypassCache             bool
	CacheTTL                time.Duration
	RefreshTTL              time.Duration
	ParseProjectFromRequest bool
}

func (s *Service) QueryRangeWithOptions(ctx context.Context, req *protos.QueryRangeRequest, options MetricsOptions) (
	resp *protos.MetricsQueryResponse, err error) {
	var project *models.Project
	if options.ParseProjectFromRequest {
		switch {
		case req.ProjectId == "":
			project, err = s.repository.GetProjectBySlug(ctx, req.ProjectOwner, req.ProjectSlug)
		default:
			project, err = s.repository.GetProjectByID(ctx, req.ProjectId)
		}
		if err != nil {
			return nil, status.Errorf(codes.NotFound, "project not found: %v", err)
		}
	} else {
		project = preloader.PreLoadedProject(ctx)
		if project == nil {
			return nil, status.Error(codes.InvalidArgument, "project not found in context")
		}
	}

	start, end, step, tz, err := s.resolveTimeRange(req.TimeRange)
	if err != nil {
		return nil, err
	}

	cacheOptions := &timescale.OverrideCacheOptions{
		ByPassCache: options.BypassCache,
		TTL:         &options.CacheTTL,
		RefreshTTL:  &options.RefreshTTL,
	}
	ctx = context.WithValue(ctx, timeRangeKey, req.TimeRange)

	return s.queryRange(
		ctx,
		project.ID,
		req.GetQueries(),
		req.GetFormulas(),
		start,
		end,
		step,
		tz,
		req.SamplesLimit,
		req.SamplesOffset,
		req.Version,
		cacheOptions,
	)
}

func (s *Service) resolveTimeRange(
	tr *protoscommon.TimeRangeLite,
) (time.Time, time.Time, time.Duration, *time.Location, error) {
	if tr == nil {
		return time.Now(), time.Now(), 0, nil, status.Error(codes.InvalidArgument, "Invalid time range %s")
	}

	tz, err := time.LoadLocation(tr.Timezone)
	if err != nil {
		return time.Now(), time.Now(), 0, nil, status.Errorf(codes.InvalidArgument, "Invalid time zone %s", err.Error())
	}

	start, err := timerange.ResolveTimeStrWithAlign(tr.Start, true, tz)
	if err != nil {
		return time.Now(), time.Now(), 0, nil, status.Errorf(
			codes.InvalidArgument,
			"Invalid time string %s",
			err.Error(),
		)
	}
	var end time.Time
	// set end time to end of the day if it is now
	// this is to avoid cache miss when the end time is now
	if tr.End == "now" {
		end = time.Now().In(tz).Truncate(24 * time.Hour).Add(23*time.Hour + 59*time.Minute + 59*time.Second)
	} else {
		end, err = timerange.ResolveTimeStrWithAlign(tr.End, false, tz)
		if err != nil {
			return time.Now(), time.Now(), 0, nil, status.Errorf(
				codes.InvalidArgument,
				"Invalid time string %s",
				err.Error(),
			)
		}
	}
	step := time.Duration(tr.Step) * time.Second
	minStep := timerange.GetMetricsMinStep(start, end)

	if step < minStep {
		step = minStep
	}

	return start, end, step, tz, nil
}

func (s *Service) Query(
	ctx context.Context,
	req *protos.QueryValueRequest,
) (resp *protos.QueryValueResponse, err error) {
	project := preloader.PreLoadedProject(ctx)

	tz, err := time.LoadLocation(req.Timezone)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "Invalid time zone %s", err.Error())
	}
	ts, err := timerange.ResolveTimeStrWithAlign(req.Time, true, tz)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "Invalid time string %s", err.Error())
	}

	result, err := s.queryRange(
		ctx,
		project.ID,
		req.GetQueries(),
		req.GetFormulas(),
		ts,
		ts,
		time.Second,
		tz,
		req.SamplesLimit,
		req.SamplesOffset,
		req.Version,
		nil,
	)

	if err != nil {
		return nil, err
	}
	response := &protos.QueryValueResponse{
		Results: make([]*protos.QueryValueResponse_Result, 0),
	}
	for _, r := range result.Results {
		if r.GetMatrix() != nil {
			response.Results = append(response.Results, &protos.QueryValueResponse_Result{
				Sample: utils.MapSliceNoError(
					r.GetMatrix().GetSamples(),
					func(s *protos.MetricsQueryResponse_Sample) *protos.MetricsQueryResponse_Sample {
						return &protos.MetricsQueryResponse_Sample{
							Metric: s.Metric,
							Values: []*protos.MetricsQueryResponse_Value{
								{
									Timestamp: s.Values[0].Timestamp,
									Value:     s.Values[0].Value,
								},
							},
						}
					},
				),
				Error: r.GetError(),
				Alias: r.Alias,
				Color: r.Color,
				Id:    r.Id,
			})
		} else {
			response.Results = append(response.Results, &protos.QueryValueResponse_Result{
				Error: r.GetError(),
				Alias: r.Alias,
				Color: r.Color,
				Id:    r.Id,
			})
		}
	}
	sort.Slice(response.Results, func(i, j int) bool {
		return response.Results[i].Id < response.Results[j].Id
	})
	return response, nil
}

type ContextKeyTr string

const timeRangeKey ContextKeyTr = "timerangelite"
