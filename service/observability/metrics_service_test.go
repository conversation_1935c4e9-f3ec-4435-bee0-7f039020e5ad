package observability

import (
	"context"
	"fmt"
	"sentioxyz/sentio/service/common/preloader"
	protoscommon "sentioxyz/sentio/service/common/protos"
	"testing"
	"time"

	"sentioxyz/sentio/service/common/contract"
	mockmapper "sentioxyz/sentio/service/common/contract/mock"
	"sentioxyz/sentio/service/processor/models"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"sentioxyz/sentio/common/timescale"
	mocktimescale "sentioxyz/sentio/common/timescale/mock"
	common "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/observability/protos"
	mockrepo "sentioxyz/sentio/service/observability/repository/mock"
	mockusage "sentioxyz/sentio/service/usage/protos/mock"
)

type Suite struct {
	suite.Suite
	service         *Service
	mockClient      *mocktimescale.MockClient
	mockMultiClient *mocktimescale.MockMultiClient
	mockRepo        *mockrepo.MockRepository
	ctrl            *gomock.Controller
	mockMapper      *mockmapper.MockMapper
	mockUsageClient *mockusage.MockUsageServiceClient
}

type mockContractMapperFactory struct {
	mapper contract.Mapper
}

func (m *mockContractMapperFactory) CreateMapper(projectID string) (contract.Mapper, error) {
	return m.mapper, nil
}

func (s *Suite) SetupTest() {
	ctrl := gomock.NewController(s.T())

	s.mockClient = mocktimescale.NewMockClient(ctrl)
	s.mockMultiClient = mocktimescale.NewMockMultiClient(ctrl)
	s.mockRepo = mockrepo.NewMockRepository(ctrl)
	s.mockMapper = mockmapper.NewMockMapper(ctrl)
	s.mockUsageClient = mockusage.NewMockUsageServiceClient(ctrl)
	s.service = &Service{
		timescaleMultiClient:  s.mockMultiClient,
		repository:            s.mockRepo,
		contractMapperFactory: &mockContractMapperFactory{s.mockMapper},
	}
	s.ctrl = ctrl
}

func (s *Suite) TearDownTest() {
	s.ctrl.Finish()
}

func (s *Suite) TestQueryRange() {
	s.T().Skip("skip for now")
	testProjectID := "testprojectid"
	testProcessorID := "testprocessorid"
	metricName := "testmetricname"
	testMetricName := fmt.Sprintf("metric_%s_%s", testProcessorID, metricName)

	ctx := context.TODO()

	s.mockMultiClient.EXPECT().
		GetClientByProcessors(gomock.Any(), gomock.Any()).
		Return(s.mockClient, nil)

	s.mockRepo.EXPECT().
		GetProcessorsByProjectAndVersion(gomock.Any(), gomock.Eq(testProjectID), gomock.Any()).
		Return(models.Processors([]*models.Processor{
			{
				ID: testProcessorID,
				SentioProcessorProperties: models.SentioProcessorProperties{
					TimescaleShardingIndex: 0,
				},
			},
		}), nil)

	s.mockRepo.EXPECT().GetChainIDsByProcessor(gomock.Any(), gomock.Any()).Return([]string{"1"}, nil)

	s.service.QueryRange(ctx, &protos.QueryRangeRequest{
		ProjectId: testProjectID,
		Queries: []*protoscommon.Query{
			{
				Query: testMetricName,
				Aggregate: &protoscommon.Aggregate{
					Op: protoscommon.Aggregate_SUM,
				},
				Functions: []*protoscommon.Function{
					{
						Name: "rollup_delta",
						Arguments: []*protoscommon.Argument{
							{
								ArgumentValue: &protoscommon.Argument_DurationValue{
									DurationValue: &protoscommon.Duration{
										Value: 1,
										Unit:  "d",
									},
								},
							},
						},
					},
				},
			},
		},
		TimeRange: &protoscommon.TimeRangeLite{
			Start:    "now-6M",
			End:      "now",
			Step:     86400,
			Timezone: "Asia/Taipei",
		},
	})

}

func (s *Suite) TestGetMetricsByProject() {
	testProjectID := "testprojectid"
	testProcessorID := "testprocessorid"
	var testProcessorTimescaleIndex int32 = 0
	metricName := "testmetricname"
	testMetricName := fmt.Sprintf("metric_%s_%s", testProcessorID, metricName)
	now := time.Now()

	project := &common.Project{
		ID: testProjectID,
	}

	ctx := context.WithValue(context.Background(), preloader.ProjectKeyName, project)
	s.mockMultiClient.EXPECT().
		GetClientByProcessors(ctx, gomock.Any()).
		Return(s.mockClient, nil)

	s.mockClient.EXPECT().
		GetProjectMetrics(ctx, gomock.Eq([]string{testProcessorID}), gomock.Any(), nil).
		Return(map[string]*timescale.Metric{
			testMetricName: {
				Name: testMetricName,
				Series: []timescale.Series{
					{
						Name:     testMetricName,
						SeriesID: 0,
						LabelValues: map[string]string{
							"label_key":        "label_value",
							"contract_name":    "test-contract-name",
							"contract_address": "0x0000",
							"chain":            "1",
						},
					},
				},
				Metadata: &timescale.MetricMetadata{
					Type:     "COUNTER",
					Help:     "help",
					Unit:     "unit",
					LastSeen: now,
				},
			},
		}, nil)

	s.mockRepo.EXPECT().
		GetProcessorsByProjectAndVersion(ctx, gomock.Eq(testProjectID), gomock.Any()).
		Return(models.Processors([]*models.Processor{
			{
				ID: testProcessorID,
				SentioProcessorProperties: models.SentioProcessorProperties{
					TimescaleShardingIndex: testProcessorTimescaleIndex,
				},
			},
		}), nil)

	s.mockMapper.EXPECT().
		GetAliasByChainAddress(gomock.Eq("1"), gomock.Eq("0x0000")).
		Return("test-contract-name", true)

	req := &protos.GetMetricsRequest{ProjectId: testProjectID}
	resp, err := s.service.GetMetrics(ctx, req)
	require.NoError(s.T(), err)
	expectResp := &protos.GetMetricsResponse{
		Metrics: []*protos.MetricInfo{
			{
				Name:            metricName,
				DisplayName:     metricName,
				ProjectId:       testProjectID,
				ContractName:    []string{"test-contract-name"},
				ContractAddress: []string{"0x0000"},
				ChainId:         []string{"1"},
				Labels: map[string]*protos.MetricInfo_LabelValues{
					"label_key": {
						Values: []string{"label_value"},
					},
				},
				Metadata: &protos.MetricMetadata{
					Type:     "COUNTER",
					Help:     "help",
					Unit:     "unit",
					LastSeen: now.Unix(),
				},
			},
		},
	}
	require.Equal(s.T(), expectResp, resp)
}

func TestService(t *testing.T) {
	suite.Run(t, new(Suite))
}
