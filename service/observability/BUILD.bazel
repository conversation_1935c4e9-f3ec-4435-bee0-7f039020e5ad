load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "observability",
    srcs = [
        "metrics_service.go",
        "observability_service.go",
    ],
    importpath = "sentioxyz/sentio/service/observability",
    visibility = ["//visibility:public"],
    deps = [
        "//common/identifier",
        "//common/log",
        "//common/sysmetric",
        "//common/timescale",
        "//common/utils",
        "//service/common",
        "//service/common/contract",
        "//service/common/models",
        "//service/common/preloader",
        "//service/common/protos",
        "//service/common/query",
        "//service/common/redis",
        "//service/common/timerange",
        "//service/common/util",
        "//service/observability/protos",
        "//service/observability/repository",
        "//service/observability/util",
        "@com_github_pkg_errors//:errors",
        "@com_github_prometheus_client_golang//api/prometheus/v1:prometheus",
        "@com_github_prometheus_common//model",
        "@io_gorm_gorm//:gorm",
        "@io_opentelemetry_go_otel//:otel",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_x_exp//maps",
        "@org_golang_x_exp//slices",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "observability_test",
    srcs = ["metrics_service_test.go"],
    embed = [":observability"],
    deps = [
        "//common/timescale",
        "//common/timescale/mock",
        "//service/common/contract",
        "//service/common/contract/mock",
        "//service/common/models",
        "//service/common/preloader",
        "//service/common/protos",
        "//service/observability/protos",
        "//service/observability/repository/mock",
        "//service/processor/models",
        "//service/usage/protos/mock",
        "@com_github_golang_mock//gomock",
        "@com_github_stretchr_testify//require",
        "@com_github_stretchr_testify//suite",
    ],
)
