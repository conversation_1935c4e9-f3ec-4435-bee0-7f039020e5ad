package observability

import (
	"gorm.io/gorm"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/sysmetric"
	"sentioxyz/sentio/common/timescale"
	"sentioxyz/sentio/service/common/contract"
	"sentioxyz/sentio/service/common/redis"
	"sentioxyz/sentio/service/observability/protos"
	"sentioxyz/sentio/service/observability/repository"
)

type Service struct {
	protos.UnimplementedObservabilityServiceServer
	timescaleMultiClient  timescale.MultiClient
	repository            repository.Repository
	db                    *gorm.DB
	contractMapperFactory contract.MapperFactory
	systemMetricClient    sysmetric.API
}

func NewService(db *gorm.DB, timescaleConfigPath string, cacheOptions *timescale.CacheTTLOptions, systemMetricServerAddress string) *Service {
	var err error
	var systemMetricClient sysmetric.API
	if len(systemMetricServerAddress) > 0 {
		redisCli := redis.NewClientWithDefaultOptions()
		systemMetricClient, err = sysmetric.NewClientWithRedis(systemMetricServerAddress, redisCli, nil)
		if err != nil {
			log.Errore(err, "failed to create system metric client")
		}
	}
	return &Service{
		repository: repository.NewRepository(db),
		timescaleMultiClient: timescale.MustNewMultiClient(
			timescaleConfigPath,
			timescale.WithRedis(*redis.Address, cacheOptions),
		),
		db:                    db,
		contractMapperFactory: contract.NewMapperFactory(db),
		systemMetricClient:    systemMetricClient,
	}
}
