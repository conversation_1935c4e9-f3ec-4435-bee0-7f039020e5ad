package repository

import (
	"context"

	"sentioxyz/sentio/service/common/models"
	commRepo "sentioxyz/sentio/service/common/repository"
	processorModel "sentioxyz/sentio/service/processor/models"

	"gorm.io/gorm"
)

type Repository interface {
	GetProjectBySlug(ctx context.Context, owner string, slug string) (*models.Project, error)
	GetProcessorsByProjectAndVersion(
		ctx context.Context,
		projectID string,
		version int32,
	) (processorModel.Processors, error)
	GetChainIDsByProcessor(ctx context.Context, processorIDs []string) ([]string, error)
	GetProjectByID(ctx context.Context, projectID string) (*models.Project, error)
}

type repository struct {
	commRepo.Repository
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{
		commRepo.NewRepository(db),
	}
}

func (r *repository) GetChainIDsByProcessor(ctx context.Context, processorIDs []string) ([]string, error) {
	var result []string
	err := r.DB.WithContext(ctx).
		Raw("SELECT chain_id FROM chain_states WHERE processor_id in ? and chain_id != 'meta'", processorIDs).
		Scan(&result).Error
	return result, err
}

func (r *repository) GetProjectByID(ctx context.Context, projectID string) (*models.Project, error) {
	return r.Repository.GetProjectByID(r.DB, projectID)
}
