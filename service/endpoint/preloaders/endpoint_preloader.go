package preloaders

import (
	"context"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"gorm.io/gorm"
	"sentioxyz/sentio/service/common/preloader"
	"sentioxyz/sentio/service/common/repository"
	"sentioxyz/sentio/service/endpoint/model"
	"sentioxyz/sentio/service/endpoint/protos"
)

func EndpointPreLoader(ctx context.Context, method protoreflect.MethodDescriptor, req proto.Message, db *gorm.DB) (context.Context, error) {
	var endpointID string
	if r, ok := req.(*protos.Endpoint); ok {
		endpointID = r.Id
	}

	if r, ok := req.(*protos.DeleteEndpointRequest); ok {
		endpointID = r.EndpointId
	}
	if r, ok := req.(*protos.EndpointStatsRequest); ok {
		endpointID = r.EndpointId
	}
	if r, ok := req.(*protos.EndpointDocRequest); ok {
		endpointID = r.EndpointId
	}

	projectID := ""
	if endpointID != "" {
		endpoint := &model.Endpoint{
			ID: endpointID,
		}
		if err := db.WithContext(ctx).First(endpoint).Error; err == nil {
			projectID = endpoint.ProjectID
			ctx = context.WithValue(ctx, "endpoint", endpoint)
		}
	}

	if projectID != "" {
		repo := repository.NewRepository(db)
		project, err := repo.GetProjectByID(db, projectID)
		if err != nil {
			return ctx, err
		}
		ctx = context.WithValue(ctx, preloader.ProjectKeyName, project)
	}
	return ctx, nil
}

func PreloadedEndpoint(ctx context.Context) *model.Endpoint {
	if v, ok := ctx.Value("endpoint").(*model.Endpoint); ok {
		return v
	}
	return nil
}
