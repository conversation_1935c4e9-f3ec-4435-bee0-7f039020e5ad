package endpoint

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"sentioxyz/sentio/service/common/preloader"
	protos2 "sentioxyz/sentio/service/graphql/protos"
	"strconv"
	"strings"
	"time"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/protojson"
	analytic "sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/common/auth"
	commonModels "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/endpoint/model"
	"sentioxyz/sentio/service/endpoint/protos"

	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

func (e *EndpointService) Proxy(ctx context.Context, req *protos.EndpointRequest) (*httpbody.HttpBody, error) {
	project, err := e.repo.GetProjectBySlug(ctx, req.Owner, req.ProjectSlug)
	if err != nil {
		return nil, err
	}

	if project.Type == commonModels.ProjectTypeAction {
		return e.proxyAction(ctx, req, project)
	} else {
		identity := preloader.PreLoadedIdentity(ctx)
		return e.proxyEndpoint(ctx, req, identity, project)
	}
}

func (e *EndpointService) proxyEndpoint(ctx context.Context, req *protos.EndpointRequest, identity *commonModels.Identity, project *commonModels.Project) (*httpbody.HttpBody, error) {
	slug, async, _ := strings.Cut(req.Slug, "/")
	endpoint, err := e.repo.FindEndpoint(ctx, req.Owner, req.ProjectSlug, slug)
	if err != nil {
		return nil, err
	}
	if endpoint == nil {
		return nil, status.Errorf(codes.NotFound, "Endpoint not found")
	}
	meta := metadata.Pairs(EndpointIDHeader, endpoint.ID, ProjectIDHeader, endpoint.ProjectID)
	_ = grpc.SetHeader(ctx, meta)
	if !endpoint.Enabled {
		return nil, status.Errorf(codes.Unavailable, "Endpoint is disabled")
	}
	if !(endpoint.IsPublic && project.Public) {
		projectAccesses := auth.PreLoadedProjectAccess(ctx)
		if len(projectAccesses) == 0 || identity.IsAnonymous() {
			return nil, status.Errorf(codes.PermissionDenied, "api-key is required to access endpoint")
		}
	}

	switch endpoint.EndpointType {
	case model.QueryTypeSQL:
		if endpoint.Query == nil {
			return nil, status.Errorf(codes.NotFound, "Query not found")
		}
		resp, err := e.proxySQL(ctx, identity, req, endpoint, async)
		if err != nil {
			return nil, err
		}
		return resp, nil
	case model.QueryTypeGraphQL:
		if endpoint.GraphQLQuery == nil {
			return nil, status.Errorf(codes.NotFound, "Query not found")
		}
		resp, err := e.proxyGraphQL(ctx, identity, req, endpoint)
		if err != nil {
			return nil, err
		}
		return resp, nil
	default:
		return nil, status.Errorf(codes.InvalidArgument, "Invalid endpoint type")
	}
}

func (e *EndpointService) proxySQL(ctx context.Context, identity *commonModels.Identity, req *protos.EndpointRequest, endpoint *model.Endpoint, async string) (*httpbody.HttpBody, error) {
	var err error
	md, _ := metadata.FromIncomingContext(ctx)
	method := md.Get(".method")
	path := md.Get(".path")
	if len(method) == 0 || len(path) == 0 {
		return nil, status.Errorf(codes.Internal, "Method not found")
	}

	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)
	// mark the request as internal, so the usage won't be double counted
	ctx = metadata.AppendToOutgoingContext(ctx, "internal", "true")

	var result *protos.ProxyResponse
	switch method[0] {
	case "GET":
		// retrieve the query result
		if req.ResultId == "" {
			return nil, status.Errorf(codes.InvalidArgument, "Result id is required for GET method")
		}
		result, err = e.getResult(ctx, req.ResultId, endpoint)
	default:
		if async == "async" {
			// async call
			result, err = e.asyncQuery(ctx, req, endpoint)
		} else {
			// sync call
			result, err = e.syncQuery(ctx, req, endpoint)
		}
	}

	if identity != nil {
		if identity.APIKey != nil {
			_ = grpc.SetHeader(ctx, metadata.Pairs(CallerHeader, "api-key/"+identity.APIKey.Name))
		} else if identity.User != nil {
			_ = grpc.SetHeader(ctx, metadata.Pairs(CallerHeader, "user/"+identity.User.Username))
		}
	}

	if err != nil {
		return nil, err
	}
	response := &httpbody.HttpBody{
		ContentType: "application/json",
	}

	response.Data, err = protojson.Marshal(result)
	return response, err
}

func (e *EndpointService) asyncQuery(ctx context.Context, req *protos.EndpointRequest, endpoint *model.Endpoint) (*protos.ProxyResponse, error) {
	sqlQuery := endpoint.Query.ToProto()
	if err := ValidateParameter(sqlQuery.Parameters, req.Variables); err != nil {
		return nil, err
	}
	params, err := StructpbToRichStruct(req.Variables, sqlQuery.Parameters)
	if err != nil {
		return nil, err
	}
	sqlQuery.Parameters = params

	engine := analytic.ExecuteEngine_DEFAULT
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		query := md.Get(".query")
		if values, err := url.ParseQuery(query[0]); err == nil {
			if v := values.Get("version"); v != "" {
				version, err := strconv.Atoi(v)
				if err != nil {
					return nil, status.Errorf(codes.InvalidArgument, "Invalid version %s", version)
				} else {
					req.Version = int32(version)
				}
			}
			if sqlResultSize := values.Get(SQLResultSize); sqlResultSize != "" {
				if v, err := strconv.Atoi(sqlResultSize); err == nil {
					req.Size = int32(v)
				}
			}
			if sqlEngine := values.Get(SQLEngine); sqlEngine != "" {
				if e, ok := analytic.ExecuteEngine_value[strings.ToUpper(sqlEngine)]; ok {
					engine = analytic.ExecuteEngine(e)
				}
			}
		}
	}
	sqlQuery.Size = req.Size

	request := &analytic.RerunSQLQueryRequest{
		ProjectId:    endpoint.ProjectID,
		ProjectOwner: endpoint.Project.OwnerName,
		ProjectSlug:  endpoint.Project.Slug,
		QueryId:      *endpoint.QueryID,
		Version:      req.Version,
		SqlQuery:     sqlQuery,
		Engine:       &engine,
	}

	rjson, _ := protojson.Marshal(request)
	if rjson != nil {
		log.Infow("rerun SQL Query", "request", string(rjson))
	}

	response, err := e.analyticClient.RerunSQLQuery(ctx, request)
	if err != nil {
		return nil, err
	}

	ret := &protos.ProxyResponse{}
	ret.Response = &protos.ProxyResponse_AsyncResponse_{
		AsyncResponse: &protos.ProxyResponse_AsyncResponse{
			QueryId:       *endpoint.QueryID,
			ExecutionInfo: response.ExecutionInfo,
			ResultUrl:     e.resultUrl(endpoint, response.ExecutionInfo.ExecutionId),
			ResultId:      response.ExecutionInfo.ExecutionId,
		},
	}

	_ = grpc.SetHeader(ctx, metadata.Pairs(HttpCodeHeader, "202"))
	return ret, nil
}

func (e *EndpointService) syncQuery(ctx context.Context, req *protos.EndpointRequest, endpoint *model.Endpoint) (*protos.ProxyResponse, error) {
	ret := &protos.ProxyResponse{}
	var err error
	cursor := ""
	sqlQuery := endpoint.Query.ToProto()
	if err = ValidateParameter(sqlQuery.Parameters, req.Variables); err != nil {
		return nil, err
	}
	sqlQuery.Parameters, err = StructpbToRichStruct(req.Variables, sqlQuery.Parameters)
	if err != nil {
		return nil, err
	}
	engine := analytic.ExecuteEngine_DEFAULT
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		query := md.Get(".query")
		if len(query) > 0 {
			if values, err := url.ParseQuery(query[0]); err == nil {
				if req.CachePolicy == nil {
					_ = populateCachePolicy(values, req)
				}
				if c := values.Get(SQLQueryCursor); c != "" {
					cursor = c
				}
				if v := values.Get("version"); v != "" {
					version, err := strconv.Atoi(v)
					if err != nil {
						return nil, status.Errorf(codes.InvalidArgument, "Invalid version %s", version)
					} else {
						req.Version = int32(version)
					}
				}
				if sqlResultSize := values.Get(SQLResultSize); sqlResultSize != "" {
					if v, err := strconv.Atoi(sqlResultSize); err == nil {
						req.Size = int32(v)
					}
				}
				if sqlEngine := values.Get(SQLEngine); sqlEngine != "" {
					if e, ok := analytic.ExecuteEngine_value[strings.ToUpper(sqlEngine)]; ok {
						engine = analytic.ExecuteEngine(e)
					}
				}
			}
		}
	}
	sqlQuery.Size = req.Size
	var request *analytic.SQLRequest
	if cursor != "" {
		request = &analytic.SQLRequest{
			ProjectId: endpoint.ProjectID,
			Version:   req.Version,
			QueryType: &analytic.SQLRequest_Cursor{
				Cursor: cursor,
			},
			Source:      analytic.Source_ENDPOINT,
			CachePolicy: req.CachePolicy,
			Engine:      &engine,
		}
	} else {
		request = &analytic.SQLRequest{
			ProjectId: endpoint.ProjectID,
			Version:   req.Version,
			QueryType: &analytic.SQLRequest_SqlQuery{
				SqlQuery: sqlQuery,
			},
			Source:      analytic.Source_ENDPOINT,
			CachePolicy: req.CachePolicy,
		}
	}

	rjson, _ := protojson.Marshal(request)
	if rjson != nil {
		log.Infow("execute SQL", "request", string(rjson))
	}

	result, err := e.analyticClient.ExecuteSQL(ctx, request)
	if err != nil {
		return nil, err
	}

	ret.Response = &protos.ProxyResponse_SyncSqlResponse{
		SyncSqlResponse: result,
	}
	if result.ComputeStats != nil {
		queryDuration := result.ComputeStats.ComputeCostMs
		_ = grpc.SetHeader(ctx, metadata.Pairs(SQLQueryDurationHeader, strconv.FormatInt(queryDuration, 10)))
	}
	return ret, nil
}

func (e *EndpointService) getResult(ctx context.Context, requestID string, endpoint *model.Endpoint) (*protos.ProxyResponse, error) {
	result, err := e.analyticClient.QuerySQLResult(ctx, &analytic.QuerySQLResultRequest{
		ExecutionId: requestID,
		ProjectId:   endpoint.ProjectID,
	})
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "Failed to get query result: %v", err)
	}
	ret := &protos.ProxyResponse{}
	switch result.ExecutionInfo.Status {
	case analytic.ExecutionStatus_FINISHED:
		ret.Response = &protos.ProxyResponse_SqlQueryResult{
			SqlQueryResult: result,
		}
		if result.ExecutionInfo != nil && result.ExecutionInfo.ComputeStats != nil {
			queryDuration := result.ExecutionInfo.ComputeStats.ComputeCostMs
			_ = grpc.SetHeader(ctx, metadata.Pairs(SQLQueryDurationHeader, strconv.FormatInt(queryDuration, 10)))
		}

	case analytic.ExecutionStatus_RUNNING | analytic.ExecutionStatus_PENDING:
		_ = grpc.SetHeader(ctx, metadata.Pairs(HttpCodeHeader, "302"))

		ret.Response = &protos.ProxyResponse_AsyncResponse_{
			AsyncResponse: &protos.ProxyResponse_AsyncResponse{
				QueryId:       *endpoint.QueryID,
				ExecutionInfo: result.ExecutionInfo,
				ResultUrl:     e.resultUrl(endpoint, requestID),
				ResultId:      requestID,
			},
		}
	case analytic.ExecutionStatus_KILLED:
		ret.Response = &protos.ProxyResponse_AsyncResponse_{
			AsyncResponse: &protos.ProxyResponse_AsyncResponse{
				QueryId:       *endpoint.QueryID,
				ExecutionInfo: result.ExecutionInfo,
			},
		}
		return ret, status.Errorf(codes.Aborted, "Query failed ")
	}
	return ret, nil
}

func (e *EndpointService) proxyAction(ctx context.Context, req *protos.EndpointRequest, project *commonModels.Project) (*httpbody.HttpBody, error) {
	processor, err := e.repo.FindRunningProcessor(ctx, project.ID, req.Version)
	if err != nil {
		return nil, err
	}
	processorAddress := fmt.Sprintf("processor-action-%s.%s-driverjob-headless-service:%d", processor.ID, e.releaseName, e.processorPort)
	proxyURL := url.URL{
		Scheme: "http",
		Host:   strings.ToLower(processorAddress),
		Path:   "/" + req.Slug,
	}

	md, _ := metadata.FromIncomingContext(ctx)
	method := "GET"
	if m := md.Get(".method"); len(m) > 0 {
		method = m[0]
	}
	if m := md.Get(".query"); len(m) > 0 {
		proxyURL.RawQuery = m[0]
	}
	var body []byte
	if req.Body != nil {
		body = req.Body.Data
	}

	httpRequest, err := http.NewRequest(method, proxyURL.String(), bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	for header, values := range md {
		if strings.HasPrefix(header, ":") || strings.HasPrefix(header, ".") || strings.HasPrefix(header, "grpc") {
			continue
		}
		for _, v := range values {
			httpRequest.Header.Add(header, v)
		}
	}

	md.Set(ProjectIDHeader, project.ID)

	client := &http.Client{}
	log.Debugw("Proxy request", "method", httpRequest.Method,
		"url", httpRequest.URL.String(),
		"headers", httpRequest.Header)

	httpResponse, err := client.Do(httpRequest)
	if err != nil {
		_ = grpc.SendHeader(ctx, md)
		return nil, status.Errorf(codes.Internal, "connect to processor failed: %v", err)
	}

	response := &httpbody.HttpBody{}
	md.Append(HttpCodeHeader, strconv.Itoa(httpResponse.StatusCode))
	for k, v := range httpResponse.Header {
		switch strings.ToLower(k) {
		case "content-type":
			response.ContentType = v[0]
		// skip encoding headers
		case "content-length", "transfer-encoding", "content-encoding":
			continue
		default:
			for _, vv := range v {
				md.Append(fmt.Sprintf("%s%s", HttpResponseHeader, k), vv)
			}
		}
	}
	if route := httpResponse.Header.Get("Action-Route"); len(route) > 0 {
		md.Set(EndpointIDHeader, model.ActionId(model.MethodType{method}, route))
	}

	defer httpResponse.Body.Close()
	response.Data, _ = io.ReadAll(httpResponse.Body)
	log.Debugw("Proxy respond", "status", httpResponse.StatusCode, "received", len(response.Data))

	return response, nil
}

func (e *EndpointService) proxyGraphQL(ctx context.Context, identity *commonModels.Identity, req *protos.EndpointRequest, endpoint *model.Endpoint) (*httpbody.HttpBody, error) {
	var err error
	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)
	ctx = metadata.AppendToOutgoingContext(ctx, "internal", "true")

	query := endpoint.GraphQLQuery

	vars, err := mergeStruct(query.Variables, req.Variables)
	if err != nil {
		return nil, err
	}

	request := &protos2.GraphQLRequest{
		Query:        query.Query,
		Variables:    vars,
		Version:      req.Version,
		ProjectOwner: req.Owner,
		ProjectSlug:  req.ProjectSlug,
	}
	if operation := getOperation(query.Query); operation != "" {
		request.OperationName = operation
	}
	start := time.Now()
	result, err := e.graphqlClient.Query(ctx, request)
	queryDuration := uint64(time.Since(start).Milliseconds())
	_ = grpc.SetHeader(ctx, metadata.Pairs(SQLQueryDurationHeader, strconv.FormatUint(queryDuration, 10)))

	if identity != nil {
		if identity.APIKey != nil {
			_ = grpc.SetHeader(ctx, metadata.Pairs(CallerHeader, "api-key/"+identity.APIKey.Name))
		} else if identity.User != nil {
			_ = grpc.SetHeader(ctx, metadata.Pairs(CallerHeader, "user/"+identity.User.Username))
		}
	}

	return result, err
}

func getOperation(query string) string {
	regex := regexp.MustCompile(`(?m)^\s*query\s+(\w+)\s+{`)
	matches := regex.FindStringSubmatch(query)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}
