package repository

import (
	"errors"
	"fmt"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/huandu/go-sqlbuilder"
	"golang.org/x/net/context"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"sentioxyz/sentio/common/log"
	commonmodels "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/common/repository"
	"sentioxyz/sentio/service/endpoint/model"
	"sentioxyz/sentio/service/endpoint/protos"
	"strconv"
	"strings"
	"time"
)

type EndpointRepository struct {
	repository.Repository
}

func NewRepository(db *gorm.DB) *EndpointRepository {
	repo := repository.NewRepository(db)
	return &EndpointRepository{Repository: repo}
}

func (r *EndpointRepository) FindEndpoint(ctx context.Context, owner, projectSlug, slug string) (*model.Endpoint, error) {
	db := r.DB.WithContext(ctx)
	endpoint := model.Endpoint{}
	project, err := r.GetProjectBySlug(ctx, owner, projectSlug)
	if err != nil {
		return nil, err
	}
	err = db.Preload("Query").Preload("GraphQLQuery").Preload("Creator").Preload("Project").Where("slug = ? and project_id = ?", slug, project.ID).First(&endpoint).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	endpoint.Project.GetOwner(db)
	return &endpoint, nil
}

func (r *EndpointRepository) FindEndpointByID(ctx context.Context, id string) (*model.Endpoint, error) {
	db := r.DB.WithContext(ctx)
	endpoint := model.Endpoint{}
	err := db.Preload("Query").Preload("GraphQLQuery").
		Preload("Creator").Preload("Project").Where("id = ?", id).First(&endpoint).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	endpoint.Project.GetOwner(db)
	return &endpoint, err
}

func (r *EndpointRepository) FindEndpoints(ctx context.Context, projectID string) ([]*model.Endpoint, error) {
	db := r.DB.WithContext(ctx)
	var endpoints []*model.Endpoint
	err := db.Preload("Query").Preload("GraphQLQuery").Preload("Creator").Preload("Project").Where("project_id = ?", projectID).Find(&endpoints).Error
	return endpoints, err
}

func (r *EndpointRepository) FindEndpointLogs(ctx context.Context, conn clickhouse.Conn, env, projectID string, req *protos.EndpointLogRequest) ([]*commonmodels.RequestLog, error) {
	q := sqlbuilder.Select("Timestamp as CreatedAt",
		"LogAttributes['request_id'] as ID",
		"LogAttributes['endpoint_id'] as EndpointID",
		"LogAttributes['project_id'] as ProjectID",
		"toUInt32OrZero(LogAttributes['status_code']) as StatusCode",
		"LogAttributes['request_header'] as RequestHeader",
		"LogAttributes['response_header'] as ResponseHeader",
		"LogAttributes['request_body'] as RequestBody",
		"LogAttributes['response_body'] as ResponseBody",
		"toUInt64OrZero(LogAttributes['query_time']) as QueryTime",
		"toUInt64OrZero(LogAttributes['duration']) as Duration",
		"LogAttributes['url'] as URL",
		"LogAttributes['caller'] as Caller",
		"LogAttributes['http_method'] as Method").From("otel_logs")
	q.Where(q.Equal("ServiceName", "endpoint-server"),
		q.Equal("Body", "Endpoint Request Log"),
		q.Equal("ResourceAttributes['deployment.environment']", env),
	)

	q.Where(q.Between("TimestampTime", req.StartTime.AsTime().Format("2006-01-02 15:04:05"), req.EndTime.AsTime().Format("2006-01-02 15:04:05")))

	if projectID != "" {
		q = q.Where(q.Equal("LogAttributes['project_id']", projectID))
	}
	if req.EndpointId != "" {
		q = q.Where(q.Equal("LogAttributes['endpoint_id']", req.EndpointId))
	}
	for _, filter := range req.Filters {
		switch filter.Filter.(type) {
		case *protos.EndpointLogRequest_Filter_HttpStatusCode:
			q = q.Where(q.Equal("LogAttributes['status_code']", strconv.Itoa(int(filter.GetHttpStatusCode()))))
		case *protos.EndpointLogRequest_Filter_RequestId:
			q = q.Where(q.Equal("LogAttributes['request_id']", filter.GetRequestId()))
		case *protos.EndpointLogRequest_Filter_Method:
			q = q.Where(q.Equal("LogAttributes['http_method']", filter.GetMethod()))
		case *protos.EndpointLogRequest_Filter_DurationLte:
			q = q.Where(q.LE("toUInt64(LogAttributes['duration'])", filter.GetDurationLte()))
		case *protos.EndpointLogRequest_Filter_DurationGte:
			q = q.Where(q.GE("toUInt64(LogAttributes['duration'])", filter.GetDurationGte()))
		case *protos.EndpointLogRequest_Filter_RequestHeaderKv:
			if k, v, found := strings.Cut(filter.GetRequestHeaderKv(), ":"); found {
				q = q.Where(q.Like(fmt.Sprintf(`simpleJSONExtractRaw(LogAttributes['request_header'], '%s')`, k), fmt.Sprintf("%%%s%%", v)))
			}
		case *protos.EndpointLogRequest_Filter_ResponseHeaderKv:
			if k, v, found := strings.Cut(filter.GetResponseHeaderKv(), ":"); found {
				q = q.Where(q.Like(fmt.Sprintf(`simpleJSONExtractRaw(LogAttributes['response_header'], '%s')`, k), fmt.Sprintf("%%%s%%", v)))
			}
		case *protos.EndpointLogRequest_Filter_Caller:
			q = q.Where(q.Equal("LogAttributes['caller']", filter.GetCaller()))
		}

	}
	if len(req.Search) > 0 {
		q = q.Where(q.Or(
			q.Like("LogAttributes['request_body']", "%"+req.Search+"%"),
			q.Like("LogAttributes['response_body']", "%"+req.Search+"%"),
			q.Like("LogAttributes['request_header']", "%"+req.Search+"%"),
			q.Like("LogAttributes['response_header']", "%"+req.Search+"%"),
			q.Like("LogAttributes['url']", "%"+req.Search+"%"),
			q.Like("LogAttributes['endpoint_id']", "%"+req.Search+"%"),
			q.Like("LogAttributes['request_id']", "%"+req.Search+"%"),
		))
	}
	q = q.OrderBy("Timestamp DESC")
	if req.Pagination != nil {
		var limit, offset int64 = 100, 0
		if req.Pagination.Limit > 0 {
			limit = req.Pagination.Limit
		}
		if req.Pagination.Offset > 0 {
			offset = req.Pagination.Offset
		}
		q = q.Limit(int(limit)).Offset(int(offset))
	}
	sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
	if err != nil {
		return nil, err
	}
	rows, err := conn.Query(ctx, sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var logs []*commonmodels.RequestLog
	for rows.Next() {
		log := commonmodels.RequestLog{}
		err = rows.ScanStruct(&log)
		if err != nil {
			return nil, err
		}
		logs = append(logs, &log)
	}
	return logs, nil
}

type SimpleStats struct {
	Count    uint64
	LastCall time.Time
}

func (r *EndpointRepository) SimpleEndpointStats(ctx context.Context, conn clickhouse.Conn, ids []string) (map[string]SimpleStats, error) {
	ret := make(map[string]SimpleStats)
	if len(ids) > 0 {
		sq := sqlbuilder.Select(
			"Timestamp",
			"LogAttributes['endpoint_id'] as endpoint_id",
			"toUInt64OrZero(LogAttributes['status_code']) as status_code",
		).From("otel_logs")
		var args []interface{}
		for _, id := range ids {
			args = append(args, id)
		}
		sq.Where(
			sq.Equal("ServiceName", "endpoint-server"),
			sq.Equal("Body", "Endpoint Request Log"),
			"TimestampTime >= toStartOfMonth(now())",
			sq.In("LogAttributes['endpoint_id']", args...),
		)
		subsql, err := sqlbuilder.ClickHouse.Interpolate(sq.Build())
		if err != nil {
			return nil, err
		}
		q := sqlbuilder.Select(
			"endpoint_id",
			"sum(if(status_code>=200 and status_code <300, 1, 0)) as success",
			"max(Timestamp) as last_call",
		).From(fmt.Sprintf("(%s) x", subsql))
		q.GroupBy("endpoint_id")
		sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
		if err != nil {
			return nil, err
		}
		rows, err := conn.Query(ctx, sql)
		if err != nil {
			return nil, err
		}
		for rows.Next() {
			var id string
			var count uint64
			var lastCall time.Time
			err = rows.Scan(&id, &count, &lastCall)
			if err != nil {
				return nil, err
			}
			ret[id] = SimpleStats{Count: count, LastCall: lastCall}
		}
	}
	return ret, nil
}

func (r *EndpointRepository) GetEndpointStats(ctx context.Context, conn clickhouse.Conn, release string, req *protos.EndpointStatsRequest) (*protos.EndpointStatsResponse, error) {
	sq := sqlbuilder.Select(
		"toUInt32OrZero(LogAttributes['status_code']) as status_code",
		"toUInt64OrZero(LogAttributes['duration']) as duration",
		"toUInt64OrZero(LogAttributes['query_time']) as query_time",
	).From("otel_logs")
	sq.Where(
		sq.Equal("ServiceName", "endpoint-server"),
		sq.Equal("ResourceAttributes['deployment.environment']", release),
		sq.Equal("Body", "Endpoint Request Log"),
		sq.Between("TimestampTime", req.StartTime.AsTime().Format("2006-01-02 15:04:05"), req.EndTime.AsTime().Format("2006-01-02 15:04:05")),
		sq.Equal("LogAttributes['endpoint_id']", req.EndpointId))
	subsql, err := sqlbuilder.ClickHouse.Interpolate(sq.Build())
	if err != nil {
		return nil, err
	}
	q := sqlbuilder.Select(
		"COUNT(*) as total",
		"SUM(if(status_code >= 400, 1, 0)) as errors",
		"toUInt64(ifNotFinite(avg(duration), 0)) as avg_duration",
		"ifNull(max(duration), 0) as max_duration",
		"ifNull(min(duration), 0) as min_duration",
		"toUInt64(ifNotFinite(median(duration), 0)) as median_duration",
		"toUInt64(ifNotFinite(avg(query_time), 0)) as avg_query_duration",
		"ifNull(max(query_time), 0) as max_query_duration",
		"ifNull(min(query_time), 0) as min_query_duration",
		"toUInt64(ifNotFinite(median(query_time), 0)) as median_query_duration",
	).From(fmt.Sprintf("(%s) x", subsql))
	sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
	if err != nil {
		return nil, err
	}
	log.Debug("Query: ", sql)
	rows, err := conn.Query(ctx, sql)
	defer rows.Close()
	if err != nil {
		return nil, err
	}
	if rows.Next() {
		var total, errors, avgDuration, maxDuration, minDuration, medianDuration uint64
		var avgQueryDuration, maxQueryDuration, minQueryDuration, medianQueryDuration uint64
		err = rows.Scan(&total, &errors, &avgDuration, &maxDuration, &minDuration, &medianDuration, &avgQueryDuration, &maxQueryDuration, &minQueryDuration, &medianQueryDuration)
		if err != nil {
			return nil, err
		}

		return &protos.EndpointStatsResponse{
			TotalRequests: total,
			TotalErrors:   errors,
			Durations: &protos.EndpointStatsResponse_Durations{
				Avg:    avgDuration,
				Max:    maxDuration,
				Min:    minDuration,
				Median: medianDuration,
			},
			QueryDurations: &protos.EndpointStatsResponse_Durations{
				Avg:    avgQueryDuration,
				Max:    maxQueryDuration,
				Min:    minQueryDuration,
				Median: medianQueryDuration,
			},
		}, nil
	}
	return nil, nil
}

func (r *EndpointRepository) GetEndpointTimeseries(ctx context.Context, conn clickhouse.Conn, release string, req *protos.EndpointStatsRequest) ([]*protos.EndpointStatsResponse_TimeSeries, error) {
	sq := sqlbuilder.Select(
		"TimestampTime as time",
		"toUInt32OrZero(LogAttributes['status_code']) as status_code",
		"if(status_code >= 400, 1, 0) as error",
		"if(status_code >= 400, 0, 1) as success",
		"toUInt64OrZero(LogAttributes['duration']) as duration",
		"toUInt64OrZero(LogAttributes['query_time']) as query_time",
	).From("otel_logs")
	sq.Where(
		sq.Equal("ServiceName", "endpoint-server"),
		sq.Equal("ResourceAttributes['deployment.environment']", release),
		sq.Equal("Body", "Endpoint Request Log"),
		sq.Between("TimestampTime", req.StartTime.AsTime().Format("2006-01-02 15:04:05"), req.EndTime.AsTime().Format("2006-01-02 15:04:05")),
		sq.Equal("LogAttributes['endpoint_id']", req.EndpointId))
	subsql, err := sqlbuilder.ClickHouse.Interpolate(sq.Build())
	if err != nil {
		return nil, err
	}
	q := sqlbuilder.Select(
		"avg(duration * success) AS avg_duration",
		"max(duration * success) AS max_duration",
		"avg(query_time * success) as avg_query_time",
		"max(query_time * success) as max_query_time",
		"sum(success) as success_count",
		"sum(error) as error_count",
		fmt.Sprintf("toStartOfInterval(time,INTERVAL %d SECOND) AS h", req.StepSeconds),
	).From(fmt.Sprintf("(%s) x", subsql))
	q.GroupBy("h")
	q.OrderBy(fmt.Sprintf("h WITH FILL STEP INTERVAL %d SECOND", req.StepSeconds))
	sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
	if err != nil {
		return nil, err
	}
	log.Debug("Query: ", sql)
	rows, err := conn.Query(ctx, sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var ret []*protos.EndpointStatsResponse_TimeSeries
	for rows.Next() {
		var avgDuration, avgQueryTime float64
		var successCount, errorCount, maxDuration, maxQueryTime uint64
		var h time.Time
		err = rows.Scan(&avgDuration, &maxDuration, &avgQueryTime, &maxQueryTime, &successCount, &errorCount, &h)
		if err != nil {
			return nil, err
		}
		ret = append(ret, &protos.EndpointStatsResponse_TimeSeries{
			Time:              timestamppb.New(h),
			SuccessCount:      successCount,
			ErrorCount:        errorCount,
			AvgDurations:      uint64(avgDuration),
			MaxDurations:      maxDuration,
			AvgQueryDurations: uint64(avgQueryTime),
			MaxQueryDurations: maxQueryTime,
		})
	}

	return ret, nil
}
