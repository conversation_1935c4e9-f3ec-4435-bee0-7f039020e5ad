package endpoint

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/common/log"
	commonModel "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/endpoint/repository"
	"strconv"
	"strings"
	"time"

	"github.com/goccy/go-json"
	"golang.org/x/net/context"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const BodyLimit = 4096

type logResponseWriter struct {
	http.ResponseWriter
	statusCode     int
	responseBuffer bytes.Buffer
	header         http.Header
}

func (rsp *logResponseWriter) WriteHeader(code int) {
	rsp.statusCode = code
	for k := range rsp.ResponseWriter.Header() {
		if strings.HasPrefix(k, "Grpc-Metadata-") {
			rsp.Header().Del(k)
		}
	}
	rsp.ResponseWriter.WriteHeader(code)
}

func (rsp *logResponseWriter) Write(b []byte) (int, error) {
	// truncate the response body if it exceeds the limit
	left := BodyLimit - rsp.responseBuffer.Len()
	if left > 0 {
		if len(b) > left {
			rsp.responseBuffer.Write(b[0:left])
		} else {
			rsp.responseBuffer.Write(b)
		}
	}
	rsp.header = rsp.ResponseWriter.Header()
	return rsp.ResponseWriter.Write(b)
}

// Unwrap returns the original http.ResponseWriter. This is necessary
// to expose Flush() and Push() on the underlying response writer.
func (rsp *logResponseWriter) Unwrap() http.ResponseWriter {
	return rsp.ResponseWriter
}

func newLogResponseWriter(w http.ResponseWriter) *logResponseWriter {
	return &logResponseWriter{w,
		http.StatusOK,
		bytes.Buffer{},
		make(http.Header),
	}
}

// parseEndpointPath extracts owner, project_slug, and endpoint_slug from the URL path
// Expected format: /{owner}/{project_slug}/{slug}/**
func parseEndpointPath(path string) (owner, projectSlug, endpointSlug string) {
	// Remove leading slash and split by '/'
	path = strings.TrimPrefix(path, "/")
	parts := strings.Split(path, "/")

	if len(parts) >= 1 {
		owner = parts[0]
	}
	if len(parts) >= 2 {
		projectSlug = parts[1]
	}
	if len(parts) >= 3 {
		endpointSlug = parts[2]
	}

	return owner, projectSlug, endpointSlug
}

// LogRequestBodyWithDB creates a logging middleware that can fallback to database queries
func LogRequestBodyWithDB(db *gorm.DB) func(http.Handler) http.Handler {
	repo := repository.NewRepository(db)
	return func(h http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			if strings.HasPrefix(r.URL.Path, "/api/v1") {
				h.ServeHTTP(w, r)
				return
			}
			lw := newLogResponseWriter(w)
			// Note that buffering the entire request body could consume a lot of memory.
			body, err := io.ReadAll(r.Body)
			if err != nil {
				http.Error(w, fmt.Sprintf("failed to read body: %v", err), http.StatusBadRequest)
				return
			}
			newCtx := context.WithValue(r.Context(), "body", body)
			clonedR := r.Clone(newCtx)
			clonedR.Body = io.NopCloser(bytes.NewReader(body))

			h.ServeHTTP(lw, clonedR)
			duration := uint64(time.Since(start).Milliseconds())

			requestID := r.Header.Get("X-Request-Id")
			if requestID == "" {
				requestID = gonanoid.Must(16)
			}
			requestLog := commonModel.RequestLog{
				Method:         r.Method,
				ID:             requestID,
				EndpointID:     nil,
				StatusCode:     uint32(lw.statusCode),
				RequestBody:    string(body),
				ResponseBody:   lw.responseBuffer.String(),
				RequestHeader:  marshalHeader(r.Header),
				ResponseHeader: marshalHeader(lw.Header()),
				Duration:       &duration,
				CreatedAt:      start,
			}

			// Try to get project ID from response headers first (preferred)
			projectID := lw.Header().Get("Grpc-Metadata-" + ProjectIDHeader)
			endpointID := lw.Header().Get("Grpc-Metadata-" + EndpointIDHeader)

			// If project ID is not set, try to extract it from the request URL
			if len(projectID) == 0 {
				owner, projectSlug, endpointSlug := parseEndpointPath(r.URL.Path)
				if owner != "" && projectSlug != "" && endpointSlug != "" {
					// Try to get project by slug
					if endpoint, err := repo.FindEndpoint(clonedR.Context(), owner, projectSlug, endpointSlug); err == nil && endpoint != nil {
						projectID = endpoint.ProjectID
						endpointID = endpoint.ID
					}
				}
			}

			// Set the resolved project and endpoint IDs
			if len(projectID) > 0 {
				requestLog.ProjectID = projectID
			}
			if len(endpointID) > 0 {
				requestLog.EndpointID = &endpointID
			}

			// Get additional metadata from response headers
			if queryTime := lw.Header().Get("Grpc-Metadata-" + SQLQueryDurationHeader); len(queryTime) > 0 {
				queryDuration, err := strconv.ParseUint(queryTime, 10, 64)
				if err == nil {
					requestLog.QueryTime = &queryDuration
				}
			}

			if caller := lw.Header().Get("Grpc-Metadata-" + CallerHeader); len(caller) > 0 {
				requestLog.Caller = caller
			}

			var logFunc func(template string, args ...interface{})
			if lw.statusCode >= 500 {
				logFunc = log.Errorw
			} else if lw.statusCode >= 400 {
				logFunc = log.Warnw
			} else {
				logFunc = log.Infow
			}
			if requestLog.EndpointID != nil && requestLog.ProjectID != "" {
				logFunc("Endpoint Request Log",
					"http_method", requestLog.Method,
					"url", r.URL.Path,
					"request_id", requestLog.ID,
					"status_code", requestLog.StatusCode,
					"duration", requestLog.Duration,
					"project_id", requestLog.ProjectID,
					"endpoint_id", requestLog.EndpointID,
					"query_time", requestLog.QueryTime,
					"timestamp_override", requestLog.CreatedAt.UTC().String(),
					"request_header", string(requestLog.RequestHeader),
					"response_header", string(requestLog.ResponseHeader),
					"caller", requestLog.Caller,
					"request_body", requestLog.RequestBody,
					"response_body", requestLog.ResponseBody,
				)
			}
		})
	}
}

func marshalHeader(header map[string][]string) datatypes.JSON {
	j, err := json.Marshal(header)
	if err != nil {
		return datatypes.JSON{}
	}
	return j
}
