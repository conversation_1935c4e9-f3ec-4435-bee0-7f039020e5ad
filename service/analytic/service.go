package analytic

import (
	"context"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/clients"
	"sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/repository"
	"sentioxyz/sentio/service/analytic/repository/cache"
	analyticmodels "sentioxyz/sentio/service/analytic/repository/models"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/networklimiter"
	"sentioxyz/sentio/service/common/priorityqueue"
	protoscommon "sentioxyz/sentio/service/common/protos"
	serviceredis "sentioxyz/sentio/service/common/redis"
	"sentioxyz/sentio/service/mvcontroller/refresh"
	"sentioxyz/sentio/service/mvcontroller/usermv"

	shell "github.com/ipfs/go-ipfs-api"
	"github.com/redis/go-redis/extra/redisotel/v9"
	"github.com/redis/go-redis/v9"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
)

type Service interface {
	protos.AnalyticServiceServer
	protos.SearchServiceServer

	GetEvents(ctx context.Context, req *protos.GetEventsRequest) (*protos.GetEventsResponse, error)
	GetEventPropertyValues(
		ctx context.Context,
		req *protos.GetEventPropertyValuesRequest,
	) (*protos.GetEventPropertyValuesResponse, error)
	QuerySegmentation(ctx context.Context, req *protos.SegmentationRequest) (*protos.QuerySegmentationResponse, error)
	QuerySegmentationInternal(ctx context.Context, req *protos.InternalSegmentationRequest) (*protos.QuerySegmentationResponse, error)
	QueryCohorts(
		ctx context.Context,
		req *protos.CohortsRequest,
	) (*protos.QueryCohortsResponse, error)
	ExecuteSQL(
		ctx context.Context, req *protos.SQLRequest) (*protos.SyncExecuteSQLResponse, error)
	ExecuteSQLAsync(
		ctx context.Context, req *protos.SQLRequest) (*protos.AsyncExecuteSQLResponse, error)
	TestSQL(
		ctx context.Context, req *protos.SQLRequest) (*protos.SyncExecuteSQLResponse, error)
	GetClickhouseStatus(ctx context.Context, req *emptypb.Empty) (*protoscommon.ClickhouseStatus, error)

	ListFacets(context.Context, *protos.ListFacetsRequest) (*protos.ListFacetsResponse, error)
	GetFacet(context.Context, *protos.GetFacetRequest) (*protos.GetFacetResponse, error)
	QueryLog(context.Context, *protos.LogQueryRequest) (*protos.LogQueryResponse, error)
	QueryLogMetrics(context.Context, *protos.LogQueryRequest) (*protos.QueryLogMetricsResponse, error)
	QueryTables(ctx context.Context, req *protos.QueryTablesRequest) (*protos.QueryTablesResponse, error)

	QuerySegmentationWithOptions(ctx context.Context, req *protos.SegmentationRequest,
		options SegmentationOptions) (*protos.QuerySegmentationResponse, error)

	SaveSQL(ctx context.Context, req *protos.SaveSQLRequest) (*protos.SaveSQLResponse, error)
	ListSQLQueries(ctx context.Context, req *protos.ListSQLQueriesRequest) (*protos.ListSQLQueriesResponse, error)
	DeleteSQLQuery(ctx context.Context, req *protos.DeleteSQLQueryRequest) (*protos.DeleteSQLQueryResponse, error)
	QuerySQLResult(ctx context.Context, req *protos.QuerySQLResultRequest) (*protos.QuerySQLResultResponse, error)
	GetSQLQuery(ctx context.Context, req *protos.GetSQLQueryRequest) (*protos.GetSQLQueryResponse, error)
	CancelSQLQuery(ctx context.Context, req *protos.CancelSQLQueryRequest) (*emptypb.Empty, error)
	QuerySQLExecutionDetail(ctx context.Context, req *protos.QuerySQLExecutionDetailRequest) (*protos.QuerySQLExecutionDetailResponse, error)
	SaveRefreshableMaterializedView(ctx context.Context, req *protos.SaveRefreshableMaterializedViewRequest) (*protos.SaveRefreshableMaterializedViewResponse, error)
	GetRefreshableMaterializedStatus(ctx context.Context, req *protos.GetRefreshableMaterializedViewStatusRequest) (*protos.GetRefreshableMaterializedViewStatusResponse, error)
	DeleteRefreshableMaterializedView(ctx context.Context, req *protos.DeleteRefreshableMaterializedViewRequest) (*emptypb.Empty, error)
	ListRefreshableMaterializedViews(ctx context.Context, req *protos.ListRefreshableMaterializedViewRequest) (*protos.ListRefreshableMaterializedViewResponse, error)
	SaveSharingSQL(ctx context.Context, req *protos.SaveSharingSQLRequest) (*protos.SaveSharingSQLResponse, error)
	GetSharingSQL(ctx context.Context, req *protos.GetSharingSQLRequest) (*protos.GetSharingSQLResponse, error)
}

type AsyncSQLService interface {
	PullSQLExecPackage(ctx context.Context) *analyticmodels.SQLExecPackage
	PullSQLExecPackageByPriority(ctx context.Context, priority *priorityqueue.Priority) *analyticmodels.SQLExecPackage
	BlockPullSQLExecPackage(ctx context.Context, duration time.Duration) (*analyticmodels.SQLExecPackage, priorityqueue.Priority)
	ExecuteInternalSQL(ctx context.Context, req *protos.SQLRequest, userID, queryID, executionID string) (*protos.SyncExecuteSQLResponse, error)
}

type service struct {
	protos.UnimplementedAnalyticServiceServer
	protos.UnimplementedSearchServiceServer

	auth                    auth.AuthManager
	db                      *gorm.DB
	repository              *repository.Repository
	redisClient             *redis.Client
	clickhouseMultiSharding event.MultiSharding
	ipfsShell               *shell.Shell
	cache                   cache.Cache
	limiter                 networklimiter.Limiter
	sqlPriorityQueue        priorityqueue.PriorityQueue[analyticmodels.SQLExecPackage]
	sqlWorkers              []SQLWorker
	sqlEngines              sqllib.Setting
	seaTableMappers         *sqllib.TableMappers
	ckRewriterClient        clients.RewriterServiceClient
	userMvController        usermv.Controller
	k8sContextUse           string
}

func NewService(
	authManager auth.AuthManager,
	db *gorm.DB,
	clickhouseMultiSharding event.MultiSharding,
	clickhouseConfigPath string,
	ipfsNodeAddr, clickhouseRewriterAddr, sqlWorkerSettings string,
	clickhouseMvDisplay bool, k8sContextUse, clickhouseLimitControlConfigPath string,
	userMvWatchIntervalSeconds int64) Service {
	var err error
	var repo *repository.Repository
	redisClient := serviceredis.NewClientWithDefaultOptions()
	if redisClient != nil {
		err = redisotel.InstrumentTracing(redisClient)
		if err != nil {
			return nil
		}
	}
	repo = repository.NewRepository(db)
	var ipfsShell *shell.Shell
	if ipfsNodeAddr != "" {
		ipfsShell = shell.NewShell(ipfsNodeAddr)
		ipfsShell.SetTimeout(5 * time.Second)
	}
	seaTableMappers := mapper.NewTableMappers(mapper.WithK8sCluster(mapper.SeaCluster), mapper.WithConn(clickhouseMultiSharding.GetDefaultShard().GetSentioConn()))
	q := priorityqueue.NewPriorityQueue[analyticmodels.SQLExecPackage](redisClient, priorityqueue.StrictSchedule)
	_ = q.RegisterPriority(priorityqueue.Low)
	_ = q.RegisterPriority(priorityqueue.Medium)
	_ = q.RegisterPriority(priorityqueue.High)
	ckRewriterClient, err := clients.NewRewriterClient(clickhouseRewriterAddr)
	if err != nil {
		log.Warnf("failed to create rewriter client: %v", err)
	}
	var mvRefresher refresh.MvRefresher
	if clickhouseMvDisplay {
		mvRefresher = refresh.NewMvRefresher(context.Background(), "sentio-sea", refresh.Options{
			ViewMode: true,
		})
		for name, mappers := range mvRefresher.Mappers() {
			seaTableMappers.AddMaterializedViewMapper(name, mappers, db)
		}
	}
	var limiter networklimiter.Limiter
	if clickhouseLimitControlConfigPath != "" {
		limiter = networklimiter.NewLimiter(redisClient,
			time.Second*time.Duration(int64(*clickhouse.MaxExecutionTime)), clickhouseLimitControlConfigPath)
	} else {
		limiter = networklimiter.NewLimiterWithDefaultConfig(redisClient, time.Second*time.Duration(int64(*clickhouse.MaxExecutionTime)))
	}
	ctx := context.Background()
	c := cache.NewCache()
	var interval = 0
	if userMvWatchIntervalSeconds > 0 {
		nmin := int(userMvWatchIntervalSeconds / 2)
		nmax := int(userMvWatchIntervalSeconds)
		interval = rand.Intn(nmax-nmin+1) + nmin
	}
	engines := sqllib.NewSettings()
	if err := engines.LoadConfig(clickhouseConfigPath); err != nil {
		log.Fatalf("failed to load clickhouse engine config: %v", err)
	}
	userMvController := usermv.NewController(ctx,
		repo, clickhouseMultiSharding, redisClient, ckRewriterClient,
		ipfsShell, c, seaTableMappers, authManager, interval, engines)
	go func() {
		_ = userMvController.Init()
		if err := userMvController.Watch(ctx); err != nil {
			log.Errorf("failed to watch user mv: %v", err)
		}
	}()
	go func() {
		for {
			_, err := mapper.RefreshDashTableMappers(context.Background(),
				repo.Repository, clickhouseMultiSharding, redisClient, c, ipfsShell)
			if err != nil {
				log.Errorf("failed to refresh dash table mappers: %v", err)
			}
			time.Sleep(time.Second * 20)
		}
	}()

	s := &service{
		auth:                    authManager,
		db:                      db,
		repository:              repo,
		redisClient:             redisClient,
		clickhouseMultiSharding: clickhouseMultiSharding,
		ipfsShell:               ipfsShell,
		cache:                   c,
		seaTableMappers:         seaTableMappers,
		limiter:                 limiter,
		sqlPriorityQueue:        q,
		sqlEngines:              engines,
		ckRewriterClient:        ckRewriterClient,
		k8sContextUse:           k8sContextUse,
		userMvController:        userMvController,
	}

	// Initialize SQL workers
	var tierWorkers = strings.Split(sqlWorkerSettings, ",")
	var idx = 0
	for _, tierWorker := range tierWorkers {
		args := strings.Split(tierWorker, ":")
		if len(args) != 2 {
			log.Warnf("invalid sql worker config: %s", tierWorker)
			continue
		}
		priority := priorityqueue.FromString(args[0])
		workerNum, err := strconv.ParseUint(args[1], 10, 64)
		if err != nil {
			log.Warnf("invalid sql worker config: %s", tierWorker)
			continue
		}
		for i := uint64(0); i < workerNum; i++ {
			worker := NewSQLWorker(s, s.db, idx, &priority)
			if idx == 0 {
				go worker.KillTimeout(ctx)
			}
			idx++
			go worker.Run(ctx)
			s.sqlWorkers = append(s.sqlWorkers, worker)
		}
		log.Infof("sql worker tier for %s initialized with %d workers", priority.String(), workerNum)
	}
	return s
}

func (s *service) initialized() bool {
	return s.db != nil && s.repository != nil && s.clickhouseMultiSharding != nil
}

func (s *service) PullSQLExecPackage(ctx context.Context) *analyticmodels.SQLExecPackage {
	ctx, logger := log.FromContext(ctx)
	if s.sqlPriorityQueue == nil {
		return nil
	}
	execPackage, err := s.sqlPriorityQueue.Pop(ctx)
	if err != nil {
		logger.Warnf("failed to pop sql exec package: %v", err)
		return nil
	}
	return execPackage
}

func (s *service) PullSQLExecPackageByPriority(ctx context.Context, priority *priorityqueue.Priority) *analyticmodels.SQLExecPackage {
	ctx, logger := log.FromContext(ctx)
	if s.sqlPriorityQueue == nil {
		return nil
	}
	execPackage, err := s.sqlPriorityQueue.PopByPriorityAscending(ctx, *priority)
	if err != nil {
		logger.Warnf("failed to pop sql exec package by priority[%d]: %v", priority, err)
		return nil
	}
	return execPackage
}

func (s *service) BlockPullSQLExecPackage(ctx context.Context, duration time.Duration) (*analyticmodels.SQLExecPackage,
	priorityqueue.Priority) {
	ctx, logger := log.FromContext(ctx)
	if s.sqlPriorityQueue == nil {
		return nil, priorityqueue.Low
	}
	execPackage, priority, err := s.sqlPriorityQueue.BlockUntilPop(ctx, duration)
	if err != nil {
		logger.Warnf("failed to block pull sql exec package: %v", err)
		return nil, priorityqueue.Low
	}
	return execPackage, priority
}
