package handler

import (
	"reflect"

	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/repository"
	analyticmodels "sentioxyz/sentio/service/analytic/repository/models"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/priorityqueue"

	"github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ExecuteSQLArgs struct {
	Args            *query.Args
	Repo            *repository.Repository
	AuthManager     auth.AuthManager
	RedisClient     *redis.Client
	SeaTableMappers *sqllib.TableMappers
	InternalRequest bool
	UserID          string
	QueryID         string
	PriorityQueue   priorityqueue.PriorityQueue[analyticmodels.SQLExecPackage]
	Engines         sqllib.Setting
}

func (e *ExecuteSQLArgs) Init(argv ...any) error {
	v := reflect.ValueOf(e).Elem()
	t := v.Type()
	if len(argv) != t.NumField() {
		return status.Errorf(codes.Internal,
			"invalid number of arguments: expected %d, got %d", t.NumField(), len(argv))
	}

	for i, value := range argv {
		field := v.Field(i)
		if !field.CanSet() {
			return status.Errorf(codes.Internal, "cannot set field %s[%d]", t.Field(i).Name, i)
		}

		if value == nil {
			field.Set(reflect.Zero(field.Type()))
		} else {
			val := reflect.ValueOf(value)
			switch {
			case val.Type().AssignableTo(field.Type()):
				field.Set(val)
			case val.Type().ConvertibleTo(field.Type()):
				field.Set(val.Convert(field.Type()))
			default:
				return status.Errorf(codes.Internal,
					"cannot assign value of type %s to field %s of type %s", val.Type(), t.Field(i).Name, field.Type())
			}
		}
	}
	return nil
}

func (e *ExecuteSQLArgs) Values() (
	args *query.Args,
	repo *repository.Repository,
	authManager auth.AuthManager,
	redisClient *redis.Client,
	seaTableMappers *sqllib.TableMappers,
	internalRequest bool,
	userID string,
	queryID string,
	priorityQueue priorityqueue.PriorityQueue[analyticmodels.SQLExecPackage],
	engineSetting sqllib.Setting,
) {
	return e.Args, e.Repo, e.AuthManager, e.RedisClient, e.SeaTableMappers, e.InternalRequest, e.UserID, e.QueryID, e.PriorityQueue, e.Engines
}

func NewExecuteSQLArgs(argv ...any) (*ExecuteSQLArgs, error) {
	e := &ExecuteSQLArgs{}
	if err := e.Init(argv...); err != nil {
		return nil, err
	}
	return e, nil
}
