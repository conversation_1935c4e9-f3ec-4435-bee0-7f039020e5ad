package handler

import (
	"context"
	"errors"
	"fmt"
	"time"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/errgroup"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/identifier"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/timer"
	"sentioxyz/sentio/service/analytic/models"
	"sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/repository"
	"sentioxyz/sentio/service/analytic/results"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	"sentioxyz/sentio/service/analytic/util"
	commonmodels "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/common/networklimiter"
	protoscommon "sentioxyz/sentio/service/common/protos"

	ch "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var (
	logThreshold = map[string]time.Duration{
		"LC": time.Second * 1,
		"PP": time.Second * 1,
		"MG": time.Second * 1,
		"PG": time.Second * 1,
		"S":  time.Second * 1,
	}

	skipLimiterBySource = map[protos.Source]bool{
		protos.Source_DASHBOARD:     true,
		protos.Source_ASYNC_TRIGGER: true,
		protos.Source_ENDPOINT:      true,
	}
)

func getSQLExecutor(args *query.Args) (
	executor event.ClickhouseViewer, conn event.Conn, executeArgs map[string]any, err error) {
	switch args.Project.Type {
	case commonmodels.ProjectTypeSentio:
		executor = args.Viewer
		conn = args.RWConn
		executeArgs = map[string]any{}
	case commonmodels.ProjectTypeSubgraph:
		executor = args.SgViewer
		conn = args.RWConn
		executeArgs = map[string]any{}
	default:
		err = status.Errorf(codes.InvalidArgument, "invalid project type: %s", args.Project.Type)
	}
	return
}

func rewriteMapping(ctx context.Context, args *query.Args, userID string, repo *repository.Repository,
	redisClient *redis.Client, seaTableMappers *sqllib.TableMappers) (result sqllib.Mapping, err error) {
	applyMappingWithParallel := func(ctx context.Context, result sqllib.Mapping) (sqllib.Mapping, error) {
		wg, wgCtx := errgroup.WithContext(ctx)
		wg.Go(func() error {
			return result.ApplyExternals(wgCtx, redisClient, args)
		})
		wg.Go(func() error {
			result.ApplyEntities(ctx, args)
			return nil
		})
		wg.Go(func() error {
			result.ApplyMapper(int(args.Processor.ClickhouseShardingIndex), args.Identity, userID, repo, seaTableMappers)
			return nil
		})
		wg.Go(func() error {
			result.AppendUserMappers(mapper.NewUserRefreshableViews(repo.DB, args.Project, args.RWConn))
			return nil
		})
		wg.Go(func() error {
			result.AppendDashMappers(mapper.GetDashTableMappers(), args)
			return nil
		})
		wg.Go(func() error {
			result.ApplyQueries(ctx, repo.MustGetNamedQueriesByProject(args.Project.ID))
			return nil
		})
		if err := wg.Wait(); err != nil {
			return nil, status.Errorf(codes.Internal, "generate mappings failed: %v", err)
		}
		return result, nil
	}

	var cached, refresh bool
	result, cached, refresh = sqllib.GetOrNewMapping(args.Processor.ID)
	if refresh {
		go func() {
			// refresh the mapping in background
			// this is a non-blocking call, so we don't need to wait for it
			rhsMapping := sqllib.MustNewMapping()
			if mapping, err := applyMappingWithParallel(context.Background(), rhsMapping); err == nil {
				sqllib.SaveMapping(args.Processor.ID, mapping)
				log.WithContext(ctx).Infof("refresh mapping succeeded for processor %s", args.Processor.ID)
			} else {
				log.WithContext(ctx).Errorf("refresh mapping failed: %v", err)
			}
		}()
	}
	if cached {
		return result, nil
	}
	result, err = applyMappingWithParallel(ctx, result)
	if err != nil {
		log.WithContext(ctx).Errorf("apply mapping failed: %v", err)
		return nil, status.Errorf(codes.Internal, "apply mapping failed: %v", err)
	}
	sqllib.SaveMapping(args.Processor.ID, result)
	return result, nil
}

func logUsed(ts timer.Start, stage string, logConsole bool) (time.Duration, bool) {
	used := ts.End()
	if threshold, ok := logThreshold[stage]; ok && used > threshold {
		return used, true
	}
	return used, logConsole
}

func SyncExecuteSQL(ctx context.Context, req *models.SQLRequest, argv ...any) (
	*protos.SyncExecuteSQLResponse, error) {
	inputArgs, err := NewExecuteSQLArgs(argv...)
	if err != nil {
		return nil, err
	}
	args, repo, authManager, redisClient, seaTableMappers, isInternal, userID, specifiedQueryID, _, engines := inputArgs.Values()

	resp := &protos.SyncExecuteSQLResponse{}
	var logConsole = false
	startTime := time.Now()
	tm := timer.NewTimer()
	executeTimer := tm.Start("SQLEXECUTE")
	tier := protoscommon.Tier_FREE
	var requestVars networklimiter.RequestVars
	var limitControlUsed, preparedUsed, mappingGenerateUsed, parameterGenerateUsed, executeUsed, scanUsed time.Duration

	limitControlStart := tm.Start("LC")
	ctx, logger := log.FromContext(ctx,
		"processor_id", args.Processor.ID,
		"clickhouse_sharding", args.Processor.ClickhouseShardingIndex,
		"project", args.Project.FullName())
	var limiterID string
	if !skipLimiterBySource[req.GetSource()] {
		if args.Identity != nil && args.Identity.User != nil {
			tier = protoscommon.Tier(args.Identity.User.Tier)
		}
		requestVars = networklimiter.RequestVars{
			OwnerID:   args.Identity.GetUserID(),
			ProjectID: args.Project.ID,
			Tier:      tier,
			Data:      req,
		}
		var ok bool
		var err error
		limiterID, ok, err = args.Limiter.Acquire(ctx, requestVars)
		if err != nil {
			logger.Warnf("sql concurrent control err: %v", err)
		}
		if !ok {
			return nil, status.Errorf(codes.ResourceExhausted, "running queries concurrent control, err: %v", err)
		}
	}
	defer func() {
		if limiterID != "" {
			args.Limiter.Release(ctx, requestVars, limiterID)
		}
	}()
	limitControlUsed, logConsole = logUsed(limitControlStart, "LC", logConsole)

	prepareStart := tm.Start("PP")
	if args.Processor == nil {
		return nil, status.Errorf(codes.InvalidArgument, "need an active processor to execute sql")
	}
	executor, conn, executeArgs, err := getSQLExecutor(args)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "get sql executor failed: %v", err)
	}
	if sqllib.LoadState(logger, redisClient, args.Processor.ID) {
		err = executor.PrepareExecute(ctx, conn, executeArgs)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "prepare execute failed: %v", err)
		}
	}
	preparedUsed, logConsole = logUsed(prepareStart, "PP", logConsole)

	mappingGenerateStart := tm.Start("MG")
	mapping, err := rewriteMapping(ctx, args, userID, repo, redisClient, seaTableMappers)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "generate rewrite mappings failed: %v", err)
	}
	mappingGenerateUsed, logConsole = logUsed(mappingGenerateStart, "MG", logConsole)

	parameterGenerateStart := tm.Start("PG")
	var additionalQuerySetting = ch.Settings{}
	if isInternal {
		additionalQuerySetting["max_execution_time"] = *clickhouse.AsyncMaxExecutionTime
	}
	if engine := req.GetEngine().String(); engine != "" {
		engines.OverwriteClickhouseSettings(engine, additionalQuerySetting)
	}
	params := clickhouse.NewExecuteArgs(
		req.GetSqlQuery().GetSql(), req.GetCursor(), req.GetSqlQuery().GetParameters(),
		mapping.GetDatabaseTableMapping(), mapping.GetRemoteArgsMapping(), mapping.GetCommonTableExprArgsMapping(),
		int(req.GetSqlQuery().GetSize()), repo, authManager,
		sqllib.ProcessorAccessValidator, args.RewriterClient, isInternal, additionalQuerySetting,
		util.GetClickhouseCtxDataCallSign(ctx), specifiedQueryID, false, req.GetEngine())
	parameterGenerateUsed, logConsole = logUsed(parameterGenerateStart, "PG", logConsole)

	executeStart := tm.Start("E")
	result := executor.Execute(ctx, params)
	executeUsed, logConsole = logUsed(executeStart, "E", logConsole)

	scanStart := tm.Start("S")
	var statusErr error
	var clickhouseStats *protoscommon.ComputeStats_ClickhouseStats
	if err := result.Error(); err != nil {
		var chErr *clickhouse.ErrorWrapper
		ok := errors.As(err, &chErr)
		if ok {
			resp.ResponseType = &protos.SyncExecuteSQLResponse_Error{
				Error: chErr.Msg(),
			}
			statusErr = chErr.GRPCStatus()
		} else {
			resp.ResponseType = &protos.SyncExecuteSQLResponse_Error{
				Error: err.Error(),
			}
		}
		sqllib.DeleteState(logger, redisClient, args.Processor.ID)
		sqllib.DeleteMapping(args.Processor.ID)
	} else {
		resp.ResponseType = &protos.SyncExecuteSQLResponse_Result{
			Result: results.NewTabularFromClickhouse(ctx, result.Rows(), result.Columns(), result.Cursor(),
				executor.GetReservedKeyword(ctx), time.UTC),
		}
		sqllib.SaveState(logger, redisClient, args.Processor.ID)
	}
	scanUsed, logConsole = logUsed(scanStart, "S", logConsole)
	totalUsed := executeTimer.End()
	timerDebug := tm.ReportDistribution("SQLEXECUTE", "LC,PP,MG,PG,E,S")
	if logConsole {
		logger.Infof("execute sql completed, total used: %d ms"+
			", limit_control: %d ms"+
			", prepare: %d ms"+
			", mapping_generate: %d ms"+
			", parameter_generate: %d ms"+
			", execute: %d ms"+
			", scan: %d ms"+
			", report: %s",
			totalUsed.Milliseconds(),
			limitControlUsed.Milliseconds(),
			preparedUsed.Milliseconds(),
			mappingGenerateUsed.Milliseconds(),
			parameterGenerateUsed.Milliseconds(),
			executeUsed.Milliseconds(),
			scanUsed.Milliseconds(),
			timerDebug)
	}
	resp.RuntimeCost = time.Since(startTime).Milliseconds()
	resp.ComputeStats = &protoscommon.ComputeStats{
		ComputedAt:        timestamppb.New(startTime),
		ComputeCostMs:     time.Since(startTime).Milliseconds(),
		BinaryVersionHash: identifier.BinaryHash(),
		ComputedBy:        fmt.Sprintf("sentio/%s(%s)", identifier.PodName(), timerDebug),
		ClickhouseStats:   clickhouseStats,
	}
	return resp, statusErr
}
