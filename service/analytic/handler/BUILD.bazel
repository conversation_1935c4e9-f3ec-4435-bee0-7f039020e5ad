load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "handler",
    srcs = [
        "async_execute_sql.go",
        "execute_sql.go",
        "execute_sql_utils.go",
        "export_sql.go",
        "get_event_property_values.go",
        "get_events.go",
        "get_refreshable_materialized_view_status.go",
        "query_cohorts.go",
        "query_segmentation.go",
        "query_system_sql_segmentation.go",
        "query_tables.go",
        "util.go",
    ],
    importpath = "sentioxyz/sentio/service/analytic/handler",
    visibility = ["//visibility:public"],
    deps = [
        "//common/clickhouse",
        "//common/clickhouse/builder",
        "//common/clickhouse/builder/selector",
        "//common/clickhouse/schema",
        "//common/errgroup",
        "//common/event",
        "//common/gonanoid",
        "//common/identifier",
        "//common/log",
        "//common/protojson",
        "//common/timer",
        "//common/utils",
        "//driver/entity/clickhouse",
        "//service/analytic/clients",
        "//service/analytic/models",
        "//service/analytic/protos",
        "//service/analytic/query",
        "//service/analytic/query/event",
        "//service/analytic/query/user",
        "//service/analytic/repository",
        "//service/analytic/repository/models",
        "//service/analytic/results",
        "//service/analytic/sqllib",
        "//service/analytic/sqllib/mapper",
        "//service/analytic/util",
        "//service/common/auth",
        "//service/common/gormcache",
        "//service/common/models",
        "//service/common/networklimiter",
        "//service/common/priorityqueue",
        "//service/common/protos",
        "//service/common/timerange",
        "//service/processor/models",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_pkg_errors//:errors",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_samber_lo//:lo",
        "@com_github_sentioxyz_golang_lru//:golang-lru",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//types/known/timestamppb",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "handler_test",
    srcs = ["execute_sql_utils_test.go"],
    embed = [":handler"],
    deps = [
        "//common/log",
        "//service/analytic/query",
        "//service/analytic/repository/models",
        "//service/analytic/sqllib",
        "//service/common/priorityqueue",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_stretchr_testify//assert",
    ],
)
