package handler

import (
	"context"
	"time"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/clients"
	"sentioxyz/sentio/service/analytic/models"
	protosanalytic "sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/repository"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/common/auth"
	commonmodels "sentioxyz/sentio/service/common/models"
	processormodel "sentioxyz/sentio/service/processor/models"

	ch "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const exportLimit = 10_000_000

type ExportSQLArgs struct {
	query.SentioProcessorArgs
	query.SubgraphProcessorArgs

	Params                  *models.ExportSQLParams
	Processor               *processormodel.Processor
	Project                 *commonmodels.Project
	ClickhouseMultiSharding event.MultiSharding
	AuthManager             auth.AuthManager
	UserID                  string
	RedisClient             *redis.Client
	Repo                    *repository.Repository
	RewriterClient          clients.RewriterServiceClient
	K8sContextUse           string

	SeaTableMappers *sqllib.TableMappers

	ExternalProcessors    []*processormodel.Processor
	ExternalProjects      []*commonmodels.ImportedProject
	ExternalProcessorArgs []any

	QueryID string
}

func ExportSQL(ctx context.Context, args *ExportSQLArgs) error {
	startTime := time.Now()
	ctx, logger := log.FromContext(ctx,
		"processor_id", args.Processor.ID,
		"clickhouse_sharding", args.Processor.ClickhouseShardingIndex,
		"project", args.Project.FullName())
	var executor event.ClickhouseViewer
	var rwConn, rConn event.Conn
	var executeArgs map[string]any
	var err error
	shard := args.ClickhouseMultiSharding.GetShard(args.Processor.ClickhouseShardingIndex)
	if shard == nil {
		return status.Errorf(codes.Internal, "shard not found: %d", args.Processor.ClickhouseShardingIndex)
	}
	switch args.Project.Type {
	case commonmodels.ProjectTypeSentio:
		executor = args.Viewer
		rwConn = shard.GetSentioConn()
		rConn = shard.GetSentioViewConn()
		executeArgs = map[string]any{}
	case commonmodels.ProjectTypeSubgraph:
		executor = args.SgViewer
		rwConn = shard.GetSubgraphConn()
		rConn = shard.GetSubgraphViewConn()
		executeArgs = map[string]any{}
	default:
		return status.Errorf(codes.Internal, "unknown project type: %s", args.Project.Type)
	}
	if sqllib.LoadState(logger, args.RedisClient, args.Processor.ID) {
		err = executor.PrepareExecute(ctx, rwConn, executeArgs)
		if err != nil {
			return status.Errorf(codes.Internal, "prepare execute failed: %v", err)
		}
	}
	mapping, err := rewriteMapping(ctx, &query.Args{
		SentioProcessorArgs:     args.SentioProcessorArgs,
		SubgraphProcessorArgs:   args.SubgraphProcessorArgs,
		ClickhouseMultiSharding: args.ClickhouseMultiSharding,
		Processor:               args.Processor,
		Project:                 args.Project,
		RWConn:                  rwConn,
		RConn:                   rConn,
		RewriterClient:          args.RewriterClient,
		ExternalProjects:        args.ExternalProjects,
		ExternalProcessors:      args.ExternalProcessors,
		ExternalProcessorArgs:   args.ExternalProcessorArgs,
	}, args.UserID, args.Repo, args.RedisClient, args.SeaTableMappers)
	if err != nil {
		return status.Errorf(codes.Internal, "generate mappings failed: %v", err)
	}
	var additionalQuerySettings = ch.Settings{
		"max_execution_time": *clickhouse.AsyncMaxExecutionTime,
		"allow_push_predicate_ast_for_distributed_subqueries": 0,
		"secondary_indices_enable_bulk_filtering":             0,
	}
	executeParams := clickhouse.NewExecuteArgs(
		args.Params.SQL, "", nil,
		mapping.GetDatabaseTableMapping(), mapping.GetRemoteArgsMapping(), mapping.GetCommonTableExprArgsMapping(),
		exportLimit,
		args.Repo, args.AuthManager, sqllib.ProcessorAccessValidator, args.RewriterClient, true,
		additionalQuerySettings, "", args.QueryID, false, protosanalytic.ExecuteEngine_DEFAULT)
	result := executor.Execute(ctx, executeParams)
	if err := result.Error(); err != nil {
		sqllib.DeleteState(logger, args.RedisClient, args.Processor.ID)
		return err
	}
	args.Params.Progress = make(chan event.ExportProgress, 10240)
	if err = executor.Export(ctx, args.K8sContextUse,
		args.ClickhouseMultiSharding.GetShard(args.Processor.ClickhouseShardingIndex), args.Params.Progress,
		args.Params.Filepath, result.SQL(), int(args.Params.Compression)); err != nil {
		sqllib.DeleteState(logger, args.RedisClient, args.Processor.ID)
		return err
	}
	sqllib.SaveState(logger, args.RedisClient, args.Processor.ID)
	logger.Infof("Export SQL took %s, sql: %s, rewrited: %s, filepath: %s",
		time.Since(startTime), args.Params.SQL, result.SQL(), args.Params.Filepath)
	return nil
}
