package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/clickhouse/schema"
	"sentioxyz/sentio/common/log"
	subgraphchs "sentioxyz/sentio/driver/entity/clickhouse"
	"sentioxyz/sentio/service/analytic/models"
	"sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/repository"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	modelscommon "sentioxyz/sentio/service/common/models"
	processormodels "sentioxyz/sentio/service/processor/models"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	lru "github.com/sentioxyz/golang-lru"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var mapperTableLRUCache *lru.Cache[string, *protos.Table]

func init() {
	mapperTableLRUCache, _ = lru.New[string, *protos.Table](1024)
}

func newTable(eventName string, tableType protos.Table_TableType, builtInColumns []*protos.Table_Column) *protos.Table {
	t := &protos.Table{
		Name:      eventName,
		Columns:   map[string]*protos.Table_Column{},
		TableType: tableType,
	}
	for _, c := range builtInColumns {
		t.Columns[c.Name] = c
	}
	return t
}

func property2columnType(f schema.Field) protos.Table_Column_ColumnType {
	switch {
	case f.IsNumeric():
		return protos.Table_Column_NUMBER
	case f.IsString():
		return protos.Table_Column_STRING
	case f.IsTime():
		return protos.Table_Column_TIME
	case f.IsArray():
		return protos.Table_Column_LIST
	case f.IsBool():
		return protos.Table_Column_BOOLEAN
	case f.IsJSON():
		return protos.Table_Column_JSON
	case f.IsToken():
		return protos.Table_Column_TOKEN
	}
	return protos.Table_Column_STRING
}

func property2ClickhouseDataType(f schema.Field) string {
	if f.IsBuiltIn() {
		switch f.GetDisplayName() {
		case "timestamp":
			return "DateTime64"
		case "log_index", "transaction_index":
			return "Int32"
		case "block_number":
			return "UInt64"
		default:
			return "String"
		}
	}
	switch {
	case f.IsNumeric():
		return "Decimal256"
	case f.IsString():
		return "String"
	case f.IsTime():
		return "DateTime64"
	case f.IsArray():
		return "Array(String)"
	case f.IsBool():
		return "UInt8"
	case f.IsJSON():
		return "JSON"
	case f.IsToken():
		return "Token Tuple"
	}
	return "String"
}

func tableAddColumn(t *protos.Table, f schema.Field) {
	if f == nil {
		return
	}
	c := &protos.Table_Column{
		Name:               f.GetDisplayName(),
		ColumnType:         property2columnType(f),
		ClickhouseDataType: property2ClickhouseDataType(f),
		IsBuiltin:          f.IsBuiltIn(),
	}
	t.Columns[f.GetDisplayName()] = c
}

func genBuiltInColumns(properties map[string]schema.Field) (columns []*protos.Table_Column) {
	for _, p := range properties {
		if p.IsBuiltIn() {
			c := &protos.Table_Column{
				Name:               p.GetDisplayName(),
				ColumnType:         property2columnType(p),
				ClickhouseDataType: property2ClickhouseDataType(p),
				IsBuiltin:          p.IsBuiltIn(),
			}
			columns = append(columns, c)
		}
	}
	return
}

func subgraphAddReserve(t *protos.Table, field, dbType string) {
	t.Columns[field] = &protos.Table_Column{
		Name:               field,
		ClickhouseDataType: dbType,
		ColumnType:         detectColumnType(dbType),
		IsBuiltin:          false,
	}
}

type QueryTableContext struct {
	ctx           context.Context
	logger        *log.SentioLogger
	mu            *sync.Mutex
	req           *protos.QueryTablesRequest
	resp          *protos.QueryTablesResponse
	args          *query.Args
	redisClient   *redis.Client
	repo          *repository.Repository
	cost          *map[string]time.Duration
	externalAlias string
}

func NewQueryTableContext(ctx context.Context,
	req *protos.QueryTablesRequest, resp *protos.QueryTablesResponse,
	args *query.Args, redisClient *redis.Client, repo *repository.Repository) *QueryTableContext {
	ctx, logger := log.FromContext(ctx)
	if args.Processor != nil {
		logger = logger.With("processor", args.Processor.ID)
	}
	if args.Project != nil {
		logger = logger.With("project", fmt.Sprintf("%s/%s", args.Project.GetOwnerName(), args.Project.Slug))
	}
	cost := make(map[string]time.Duration)
	return &QueryTableContext{
		ctx:         ctx,
		logger:      logger,
		mu:          &sync.Mutex{},
		req:         req,
		resp:        resp,
		args:        args,
		redisClient: redisClient,
		repo:        repo,
		cost:        &cost,
	}
}

func (c *QueryTableContext) Clone() *QueryTableContext {
	return &QueryTableContext{
		ctx:           c.ctx,
		logger:        c.logger,
		mu:            c.mu,
		req:           c.req,
		resp:          c.resp,
		args:          c.args,
		redisClient:   c.redisClient,
		repo:          c.repo,
		cost:          c.cost,
		externalAlias: c.externalAlias,
	}
}

func (c *QueryTableContext) apply(logger *log.SentioLogger, t *protos.Table, cost time.Duration) {
	c.mu.Lock()
	c.resp.Tables[t.Name] = t
	(*c.cost)[t.Name] = cost
	c.mu.Unlock()
	logger.Debugf("append table: %s, cost: %v", t.Name, cost)
}

func (c *QueryTableContext) appendSentioTables(specifiedProcessorArgs *query.SentioProcessorArgs, eventlogVersion int32, external bool, relatedProjectID string) {
	logger := c.logger.With("type", "sentio")
	var s schema.Meta
	if specifiedProcessorArgs != nil && specifiedProcessorArgs.Schema != nil {
		s = specifiedProcessorArgs.Schema
	} else {
		if c.args.Schema == nil {
			if c.args.Processor != nil {
				logger.Infof("schema is nil, processor: %s", c.args.Processor.ID)
			}
			return
		}
		s = c.args.Schema
	}
	builtInColumns := genBuiltInColumns(s.GetFields())
	schemaFields := s.GetFields()
	for eventName, fields := range s.GetInvertedIndex()["event_name"] {
		start := time.Now()
		tableName := sqllib.ExternalTableName(c.externalAlias, eventName)
		var t *protos.Table
		if c.externalAlias == "" {
			t = newTable(tableName, protos.Table_EVENT, builtInColumns)
		} else {
			t = newTable(tableName, protos.Table_IMPORTED_EVENT, builtInColumns)
		}
		if (external && c.req.GetIncludeExternals()) || !external {
			for fieldName := range fields {
				if schemaFields[fieldName] == nil {
					continue
				}
				if schemaFields[fieldName].IsJSON() || schemaFields[fieldName].IsToken() {
					if eventlogVersion < processormodels.EventlogVersion_V2.Int32() {
						continue
					}
				}
				tableAddColumn(t, schemaFields[fieldName])
			}
		}
		if relatedProjectID != "" {
			t.RelatedProjectId = lo.ToPtr(relatedProjectID)
		}
		cost := time.Since(start)
		c.apply(logger, t, cost)
	}
}

func (c *QueryTableContext) appendSubgraphTable(specifiedProcessorArgs *query.SubgraphProcessorArgs, external bool, relatedProjectID string) {
	logger := c.logger.With("type", "subgraph")
	var viewer *clickhouse.SgViewer
	if specifiedProcessorArgs != nil {
		if specifiedProcessorArgs.EntitySchema == nil {
			return
		}
		viewer = specifiedProcessorArgs.SgViewer
	} else {
		if c.args.EntitySchema == nil {
			return
		}
		viewer = c.args.SgViewer
	}
	viewFields := viewer.GetViewFields()
	for entityName, fields := range viewFields {
		{
			// raw view table
			start := time.Now()
			tableName := sqllib.ExternalTableName(c.externalAlias, entityName+subgraphchs.SubgraphTableDisplayNameRawSuffix)
			var t *protos.Table
			if c.externalAlias == "" {
				t = newTable(tableName, protos.Table_SUBGRAPH, nil)
			} else {
				t = newTable(tableName, protos.Table_IMPORTED_SUBGRAPH, nil)
			}
			if (external && c.req.GetIncludeExternals()) || !external {
				subgraphAddReserve(t, "__genBlockNumber__", "UInt64")
				subgraphAddReserve(t, "__genBlockTime__", "DateTime64")
				subgraphAddReserve(t, "__genBlockHash__", "String")
				subgraphAddReserve(t, "__genBlockChain__", "String")
				subgraphAddReserve(t, "__deleted__", "Bool")
				subgraphAddReserve(t, "__timestamp__", "DateTime64")
				for field, dbType := range fields {
					c := &protos.Table_Column{
						Name:               field,
						ClickhouseDataType: dbType,
						ColumnType:         detectColumnType(dbType),
						IsBuiltin:          false,
					}
					t.Columns[field] = c
				}
			}
			if relatedProjectID != "" {
				t.RelatedProjectId = lo.ToPtr(relatedProjectID)
			}
			cost := time.Since(start)
			c.apply(logger, t, cost)
		}
		{
			// latest view table
			start := time.Now()
			tableName := sqllib.ExternalTableName(c.externalAlias, entityName)
			var t *protos.Table
			if c.externalAlias == "" {
				t = newTable(tableName, protos.Table_SUBGRAPH, nil)
			} else {
				t = newTable(tableName, protos.Table_IMPORTED_SUBGRAPH, nil)
			}
			if (external && c.req.GetIncludeExternals()) || !external {
				for field, dbType := range fields {
					c := &protos.Table_Column{
						Name:               field,
						ClickhouseDataType: dbType,
						ColumnType:         detectColumnType(dbType),
						IsBuiltin:          false,
					}
					t.Columns[field] = c
				}
			}
			if relatedProjectID != "" {
				t.RelatedProjectId = lo.ToPtr(relatedProjectID)
			}
			cost := time.Since(start)
			c.apply(logger, t, cost)
		}
	}
}

func (c *QueryTableContext) appendEntityTables(specifiedProcessorArgs *query.SentioProcessorArgs, external bool, relatedProjectID string) {
	logger := c.logger.With("type", "entity")
	var viewer *clickhouse.SgViewer
	if specifiedProcessorArgs != nil {
		if specifiedProcessorArgs.Entity.SgViewer == nil {
			return
		}
		viewer = specifiedProcessorArgs.Entity.SgViewer
	} else {
		if c.args.SentioProcessorArgs.Entity.SgViewer == nil {
			return
		}
		viewer = c.args.SentioProcessorArgs.Entity.SgViewer
	}
	viewFields := viewer.GetViewFields()
	for entityName, fields := range viewFields {
		{
			// raw view table
			start := time.Now()
			tableName := sqllib.ExternalTableName(c.externalAlias, entityName+subgraphchs.SubgraphTableDisplayNameRawSuffix)
			var t *protos.Table
			if c.externalAlias == "" {
				t = newTable(tableName, protos.Table_ENTITY, nil)
			} else {
				t = newTable(tableName, protos.Table_IMPORTED_ENTITY, nil)
			}
			if (external && c.req.GetIncludeExternals()) || !external {
				subgraphAddReserve(t, "__genBlockNumber__", "UInt64")
				subgraphAddReserve(t, "__genBlockTime__", "DateTime64")
				subgraphAddReserve(t, "__genBlockHash__", "String")
				subgraphAddReserve(t, "__genBlockChain__", "String")
				subgraphAddReserve(t, "__deleted__", "Bool")
				subgraphAddReserve(t, "__timestamp__", "DateTime64")
				for field, dbType := range fields {
					c := &protos.Table_Column{
						Name:               field,
						ClickhouseDataType: dbType,
						ColumnType:         detectColumnType(dbType),
						IsBuiltin:          false,
					}
					t.Columns[field] = c
				}
			}
			if relatedProjectID != "" {
				t.RelatedProjectId = lo.ToPtr(relatedProjectID)
			}
			cost := time.Since(start)
			c.apply(logger, t, cost)
		}
		{
			// latest view table
			start := time.Now()
			tableName := sqllib.ExternalTableName(c.externalAlias, entityName)
			var t *protos.Table
			if c.externalAlias == "" {
				t = newTable(tableName, protos.Table_ENTITY, nil)
			} else {
				t = newTable(tableName, protos.Table_IMPORTED_ENTITY, nil)
			}
			if (external && c.req.GetIncludeExternals()) || !external {
				subgraphAddReserve(t, "__genBlockChain__", "String")
				for field, dbType := range fields {
					c := &protos.Table_Column{
						Name:               field,
						ClickhouseDataType: dbType,
						ColumnType:         detectColumnType(dbType),
						IsBuiltin:          false,
					}
					t.Columns[field] = c
				}
			}
			if relatedProjectID != "" {
				t.RelatedProjectId = lo.ToPtr(relatedProjectID)
			}
			cost := time.Since(start)
			c.apply(logger, t, cost)
		}
	}
}

func (c *QueryTableContext) addTable(tableName string, mapper sqllib.TableMapper, start time.Time, relatedProjectID string,
	hashKeyArgs ...string) {
	k8sCluster := getK8sCluster(c.args)
	table := newTable(mapper.DisplayName(), mapper.Type(), nil)
	var (
		columns map[string]string
		err     error
	)
	switch c.args.Project.Type {
	case modelscommon.ProjectTypeSentio:
		columns, err = c.args.Viewer.DescribeTable(c.ctx, tableName)
	case modelscommon.ProjectTypeSubgraph:
		columns, err = c.args.SgViewer.DescribeTable(c.ctx, tableName)
	}
	if err != nil {
		log.Debugf("describe table failed, table: %s, err: %v", tableName, err)
		return
	}
	for k, v := range columns {
		table.Columns[k] = &protos.Table_Column{
			Name:               k,
			ClickhouseDataType: v,
			ColumnType:         detectColumnType(v),
			IsBuiltin:          false,
		}
	}
	if relatedProjectID != "" {
		table.RelatedProjectId = lo.ToPtr(relatedProjectID)
	}
	writeMapperTableCache(c.ctx, k8sCluster, c.redisClient, tableName, table, hashKeyArgs...)
	cost := time.Since(start)
	c.apply(c.logger, table, cost)
}

func (c *QueryTableContext) appendMapperTables(tableMappers *sqllib.TableMappers) {
	k8sCluster := getK8sCluster(c.args)
	handleMapper := func(c *QueryTableContext, mapper sqllib.TableMapper) {
		start := time.Now()
		var (
			finalTableName string
			displayName    = mapper.DisplayName()
		)
		if mapper.ShardingSensitive() {
			if int(c.args.Processor.ClickhouseShardingIndex) != mapper.RemoteSharding() {
				finalTableName = mapper.RemotePattern()
			} else {
				finalTableName = mapper.TableName()
			}
		} else {
			finalTableName = mapper.TableName()
		}

		switch mapper.DataType() {
		case sqllib.TokenData, sqllib.ChainData:
			if !c.req.GetIncludeChains() {
				table := newTable(displayName, mapper.Type(), nil)
				c.apply(c.logger, table, time.Since(start))
			} else {
				table, ok := readMapperTableCache(c.ctx, k8sCluster, c.redisClient, finalTableName, string(mapper.DataType()))
				if ok {
					c.apply(c.logger, table, time.Since(start))
				} else {
					c.addTable(finalTableName, mapper, start, "", string(mapper.DataType()))
				}
			}
		case sqllib.UserData, sqllib.MaterializedViewData:
			if !c.req.GetIncludeViews() {
				table := newTable(displayName, mapper.Type(), nil)
				c.apply(c.logger, table, time.Since(start))
			} else {
				table, ok := readMapperTableCache(c.ctx, k8sCluster, c.redisClient, finalTableName, string(mapper.DataType()))
				if ok {
					c.apply(c.logger, table, time.Since(start))
				} else {
					c.addTable(finalTableName, mapper, start, "", string(mapper.DataType()))
				}
			}
		}
	}

	for _, m := range tableMappers.ChainMappers() {
		handleMapper(c, m)
	}
	for _, m := range tableMappers.TokenMappers() {
		handleMapper(c, m)
	}

	sharding := c.args.ClickhouseMultiSharding.GetShard(c.args.Processor.ClickhouseShardingIndex)
	if sharding == nil {
		return
	}
	var userMv []sqllib.TableMapper
	switch c.args.Project.Type {
	case modelscommon.ProjectTypeSentio:
		userMv = mapper.NewUserRefreshableViews(c.repo.DB, c.args.Project, sharding.GetSentioConn())
	case modelscommon.ProjectTypeSubgraph:
		userMv = mapper.NewUserRefreshableViews(c.repo.DB, c.args.Project, sharding.GetSubgraphConn())
	}
	for _, m := range userMv {
		handleMapper(c, m)
	}
	for _, m := range sqllib.FilterMapperWithIdentity(sqllib.GetUser(c.args.Identity, ""), tableMappers, c.repo) {
		handleMapper(c, m)
	}
}

func (c *QueryTableContext) appendDashTables() {
	k8sCluster := getK8sCluster(c.args)
	mappers := mapper.GetDashTableMappers()
	for _, m := range mappers {
		tableName := m.DisplayName()
		dashMapper, ok := m.(*mapper.DashMapper)
		if !ok {
			continue
		}
		var relatedProjectID string
		if dashMapper.Project != nil {
			relatedProjectID = dashMapper.Project.ID
		}
		switch {
		case dashMapper.EventSchema != nil:
			s := dashMapper.EventSchema
			builtInColumns := genBuiltInColumns(s.GetFields())
			for _, fields := range s.InvertedIndex["event_name"] {
				start := time.Now()
				table := newTable(tableName, m.Type(), builtInColumns)
				for fieldName := range fields {
					if s.Fields[fieldName] == nil {
						continue
					}
					if s.Fields[fieldName].IsJSON() || s.Fields[fieldName].IsToken() {
						if dashMapper.Processor.EventlogVersion < processormodels.EventlogVersion_V2.Int32() {
							continue
						}
					}
					tableAddColumn(table, s.Fields[fieldName])
				}
				table.RelatedProjectId = lo.ToPtr(relatedProjectID)
				cost := time.Since(start)
				c.apply(c.logger, table, cost)
			}
		case dashMapper.EntitySchema != nil:
			viewer, ok := dashMapper.Viewer.(*clickhouse.SgViewer)
			if !ok {
				continue
			}
			viewFields := viewer.GetViewFields()
			for _, fields := range viewFields {
				{
					// raw view table
					start := time.Now()
					table := newTable(tableName+subgraphchs.SubgraphTableDisplayNameRawSuffix, m.Type(), nil)
					subgraphAddReserve(table, "__genBlockNumber__", "UInt64")
					subgraphAddReserve(table, "__genBlockTime__", "DateTime64")
					subgraphAddReserve(table, "__genBlockHash__", "String")
					subgraphAddReserve(table, "__genBlockChain__", "String")
					subgraphAddReserve(table, "__deleted__", "Bool")
					subgraphAddReserve(table, "__timestamp__", "DateTime64")
					for field, dbType := range fields {
						c := &protos.Table_Column{
							Name:               field,
							ClickhouseDataType: dbType,
							ColumnType:         detectColumnType(dbType),
							IsBuiltin:          false,
						}
						table.Columns[field] = c
					}
					table.RelatedProjectId = lo.ToPtr(relatedProjectID)
					cost := time.Since(start)
					c.apply(c.logger, table, cost)
				}
				{
					// latest view table
					start := time.Now()
					table := newTable(tableName, m.Type(), nil)
					for field, dbType := range fields {
						c := &protos.Table_Column{
							Name:               field,
							ClickhouseDataType: dbType,
							ColumnType:         detectColumnType(dbType),
							IsBuiltin:          false,
						}
						table.Columns[field] = c
					}
					table.RelatedProjectId = lo.ToPtr(relatedProjectID)
					cost := time.Since(start)
					c.apply(c.logger, table, cost)
				}
			}
		default:
			start := time.Now()
			var finalTableName string
			if dashMapper.ShardingSensitive() {
				if int(c.args.Processor.ClickhouseShardingIndex) != dashMapper.RemoteSharding() {
					finalTableName = dashMapper.RemotePattern()
				} else {
					database, table := dashMapper.DatabaseAndTableName()
					finalTableName = fmt.Sprintf("`%s`.`%s`", database, table)
				}
			} else {
				database, table := dashMapper.DatabaseAndTableName()
				finalTableName = fmt.Sprintf("`%s`.`%s`", database, table)
			}
			table, ok := readMapperTableCache(c.ctx, k8sCluster, c.redisClient, finalTableName, "dash")
			if ok {
				c.apply(c.logger, table, time.Since(start))
			} else {
				c.addTable(finalTableName, dashMapper, start, relatedProjectID, "dash")
			}
		}
	}
}

func (c *QueryTableContext) appendTables(tableMappers *sqllib.TableMappers) {
	start := time.Now()
	eg, _ := errgroup.WithContext(c.ctx)
	eg.Go(func() error {
		if !c.req.GetIncludeDash() {
			return nil
		}
		if *sqllib.DashWhitelistEnable {
			whitelistMap := lo.SliceToMap(strings.Split(*sqllib.DashWhitelist, ","), func(s string) (string, bool) {
				return strings.TrimSpace(s), true
			})
			if !whitelistMap[c.args.Project.FullName()] {
				return nil
			}
		}
		c.appendDashTables()
		return nil
	})
	eg.Go(func() error {
		c.appendMapperTables(tableMappers)
		return nil
	})
	switch c.args.Project.Type {
	case modelscommon.ProjectTypeSentio:
		eg.Go(func() error {
			c.appendSentioTables(nil, c.args.Processor.EventlogVersion, false, "")
			return nil
		})
		eg.Go(func() error {
			c.appendEntityTables(nil, false, "")
			return nil
		})
	case modelscommon.ProjectTypeSubgraph:
		eg.Go(func() error {
			c.appendSubgraphTable(nil, false, "")
			return nil
		})
	}
	for idx := range c.args.ExternalProcessors {
		idx := idx
		eg.Go(func() error {
			if c.args.ExternalProjects[idx].ImportProject == nil {
				c.logger.Warnf("external project is nil, idx: %d", idx)
				return nil
			}
			processorArgs := c.args.ExternalProcessorArgs[idx]
			if processorArgs == nil {
				c.logger.Warnf("external processor args is nil, idx: %d", idx)
				return nil
			}
			switch c.args.ExternalProjects[idx].ImportProject.Type {
			case modelscommon.ProjectTypeSentio:
				args, ok := processorArgs.(*query.SentioProcessorArgs)
				if !ok {
					c.logger.Warnf("invalid external processor args, idx: %d", idx)
					return nil
				}
				tContext := c.Clone()
				tContext.externalAlias = c.args.ExternalProjects[idx].Name
				tContext.appendSentioTables(args, c.args.ExternalProcessors[idx].EventlogVersion, true, c.args.ExternalProjects[idx].ImportProjectID)
				tContext.appendEntityTables(args, true, c.args.ExternalProjects[idx].ImportProjectID)
			case modelscommon.ProjectTypeSubgraph:
				args, ok := processorArgs.(*query.SubgraphProcessorArgs)
				if !ok {
					c.logger.Warnf("invalid external processor args, idx: %d", idx)
					return nil
				}
				tContext := c.Clone()
				tContext.externalAlias = c.args.ExternalProjects[idx].Name
				tContext.appendSubgraphTable(args, true, c.args.ExternalProjects[idx].ImportProjectID)
			}
			return nil
		})
	}
	_ = eg.Wait()
	if latency := time.Since(start); latency > time.Second*3 {
		costDebug, _ := json.Marshal(*c.cost)
		c.logger.Infof("query table cost %.2fs, detail: %s", latency.Seconds(), string(costDebug))
	}
}

func mapperTableCacheKey(k8sCluster, tableName string, hashKeyArgs ...string) string {
	if len(hashKeyArgs) > 0 {
		return fmt.Sprintf("describe_table:%s:%s:%s", k8sCluster, tableName, strings.Join(hashKeyArgs, ":"))
	}
	return fmt.Sprintf("describe_table:%s:%s", k8sCluster, tableName)
}

func readMapperTableCache(ctx context.Context, k8sCluster string, redisClient *redis.Client, tableName string, hashKeyArgs ...string) (*protos.Table, bool) {
	key := mapperTableCacheKey(k8sCluster, tableName, hashKeyArgs...)
	table, exists := mapperTableLRUCache.Get(key)
	if exists {
		return table, true
	}
	if redisClient == nil {
		return nil, false
	}
	val, err := redisClient.Get(ctx, key).Result()
	switch {
	case errors.Is(err, redis.Nil):
		return nil, false
	case err != nil:
		log.Warnf("get redis key failed, key: %s, err: %v", key, err)
		return nil, false
	default:
		var t protos.Table
		if err := json.Unmarshal([]byte(val), &t); err != nil {
			log.Warnf("unmarshal table failed, key: %s, err: %v", key, err)
			return nil, false
		}
		return &t, true
	}
}

func writeMapperTableCache(ctx context.Context, k8sCluster string,
	redisClient *redis.Client, tableName string, table *protos.Table, hashKeyArgs ...string) {
	key := mapperTableCacheKey(k8sCluster, tableName, hashKeyArgs...)
	_ = mapperTableLRUCache.Add(key, table)
	if redisClient == nil {
		return
	}
	data, err := json.Marshal(table)
	if err != nil {
		return
	}
	err = redisClient.Set(ctx, key, data, time.Minute*60).Err()
	if err != nil {
		log.Warnf("set redis key failed, key: %s, err: %v", key, err)
	}
}

func detectColumnType(dbType string) protos.Table_Column_ColumnType {
	switch {
	case strings.Contains(dbType, "Array"):
		return protos.Table_Column_LIST
	case strings.Contains(dbType, "Int"):
		return protos.Table_Column_NUMBER
	case strings.Contains(dbType, "String"):
		return protos.Table_Column_STRING
	case strings.Contains(dbType, "DateTime"):
		return protos.Table_Column_TIME
	case strings.Contains(dbType, "UInt8"):
		return protos.Table_Column_BOOLEAN
	case strings.Contains(dbType, "JSON"):
		return protos.Table_Column_JSON
	}
	return protos.Table_Column_STRING
}

func QueryTables(ctx context.Context, req *models.QueryTablesRequest, argv ...any) (
	*protos.QueryTablesResponse, error) {
	if len(argv) != 4 {
		return nil, status.Errorf(codes.Internal, "invalid argument")
	}
	args, ok := argv[0].(*query.Args)
	if !ok {
		return nil, status.Errorf(codes.Internal, "invalid argument")
	}
	redisClient, ok := argv[1].(*redis.Client)
	if !ok {
		return nil, status.Errorf(codes.Internal, "invalid argument")
	}
	seaTableMappers, ok := argv[2].(*sqllib.TableMappers)
	if !ok {
		return nil, status.Errorf(codes.Internal, "invalid argument")
	}
	repo, ok := argv[3].(*repository.Repository)
	if !ok {
		return nil, status.Errorf(codes.Internal, "invalid argument")
	}
	var mappers = sqllib.GetTableMappers(args, seaTableMappers)
	resp := &protos.QueryTablesResponse{
		Tables:       map[string]*protos.Table{},
		ComputeStats: initComputeStats(),
	}
	startTime := time.Now()
	queryTableContext := NewQueryTableContext(ctx, req.QueryTablesRequest, resp, args, redisClient, repo)
	queryTableContext.appendTables(mappers)
	resp.ComputeStats.ComputeCostMs = time.Since(startTime).Milliseconds()
	resp.ComputeStats.ComputedAt = timestamppb.New(startTime)
	return resp, nil
}
