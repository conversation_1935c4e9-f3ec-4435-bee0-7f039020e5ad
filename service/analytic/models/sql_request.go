package models

import (
	"time"

	"sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/query"
	cache "sentioxyz/sentio/service/common/requestcache"
)

type SQLRequest struct {
	*protos.SQLRequest
	*cache.Identifier
	hash string
}

func NewSQLRequest(req *protos.SQLRequest, mode string, args *query.Args) *SQLRequest {
	var hash string
	switch {
	case req.GetSqlQuery() != nil:
		sqlQuery := &protos.SQLRequest_SqlQuery{
			SqlQuery: &protos.SQLQuery{},
		}
		switch {
		case req.GetSqlQuery().GetQueryId() != "":
			sqlQuery.SqlQuery.QueryId = req.GetSqlQuery().GetQueryId()
		case req.GetSqlQuery().GetName() != "":
			sqlQuery.SqlQuery.Name = req.GetSqlQuery().GetName()
		default:
			sqlQuery.SqlQuery.Sql = req.GetSqlQuery().GetSql()
		}
		sqlQuery.SqlQuery.Size = req.GetSqlQuery().GetSize()
		sqlQuery.SqlQuery.Parameters = req.GetSqlQuery().GetParameters()
		if mode == "sync" {
			hash = EncodeParams(sqlQuery, req.GetSqlQuery().GetSql(), req.GetSource(), req.GetSyncV1(), req.GetEngine().String(), mode)
		} else {
			hash = EncodeParams(sqlQuery, req.GetSource(), req.GetSyncV1(), req.GetEngine().String(), mode)
		}
	default:
		hash = EncodeParams(req.GetCursor(), req.GetSource(), req.GetSyncV1(), req.GetEngine().String(), mode)
	}
	return &SQLRequest{
		SQLRequest: req,
		Identifier: NewIdentifierFromQueryArgs(args),
		hash:       hash,
	}
}

func (r SQLRequest) Key() cache.Key {
	return cache.Key{
		Prefix:     "execute_sql",
		UniqueID:   r.hash,
		Identifier: r.Identifier,
	}
}

func (r SQLRequest) TTL() time.Duration {
	switch r.SQLRequest.GetSource() {
	case protos.Source_SQL_EDITOR:
		return time.Minute
	case protos.Source_DASHBOARD:
		return time.Hour * 24 * 7
	case protos.Source_ASYNC_TRIGGER:
		return time.Minute * 3
	case protos.Source_CURL:
		return time.Minute * 5
	case protos.Source_ENDPOINT:
		return time.Minute * 5
	default:
		return time.Minute * 10
	}
}

func (r SQLRequest) RefreshInterval() time.Duration {
	switch r.SQLRequest.GetSource() {
	case protos.Source_SQL_EDITOR:
		return 0
	case protos.Source_DASHBOARD:
		return time.Hour
	case protos.Source_ASYNC_TRIGGER:
		return 0
	case protos.Source_CURL:
		return 0
	case protos.Source_ENDPOINT:
		return time.Minute * 3
	default:
		return 0
	}
}

func (r SQLRequest) String() string {
	return r.hash
}

func (r SQLRequest) GetQueryID() string {
	if r.SQLRequest.GetSqlQuery() != nil {
		return r.SQLRequest.GetSqlQuery().GetQueryId()
	}
	return ""
}

func (r SQLRequest) GetQueryName() string {
	if r.SQLRequest.GetSqlQuery() != nil {
		return r.SQLRequest.GetSqlQuery().GetName()
	}
	return ""
}
