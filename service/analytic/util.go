package analytic

import (
	"context"
	"fmt"
	"reflect"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/clickhouse/metadata"
	chmodels "sentioxyz/sentio/common/clickhouse/models"
	"sentioxyz/sentio/common/clickhouse/roles"
	"sentioxyz/sentio/common/clickhouse/schema"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/functions"
	"sentioxyz/sentio/common/log"
	subgraphchs "sentioxyz/sentio/driver/entity/clickhouse"
	subgraphschema "sentioxyz/sentio/driver/entity/schema"
	"sentioxyz/sentio/driver/subgraph/manifest"
	"sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/util"
	"sentioxyz/sentio/service/common/auth"
	commonmodels "sentioxyz/sentio/service/common/models"
	protoscommon "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/insights/constants"
	"sentioxyz/sentio/service/processor"
	processormodels "sentioxyz/sentio/service/processor/models"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type timeRangeLiteParams interface {
	GetTimeRange() *protoscommon.TimeRangeLite
}

type timeRangeParams interface {
	GetTimeRange() *protoscommon.TimeRange
}

func (s *service) initSchema(schemaInterface event.Schema) (*schema.Schema, error) {
	if schemaInterface == nil {
		return nil, status.Errorf(codes.NotFound, "schema not found")
	}
	eventSchema, ok := schemaInterface.(*schema.Schema)
	if !ok {
		return nil, status.Errorf(codes.Internal, "schema type error")
	}
	return eventSchema, nil
}

func (s *service) newSentioProcessorArgs(ctx context.Context, processor *processormodels.Processor) (
	*query.SentioProcessorArgs, event.Conn, event.Conn, error) {
	conn := s.clickhouseMultiSharding.GetShard(processor.ClickhouseShardingIndex)
	if conn == nil {
		return nil, nil, nil, status.Errorf(codes.Internal,
			"clickhouse shard %d is not exists now", processor.ClickhouseShardingIndex)
	}
	processorArgs := &query.SentioProcessorArgs{}
	rwConn := conn.GetSentioConn()
	rConn := conn.GetSentioViewConn()
	rwSgConn := conn.GetSubgraphConn()
	if rwConn == nil || rConn == nil || rwSgConn == nil {
		return nil, nil, nil, status.Errorf(codes.Internal,
			"clickhouse shard %d is not exists now", processor.ClickhouseShardingIndex)
	}
	processorArgs.Reader = clickhouse.NewEventReader[*chmodels.LogEvent](
		ctx, rwConn, nil,
		processor,
		metadata.InitRedisMetadataClient(s.redisClient)).(*clickhouse.Reader[*chmodels.LogEvent])
	processorArgs.Viewer = clickhouse.NewEventViewer[*chmodels.LogEvent](
		ctx, rConn, conn.GetReadonlyConns(roles.SentioCategory),
		processor,
		metadata.InitRedisMetadataClient(s.redisClient),
		clickhouse.SchemaFromMetadata|clickhouse.SchemaFromDB).(*clickhouse.Viewer[*chmodels.LogEvent])
	eventSchema, err := s.initSchema(processorArgs.Reader.GetSchema())
	if err != nil {
		return nil, nil, nil, err
	}
	processorArgs.Schema = eventSchema
	if processor.EntitySchema != "" {
		processorArgs.Entity.EntitySchema, err = subgraphschema.ParseAndVerifySchema(processor.EntitySchema)
		if err != nil {
			log.WithContext(ctx).Warnf("failed to parse entity schema: %s", err.Error())
			return nil, nil, nil, status.Errorf(codes.Internal, "failed to parse entity schema: %s", err.Error())
		}
		processorArgs.Entity.SgViewer = clickhouse.NewSgViewer(processor.ID,
			metadata.InitRedisMetadataClient(s.redisClient),
			processorArgs.Entity.EntitySchema,
			subgraphchs.NewStore(
				rwSgConn.GetClickhouseConn(),
				processor.ID,
				subgraphchs.BuildFeatures(processor.EntitySchemaVersion),
				processorArgs.Entity.EntitySchema,
				subgraphchs.DefaultCreateTableOption),
			conn.GetSubgraphViewConn(), conn.GetReadonlyConns(roles.SubgraphCategory),
		)
	}
	return processorArgs, rwConn, rConn, nil
}

func (s *service) newSubgraphProcessorArgs(ctx context.Context, processor *processormodels.Processor) (
	*query.SubgraphProcessorArgs, event.Conn, event.Conn, error) {
	conn := s.clickhouseMultiSharding.GetShard(processor.ClickhouseShardingIndex)
	if conn == nil {
		return nil, nil, nil, status.Errorf(codes.Internal,
			"clickhouse shard %d is not exists now", processor.ClickhouseShardingIndex)
	}
	processorArgs := &query.SubgraphProcessorArgs{}
	rwConn := conn.GetSubgraphConn()
	rConn := conn.GetSubgraphViewConn()
	if rwConn == nil || rConn == nil {
		return nil, nil, nil, status.Errorf(codes.Internal,
			"clickhouse shard %d is not exists now", processor.ClickhouseShardingIndex)
	}
	cacheSchema, ok := s.cache.GetSubgraphEntitySchema(processor.ID)
	if ok {
		processorArgs.EntitySchema = cacheSchema
	} else {
		var err error
		var mf *manifest.Manifest
		mf, err = manifest.LoadFromIpfs(s.ipfsShell, processor.IpfsHash)
		if err != nil {
			log.WithContext(ctx).Warnf("failed to load subgraph manifest from ipfs: %s", err.Error())
			return nil, nil, nil, status.Errorf(codes.Internal, "failed to load subgraph manifest from ipfs: %s", err.Error())
		}
		processorArgs.EntitySchema, err = subgraphschema.ParseAndVerifySchema(mf.Schema.File.GetContent())
		if err != nil {
			log.WithContext(ctx).Warnf("failed to parse subgraph schema: %s", err.Error())
			return nil, nil, nil, status.Errorf(codes.Internal, "failed to parse subgraph schema: %s", err.Error())
		}
		s.cache.SetSubgraphEntitySchema(processor.ID, processorArgs.EntitySchema)
	}
	processorArgs.SgViewer = clickhouse.NewSgViewer(processor.ID,
		metadata.InitRedisMetadataClient(s.redisClient),
		processorArgs.EntitySchema,
		subgraphchs.NewStore(
			rwConn.GetClickhouseConn(),
			processor.ID,
			subgraphchs.BuildFeatures(processor.EntitySchemaVersion),
			processorArgs.EntitySchema,
			subgraphchs.DefaultCreateTableOption),
		rConn, conn.GetReadonlyConns(roles.SubgraphCategory),
	)
	return processorArgs, rwConn, rConn, nil
}

func (s *service) initSentioProcessorContext(ctx context.Context, args *query.Args, _ PreprocessOption) (*query.Args, error) {
	if args.Processor == nil {
		args.Processor = &processormodels.Processor{
			ID:                      processormodels.MockProcessorID(),
			ClickhouseShardingIndex: args.ClickhouseMultiSharding.Pick(processor.NewProperties(args.Project)),
			Version:                 0,
		}
	}
	processorArgs, rwConn, rConn, err := s.newSentioProcessorArgs(ctx, args.Processor)
	if err != nil {
		return nil, err
	}
	args.SentioProcessorArgs = *processorArgs
	args.RWConn = rwConn
	args.RConn = rConn
	return args, nil
}

func (s *service) initSubgraphProcessorContext(ctx context.Context, args *query.Args, option PreprocessOption) (*query.Args, error) {
	if args.Processor == nil {
		if !option.supportExternalProject {
			return nil, status.Errorf(codes.Aborted, "empty subgraph project is not allowed, please upload a subgraph code")
		}
		return args, nil
	}
	processorArgs, rwConn, rConn, err := s.newSubgraphProcessorArgs(ctx, args.Processor)
	if err != nil {
		return nil, err
	}
	args.SubgraphProcessorArgs = *processorArgs
	args.RWConn = rwConn
	args.RConn = rConn
	return args, nil
}

func (s *service) initProcessorContext(ctx context.Context, args *query.Args, option PreprocessOption) (*query.Args, error) {
	var err error
	switch args.Project.Type {
	case commonmodels.ProjectTypeSentio:
		args, err = s.initSentioProcessorContext(ctx, args, option)
	case commonmodels.ProjectTypeSubgraph:
		args, err = s.initSubgraphProcessorContext(ctx, args, option)
	}
	if err != nil {
		return args, err
	}
	if option.supportExternalProject {
		for idx := range args.ExternalProjects {
			if args.ExternalProjects[idx].ImportProject == nil {
				args.ExternalProcessorArgs = append(args.ExternalProcessorArgs, nil)
				continue
			}
			switch args.ExternalProjects[idx].ImportProject.Type {
			case commonmodels.ProjectTypeSentio:
				processorArgs, _, _, err := s.newSentioProcessorArgs(ctx, args.ExternalProcessors[idx])
				if err != nil {
					return nil, err
				}
				args.ExternalProcessorArgs = append(args.ExternalProcessorArgs, processorArgs)
			case commonmodels.ProjectTypeSubgraph:
				processorArgs, _, _, err := s.newSubgraphProcessorArgs(ctx, args.ExternalProcessors[idx])
				if err != nil {
					return nil, err
				}
				args.ExternalProcessorArgs = append(args.ExternalProcessorArgs, processorArgs)
			default:
				return nil, status.Errorf(codes.Internal, "unknown project type %s", args.Project.Type)
			}
		}
	}
	return args, nil
}

func (s *service) verifyAndGetProcessor(ctx context.Context,
	projectOwner, projectSlug, projectID string, projectVersion int32,
	option PreprocessOption) (
	*commonmodels.Identity, *commonmodels.Project,
	*processormodels.Processor, []*commonmodels.ImportedProject, []*processormodels.Processor,
	error) {
	if !s.initialized() {
		return nil, nil, nil, nil, nil, status.Error(codes.Unavailable, "service not ready")
	}
	var identity *commonmodels.Identity
	var project *commonmodels.Project
	var processor *processormodels.Processor
	var err error
	if len(projectOwner) > 0 {
		project, err = s.repository.GetProjectBySlug(ctx, projectOwner, projectSlug)
		if err != nil {
			return nil, nil, nil, nil, nil, status.Error(codes.Internal,
				fmt.Sprintf("failed to get project %s/%s: %s", projectOwner, projectSlug, err.Error()))
		}
		if project == nil {
			return nil, nil, nil, nil, nil, status.Error(codes.NotFound,
				fmt.Sprintf("project %s/%s not found", projectOwner, projectSlug))
		}
		if len(projectID) > 0 && projectID != project.ID {
			return nil, nil, nil, nil, nil, status.Error(codes.NotFound,
				fmt.Sprintf("project %s/%s is not match with project id %s",
					projectOwner, projectSlug, projectID))
		}
		projectID = project.ID
	} else {
		project, err = s.repository.GetProjectByID(s.repository.DB.WithContext(ctx), projectID)
		if err != nil {
			return nil, nil, nil, nil, nil, status.Error(codes.Internal,
				fmt.Sprintf("failed to get project %s: %s", projectID, err.Error()))
		}
		if project == nil {
			return nil, nil, nil, nil, nil, status.Error(codes.NotFound,
				fmt.Sprintf("project %s not found", projectID))
		}
	}
	if !option.skipAuth {
		if option.requireWritePermission {
			identity, _, err = s.auth.RequiredLoginForProject(ctx, projectID, auth.WRITE)
		} else {
			identity, _, err = s.auth.RequiredLoginForProject(ctx, projectID, auth.READ)
		}
		if err != nil {
			log.Warnf("failed to verify auth for project %s: %s", projectID, err.Error())
			return nil, nil, nil, nil, nil, err
		}
	}
	processors, err := s.repository.GetProcessorsByProjectAndVersion(ctx, projectID, projectVersion)
	if err != nil {
		return nil, nil, nil, nil, nil, status.Error(codes.Internal,
			fmt.Sprintf("failed to get processors for project %s: %s", projectID, err.Error()))
	}
	if option.supportExternalProject {
		var importedProjectsAfterFilter []*commonmodels.ImportedProject
		var importedProcessors []*processormodels.Processor
		importedProjects, err := s.repository.ListImportedProjects(ctx, projectID)
		if err != nil {
			return nil, nil, nil, nil, nil, status.Error(codes.Internal,
				fmt.Sprintf("failed to get imported projects for project %s: %s", projectID, err.Error()))
		}
		for _, importedProject := range importedProjects {
			if !option.skipAuth {
				_, _, err = s.auth.RequiredLoginForProject(ctx, importedProject.ImportProjectID, auth.READ)
				if err != nil {
					log.WithContext(ctx).Warnf("failed to verify auth for project %s: %s",
						importedProject.ImportProjectID, err.Error())
					continue
				}
			}
			importedProcessor, err := s.repository.GetProcessorsByProjectAndVersion(ctx,
				importedProject.ImportProjectID, 0)
			if err != nil {
				log.WithContext(ctx).Warnf("failed to get processors for project %s: %s",
					importedProject.ImportProjectID, err.Error())
				continue
			}
			if len(importedProcessor) == 0 {
				continue
			}
			importedProjectsAfterFilter = append(importedProjectsAfterFilter, importedProject)
			importedProcessors = append(importedProcessors, importedProcessor[0])
		}
		if len(processors) > 0 {
			processor = processors[0]
		} else {
			switch project.Type {
			case commonmodels.ProjectTypeSubgraph:
				return identity, project, nil, nil, nil, status.Errorf(codes.NotFound, "empty subgraph project is not allowed")
			default:
				log.Debugf("no active processor found for project %s, will run as empty project", projectID)
			}
		}
		return identity, project, processor, importedProjectsAfterFilter, importedProcessors, nil
	} else {
		if len(processors) > 0 {
			processor = processors[0]
		} else {
			switch project.Type {
			case commonmodels.ProjectTypeSubgraph:
				return identity, project, nil, nil, nil, status.Errorf(codes.NotFound, "empty subgraph project is not allowed")
			default:
				log.Debugf("no active processor found for project %s, will run as empty project", projectID)
			}
		}
		return identity, project, processor, nil, nil, nil
	}
}

func valueGetter[T any](a reflect.Value, method string) (defaultValue T) {
	m := a.MethodByName(method)
	if !m.IsValid() {
		return
	}

	values := m.Call(nil)
	if len(values) == 0 {
		return
	}

	result, ok := values[0].Interface().(T)
	if !ok {
		return
	}
	return result
}

func (s *service) authCheck(ctx context.Context, req any, option PreprocessOption) (
	identity *commonmodels.Identity,
	project *commonmodels.Project,
	processor *processormodels.Processor,
	importedProjects []*commonmodels.ImportedProject,
	importedProcessors []*processormodels.Processor,
	err error) {
	if req == nil {
		return nil, nil, nil, nil, nil, status.Errorf(codes.InvalidArgument, "nil request")
	}
	var (
		projectSlug  string
		projectOwner string
		projectID    string
		version      int32
	)
	val := reflect.ValueOf(req)
	projectSlug = valueGetter[string](val, "GetProjectSlug")
	projectOwner = valueGetter[string](val, "GetProjectOwner")
	projectID = valueGetter[string](val, "GetProjectId")
	version = valueGetter[int32](val, "GetVersion")
	identity, project, processor, importedProjects, importedProcessors, err = s.verifyAndGetProcessor(
		ctx, projectOwner, projectSlug, projectID, version, option)
	if err != nil {
		return nil, nil, nil, nil, nil, err
	}
	return identity, project, processor, importedProjects, importedProcessors, nil
}

func (s *service) initTimeRange(ctx context.Context, req any) (*util.TimeRange, error) {
	if req == nil {
		return nil, status.Errorf(codes.InvalidArgument, "nil request")
	}
	if req, ok := req.(timeRangeLiteParams); ok {
		timeRange := req.GetTimeRange()
		if timeRange != nil {
			return util.NewTimeRangeFromLite(ctx, timeRange)
		}
	}
	if req, ok := req.(timeRangeParams); ok {
		timeRange := req.GetTimeRange()
		if timeRange != nil {
			return util.NewTimeRangeFromProto(ctx, timeRange)
		}
	}
	return nil, nil
}

const (
	skipAuthOptID = iota
	supportExternalProjectOptID
	asyncSQLExecuteSignOptID
	logMethodOptID
)

type PreprocessOption struct {
	optID                  int
	skipAuth               bool
	supportExternalProject bool
	asyncSQLExecuteUserID  string
	asyncSQLExecuteQueryID string
	asyncSQLExecutionID    string
	method                 string
	requireWritePermission bool
}

func WithSkipAuth(skipAuth bool) PreprocessOption {
	return PreprocessOption{
		optID:    skipAuthOptID,
		skipAuth: skipAuth,
	}
}

func WithSupportExternalProject() PreprocessOption {
	return PreprocessOption{
		optID:                  supportExternalProjectOptID,
		supportExternalProject: true,
	}
}

func WithAsyncSQLExecuteSign(userID, queryID, executionID string) PreprocessOption {
	return PreprocessOption{
		optID:                  asyncSQLExecuteSignOptID,
		asyncSQLExecuteUserID:  userID,
		asyncSQLExecuteQueryID: queryID,
		asyncSQLExecutionID:    executionID,
	}
}

func WithLogMethod(method string) PreprocessOption {
	return PreprocessOption{
		optID:  logMethodOptID,
		method: method,
	}
}

func (s *service) preprocess(ctx context.Context, req any, options ...PreprocessOption) (context.Context, *query.Args, error) {
	args := &query.Args{
		Extra:                   make(map[string]interface{}),
		ClickhouseMultiSharding: s.clickhouseMultiSharding,
		Limiter:                 s.limiter,
		RewriterClient:          s.ckRewriterClient,
	}
	var err error
	var option = PreprocessOption{
		skipAuth:               false,
		supportExternalProject: false,
		asyncSQLExecuteUserID:  "",
		asyncSQLExecuteQueryID: "",
		asyncSQLExecutionID:    "",
	}
	for _, opt := range options {
		switch opt.optID {
		case skipAuthOptID:
			option.skipAuth = opt.skipAuth
		case supportExternalProjectOptID:
			option.supportExternalProject = opt.supportExternalProject
		case asyncSQLExecuteSignOptID:
			option.asyncSQLExecuteUserID = opt.asyncSQLExecuteUserID
			option.asyncSQLExecuteQueryID = opt.asyncSQLExecuteQueryID
			option.asyncSQLExecutionID = opt.asyncSQLExecutionID
		case logMethodOptID:
			option.method = opt.method
		}
	}
	args.Identity,
		args.Project,
		args.Processor,
		args.ExternalProjects,
		args.ExternalProcessors, err = s.authCheck(ctx, req, option)
	if err != nil {
		return ctx, nil, err
	}
	args, err = s.initProcessorContext(ctx, args, option)
	if err != nil {
		return ctx, nil, err
	}
	args.TimeRange, err = s.initTimeRange(ctx, req)
	if err != nil {
		return ctx, nil, err
	}
	return util.SetClickhouseCtxData(ctx, util.NewClickhouseCtxData(
			args.Processor, args.Project, args.Identity,
			option.asyncSQLExecuteUserID, option.asyncSQLExecuteQueryID, option.asyncSQLExecutionID, option.method)),
		args, nil
}

func (s *service) handleFormulas(ctx context.Context, req *protos.SegmentationRequest,
	resp *protos.QuerySegmentationResponse) *protos.QuerySegmentationResponse {
	if len(req.GetFormulas()) == 0 {
		return resp
	}
	log.Errorf("handle formulas function is deprecated")
	return resp
}

func (s *service) handleDisable(ctx context.Context, req *protos.SegmentationRequest,
	resp *protos.QuerySegmentationResponse) *protos.QuerySegmentationResponse {
	type request interface {
		GetDisabled() bool
	}
	var requests = make(map[string]request)
	for _, q := range req.GetQueries() {
		requests[q.GetId()] = q
	}
	for _, f := range req.GetFormulas() {
		requests[f.GetId()] = f
	}
	for _, q := range req.GetSystemSqlQueries() {
		requests[q.GetId()] = q
	}

	var results []*protos.QuerySegmentationResponse_Result
	for _, result := range resp.GetResults() {
		if r, ok := requests[result.GetId()]; ok {
			if !r.GetDisabled() {
				results = append(results, result)
			} else {
				log.WithContext(ctx).Debugf("result %s is disabled", result.GetId())
			}
		} else {
			log.WithContext(ctx).Infof("request not found for result %s", result.GetId())
		}
	}
	resp.Results = results
	return resp
}

func getFuncsByID(req *protos.SegmentationRequest, id string) []*protoscommon.Function {
	for _, q := range req.GetQueries() {
		if q.GetId() == id {
			return q.GetFunctions()
		}
	}
	for _, f := range req.GetFormulas() {
		if f.GetId() == id {
			return f.GetFunctions()
		}
	}
	return []*protoscommon.Function{}
}

func (s *service) postprocess(ctx context.Context, req *protos.SegmentationRequest,
	limitMap map[string]int32, resp *protos.QuerySegmentationResponse) *protos.QuerySegmentationResponse {
	for _, result := range resp.GetResults() {
		if result.GetMatrix() == nil {
			continue
		}
		var limit int32 = constants.AfterFormulaDefaultLimit
		if req.GetLimit() > 0 {
			limit = req.GetLimit()
		}
		if l, ok := limitMap[result.GetId()]; ok {
			limit = l
		}
		funcs := getFuncsByID(req, result.GetId())
		funcs = append(funcs, &protoscommon.Function{
			Name: "truncate",
			Arguments: []*protoscommon.Argument{
				{ArgumentValue: &protoscommon.Argument_IntValue{IntValue: limit}},
			},
		})
		matrix, err := functions.Process(ctx, result.GetMatrix(), funcs)
		if err != nil {
			result.AnalyticQueryResponseType = &protos.QuerySegmentationResponse_Result_Error{
				Error: err.Error(),
			}
		} else {
			result.AnalyticQueryResponseType = &protos.QuerySegmentationResponse_Result_Matrix{
				Matrix: matrix,
			}
		}
	}
	return resp
}

func rewriteLimit(req *protos.SegmentationRequest, segmentationQuery *protoscommon.SegmentationQuery) int32 {
	var originalLimit = segmentationQuery.Limit
	if len(req.GetFormulas()) != 0 {
		// need rewrite limit for calculate formula
		segmentationQuery.Limit = constants.BeforeFormulaLimit
	}
	return originalLimit
}
