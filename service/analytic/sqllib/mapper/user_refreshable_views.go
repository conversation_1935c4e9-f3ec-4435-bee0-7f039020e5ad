package mapper

import (
	"flag"
	"fmt"

	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/protos"
	analyticmodels "sentioxyz/sentio/service/analytic/repository/models"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/common/models"

	"gorm.io/gorm"
)

var UserMvDatabaseName = flag.String("user-mv-database-name", "user_refreshable_mv", "database name")

type UserRefreshableView struct {
	mv   *analyticmodels.SQLRefreshableMaterializedView
	conn event.Conn
}

func (m *UserRefreshableView) DisplayName() string {
	return m.mv.Name
}

func (m *UserRefreshableView) TableName() string {
	return "`" + *UserMvDatabaseName + "`.`" + m.mv.EncryptName() + "`"
}

func (m *UserRefreshableView) DatabaseAndTableName() (string, string) {
	return *UserMvDatabaseName, m.mv.EncryptName()
}

func (m *UserRefreshableView) DataType() sqllib.TableDataType {
	return sqllib.UserData
}

func (m *UserRefreshableView) Type() protos.Table_TableType {
	return protos.Table_USER_REFRESHABLE_VIEW
}

func (m *UserRefreshableView) PrimaryColumn() string {
	return ""
}

func (m *UserRefreshableView) TimeColumn() string {
	return ""
}

func (m *UserRefreshableView) ShardingSensitive() bool {
	return true
}

func (m *UserRefreshableView) RemoteSharding() int {
	return int(m.mv.ProcessorSharding)
}

func (m *UserRefreshableView) RemotePattern() string {
	return fmt.Sprintf("remote('%s','%s','%s','%s','%s')",
		m.RemoteHost(), *UserMvDatabaseName, m.RemoteTableName(),
		m.RemoteUsername(), m.RemotePassword())
}

func (m *UserRefreshableView) RemoteHost() string {
	return m.conn.GetHost()
}

func (m *UserRefreshableView) RemoteDatabase() string {
	return *UserMvDatabaseName
}

func (m *UserRefreshableView) RemoteTableName() string {
	return m.mv.EncryptName()
}

func (m *UserRefreshableView) RemoteUsername() string {
	return m.conn.GetUsername()
}

func (m *UserRefreshableView) RemotePassword() string {
	return m.conn.GetPassword()
}

func NewUserRefreshableViews(
	db *gorm.DB, project *models.Project, conn event.Conn) []sqllib.TableMapper {
	var mvs []analyticmodels.SQLRefreshableMaterializedView
	if err := db.Model(&analyticmodels.SQLRefreshableMaterializedView{}).
		Where("project_id = ?", project.ID).Find(&mvs).Error; err != nil {
		log.Errorf("failed to find materialized views for project %s: %v", project.FullName(), err)
		return nil
	}
	var mappers []sqllib.TableMapper
	for _, mv := range mvs {
		mappers = append(mappers, &UserRefreshableView{
			mv:   &mv,
			conn: conn,
		})
	}
	return mappers
}
