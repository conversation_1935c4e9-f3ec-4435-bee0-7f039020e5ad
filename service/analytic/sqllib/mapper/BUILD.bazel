load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "mapper",
    srcs = [
        "chains.go",
        "dash.go",
        "init.go",
        "reserved_materialized_views.go",
        "tokens.go",
        "user_refreshable_views.go",
        "utils.go",
    ],
    embedsrcs = ["data/sea_system_table_config.yaml"],
    importpath = "sentioxyz/sentio/service/analytic/sqllib/mapper",
    visibility = ["//visibility:public"],
    deps = [
        "//common/clickhouse",
        "//common/clickhouse/builder",
        "//common/clickhouse/metadata",
        "//common/clickhouse/models",
        "//common/clickhouse/roles",
        "//common/clickhouse/schema",
        "//common/event",
        "//common/log",
        "//driver/entity/clickhouse",
        "//driver/entity/schema",
        "//driver/subgraph/manifest",
        "//service/analytic/protos",
        "//service/analytic/repository/cache",
        "//service/analytic/repository/models",
        "//service/analytic/sqllib",
        "//service/common/models",
        "//service/common/repository",
        "//service/processor/models",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_ipfs_go_ipfs_api//:go-ipfs-api",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_samber_lo//:lo",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "mapper_test",
    srcs = ["init_test.go"],
    embed = [":mapper"],
    deps = [
        "//common/clickhouse",
        "//common/log",
        "@com_github_stretchr_testify//assert",
    ],
)
