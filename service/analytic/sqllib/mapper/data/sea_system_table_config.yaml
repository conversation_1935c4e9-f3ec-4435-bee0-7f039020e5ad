admin:
  username: sentio
  password: 2vwzZBJ6cbZbyoKm4j
clickhouseTemplate:
  chain-part-a:
    # a placeholder for sharding
    sharding: 99
    host: clickhouse-chain-part-a.clickhouse:9000
    database: chain_db
    username: sentio_viewer
    password: 0i6yGMWisu3pLNv
  chain-part-b:
    # a placeholder for sharding
    sharding: 98
    host: clickhouse-chain-part-b.clickhouse:9000
    database: chain_db
    username: sentio_viewer
    password: 0i6yGMWisu3pLNv
  chain-part-c:
    sharding: 97
    host: clickhouse-chain-part-c.clickhouse:9000
    database: chain_db
    username: sentio_viewer
    password: 0i6yGMWisu3pLNv
chainsTemplate:
  - chain: sui-mainnet
    chainDisplayName: sui
    database: chain_db
    clickhouseTemplate: chain-part-b
    tables:
      - name: object_changes
        uniqueColumn: object_id
        timeColumn: timestamp
      - name: transactions
        uniqueColumn: digest
        timeColumn: timestamp
      - name: coins
        uniqueColumn: coin_type
        timeColumn: create_time
      - name: move_calls
        uniqueColumn: digest
        timeColumn: timestamp
      - name: events
        uniqueColumn: digest
        timeColumn: timestamp
      - name: balance_changes
        uniqueColumn: digest
        timeColumn: timestamp
      - name: coin_holders
        uniqueColumn: owner_address
        timeColumn: timestamp
      - name: coin_prices
        uniqueColumn: symbol
        timeColumn: time
  - chain: ethereum,arb,linea
    chainDisplayName: ethereum,arbitrum,linea
    database: chain_db
    clickhouseTemplate: chain-part-a
    tables:
      - name: blocks
        uniqueColumn: block_number
        timeColumn: block_timestamp
      - name: logs
        uniqueColumn: (transaction_hash, log_index)
        timeColumn: block_timestamp
      - name: traces
        uniqueColumn: (transaction_hash, trace_index)
        timeColumn: block_timestamp
      - name: transactions
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: withdrawals
        uniqueColumn: block_hash
        timeColumn: block_timestamp
  - chain: aptos-mainnet,aptos-movement-mainnet
    chainDisplayName: aptos,movement
    database: chain_db
    clickhouseTemplate: chain-part-a
    tables:
      - name: transactions
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: table_items
        uniqueColumn: (transaction_hash, transaction_index, change_index)
        timeColumn: block_timestamp
      - name: resources
        uniqueColumn: (transaction_hash, transaction_index, change_index)
        timeColumn: block_timestamp
      - name: modules
        uniqueColumn: (transaction_hash, transaction_index, change_index)
        timeColumn: block_timestamp
      - name: events
        uniqueColumn: (transaction_hash, transaction_index, event_index)
        timeColumn: block_timestamp
      - name: changes
        uniqueColumn: (transaction_hash, transaction_index, change_index)
        timeColumn: block_timestamp
      - name: blocks
        uniqueColumn: block_hash
        timeColumn: block_timestamp
  - chain: bitcoin-full
    chainDisplayName: bitcoin
    database: chain_db
    clickhouseTemplate: chain-part-a
    tables:
      - name: blocks
        uniqueColumn: block_hash
        timeColumn: block_timestamp
      - name: inputs
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: outputs
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: transactions
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
  - chain: fuel.mainnet
    chainDisplayName: fuel
    database: chain_db
    clickhouseTemplate: chain-part-b
    tables:
      - name: balances
        uniqueColumn: distinct_id
        timeColumn: timestamp
      - name: hourly_balances
        uniqueColumn: distinct_id
        timeColumn: timestamp
      - name: transactions
        uniqueColumn: transaction_id
        timeColumn: block_time
  - chain: zircuit,swell,berachain,soneium
    chainDisplayName: zircuit,swell,berachain,soneium
    database: chain_db
    clickhouseTemplate: chain-part-b
    tables:
      - name: blocks
        uniqueColumn: block_number
        timeColumn: block_timestamp
      - name: logs
        uniqueColumn: (transaction_hash, log_index)
        timeColumn: block_timestamp
      - name: traces
        uniqueColumn: (transaction_hash, trace_index)
        timeColumn: block_timestamp
      - name: transactions
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: withdrawals
        uniqueColumn: block_hash
        timeColumn: block_timestamp
  - chain: sonic,cronos-zkevm
    chainDisplayName: sonic,cronos-zkevm
    database: chain_db
    clickhouseTemplate: chain-part-b
    tables:
      - name: blocks
        uniqueColumn: block_number
        timeColumn: block_timestamp
      - name: logs
        uniqueColumn: (transaction_hash, log_index)
        timeColumn: block_timestamp
      - name: traces
        uniqueColumn: (transaction_hash, trace_index)
        timeColumn: block_timestamp
      - name: transactions
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: withdrawals
        uniqueColumn: block_hash
        timeColumn: block_timestamp
      - name: balances
        uniqueColumn: (token_address, address_address)
        timeColumn: timestamp
  - chain: cronos
    chainDisplayName: cronos
    database: chain_db
    clickhouseTemplate: chain-part-a
    tables:
      - name: blocks
        uniqueColumn: block_number
        timeColumn: block_timestamp
      - name: logs
        uniqueColumn: (transaction_hash, log_index)
        timeColumn: block_timestamp
      - name: traces
        uniqueColumn: (transaction_hash, trace_index)
        timeColumn: block_timestamp
      - name: transactions
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: withdrawals
        uniqueColumn: block_hash
        timeColumn: block_timestamp
      - name: balances
        uniqueColumn: (token_address, address_address)
        timeColumn: timestamp
  - chain: mev-commit,base
    chainDisplayName: mev-commit,base
    database: chain_db
    clickhouseTemplate: chain-part-c
    tables:
      - name: blocks
        uniqueColumn: block_number
        timeColumn: block_timestamp
      - name: logs
        uniqueColumn: (transaction_hash, log_index)
        timeColumn: block_timestamp
      - name: traces
        uniqueColumn: (transaction_hash, trace_index)
        timeColumn: block_timestamp
      - name: transactions
        uniqueColumn: transaction_hash
        timeColumn: block_timestamp
      - name: withdrawals
        uniqueColumn: block_hash
        timeColumn: block_timestamp
  - chain: sui-testnet
    chainDisplayName: sui-testnet
    database: chain_db
    clickhouseTemplate: chain-part-b
    tables:
      - name: object_changes
        uniqueColumn: object_id
        timeColumn: timestamp
      - name: transactions
        uniqueColumn: digest
        timeColumn: timestamp
      - name: move_calls
        uniqueColumn: digest
        timeColumn: timestamp
      - name: events
        uniqueColumn: digest
        timeColumn: timestamp
      - name: balance_changes
        uniqueColumn: digest
        timeColumn: timestamp
  - chain: iota-mainnet,iota-testnet
    chainDisplayName: iota,iota-testnet
    database: chain_db
    clickhouseTemplate: chain-part-b
    tables:
      - name: balance_changes
        uniqueColumn: digest
        timeColumn: timestamp
      - name: events
        uniqueColumn: digest
        timeColumn: timestamp
      - name: move_calls
        uniqueColumn: digest
        timeColumn: timestamp
      - name: object_changes
        uniqueColumn: object_id
        timeColumn: timestamp
      - name: transactions
        uniqueColumn: digest
        timeColumn: timestamp
