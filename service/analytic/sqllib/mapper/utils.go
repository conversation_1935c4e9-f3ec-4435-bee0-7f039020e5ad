package mapper

import (
	"context"
	"strings"

	"sentioxyz/sentio/common/log"

	clickhousev2 "github.com/ClickHouse/clickhouse-go/v2"
)

var defaultK8sCluster = GkeCluster

// GetK8sCluster returns the k8s cluster based on the host
// it is a hacked function in migration
func GetK8sCluster(host string) string {
	parts := strings.Split(host, ".")
	k8sNamespace := parts[len(parts)-1]
	switch {
	case strings.Contains(k8sNamespace, "clickhouse-balanced"):
		log.Debugf("host: %s, k8sNamespace: %s, goto gke", host, k8sNamespace)
		return GkeCluster
	case strings.Contains(k8sNamespace, "clickhouse-premium"):
		log.Debugf("host: %s, k8sNamespace: %s, goto gke", host, k8sNamespace)
		return GkeCluster
	case strings.Contains(k8sNamespace, "clickhouse-gcs"):
		log.Debugf("host: %s, k8sNamespace: %s, goto gke", host, k8sNamespace)
		return GkeCluster
	case strings.Contains(k8sNamespace, "clickhouse-test"):
		log.Debugf("host: %s, k8sNamespace: %s, goto gke", host, k8sNamespace)
		return GkeCluster
	case strings.Contains(k8sNamespace, "clickhouse"):
		log.Debugf("host: %s, k8sNamespace: %s, goto sea", host, k8sNamespace)
		return SeaCluster
	default:
		log.Debugf("host: %s(%s)missing, use default", host, k8sNamespace)
		return defaultK8sCluster
	}
}

func clusterName(conn clickhousev2.Conn) string {
	row := conn.QueryRow(context.Background(), "SELECT cluster FROM ("+
		"SELECT cluster, count(*) AS rs, SUM(host_address = '127.0.0.1') AS cl "+
		"FROM system.clusters "+
		"WHERE cluster not like 'all-%' "+
		"GROUP BY cluster"+
		") WHERE cl > 0 AND rs > 1")
	if row.Err() != nil {
		log.Errorf("get cluster failed: %v", row.Err())
		return ""
	}
	var cluster string
	err := row.Scan(&cluster)
	if err != nil {
		log.Errorf("scan cluster failed: %v", err)
		return ""
	}
	return cluster
}
