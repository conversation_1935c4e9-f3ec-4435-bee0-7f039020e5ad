load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "sqllib",
    srcs = [
        "mapping_result.go",
        "settings.go",
        "table_mapper.go",
        "validator.go",
    ],
    importpath = "sentioxyz/sentio/service/analytic/sqllib",
    visibility = ["//visibility:public"],
    deps = [
        "//common/clickhouse",
        "//common/clickhouse/models",
        "//common/errgroup",
        "//common/event",
        "//common/log",
        "//driver/entity/clickhouse",
        "//service/analytic/protos",
        "//service/analytic/query",
        "//service/analytic/repository",
        "//service/analytic/repository/models",
        "//service/common/auth",
        "//service/common/models",
        "//service/processor/models",
        "@com_github_pkg_errors//:errors",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_samber_lo//:lo",
        "@com_github_sentioxyz_golang_lru//:golang-lru",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_gorm_gorm//:gorm",
    ],
)
