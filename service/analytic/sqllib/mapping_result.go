package sqllib

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"sync"
	"time"

	"sentioxyz/sentio/common/clickhouse"
	chmodels "sentioxyz/sentio/common/clickhouse/models"
	"sentioxyz/sentio/common/errgroup"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	subgraphchs "sentioxyz/sentio/driver/entity/clickhouse"
	"sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/repository"
	"sentioxyz/sentio/service/analytic/repository/models"
	commonmodels "sentioxyz/sentio/service/common/models"
	processormodel "sentioxyz/sentio/service/processor/models"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	lru "github.com/sentioxyz/golang-lru"
)

var (
	invisibleSystemTable = []string{
		"system.tables",
		"system.columns",
		"system.mutations",
		"system.parts",
		"system.databases",
		"system.disks",
		"system.distributed_ddl_queue",
		"system.settings",
		"system.processes",
		"system.replication_queue",
		"system.replica_settings",
		"system.metrics",
	}
)

func GetTableMappers(args *query.Args, seaTableMappers *TableMappers) *TableMappers {
	var mappers *TableMappers
	if args.Processor == nil {
		mappers = seaTableMappers
	} else {
		k8sCluster := args.ClickhouseMultiSharding.GetShard(args.Processor.ClickhouseShardingIndex).GetK8sCluster()
		switch k8sCluster {
		case "sea", "sentio-sea":
			mappers = seaTableMappers
		default:
			log.Warnf("missing k8s cluster: %s, use default", k8sCluster)
			mappers = seaTableMappers
		}
	}
	return mappers
}

var (
	SQLProcessorStateKey        = flag.String("sql-processor-state-key", "sql_prepare_execute_state", "sql prepare execute state key")
	SQLProcessorStateExpiration = flag.Duration("sql-processor-state-expiration", time.Hour, "sql prepare execute state expiration")
	DashWhitelistEnable         = flag.Bool("dash-whitelist-enable", true, "enable dash whitelist")
	DashWhitelist               = flag.String("dash-whitelist", "sui/main", "dash whitelist")
	MappingToleranceExpiration  = flag.Duration("mapping-tolerance-expiration", time.Hour, "mapping result tolerance expiration")
)

type Mapping interface {
	GetDatabaseTableMapping() map[string]event.DatabaseTableArgs
	GetRemoteArgsMapping() map[string]event.RemoteArgs
	GetCommonTableExprArgsMapping() map[string]event.CommonTableExprArgs
	ApplyExternals(ctx context.Context, redisClient *redis.Client, args *query.Args) error
	ApplySgViewer(ctx context.Context, processorID, prefix string, crossSharding, crossK8sCluster bool,
		shard event.ShardingConn, redisClient *redis.Client, viewer *clickhouse.SgViewer)
	ApplyMapper(currentSharding int, identity *commonmodels.Identity, userID string,
		repo *repository.Repository, tableMappers *TableMappers)
	ApplyEntities(ctx context.Context, args *query.Args)
	AppendUserMappers(mappers []TableMapper)
	AppendDashMappers(mappers []TableMapper, args *query.Args)
	ApplyQueries(ctx context.Context, queries []*models.SQLQuery)
	Timestamp() time.Time
}

type mapping struct {
	databaseTableMapping   map[string]event.DatabaseTableArgs
	remoteArgsMapping      map[string]event.RemoteArgs
	commonTableArgsMapping map[string]event.CommonTableExprArgs
	lock                   sync.Mutex // lock to protect concurrent access to the mapping
	timestamp              time.Time
}

var (
	mappingCache *lru.Cache[string, Mapping]

	externalTablePrefix = func(alias string) string {
		if alias == "" {
			return ""
		}
		alias = strings.ReplaceAll(alias, "-", "_")
		alias = strings.ReplaceAll(alias, "/", "_")
		return alias + "."
	}
)

func init() {
	var err error
	mappingCache, err = lru.New[string, Mapping](1024)
	if err != nil {
		panic(err)
	}
}

func GetOrNewMapping(processorID string) (Mapping, bool, bool) {
	if v, ok := mappingCache.Get(processorID); ok {
		if v.Timestamp().Add(*MappingToleranceExpiration).After(time.Now()) {
			needRefresh := v.Timestamp().Add(*MappingToleranceExpiration / 2).Before(time.Now())
			return v, true, needRefresh
		} else {
			mappingCache.Remove(processorID)
		}
	}
	return NewMapping(), false, false
}

func MustNewMapping() Mapping {
	return NewMapping()
}

func SaveMapping(processorID string, result Mapping) {
	if result == nil {
		return
	}
	mappingCache.Add(processorID, result)
}

func DeleteMapping(processorID string) {
	mappingCache.Remove(processorID)
}

func ExternalTableName(externalAlias, originalTable string) string {
	prefix := externalTablePrefix(externalAlias)
	return prefix + originalTable
}

func processorKey(processorID string) string {
	return fmt.Sprintf("%s:%s", *SQLProcessorStateKey, processorID)
}

func LoadState(logger *log.SentioLogger, redisClient *redis.Client, processorID string) bool {
	val, err := redisClient.Get(context.Background(), processorKey(processorID)).Result()
	switch {
	case err != nil:
		if errors.Is(err, redis.Nil) {
			return true
		}
		logger.Infof("get prepare execute state failed: %v", err)
		return true
	case val == "1":
		return false
	default:
		return true
	}
}

func SaveState(logger *log.SentioLogger, redisClient *redis.Client, processorID string) {
	if err := redisClient.Set(context.Background(), processorKey(processorID),
		"1", *SQLProcessorStateExpiration).Err(); err != nil {
		logger.Infof("set prepare execute state failed: %v", err)
	}
}

func DeleteState(logger *log.SentioLogger, redisClient *redis.Client, processorID string) {
	if err := redisClient.Del(context.Background(), processorKey(processorID)).Err(); err != nil {
		logger.Infof("del prepare execute state failed: %v", err)
	}
}

func GetUser(identity *commonmodels.Identity, userID string) string {
	switch {
	case userID != "":
		return userID
	case identity != nil:
		return identity.GetUserID()
	default:
		return ""
	}
}

func FilterMapperWithIdentity(user string, mappers *TableMappers, repo *repository.Repository) []TableMapper {
	if user == "" {
		return nil
	}
	candidateOrgIDs := mappers.MaterializedViewMappersOrgIDs()
	adminOrg, err := repo.GetAdminOrg()
	if err != nil {
		return nil
	}
	userOrgs, err := repo.GetUserOrgIDsFromCandidateOrgs(user, candidateOrgIDs, adminOrg.ID)
	if err != nil {
		return nil
	}
	return mappers.MaterializedViewMappersByOrgIDs(userOrgs)
}

func (r *mapping) ApplyExternals(ctx context.Context, redisClient *redis.Client, args *query.Args) (err error) {
	ctx, logger := log.FromContext(ctx)
	if args.ExternalProjects == nil || len(args.ExternalProjects) == 0 {
		return nil
	}
	currentIdx := args.Processor.ClickhouseShardingIndex
	var value chmodels.LogEvent
	currentShard := args.ClickhouseMultiSharding.GetShard(currentIdx)
	if currentShard == nil {
		log.Infof("current shard not found: %d", currentIdx)
		return nil
	}

	wg, _ := errgroup.WithContext(ctx)
	handleExternal := func(processor *processormodel.Processor, project *commonmodels.ImportedProject, argv any) {
		shard := args.ClickhouseMultiSharding.GetShard(processor.ClickhouseShardingIndex)
		if shard == nil {
			log.Infof("shard not found: %d", processor.ClickhouseShardingIndex)
			return
		}
		crossSharding := processor.ClickhouseShardingIndex != currentIdx
		crossK8sCluster := currentShard.GetK8sCluster() != shard.GetK8sCluster()
		switch project.ImportProject.Type {
		case commonmodels.ProjectTypeSentio:
			processorArgs, ok := argv.(*query.SentioProcessorArgs)
			if !ok {
				log.Infof("invalid external processor args: %v", argv)
				return
			}
			need := LoadState(logger, redisClient, processor.ID)
			if need {
				var conn = shard.GetSentioConn()
				if err := processorArgs.Viewer.PrepareExecute(ctx, conn, map[string]any{}); err != nil {
					logger.Warnf("prepare external execute failed, processor: %s, err:%v", processor.ID, err)
					DeleteState(logger, redisClient, processor.ID)
					return
				}
				SaveState(logger, redisClient, processor.ID)
			}

			for eventName := range processorArgs.Schema.GetInvertedIndex()["event_name"] {
				tableName := ExternalTableName(project.Name, eventName)
				viewName := value.ViewName(processor.ID, processorArgs.Schema.Hash(), eventName)
				if crossSharding {
					host, port, database, username, password := shard.GetConnectInfoByDataType(
						"clickhouse", "sentio", crossK8sCluster, true)
					r.lock.Lock()
					r.remoteArgsMapping[tableName] = clickhouse.NewRemoteArgs(
						fmt.Sprintf("%s:%s", host, port), database, viewName, username, password)
					r.lock.Unlock()
				} else {
					_, _, database, _, _ := shard.GetConnectInfoByDataType("clickhouse", "sentio", false, true)
					r.lock.Lock()
					r.databaseTableMapping[tableName] = clickhouse.NewDatabaseTableArgs(database, viewName)
					r.lock.Unlock()
				}
			}
			if processorArgs.Entity.SgViewer != nil {
				r.ApplySgViewer(ctx,
					processor.ID,
					project.Name,
					crossSharding, crossK8sCluster,
					shard, redisClient, processorArgs.Entity.SgViewer)
			}
		case commonmodels.ProjectTypeSubgraph:
			processorArgs, ok := argv.(*query.SubgraphProcessorArgs)
			if !ok {
				log.Infof("invalid external processor args: %v", argv)
				return
			}
			if processorArgs.SgViewer == nil {
				log.Warnf("sg viewer is nil")
				return
			}
			r.ApplySgViewer(ctx,
				processor.ID,
				project.Name,
				crossSharding, crossK8sCluster,
				shard, redisClient, processorArgs.SgViewer)
		}
	}
	for idx := range args.ExternalProjects {
		if args.ExternalProjects[idx].ImportProject == nil {
			continue
		}
		if args.ExternalProcessorArgs[idx] == nil {
			continue
		}
		idx := idx // capture the loop variable
		wg.Go(func() error {
			handleExternal(args.ExternalProcessors[idx], args.ExternalProjects[idx], args.ExternalProcessorArgs[idx])
			return nil
		})
	}
	if err := wg.Wait(); err != nil {
		logger.Errorf("apply external projects failed: %v", err)
		return errors.Wrap(err, "apply external projects")
	}
	return nil
}

func (r *mapping) ApplyMapper(currentSharding int, identity *commonmodels.Identity, userID string,
	repo *repository.Repository, tableMappers *TableMappers) {
	handleMapper := func(mapper TableMapper) {
		if mapper.Type() != protos.Table_SYSTEM &&
			mapper.Type() != protos.Table_MATERIALIZED_VIEW &&
			mapper.Type() != protos.Table_USER_REFRESHABLE_VIEW {
			return
		}
		if mapper.ShardingSensitive() {
			if currentSharding != mapper.RemoteSharding() {
				r.lock.Lock()
				r.remoteArgsMapping[mapper.DisplayName()] = clickhouse.NewRemoteArgs(
					mapper.RemoteHost(), mapper.RemoteDatabase(), mapper.RemoteTableName(),
					mapper.RemoteUsername(), mapper.RemotePassword())
				r.lock.Unlock()
			} else {
				d, t := mapper.DatabaseAndTableName()
				r.lock.Lock()
				r.databaseTableMapping[mapper.DisplayName()] = clickhouse.NewDatabaseTableArgs(d, t)
				r.lock.Unlock()
			}
		} else {
			d, t := mapper.DatabaseAndTableName()
			r.lock.Lock()
			r.databaseTableMapping[mapper.DisplayName()] = clickhouse.NewDatabaseTableArgs(d, t)
			r.lock.Unlock()
		}
	}
	for _, mapper := range tableMappers.TokenMappers() {
		handleMapper(mapper)
	}
	for _, mapper := range tableMappers.ChainMappers() {
		handleMapper(mapper)
	}
	for _, mapper := range FilterMapperWithIdentity(GetUser(identity, userID), tableMappers, repo) {
		handleMapper(mapper)
	}
}

func (r *mapping) ApplySgViewer(ctx context.Context, processorID, prefix string, crossSharding, crossK8sCluster bool,
	shard event.ShardingConn, redisClient *redis.Client, viewer *clickhouse.SgViewer) {
	ctx, logger := log.FromContext(ctx)
	need := LoadState(logger, redisClient, processorID)
	if need {
		if err := viewer.PrepareExecute(ctx, shard.GetSubgraphConn(), nil); err != nil {
			logger.Warnf("prepare external execute failed, processor: %s, err:%v", processorID, err)
			DeleteState(logger, redisClient, processorID)
			return
		}
		SaveState(logger, redisClient, processorID)
	}

	viewFields := viewer.GetViewFields()
	viewMapping := viewer.GetViewNameMapping()
	for entityName := range viewFields {
		rawTableName := ExternalTableName(prefix, entityName+subgraphchs.SubgraphTableDisplayNameRawSuffix)
		viewTableName := ExternalTableName(prefix, entityName)
		if crossSharding {
			host, port, database, username, password := shard.GetConnectInfoByDataType(
				"clickhouse", "subgraph", crossK8sCluster, true)
			r.lock.Lock()
			r.remoteArgsMapping[rawTableName] = clickhouse.NewRemoteArgs(
				fmt.Sprintf("%s:%s", host, port), database,
				viewMapping[entityName+subgraphchs.SubgraphTableDisplayNameRawSuffix],
				username, password)
			r.remoteArgsMapping[viewTableName] = clickhouse.NewRemoteArgs(
				fmt.Sprintf("%s:%s", host, port), database,
				viewMapping[entityName],
				username, password)
			r.lock.Unlock()
		} else {
			_, _, database, _, _ :=
				shard.GetConnectInfoByDataType("clickhouse", "subgraph", false, true)
			r.lock.Lock()
			r.databaseTableMapping[rawTableName] = clickhouse.NewDatabaseTableArgs(
				database,
				viewMapping[entityName+subgraphchs.SubgraphTableDisplayNameRawSuffix])
			r.databaseTableMapping[viewTableName] = clickhouse.NewDatabaseTableArgs(
				database,
				viewMapping[entityName])
			r.lock.Unlock()
		}
	}
}

func (r *mapping) ApplyEntities(ctx context.Context, args *query.Args) {
	if args.SentioProcessorArgs.Entity.SgViewer == nil {
		return
	}
	viewer := args.SentioProcessorArgs.Entity.SgViewer
	_ = viewer.PrepareExecute(ctx, args.RWConn, map[string]any{})
	r.lock.Lock()
	for k, v := range viewer.GetViewNameMapping() {
		r.databaseTableMapping[k] = clickhouse.NewDatabaseTableArgs(viewer.GetConn().GetDatabaseName(), v)
	}
	r.lock.Unlock()
}

func (r *mapping) GetDatabaseTableMapping() map[string]event.DatabaseTableArgs {
	r.lock.Lock()
	defer r.lock.Unlock()
	return r.databaseTableMapping
}

func (r *mapping) GetRemoteArgsMapping() map[string]event.RemoteArgs {
	r.lock.Lock()
	defer r.lock.Unlock()
	return r.remoteArgsMapping
}

func (r *mapping) GetCommonTableExprArgsMapping() map[string]event.CommonTableExprArgs {
	r.lock.Lock()
	defer r.lock.Unlock()
	return r.commonTableArgsMapping
}

func (r *mapping) AppendUserMappers(mappers []TableMapper) {
	handleMapper := func(mapper TableMapper) {
		if mapper.Type() != protos.Table_USER_REFRESHABLE_VIEW {
			return
		}

		d, t := mapper.DatabaseAndTableName()
		r.lock.Lock()
		r.databaseTableMapping[mapper.DisplayName()] = clickhouse.NewDatabaseTableArgs(d, t)
		r.lock.Unlock()
	}

	for _, mapper := range mappers {
		handleMapper(mapper)
	}
}

func (r *mapping) AppendDashMappers(mappers []TableMapper, args *query.Args) {
	whitelistMap := lo.SliceToMap(strings.Split(*DashWhitelist, ","), func(s string) (string, bool) {
		return strings.TrimSpace(s), true
	})
	if *DashWhitelistEnable && !whitelistMap[args.Project.FullName()] {
		return
	}

	handleMapper := func(mapper TableMapper) {
		var dataType string
		switch mapper.Type() {
		case protos.Table_DASH_COMMUNITY_EVENT, protos.Table_DASH_CURATED_EVENT:
			dataType = "sentio"
		case protos.Table_DASH_COMMUNITY_SUBGRAPH, protos.Table_DASH_CURATED_SUBGRAPH:
			dataType = "subgraph"
		case protos.Table_DASH_COMMUNITY_ENTITY, protos.Table_DASH_CURATED_ENTITY:
			dataType = "subgraph"
		case protos.Table_DASH_COMMUNITY_MATERIALIZED_VIEW, protos.Table_DASH_CURATED_MATERIALIZED_VIEW:
			dataType = "sentio"
		}
		if dataType == "" {
			return
		}
		sharding := int32(mapper.RemoteSharding())
		shard := args.ClickhouseMultiSharding.GetShard(sharding)
		if shard == nil {
			log.Infof("shard not found: %d", sharding)
			return
		}
		currentShard := args.ClickhouseMultiSharding.GetShard(args.Processor.ClickhouseShardingIndex)
		if currentShard == nil {
			log.Infof("current shard not found: %d", args.Processor.ClickhouseShardingIndex)
			return
		}
		crossSharding := sharding != args.Processor.ClickhouseShardingIndex
		crossK8sCluster := currentShard.GetK8sCluster() != shard.GetK8sCluster()
		if crossSharding {
			database, table := mapper.DatabaseAndTableName()
			host, port, _, username, password := shard.GetConnectInfoByDataType(
				"clickhouse", dataType, crossK8sCluster, true)
			r.lock.Lock()
			r.remoteArgsMapping[mapper.DisplayName()] = clickhouse.NewRemoteArgs(
				fmt.Sprintf("%s:%s", host, port), database, table, username, password)
			r.lock.Unlock()
		} else {
			database, table := mapper.DatabaseAndTableName()
			r.lock.Lock()
			r.databaseTableMapping[mapper.DisplayName()] = clickhouse.NewDatabaseTableArgs(database, table)
			r.lock.Unlock()
		}
	}
	for _, mapper := range mappers {
		handleMapper(mapper)
	}
}

func (r *mapping) Timestamp() time.Time {
	return r.timestamp
}

func (r *mapping) fakeArgs() event.DatabaseTableArgs {
	return clickhouse.NewDatabaseTableArgs("<invisible>", "<invisible>")
}

func (r *mapping) ApplyQueries(ctx context.Context, queries []*models.SQLQuery) {
	ctx, logger := log.FromContext(ctx)
	r.lock.Lock()
	for _, q := range queries {
		sqlQuery := q.ToProto()
		switch {
		case sqlQuery == nil:
			logger.Warnf("failed to convert SQLQuery to proto: %s", q.QueryID)
		case sqlQuery.Sql == "":
			logger.Warnf("SQLQuery has empty SQL: %s", q.QueryID)
		default:
			r.commonTableArgsMapping[q.QueryID] = clickhouse.NewCommonTableArgs(
				q.QueryID, sqlQuery.Sql)
		}
	}
	r.lock.Unlock()

}

func NewMapping() Mapping {
	mr := &mapping{
		databaseTableMapping:   make(map[string]event.DatabaseTableArgs),
		remoteArgsMapping:      make(map[string]event.RemoteArgs),
		commonTableArgsMapping: make(map[string]event.CommonTableExprArgs),
		timestamp:              time.Now(),
	}
	for _, table := range invisibleSystemTable {
		// Use fake args to avoid nil pointer dereference
		mr.databaseTableMapping[table] = mr.fakeArgs()
	}
	return mr
}
