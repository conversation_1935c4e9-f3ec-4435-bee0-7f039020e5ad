package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"sentioxyz/sentio/service/common/preloader"
	"time"

	"sentioxyz/sentio/common/timescale"
	"sentioxyz/sentio/service/insights"
	protoinsights "sentioxyz/sentio/service/insights/protos"
	"sentioxyz/sentio/service/observability"
	protoso11y "sentioxyz/sentio/service/observability/protos"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/reflection"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/flags"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/monitoring"
	"sentioxyz/sentio/service/analytic"
	protoanalytic "sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/rpc"
	usageUtils "sentioxyz/sentio/service/usage/utils"
	"sentioxyz/sentio/service/web/repository"
	webUtils "sentioxyz/sentio/service/web/utils"
)

func main() {
	var (
		dbURL = flag.String(
			"database",
			"postgres://postgres:postgres@localhost:5432/postgres",
			"The postgres database address",
		)
		port                  = flag.Int("port", 10018, "The grpc port")
		authIssuerURL         = flag.String("auth-issuer-url", "https://sentio-dev.us.auth0.com/", "The auth0 issue url")
		authAudience          = flag.String("auth-audience", "http://localhost:8080/v1", "The auth0 audience")
		authClientID          = flag.String("auth-client-id", "JREam3EysMTM49eFbAjNK02OCykpmda3", "The auth0 app clientId")
		enableProf            = flag.Bool("enable-pprof", false, "Enable pprof")
		priceServiceAddress   = flag.String("price-service-address", "localhost:10070", "Price service address")
		usageServiceAddress   = flag.String("usage-service-address", "", "Usage service address")
		formulaServiceAddress = flag.String("formula-service-address", "localhost:10013", "Formula service address")
		ipfsNodeAddr          = flag.String("ipfs", "localhost:5001", "address of the ipfs node")
		timescaleConfigPath   = flag.String("timescale-db-config",
			"common/timescale/timescale_db_local_config.yml",
			"The timescale multi db config, will use default config if not set")
		cacheTTL = flag.String(
			"cache-ttl",
			"5m,24h,1m",
			"The cache ttl for timescaledb,metricMeta,refresh ",
		)
		clickhouseConfigPath   = flag.String("clickhouse-config-path", "common/clickhouse/clickhouse_docker_config.yaml", "Clickhouse config path")
		clickhouseRewriterAddr = flag.String("clickhouse-rewriter-address",
			"clickhouse-rewriter-sql-rewriter.clickhouse-premium:50051",
			"Clickhouse rewriter address")
		clickhouseLimitControlConfigPath = flag.String("clickhouse-limit-control-config-path", "", "Clickhouse limit control config path")
		sqlWorkerSetting                 = flag.String("sql-worker-setting", "low:3,medium:3,high:3", "The number of sql worker")
		clickhouseMvDisplay              = flag.Bool("clickhouse-mv-display", false, "enable display clickhouse mv on editor")
		k8sContextUse                    = flag.String("k8s-context-use", "sentio-sea", "The k8s context use")
		grpcServerTimeout                = flag.Duration("grpc-server-timeout", 60*time.Second, "The grpc server timeout")
		userMvWatchIntervalSeconds       = flag.Int64("user-mv-watch-interval-seconds", 600, "The user mv watch interval")
		systemMetricServerAddress        = flag.String("system-metric-server-address", "http://vmsingle-vm:8429", "The system metric server address")
	)
	flags.ParseAndInitLogFlag()

	monitoring.StartMonitoring()
	defer monitoring.StopMonitoring()

	var cacheTTLOptions timescale.CacheTTLOptions
	if *cacheTTL == "" {
		cacheTTLOptions = timescale.DefaultTTLOption
	} else {
		cacheTTLOptions = timescale.ParseCacheTTLOptions(*cacheTTL)
	}

	// register the GreeterServerImpl on the gRPC server
	conn, err := repository.SetupDB(*dbURL)
	if err != nil {
		log.Fatale(err)
	}

	authConfig := auth.AuthConfig{
		IssuerURL: *authIssuerURL,
		Audience:  *authAudience,
		ClientID:  *authClientID,
	}

	authManager := auth.NewAuthManager(&authConfig, conn)
	usageClient := usageUtils.MustNewClient(*usageServiceAddress)
	var clickhouseMultiSharding event.MultiSharding
	if *clickhouseConfigPath != "" {
		clickhouseMultiSharding, err = clickhouse.NewMultiSharding(*clickhouseConfigPath)
		if err != nil {
			log.Fatale(err)
		}
	}

	// create new gRPC server
	grpcSever := rpc.NewServer(true,
		grpc.ConnectionTimeout(*grpcServerTimeout),
		grpc.ChainUnaryInterceptor(
			preloader.Interceptor(conn, authManager.IdentityPreloader, preloader.ProjectLoader),
			authManager.GetAuthInterceptor(nil),
			usageUtils.UsageInterceptor(authManager, usageClient),
			webUtils.ShareDashboardChecker(conn),
			webUtils.ShareSQLQuery(conn),
		))

	analyticService := analytic.NewService(
		authManager,
		conn,
		clickhouseMultiSharding,
		*clickhouseConfigPath,
		*ipfsNodeAddr,
		*clickhouseRewriterAddr,
		*sqlWorkerSetting,
		*clickhouseMvDisplay,
		*k8sContextUse,
		*clickhouseLimitControlConfigPath,
		*userMvWatchIntervalSeconds,
	)
	protoanalytic.RegisterAnalyticServiceServer(grpcSever, analyticService)
	protoanalytic.RegisterSearchServiceServer(grpcSever, analyticService)

	o11yService := observability.NewService(
		conn,
		*timescaleConfigPath,
		&cacheTTLOptions,
		*systemMetricServerAddress,
	)
	protoso11y.RegisterObservabilityServiceServer(grpcSever, o11yService)

	insightsService := insights.NewService(
		authManager,
		conn,
		*priceServiceAddress,
		*formulaServiceAddress,
		analyticService,
		o11yService,
	)
	protoinsights.RegisterInsightsServiceServer(grpcSever, insightsService)

	reflection.Register(grpcSever)

	mux := runtime.NewServeMux(runtime.WithMetadata(rpc.WithAuthAndTraceMetadata),
		runtime.WithMetadata(func(ctx context.Context, request *http.Request) metadata.MD {
			return metadata.New(map[string]string{
				"grpcgateway-http-raw-query": request.URL.RawQuery,
			})
		}))

	err = mux.HandlePath("GET", "/healthz", rpc.HealthCheck(conn))
	if err != nil {
		log.Fatale(err)
	}
	err = protoanalytic.RegisterAnalyticServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}
	err = protoanalytic.RegisterSearchServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}
	err = protoso11y.RegisterObservabilityServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}
	err = protoinsights.RegisterInsightsServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}
	if *enableProf {
		go func() {
			http.Handle("/metrics", promhttp.Handler())
			log.Fatale(http.ListenAndServe(":6060", nil))
		}()
	}

	rpc.BindAndServeWithHTTP(mux, grpcSever, *port, nil)
}
