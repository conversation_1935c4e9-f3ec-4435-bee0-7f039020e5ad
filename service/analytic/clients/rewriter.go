package clients

import (
	"context"
	"strings"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/protojson"
	"sentioxyz/sentio/service/common/rpc"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type rewriterClient struct {
	RewriterServiceClient
}

func NewRewriterClient(addr string) (RewriterServiceClient, error) {
	conn, err := rpc.Dial(addr, rpc.RetryDialOption, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Warnf("failed to dial rewriter service: %v", err)
		return nil, err
	}
	cli := NewRewriterServiceClient(conn)
	return &rewriterClient{
		RewriterServiceClient: cli,
	}, nil
}

func (r *rewriterClient) Rewrite(ctx context.Context, req *RewriteSQLRequest, options ...grpc.CallOption) (*RewriteSQLResponse, error) {
	for _, op := range req.GetOptions() {
		if op.Op == RewriteOp_TableNameRewrite && op.GetTableNameArgs() != nil {
			args := op.GetTableNameArgs()
			for _, t := range args.TableWithDatabaseMap {
				t.Database = strings.Trim(strings.TrimSpace(t.Database), "`")
				t.Table = strings.Trim(strings.TrimSpace(t.Table), "`")
			}
			for _, r := range args.RemoteTableMap {
				r.Table = strings.Trim(strings.TrimSpace(r.Table), "`")
				r.Database = strings.Trim(strings.TrimSpace(r.Database), "`")
			}
		}
	}
	reqJSON, _ := protojson.Marshal(req)
	log.Debugf("rewriting request: %s", string(reqJSON))

	resp, err := r.RewriterServiceClient.Rewrite(ctx, req)
	if err != nil {
		log.Errorf("failed to rewrite sql: %v", err)
	} else {
		log.Debugf("rewritten sql: %s->%s", req.Sql, resp.SqlAfterRewrite)
	}
	return resp, err
}
