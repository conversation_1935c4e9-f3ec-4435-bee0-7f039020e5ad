package results

import (
	"context"
	"math"
	"math/big"
	"reflect"
	"strings"
	"time"
	"unicode/utf8"

	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	protoscommon "sentioxyz/sentio/service/common/protos"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func clickhouseType2ColumnType(chType string) protoscommon.TabularData_ColumnType {
	chType = strings.ToLower(chType)
	if strings.HasPrefix(chType, "nullable(") {
		chType = strings.TrimPrefix(chType, "nullable(")
		chType = strings.TrimSuffix(chType, ")")
	}
	// refer doc: https://github.com/ClickHouse/clickhouse-go/blob/main/TYPES.md
	switch chType {
	case "string", "fixedstring", "uuid", "enum8", "enum16":
		return protoscommon.TabularData_STRING
	case "date", "date32", "datetime", "datetime64":
		return protoscommon.TabularData_TIME
	case "uint8", "uint16", "uint32", "uint64", "int8", "int16", "int32", "int64", "float32", "float64", "decimal":
		return protoscommon.TabularData_NUMBER
	case "bool":
		return protoscommon.TabularData_BOOLEAN
	case "json":
		return protoscommon.TabularData_JSON
	default:
		if strings.HasPrefix(chType, "array") {
			return protoscommon.TabularData_LIST
		}
		if strings.HasPrefix(chType, "tuple") {
			return protoscommon.TabularData_MAP
		}
		if strings.HasPrefix(chType, "map") {
			return protoscommon.TabularData_MAP
		}
		if strings.HasPrefix(chType, "decimal") {
			return protoscommon.TabularData_NUMBER
		}
		if strings.HasPrefix(chType, "date") {
			return protoscommon.TabularData_TIME
		}
		if strings.HasPrefix(chType, "uint") {
			return protoscommon.TabularData_NUMBER
		}
		if strings.HasPrefix(chType, "int") {
			return protoscommon.TabularData_NUMBER
		}
		if strings.HasPrefix(chType, "float") {
			return protoscommon.TabularData_NUMBER
		}
		if strings.HasPrefix(chType, "dynamic") {
			return protoscommon.TabularData_DYNAMIC
		}
		if strings.HasPrefix(chType, "fixedstring") {
			return protoscommon.TabularData_STRING
		}
		log.Infof("unknown clickhouse type: %s", chType)
		return protoscommon.TabularData_STRING
	}
}

func convertPointer(v any) any {
	r := reflect.TypeOf(v)
	switch r.Kind() {
	case reflect.Ptr:
		if reflect.ValueOf(v).IsNil() {
			return nil
		}
		return reflect.ValueOf(v).Elem().Interface()
	default:
		return v
	}
}

func convertTime(t time.Time, timezone *time.Location) string {
	return t.In(timezone).Format(time.RFC3339Nano)
}

func convertDecimal(d decimal.Decimal) string {
	return d.String()
}

func convertBigInt(i big.Int) string {
	return i.String()
}

func convertString(s string, reserved map[string]struct{}) (string, bool) {
	if utf8.ValidString(s) {
		if _, ok := reserved[s]; !ok {
			return s, true
		}
		return "", true
	} else {
		return "", false
	}
}

func convertUInt8(i uint8, databaseType string) (*uint8, *bool) {
	if databaseType == "Bool" {
		return nil, lo.ToPtr[bool](i == 1)
	} else {
		return lo.ToPtr[uint8](i), nil
	}
}

func convertFloat64(f float64) *float64 {
	if math.IsNaN(f) || math.IsInf(f, 0) {
		return nil
	}
	return lo.ToPtr(f)
}

func isArray(a any) bool {
	value := reflect.ValueOf(a)
	return value.Kind() == reflect.Slice
}

func isMap(a any) bool {
	value := reflect.ValueOf(a)
	return value.Kind() == reflect.Map
}

func isStruct(a any) bool {
	value := reflect.ValueOf(a)
	return value.Kind() == reflect.Struct
}

type convertOption struct {
	timezone     *time.Location
	reserved     map[string]struct{}
	databaseType string
}

func convertAny(a any, option *convertOption) any {
	if a == nil {
		return nil
	}
	a = convertPointer(a)
	switch aValue := a.(type) {
	case time.Time:
		return convertTime(aValue, option.timezone)
	case decimal.Decimal:
		return convertDecimal(aValue)
	case big.Int:
		return convertBigInt(aValue)
	case string:
		if s, ok := convertString(aValue, option.reserved); ok {
			return s
		} else {
			return []byte(aValue)
		}
	case uint8:
		if u, b := convertUInt8(aValue, option.databaseType); u != nil {
			return *u
		} else if b != nil {
			return *b
		}
	case float64:
		if f := convertFloat64(aValue); f != nil {
			return *f
		}
		return nil
	case float32:
		if f := convertFloat64(float64(aValue)); f != nil {
			return *f
		}
		return nil
	case nil:
		return nil
	case []byte:
		return aValue
	case bool:
		return a
	case int8, int16, int32, int64, uint16, uint32, uint64, int, uint, complex64, complex128:
		return a
	case clickhouse.Dynamic:
		return convertAny(aValue.Any(), option)
	case clickhouse.JSON:
		rValue := reflect.ValueOf(aValue)
		return convertMap(aValue, rValue, option)
	default:
		rValue := reflect.ValueOf(a)
		switch {
		case isArray(a):
			return convertArray(a, rValue, option)
		case isMap(a):
			return convertMap(a, rValue, option)
		case isStruct(a):
			return convertStruct(a, rValue, option)
		}
	}
	return "unsupported type, please contact the sentio admin"
}

func convertArray(a any, aValue reflect.Value, option *convertOption) []any {
	if !isArray(a) {
		return nil
	}
	result := make([]any, aValue.Len())
	for i := 0; i < aValue.Len(); i++ {
		data := aValue.Index(i).Interface()
		result[i] = convertAny(data, option)
	}
	return result
}

func convertMap(a any, aValue reflect.Value, option *convertOption) map[string]any {
	if !isMap(a) {
		return nil
	}
	result := map[string]any{}
	for _, key := range aValue.MapKeys() {
		data := aValue.MapIndex(key).Interface()
		result[key.String()] = convertAny(data, option)
	}
	return result
}

func convertStruct(a any, aValue reflect.Value, option *convertOption) map[string]any {
	if !isStruct(a) {
		return nil
	}
	result := map[string]any{}
	for i := 0; i < aValue.NumField(); i++ {
		field := aValue.Type().Field(i)
		data := aValue.Field(i).Interface()
		result[field.Name] = convertAny(data, option)
	}
	return result
}

func NewTabularFromClickhouse(
	ctx context.Context,
	rows [][]any,
	columns []event.ColumnType,
	cursor string,
	reservedKeyword map[string]struct{},
	tz *time.Location,
) *protoscommon.TabularData {
	_, logger := log.FromContext(ctx)
	tabular := &protoscommon.TabularData{
		ColumnTypes: map[string]protoscommon.TabularData_ColumnType{},
		GeneratedAt: timestamppb.New(time.Now()),
	}
	if cursor != "" {
		tabular.Cursor = cursor
	}
	for _, columnType := range columns {
		tabular.Columns = append(tabular.Columns, columnType.Name())
		tabular.ColumnTypes[columnType.Name()] = clickhouseType2ColumnType(columnType.DatabaseTypeName())
	}

	for _, row := range rows {
		data := map[string]any{}
		for idx, column := range row {
			data[columns[idx].Name()] = convertAny(column, &convertOption{
				timezone:     tz,
				reserved:     reservedKeyword,
				databaseType: columns[idx].DatabaseTypeName(),
			})
		}
		if len(data) == 0 {
			continue
		}
		if s, err := structpb.NewStruct(data); err == nil {
			tabular.Rows = append(tabular.Rows, s)
		} else {
			logger.Errorf("convert clickhouse row to struct failed: %v", err)
		}
	}
	return tabular
}
