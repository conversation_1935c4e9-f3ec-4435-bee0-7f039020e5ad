syntax = "proto3";

package processor_service;

option go_package = "sentioxyz/sentio/service/processor/protos";

import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "google/protobuf/timestamp.proto";
import "service/common/protos/common.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

service ProcessorService {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_tag) = {
    name: "Processor"
  };
  // Private API below:
  // Get processors belongs to the given project id.
  rpc GetProcessors (GetProcessorsRequest) returns (GetProcessorsResponse);

  // Get processor of the given processor id.
  rpc GetProcessor (GetProcessorRequest) returns (GetProcessorResponse);

  // Get processor of the given processor id, and the project which the processor belongs to
  rpc GetProcessorWithProject (GetProcessorRequest) returns (GetProcessorWithProjectResponse);

  rpc GetProjectVariables(GetProjectVariablesRequest) returns (common.ProjectVariables);

  // Updates a processor status of a given chain and processor.
  rpc UpdateChainProcessorStatus (UpdateChainProcessorStatusRequest) returns (UpdateChainProcessorStatusResponse);

  rpc RecordNotificationInternal (RecordNotificationRequest) returns (google.protobuf.Empty);

  rpc RemoveProcessorInternal (ProcessorIdRequest) returns (google.protobuf.Empty);

  rpc StartProcessorInternal (ProcessorIdRequest) returns (google.protobuf.Empty);

  rpc DownloadProcessor (DownloadProcessorRequest) returns (DownloadProcessorResponse);

  rpc SetProcessorEntitySchema (SetProcessorEntitySchemaRequest) returns (google.protobuf.Empty);

  rpc GetProcessorCode (ProcessorIdRequest) returns (GetProcessorCodeResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/processors/code"
    };
  };

  rpc GetProcessorHistoryCode (GetProcessorCodeRequest) returns (GetProcessorCodeResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/processors/code_history"
    };
  };

  rpc GetProjectChains(GetProjectChainsRequest) returns (GetProjectChainsResponse) {
    option (google.api.http) = {
      get: "/api/v1/processors/project_chains"
    };
  }

  // Get the source files of a processor
  rpc GetProcessorSourceFiles (GetProcessorSourceFilesRequest) returns (GetProcessorSourceFilesResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };

    option (google.api.http) = {
      get: "/api/v1/processors/{project_owner}/{project_slug}/source_files"
    };
  };

  rpc GetProcessorStatusInternal (GetProcessorStatusRequest) returns (GetProcessorStatusResponse);

  // Update the eventlog migrate status of a processor, used in migration
  rpc UpdateEventlogMigrateStatus (UpdateEventlogMigrateStatusRequest) returns (google.protobuf.Empty);

  // Restart the waiting driver jobs immediately
  rpc ResumeWaitingProcessorsInternal (ResumeWaitingProcessorsRequest) returns (google.protobuf.Empty);

  rpc GetProcessorStatus (GetProcessorStatusRequest) returns (GetProcessorStatusResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/processors/status"
    };
  };

  // Get processor status
  rpc GetProcessorStatusV2 (GetProcessorStatusRequestV2) returns (GetProcessorStatusResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    option (google.api.http) = {
      get: "/api/v1/processors/{project_owner}/{project_slug}/status"
    };
  };

  rpc GetProcessorUpgradeHistories (GetProcessorUpgradeHistoryRequest) returns (GetProcessorUpgradeHistoryResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/processors/{processor_id}/history"
    };
  }

  rpc RemoveProcessor (ProcessorIdRequest) returns (RemoveProcessorResponse) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/processors/{id}"
    };
  };

  rpc PauseProcessor (GetProcessorRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      put: "/api/v1/processors/{processor_id}/pause"
    };
  };

  rpc ResumeProcessor (GetProcessorRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      put: "/api/v1/processors/{processor_id}/resume"
    };
  };

  rpc SetVersionActive (GetProcessorRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      put: "/api/v1/processors/{processor_id}/active"
    };
  };

  // activate the pending version of a processor
  rpc ActivatePendingVersion (ActivatePendingRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    option (google.api.http) = {
      put: "/api/v1/processors/{project_owner}/{project_slug}/activate_pending"
    };
  };

  // create new version processor from specified version
  rpc RerunProcessorAsNewVersion(RerunProcessorRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      put: "/api/v1/processors/{processor_id}/rerun"
    };
  }

  rpc RestartProcessor (GetProcessorRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      put: "/api/v1/processors/{processor_id}/restart"
    };
  };

  // Get Versions
  rpc GetProjectVersions(GetProjectVersionsRequest) returns (GetProjectVersionsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (google.api.http) = {
      get: "/api/v1/processors/{project_id}/versions"
    };
  };

  rpc CheckKey (google.protobuf.Empty) returns (CheckKeyResponse) {
    option (common.auth) = {};
    option (google.api.method_visibility).restriction = "INTERNAL";
    option (google.api.http) = {
      get : "/api/v1/processors/check_key"
    };
  }

  rpc InitUpload (InitUploadRequest) returns (InitUploadResponse) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/processors/init_upload",
      body: "*"
    };
  }

  rpc FinishUpload (FinishUploadRequest) returns (FinishUploadResponse) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/processors/finish_upload",
      body: "*"
    };
  }

  rpc GetLogs (GetLogsRequest) returns (GetLogsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/processors/{processor_id}/logs",
      body: "*"
    };
  }

  rpc GetSystemStat (GetSystemStatRequest) returns (GetSystemStatResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/processors/{processor_id}/stats",
      body: "*"
    };
  }

  rpc GetChainBlockStat (GetChainBlockStatRequest) returns (GetChainBlockStatResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/processors/{processor_id}/chain_stats",
      body: "*"
    };
  }

  rpc GetProcessorMetrics (GetProcessorMetricsRequest) returns (GetProcessorMetricsResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/processors/{processor_id}/processor_metrics"
      body: "*"
    };
  }

  rpc GetProcessorProfile (GetProcessorProfileRequest) returns (GetProcessorProfileResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/processors/{processor_id}/profile",
      body: "*"
    };
  }
}

message InitUploadRequest {
  string project_slug = 1;
  string sdk_version = 2;
  int32 sequence = 3;
  string content_type = 4;
}

message InitUploadResponse {
  string url = 1;
  string warning = 2;
  int32 replacing_version = 3;
  bool multi_version = 4;
  string project_id = 5;
}

message NetworkOverride {
  string chain = 1;
  string host = 2;
}

message FinishUploadRequest {
  string project_slug = 1;
  string sdk_version = 2;
  string sha256 = 3;
  string commit_sha = 4;
  string git_url = 5;
  bool debug = 6;
  int32 sequence = 7;
  int32 continue_from = 8;
  string cli_version = 9;
  repeated NetworkOverride network_overrides = 10;
  repeated string warnings = 11;
  bool binary = 12; 
}

message FinishUploadResponse {
  string project_full_slug = 1;
  int32 version = 2;
  string processor_id = 3;
}

message DownloadProcessorRequest {
  string processor_id = 1;
}

message DownloadProcessorResponse {
  string url = 1;
}

message GetProcessorCodeRequest {
  string id = 1;
  string history_id = 2;
}

message GetProjectChainsRequest {
  repeated string project_id_list = 1;
  bool include_pending_version = 2;
}

message GetProjectChainsResponse {
  message ChainList {
    repeated string chains = 1;
  }
  map<string, ChainList> project_chains = 1;
}

message GetProcessorCodeResponse {
  string code_url = 1;
  string zip_url = 2;
}

message GetProjectVersionsRequest {
  string project_id = 1;
}

message SetProcessorEntitySchemaRequest {
  string processor_id = 1;
  string schema = 2;
}

message GetProjectVersionsResponse {
  repeated Version versions = 1;
  message Version {
    int32 version = 1;
    ProcessorVersionState state = 2;
    string processorId = 3;
  }
}

message CheckKeyResponse {
  string username = 1;
}

message GetProcessorsRequest {
  string project_id = 1;
}

message GetProcessorsResponse {
  repeated Processor processors = 1;
}

message GetProjectVariablesRequest {
  string project_id = 1;
}

message GetProcessorRequest {
  string processor_id = 1;
}

message RerunProcessorRequest {
  string processor_id = 1;
  string sdk_version = 2;
}

message GetProcessorResponse {
  Processor processor = 1;
}

message GetProcessorUpgradeHistoryRequest {
  string processor_id = 1;
}

message GetProcessorUpgradeHistoryResponse {
  repeated ProcessorUpgradeHistory histories = 1;
}

message GetProcessorWithProjectResponse {
  Processor processor = 1;
  common.Project project = 2;
}

message ResumeWaitingProcessorsRequest {
  string owner_id = 1;
  string owner_type = 2;
}

message UpdateChainProcessorStatusRequest {
  // This is the processor id.
  string id = 1;
  ChainState chain_state = 2;
}

message RecordNotificationRequest {
  string project_id = 1;
  string source = 2;
  string level = 3;
  string msg = 4;
  map<string,string> attributes = 5;
}

message UpdateChainProcessorStatusResponse {
}

message ChainState {
  // The chain id.
  string chain_id = 1;

  // The most recently processed block number and block hash.
  int64 processed_block_number = 2;
  int64 processed_timestamp_micros = 15;
  string processed_block_hash = 3;

  // Processed version
  int32 processed_version = 6;

  // Status of this chain indexer
  message Status {
    enum State {
      UNKNOWN = 0;
      ERROR = 1;
      CATCHING_UP = 2;
      PROCESSING_LATEST = 3;
      QUEUING = 4;
    }
    State state = 1;
    // This field is set if state is ERROR.
    common.ErrorRecord error_record = 3;
  }
  Status status = 7;

  google.protobuf.Timestamp updated_at = 8;

  // The serialized templates info.
  string templates = 9;

  // The serialized indexer state.
  string indexer_state = 13;

  // The serialized meter state.
  string meter_state = 14;

  // The serialized handler stat
  string handler_stat = 18;

  int64 initial_start_block_number = 16;
  int64 estimated_latest_block_number = 17;

  // To be deprecated after the migration.
  string trackers = 10;
  reserved 11, 12, 4, 5;
}

// This represents a processor which backend works on.
message Processor {
  // The unique processor id.
  string processor_id = 1;

  // The project this processor belongs to.
  string project_id = 2;

  // The version of the code_url below.
  int32 version = 3;

  // The SDK version used when uploaded
  string sdk_version = 5;

  // Call should be able to fetch the code to run from code_url.
  string code_url = 4;

  // Each maintains a state of a processor w.r.t a given chain.
  // This is a one-to-many mapping (processor to processor-chain)
  repeated ChainState chain_states = 6;

  // If non empty, this is the contract associated with the processor.
  string contract_id = 7;

  ProcessorVersionState version_state = 8;

  // If enabled, driver job's log level will be set to debug
  bool debug = 9;

  // The timescale-db sharding index of this processor.
  int32 timescale_sharding_index = 10;

  // The version label of the subgraph
  // used for subgraph project
  string version_label = 11;

  // The hash (object CID) of the subgraph description file in ipfs node
  // used for subgraph project
  string ipfs_hash = 12;

  // ID of a remote subgraph whose store will be GraphQL queried
  // used for subgraph project
  string debug_fork = 13;

  // The created timestamp of the processor.
  int64 created_at = 14;

  // The clickhouse sharding index of this processor.
  int32 clickhouse_sharding_index = 15;

  // The k8s cluster id of this processor
  int32 k8s_cluster_id = 19;

  bool enable_materialized_view = 16 [deprecated = true];

  string reference_project_id = 17;

  repeated NetworkOverride network_overrides = 18;

  int32 eventlog_migrate_status = 20;

  int32 eventlog_version = 21;

  bool pause = 22;

  int32 entity_schema_version = 23;

  bool is_binary = 24;
}

message ProcessorUpgradeHistory {
  int32 index = 2;
  google.protobuf.Timestamp uploaded_at = 3;
  google.protobuf.Timestamp obsolete_at = 4;
  Processor snapshot = 1;
  string id = 5;
}

message GetProcessorStatusRequest {
  oneof get_metrics_by {
    string project_id = 1;
    string id = 2;
  }
}

message GetProcessorStatusRequestV2 {
  enum VersionSelector {
    // Only active version
    ACTIVE = 0;
    // Only pending versions
    PENDING = 1;
    // All version
    ALL = 2;

  }
  string project_owner = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  string project_slug = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];

  VersionSelector version = 3;
}

enum ProcessorVersionState {
  UNKNOWN = 0;
  PENDING = 1;
  ACTIVE = 2;
  OBSOLETE = 3;
}

message GetProcessorStatusResponse {
  repeated ProcessorEx processors = 1;
  message ProcessorEx {
    repeated ChainState states = 2;
    string processor_id = 3;
    string code_hash = 4;
    string commit_sha = 5;
    common.UserInfo uploaded_by = 6;
    google.protobuf.Timestamp uploaded_at = 7;
    // The processor status.
    ProcessorStatus processor_status = 8;
    int32 version = 9;
    string sdk_version = 10;
    string git_url = 11;
    ProcessorVersionState version_state = 12;
    string version_label = 13;
    string ipfs_hash = 14;
    string debug_fork = 15;
    string cli_version = 16;
    string reference_project_id = 17;
    repeated string warnings = 18;
    bool pause = 19;
    repeated NetworkOverride network_overrides = 20;
  }

  message ProcessorStatus {
    enum State {
      UNKNOWN = 0;
      ERROR = 1;
      STARTING = 3;
      PROCESSING = 2;
    }
    State state = 1;
    // This field is set if state is ERROR.
    common.ErrorRecord error_record = 3;
  }
}

message ProcessorIdRequest {
  string id = 1;
}

message RemoveProcessorResponse {
  Processor deleted = 1;
}

//message ProcessorStatus {
//  enum Status {
//    UNKNOWN = 0;
//    ERROR = 1;
//    CATCHING_UP = 2;
//    PROCESSING_LATEST = 3;
//  }
//  string id = 1;
//  Status status = 3;
//  int64 updated_at = 4;
//  string latest_block = 5;
//  string version = 6;
//}

message GetLogsRequest {
  string processor_id = 1;
  int32 limit = 2;
  repeated common.Any after = 3;
  repeated string log_type_filters = 4;
  string query = 5;
}

message GetLogsResponse {
  repeated Log logs = 1;
  message Log {
    string id = 1;
    string message = 2;
    google.protobuf.Timestamp timestamp = 3;
    google.protobuf.Struct attributes = 4;
    string log_type = 5;
    string level = 6;
    string highlighted_message = 7;
    string chain_id = 8;
  }
  repeated common.Any after = 2;
  int64 total = 3;
}

enum SystemStatType {
  CPU = 0;
  MEM = 1;
}

message GetSystemStatRequest {
  string processor_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  SystemStatType type = 4;
}

message GetSystemStatResponse {
  message MetricData {
    map<string, string> metric = 1;
    repeated DataPoint values = 2;
  }

  message DataPoint {
    string timestamp = 1;
    string value = 2;
  }

  repeated MetricData results = 1;
}

message GetChainBlockStatRequest {
  string processor_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
}

message GetChainBlockStatResponse {
  repeated GetSystemStatResponse.MetricData results = 1;
  repeated GetSystemStatResponse.MetricData headResults = 2;
}

message GetProcessorMetricsRequest {
  string processor_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  string interval = 4;
}

message GetProcessorMetricsResponse {
  repeated GetSystemStatResponse.MetricData driver_reorg = 1; // driver reorg count
  repeated GetSystemStatResponse.MetricData template_instance = 2; // template instances count
  repeated GetSystemStatResponse.MetricData driver_data = 3; // driver data emitted
  repeated GetSystemStatResponse.MetricData avg_handler_duration = 4; // average handler duration
  repeated GetSystemStatResponse.MetricData total_handler_duration = 5; // total handler duration
  repeated GetSystemStatResponse.MetricData avg_rpc_duration = 6; // rpc duration
  repeated GetSystemStatResponse.MetricData total_rpc_duration = 7; // total rpc duration
}

message GetProcessorProfileRequest {
  string processor_id = 1;
  int32 time = 2;
  bool heap = 3;
}

message GetProcessorProfileResponse {
  string profile_url = 1;
  google.protobuf.Struct profile_data = 2;
}

message UpdateEventlogMigrateStatusRequest {
  string processor_id = 1;
  int32 eventlog_migrate_status = 2;
  int32 eventlog_new_shard_index = 3;
}

message GetProcessorSourceFilesRequest {
  string project_owner = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  string project_slug = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
}

message GetProcessorSourceFilesResponse {
  repeated ProcessorSourceFile source_files = 1; // list of source files
}

message ProcessorSourceFile {
  string path = 1; // relative path of the source file
  string content = 2; // content of the source file
}

message ActivatePendingRequest {
  string project_owner = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  string project_slug = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
}
