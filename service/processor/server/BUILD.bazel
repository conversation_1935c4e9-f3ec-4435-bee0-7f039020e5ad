load("@rules_go//go:def.bzl", "go_binary", "go_library")
load("//bazel:images.bzl", "create_go_image")

go_library(
    name = "server_lib",
    srcs = ["main.go"],
    data = [
        "//common/clickhouse:test_config",
        "//common/timescale:test_config",
    ],
    importpath = "sentioxyz/sentio/service/processor/server",
    visibility = ["//visibility:public"],
    deps = [
        "//common/clickhouse",
        "//common/concurrency",
        "//common/event",
        "//common/flags",
        "//common/jsonrpc",
        "//common/log",
        "//common/monitoring",
        "//common/utils",
        "//driver/entity/cleaner",
        "//k8s/client",
        "//k8s/controllers",
        "//service/common/auth",
        "//service/common/preloader",
        "//service/common/rpc",
        "//service/common/storagesystem",
        "//service/processor",
        "//service/processor/protos",
        "//service/processor/repository",
        "@io_k8s_sigs_controller_runtime//pkg/controller",
        "@org_golang_google_grpc//:grpc",
    ],
)

go_binary(
    name = "server",
    embed = [":server_lib"],
    tags = [],
    visibility = ["//visibility:public"],
)

create_go_image(
    binary = ":server",
)
