package processor

import (
	"context"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"gorm.io/gorm"
	"sentioxyz/sentio/service/common/preloader"
	"sentioxyz/sentio/service/common/repository"
	"sentioxyz/sentio/service/processor/models"
	"sentioxyz/sentio/service/processor/protos"
	"strings"
)

func ProcessorPreloader(ctx context.Context, method protoreflect.MethodDescriptor, request proto.Message, db *gorm.DB) (context.Context, error) {
	var projectID string
	var processorID string
	var owner, slug string
	switch r := request.(type) {
	case *protos.ProcessorIdRequest:
		processorID = r.Id
	case *protos.GetProcessorRequest:
		processorID = r.ProcessorId
	case *protos.RerunProcessorRequest:
		processorID = r.ProcessorId
	case *protos.GetSystemStatRequest:
		processorID = r.ProcessorId
	case *protos.GetChainBlockStatRequest:
		processorID = r.ProcessorId
	case *protos.GetProcessorMetricsRequest:
		processorID = r.ProcessorId
	case *protos.GetProcessorProfileRequest:
		processorID = r.ProcessorId
	case *protos.GetProcessorUpgradeHistoryRequest:
		processorID = r.ProcessorId
	case *protos.GetProcessorStatusRequest:
		processorID = r.GetId()
		projectID = r.GetProjectId()
	case *protos.GetProcessorStatusRequestV2:
		owner = r.ProjectOwner
		slug = r.ProjectSlug
	case *protos.GetProcessorCodeRequest:
		processorID = r.GetId()
	case *protos.DownloadProcessorRequest:
		processorID = r.ProcessorId
	case *protos.InitUploadRequest:
		var ok bool
		owner, slug, ok = strings.Cut(r.ProjectSlug, "/")
		if !ok {
			owner = ""
			slug = r.ProjectSlug
		}
	case *protos.FinishUploadRequest:
		var ok bool
		owner, slug, ok = strings.Cut(r.ProjectSlug, "/")
		if !ok {
			owner = ""
			slug = r.ProjectSlug
		}
	case *protos.GetLogsRequest:
		processorID = r.ProcessorId
	case *protos.GetProcessorSourceFilesRequest:
		owner = r.ProjectOwner
		slug = r.ProjectSlug
	}
	repo := repository.NewRepository(db)

	if processorID != "" {
		if processor, err := repo.GetProcessor(ctx, processorID); err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return ctx, status.Errorf(codes.NotFound, "Processor %s not found", processorID)
			}
			return ctx, err
		} else {
			ctx = context.WithValue(ctx, "processor", processor)
			projectID = processor.ProjectID
		}
	}

	if projectID != "" {
		project, err := repo.GetProjectByID(db, projectID)
		if err != nil {
			return ctx, err
		}
		ctx = context.WithValue(ctx, preloader.ProjectKeyName, project)
	}
	if slug != "" && owner == "" {
		identity := preloader.PreLoadedIdentity(ctx)
		if identity != nil {
			if identity.APIKey != nil {
				identity.APIKey.GetOwner(db)
				owner = identity.APIKey.GetOwnerName()
			} else if identity.User != nil {
				owner = identity.User.Username
			}
		}
	}
	if owner != "" && slug != "" {
		project, err := repo.GetProjectBySlug(ctx, owner, slug)
		if err != nil {
			return ctx, err
		}
		ctx = context.WithValue(ctx, preloader.ProjectKeyName, project)
	}

	return ctx, nil
}

func PreloadedProcessor(ctx context.Context) *models.Processor {
	if processor, ok := ctx.Value("processor").(*models.Processor); ok {
		return processor
	}
	return nil
}
