package processor

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"sentioxyz/sentio/service/common/auth"
	"strconv"
	"strings"

	"sentioxyz/sentio/common/jsonrpc"
	"sentioxyz/sentio/common/log"
	commonModels "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/processor/models"

	"google.golang.org/grpc/metadata"
)

// GraphNodeService adapt for graph-cli (https://github.com/graphprotocol/graph-tooling/tree/main/packages/cli)
type GraphNodeService struct {
	svc         *Service
	authManager auth.AuthManager
}

func NewGraphNodeService(svc *Service, authManager auth.AuthManager) *GraphNodeService {
	return &GraphNodeService{svc: svc, authManager: authManager}
}

type DeployArgs struct {
	Name         string `json:"name"`
	IpfsHash     string `json:"ipfs_hash"`
	VersionLabel string `json:"version_label"`
	DebugFork    string `json:"debug_fork"`
}

type DeployResp struct {
	Playground    string `json:"playground"`
	Queries       string `json:"queries"`
	Subscriptions string `json:"subscriptions"`
}

func newIncomingContext(ctx context.Context, req *http.Request) context.Context {
	md := metadata.MD{}

	authorization := req.Header.Get("Authorization")
	if authorization != "" {
		if strings.HasPrefix(authorization, "Bearer ") {
			// It is not a bug to use the value as the api-key here, it is for adapting to graph-cli.
			// In fact, this should be the deploy-key of subgraph for graph-cli.
			md.Append("api-key", strings.TrimPrefix(authorization, "Bearer "))
		}

		if strings.HasPrefix(authorization, "Basic ") {
			md.Append("api-key", strings.TrimPrefix(authorization, "Basic "))
		}
	}

	apiKey := req.Header.Get("Api-Key")
	if apiKey != "" {
		md.Append("api-key", apiKey)
	}

	return metadata.NewIncomingContext(ctx, md)
}

func (s *GraphNodeService) Deploy(ctx context.Context, args *DeployArgs) (DeployResp, error) {
	// Authentication and permission check
	ctx = newIncomingContext(ctx, jsonrpc.GetCtxData(ctx).RawReq)
	_, logger := log.FromContext(ctx)
	projectOwner, projectSlug, _ := strings.Cut(args.Name, "/")
	identity, project, err := s.authManager.RequiredLoginForProjectOwnerAndSlug(
		ctx, projectOwner, projectSlug, auth.WRITE)
	if err != nil {
		return DeployResp{}, err
	}

	// project type should be subgraph
	if project.Type != commonModels.ProjectTypeSubgraph {
		return DeployResp{}, errors.New("project type is not subgraph")
	}

	var continueFrom int64
	if strings.HasPrefix(args.VersionLabel, "continue-from:") {
		continueFrom, err = strconv.ParseInt(strings.TrimPrefix(args.VersionLabel, "continue-from:"), 10, 32)
		if err != nil {
			return DeployResp{}, fmt.Errorf("invalid version-label %q: continue-from is not an integer: %w",
				args.VersionLabel, err)
		}
		logger.Infof("uploaded subgraph processor for %s/%s with continue from %d", project.OwnerName, project.Slug, continueFrom)
	}

	// create or update processor
	processor, err := s.svc.createOrUpdateProcessor(
		ctx,
		identity,
		project,
		int32(continueFrom),
		models.SentioProcessorProperties{},
		models.SubgraphProcessorProperties{
			VersionLabel:       args.VersionLabel,
			IpfsHash:           args.IpfsHash,
			DebugFork:          args.DebugFork,
			GraphQLQueryEngine: "v2", // new subgraph processor uses v2 query engine, default is v1
		},
	)
	if err != nil {
		return DeployResp{}, err
	}
	// TODO The returned url is only temporary
	return DeployResp{
		Playground:    "https://playground.sentio.xyz/" + args.Name + "/" + processor.ID,
		Queries:       "https://queries.sentio.xyz/" + args.Name + "/" + processor.ID,
		Subscriptions: "https://subscriptions.sentio.xyz/" + args.Name + "/" + processor.ID,
	}, nil
}
