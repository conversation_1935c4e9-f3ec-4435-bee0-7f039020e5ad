load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "processor",
    srcs = [
        "file_service.go",
        "graph_node_service.go",
        "job_service.go",
        "log_service.go",
        "processor_preloader.go",
        "processor_service.go",
    ],
    importpath = "sentioxyz/sentio/service/processor",
    visibility = ["//visibility:public"],
    deps = [
        "//common/event",
        "//common/gonanoid",
        "//common/jsonrpc",
        "//common/log",
        "//common/notification",
        "//common/timescale",
        "//common/utils",
        "//driver/entity/cleaner",
        "//k8s/api/v1:api",
        "//k8s/client",
        "//k8s/controllers",
        "//processor",
        "//service/analytic/sqllib/mapper",
        "//service/common/auth",
        "//service/common/errors:error_record",
        "//service/common/models",
        "//service/common/preloader",
        "//service/common/protos",
        "//service/common/redis",
        "//service/common/repository",
        "//service/common/rpc",
        "//service/common/storagesystem",
        "//service/processor/models",
        "//service/processor/protos",
        "//service/processor/repository",
        "//service/webhook/protos",
        "@com_github_cenkalti_backoff_v4//:backoff",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_huandu_go_sqlbuilder//:go-sqlbuilder",
        "@com_github_pkg_errors//:errors",
        "@com_github_prometheus_client_golang//api",
        "@com_github_prometheus_client_golang//api/prometheus/v1:prometheus",
        "@com_github_prometheus_common//model",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_samber_lo//:lo",
        "@com_google_cloud_go_storage//:storage",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@io_k8s_apimachinery//pkg/api/errors",
        "@io_k8s_apimachinery//pkg/apis/meta/v1:meta",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//metadata",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//reflect/protoreflect",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)

go_test(
    name = "processor_test",
    srcs = ["processor_service_test.go"],
    embed = [":processor"],
    deps = ["@com_github_stretchr_testify//assert"],
)
