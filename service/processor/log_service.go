package processor

import (
	"context"
	"crypto/md5"
	"fmt"
	"github.com/huandu/go-sqlbuilder"
	"regexp"
	"sentioxyz/sentio/common/log"
	"time"

	protoscommon "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/processor/protos"

	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *Service) GetLogs(ctx context.Context, req *protos.GetLogsRequest) (*protos.GetLogsResponse, error) {
	q := sqlbuilder.Select(
		"Timestamp",
		"Body",
		"SeverityText as level",
		"ServiceName",
		"ChainID",
	).From("processor_logs_mv")

	if len(req.After) > 0 {
		after := req.After[0].GetDateValue()
		q.Where(q.LT("Timestamp", after.AsTime().Format("2006-01-02 15:04:05")))
	}
	if len(req.Query) > 0 {
		q.Where(q.Like("Body", "%"+req.Query+"%"))
	}

	id := req.ProcessorId
	processor := PreloadedProcessor(ctx)

	if len(processor.ReferenceProjectID) > 0 {
		processor, err := s.repository.ResolveReferenceProcessor(ctx, processor)
		if err != nil {
			return nil, err
		}
		id = processor.ID
	}

	q.Where(q.Equal("ProcessorID", id))
	q.OrderBy("Timestamp DESC")
	if req.Limit > 0 {
		q.Limit(int(req.Limit))
	}

	sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
	if err != nil {
		return nil, err
	}

	rows, err := s.logCh.Query(ctx, sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	response := protos.GetLogsResponse{
		Logs: make([]*protos.GetLogsResponse_Log, 0),
	}

	for rows.Next() {
		l := &protos.GetLogsResponse_Log{}
		var ts time.Time

		err = rows.Scan(
			&ts,
			&l.Message,
			&l.Level,
			&l.LogType,
			&l.ChainId,
		)
		l.HighlightedMessage = highlight(l.Message, req.Query)

		if err != nil {
			return nil, err
		}
		l.Timestamp = timestamppb.New(ts)
		l.Id = genId(l.Timestamp, l.Message)
		response.Logs = append(response.Logs, l)
		if len(response.Logs) >= int(req.Limit) {
			response.After = []*protoscommon.Any{
				{
					AnyValue: &protoscommon.Any_DateValue{
						DateValue: timestamppb.New(ts),
					},
				},
			}
			break
		}
	}

	return &response, nil
}

func genId(timestamp *timestamppb.Timestamp, message string) string {
	hash := md5.New()
	hash.Write([]byte(timestamp.String() + message))
	return fmt.Sprintf("%x", hash.Sum(nil))
}

func highlight(message string, query string) string {
	if query == "" {
		return message
	}
	escapedQuery := regexp.MustCompile(`(\W)`).ReplaceAllString(query, `\\$1`)
	re, err := regexp.Compile(`(?i)(` + escapedQuery + ")")
	if err != nil {
		log.Errorfe(err, "failed to highlight, regex compile failed, escaped query: %s", escapedQuery)
		return message
	}
	return re.ReplaceAllString(message, "<em class='log-highlight'>$1</em>")
}
