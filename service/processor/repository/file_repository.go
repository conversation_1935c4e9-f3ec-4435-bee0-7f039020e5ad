package repository

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"strconv"
	"time"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/utils"
	common "sentioxyz/sentio/service/common/models"
	commRepo "sentioxyz/sentio/service/common/repository"
	"sentioxyz/sentio/service/processor/models"
	"sentioxyz/sentio/service/processor/protos"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

var (
	DefaultProcessorVersion = flag.Int("default-processor-version", 1, "The default processor version")
)

type Repository struct {
	commRepo.Repository
	// db *gorm.DB
}

func SetupDB(dbURL string) (*gorm.DB, error) {
	return commRepo.SetupDB(dbURL,
		&models.Processor{},
		&models.ChainState{},
		&models.ProcessorUpgradeHistory{},
	)
}

func NewRepository(db *gorm.DB) *Repository {
	return &Repository{commRepo.NewRepository(db)}
}

const (
	NewSentioProcessorEntitySchemaVersion   = 0
	NewSubgraphProcessorEntitySchemaVersion = 1
)

func (r *Repository) CreateOrUpdateProcessor(
	ctx context.Context,
	tx *gorm.DB,
	project *common.Project,
	continueFrom int32,
	identity *common.Identity,
	clickhouseShardingIndex int32,
	k8sClusterID int32,
	sentioProperties models.SentioProcessorProperties,
	subgraphProperties models.SubgraphProcessorProperties,
) (processor *models.Processor, err error) {
	if continueFrom > 0 {
		processor, err = FindProcessorByVersion(tx, project.ID, continueFrom)
		if err != nil {
			return nil, fmt.Errorf("find processor with version %d failed: %w", continueFrom, err)
		}
		switch vs := protos.ProcessorVersionState(processor.VersionState); vs {
		case protos.ProcessorVersionState_ACTIVE, protos.ProcessorVersionState_PENDING:
		default:
			return nil, fmt.Errorf("status of processor with version %d is %v, should be %v or %v",
				continueFrom, vs, protos.ProcessorVersionState_ACTIVE, protos.ProcessorVersionState_PENDING)
		}
		if err = saveProcessorUpgradeHistory(tx, processor); err != nil {
			return nil, fmt.Errorf("save processor upgrade history failed: %w", err)
		}
		processor.UserID = &identity.UserID
		processor.UploadedAt = time.Now()
	} else {
		var entitySchemaVersion int32
		if project.Type == common.ProjectTypeSubgraph {
			entitySchemaVersion = NewSubgraphProcessorEntitySchemaVersion
		} else {
			entitySchemaVersion = NewSentioProcessorEntitySchemaVersion
		}
		vars, err := r.GetProjectVariables(ctx, project.ID)
		if err != nil {
			return nil, fmt.Errorf("get project variables failed: %w", err)
		}
		for _, v := range vars {
			if v.Key == "SENTIO_ENTITY_SCHEMA_VERSION" {
				intv, _ := strconv.ParseInt(v.Value, 10, 64)
				entitySchemaVersion = int32(intv)
				log.Infof("use entity schema version %d for new processor of project %s",
					entitySchemaVersion, project.FullName())
				break
			}
		}
		processor = &models.Processor{
			ProjectID:           project.ID,
			UserID:              &identity.UserID,
			UploadedAt:          time.Now(),
			EntitySchemaVersion: entitySchemaVersion,
		}
		lastProcessor, _ := FindLatestProcessor(tx, project.ID)
		if lastProcessor == nil {
			processor.Version = 1
		} else {
			processor.Version = lastProcessor.Version + 1
		}

		if project.MultiVersion {
			processor.VersionState = int32(protos.ProcessorVersionState_PENDING)
		} else {
			processor.VersionState = int32(protos.ProcessorVersionState_ACTIVE)
		}
	}

	var processorEventlogVersion int32
	if continueFrom > 0 {
		// Must inherit the original clickhouseShardingIndex, TimescaleShardingIndex, k8sClusterID, and VersionLabel
		clickhouseShardingIndex = processor.ClickhouseShardingIndex
		k8sClusterID = processor.K8sClusterID
		sentioProperties.TimescaleShardingIndex = processor.TimescaleShardingIndex
		subgraphProperties.VersionLabel = processor.VersionLabel
		processorEventlogVersion = processor.EventlogVersion
	} else {
		processorEventlogVersion = int32(*DefaultProcessorVersion)
	}
	processor.ClickhouseShardingIndex = clickhouseShardingIndex
	processor.K8sClusterID = k8sClusterID
	processor.SentioProcessorProperties = sentioProperties
	processor.SubgraphProcessorProperties = subgraphProperties
	processor.EventlogVersion = processorEventlogVersion
	if err = tx.Save(processor).Error; err != nil {
		return nil, err
	}

	return processor, err
}

func (r *Repository) FindReplacingProcessor(db *gorm.DB, project *common.Project) (*models.Processor, error) {
	processor, _ := FindLatestProcessor(db, project.ID)
	if processor != nil {
		if (project.MultiVersion && processor.VersionState == int32(protos.ProcessorVersionState_PENDING)) || (!project.MultiVersion && processor.VersionState == int32(protos.ProcessorVersionState_ACTIVE)) {
			return processor, nil
		}
	}

	return nil, nil
}

func (r *Repository) ListProcessorUpgradeHistory(
	ctx context.Context,
	processorID string,
) (histories []models.ProcessorUpgradeHistory, err error) {
	result := r.DB.WithContext(ctx).
		Order("obsolete_at asc").
		Where(&models.ProcessorUpgradeHistory{ProcessorID: processorID}).
		Find(&histories)
	return histories, result.Error
}

func (r *Repository) ListChainsByProjects(
	ctx context.Context,
	projectIDList []string,
	versionStatList []protos.ProcessorVersionState,
) (map[string][]string, error) {
	type ProjectChain struct {
		ProjectID string `gorm:"column:project_id"`
		ChainID   string `gorm:"column:chain_id"`
	}
	var pairs []ProjectChain
	err := r.DB.WithContext(ctx).
		Model(&models.ChainState{}).
		Select("processors.project_id AS project_id", "chain_id").
		Joins("LEFT JOIN processors ON processor_id = processors.id").
		Where("processors.version_state IN ? AND processors.project_id IN ? AND chain_id != ?",
			versionStatList, projectIDList, "meta").
		Scan(&pairs).Error
	result := make(map[string][]string)
	for _, pair := range pairs {
		result[pair.ProjectID] = append(result[pair.ProjectID], pair.ChainID)
	}
	for pid := range result {
		result[pid] = utils.GetOrderedMapKeys(utils.BuildSet(result[pid]))
	}
	return result, err
}

func saveProcessorUpgradeHistory(db *gorm.DB, processor *models.Processor) error {
	cs, err := json.Marshal(utils.MapSliceNoError(processor.ChainStates, func(c *models.ChainState) *models.ChainState {
		// remove useless fields
		x := *c
		x.Trackers = nil
		x.MeterState = nil
		x.IndexerState = nil
		x.Templates = ""
		return &x
	}))
	if err != nil {
		return fmt.Errorf("marshal chain states failed: %w", err)
	}
	history := models.ProcessorUpgradeHistory{
		ProcessorID:                 processor.ID,
		UserID:                      processor.UserID,
		UploadedAt:                  processor.UploadedAt,
		ObsoleteAt:                  time.Now(),
		SentioProcessorProperties:   processor.SentioProcessorProperties,
		SubgraphProcessorProperties: processor.SubgraphProcessorProperties,
		ChainStatesJSON:             cs,
	}
	return db.Save(&history).Error
}

func FindActiveProcessor(db *gorm.DB, projectID string) (*models.Processor, error) {
	processor := models.Processor{}
	result := db.Where(&models.Processor{ProjectID: projectID, VersionState: int32(protos.ProcessorVersionState_ACTIVE)}).
		First(&processor)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return FindLatestProcessor(db, projectID)
	}

	return &processor, result.Error
}

func FindLatestProcessor(db *gorm.DB, projectID string) (*models.Processor, error) {
	processor := models.Processor{}
	result := db.Order("version desc").Where(&models.Processor{ProjectID: projectID}).First(&processor)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &processor, result.Error
}

func FindProcessorByVersion(db *gorm.DB, projectID string, version int32) (*models.Processor, error) {
	processor := models.Processor{}
	result := db.Preload("ChainStates").
		Where(&models.Processor{ProjectID: projectID, Version: version}).
		First(&processor)
	return &processor, result.Error
}
