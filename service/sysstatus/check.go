package sysstatus

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	clickhousev2 "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ethereum/go-ethereum/rpc"

	"sentioxyz/sentio/chain/aptos"
	"sentioxyz/sentio/chain/btc"
	"sentioxyz/sentio/chain/clickhouse"
	"sentioxyz/sentio/chain/evm"
	"sentioxyz/sentio/chain/fuel"
	"sentioxyz/sentio/chain/sol"
	"sentioxyz/sentio/chain/sui"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/sysstatus/protos"
)

type EndpointType string

const (
	EndpointTypeNode       EndpointType = "node"
	EndpointTypeJSONRPC    EndpointType = "jsonrpc"
	EndpointTypePersistent EndpointType = "persistent"
)

func inferEndpointType(pool *protos.ChainStatus_Pool) EndpointType {
	switch {
	case utils.IndexOf(pool.Categories, protos.ChainStatus_Pool_JSONRPC) >= 0:
		return EndpointTypeJSONRPC
	case utils.IndexOf(pool.Categories, protos.ChainStatus_Pool_SUPER_NODE) >= 0:
		return EndpointTypeJSONRPC
	case utils.IndexOf(pool.Categories, protos.ChainStatus_Pool_PERSISTENT) >= 0:
		return EndpointTypePersistent
	default:
		return EndpointTypeNode
	}
}

func (s *ChainStatusService) getLatestSlotNumber(
	ctx context.Context,
	chainType protos.ChainType,
	network string,
	chainID string,
	epType EndpointType,
	endpoint string,
) (uint64, error) {
	if endpoint == "" {
		return 0, fmt.Errorf("empty endpoint")
	}
	if epType == EndpointTypePersistent {
		if strings.HasPrefix(endpoint, "gcs://") {
			return 0, fmt.Errorf("unsupported to query range from gcs")
		}
		if strings.HasPrefix(endpoint, "clickhouse://") {
			dsn, rangeTable, _ := strings.Cut(endpoint, "###")
			option, err := clickhousev2.ParseDSN(dsn)
			if err != nil {
				return 0, err
			}
			conn, err := clickhousev2.Open(option)
			if err != nil {
				return 0, err
			}
			defer func() {
				_ = conn.Close()
			}()
			cur, err := clickhouse.NewReadOnlyRangeStore(conn, option.Auth.Database, rangeTable).Get(ctx)
			if err != nil {
				return 0, err
			}
			return uint64(cur.R()), nil
		}
		return 0, fmt.Errorf("invalid persistent endpoint %q", endpoint)
	}
	switch chainType {
	case protos.ChainType_EVM:
		switch epType {
		case EndpointTypeNode, EndpointTypeJSONRPC:
			cli, err := evm.NewAdapterBuilder(chainID)(endpoint, "")
			if err != nil {
				return 0, err
			}
			return cli.GetLatest(ctx)
		default:
			return 0, fmt.Errorf("unknown endpoint type %v", epType)
		}
	case protos.ChainType_SUI:
		switch epType {
		case EndpointTypeNode, EndpointTypeJSONRPC:
			cli, err := sui.NewAdapterBuilder(chainID)(endpoint, "")
			if err != nil {
				return 0, err
			}
			return cli.GetLatest(ctx)
		default:
			return 0, fmt.Errorf("unknown endpoint type %v", epType)
		}
	case protos.ChainType_APTOS2:
		switch epType {
		case EndpointTypeNode:
			cli, err := aptos.AdapterBuilder(endpoint, "")
			if err != nil {
				return 0, err
			}
			return cli.GetLatest(ctx)
		case EndpointTypeJSONRPC:
			var latestHeight uint64
			cli, err := rpc.Dial(endpoint)
			if err != nil {
				return 0, fmt.Errorf("new client failed: %w", err)
			}
			err = cli.CallContext(ctx, &latestHeight, "aptos_latestHeight")
			if err != nil {
				return 0, fmt.Errorf("call aptos_latestHeight failed: %w", err)
			}
			return latestHeight, nil
		default:
			return 0, fmt.Errorf("unknown endpoint type %v", epType)
		}
	case protos.ChainType_FUEL:
		switch epType {
		case EndpointTypeNode:
			cli, err := fuel.AdapterBuilder(endpoint, "")
			if err != nil {
				return 0, err
			}
			return cli.GetLatest(ctx)
		case EndpointTypeJSONRPC:
			cli, err := rpc.Dial(endpoint)
			if err != nil {
				return 0, fmt.Errorf("new client failed: %w", err)
			}
			var sn uint64
			err = cli.CallContext(ctx, &sn, "fuel_getLatestHeight")
			return sn, err
		default:
			return 0, fmt.Errorf("unknown endpoint type %v", epType)
		}
	case protos.ChainType_BTC:
		switch epType {
		case EndpointTypeNode, EndpointTypeJSONRPC:
			return btc.GetLatestSlotNumber(ctx, btc.ClientBuilder(endpoint))
		default:
			return 0, fmt.Errorf("unknown endpoint type %v", epType)
		}
	case protos.ChainType_SOL:
		switch epType {
		case EndpointTypeNode, EndpointTypeJSONRPC:
			cli, err := sol.AdapterBuilder(endpoint, "")
			if err != nil {
				return 0, err
			}
			return cli.GetLatest(ctx)
		default:
			return 0, fmt.Errorf("unknown endpoint type %v", epType)
		}
	default:
		return 0, fmt.Errorf("unknown chain type %v", chainType)
	}
}

const checkTimeout = time.Second * 3

func (s *ChainStatusService) refreshChainStatus(ctx context.Context, wg *sync.WaitGroup, cs *protos.ChainStatus) {
	for _, pool := range cs.Pools {
		endpointType := inferEndpointType(pool)
		for _, endpoint := range pool.Endpoints {
			wg.Add(1)
			go func(endpointType EndpointType, endpoint *protos.ChainStatus_Pool_EndpointStatus) {
				defer wg.Done()
				start := time.Now()
				_, logger := log.FromContext(ctx)
				sn, err := s.getLatestSlotNumber(ctx, cs.Type, cs.Network, cs.ChainId, endpointType, endpoint.Endpoint)
				logger.LogTimeUsed(start, time.Second, "refreshed endpoint status",
					"chainID", cs.ChainId,
					"network", cs.Network,
					"endpoint", endpoint.Endpoint,
					"sn", sn,
					"err", err)
				endpoint.LatestSlotNumber = sn
				if err != nil {
					endpoint.Error = err.Error()
				} else {
					endpoint.Error = ""
				}
			}(endpointType, endpoint)
		}
	}
}

func checkFallBehind(cs *protos.ChainStatus, threshold uint64) {
	var maxSlotNumber uint64
	for _, p := range cs.Pools {
		for _, ep := range p.Endpoints {
			if ep.LatestSlotNumber > maxSlotNumber {
				maxSlotNumber = ep.LatestSlotNumber
			}
		}
	}
	for _, p := range cs.Pools {
		for _, ep := range p.Endpoints {
			ep.FallBehind = ep.LatestSlotNumber+threshold < maxSlotNumber
		}
	}
}
