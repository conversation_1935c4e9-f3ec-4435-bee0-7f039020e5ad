load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "sysstatus",
    srcs = [
        "check.go",
        "sysstatus.go",
        "utils.go",
        "view.go",
    ],
    importpath = "sentioxyz/sentio/service/sysstatus",
    visibility = ["//visibility:public"],
    deps = [
        "//chain",
        "//chain/aptos",
        "//chain/btc",
        "//chain/clickhouse",
        "//chain/evm",
        "//chain/fuel",
        "//chain/sol",
        "//chain/sui",
        "//common/log",
        "//common/utils",
        "//service/common/auth",
        "//service/solidity/models",
        "//service/sysstatus/protos",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_ethereum_go_ethereum//rpc",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
