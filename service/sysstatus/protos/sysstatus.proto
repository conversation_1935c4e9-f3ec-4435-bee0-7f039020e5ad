syntax = "proto3";

package sysstatus_service;

option go_package = "sentioxyz/sentio/service/sysstatus/protos";

import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "google/protobuf/timestamp.proto";

service SysStatusService {
  rpc GetChainsStatusAdmin(GetChainsStatusRequest) returns (GetChainsStatusAdminResponse){
    option (google.api.http) = {
      get: "/api/v1/sysstatus/admin/chains"
    };
  }
  rpc GetChainsStatusSimple(GetChainsStatusRequest) returns (GetChainsStatusSimpleResponse){
    option (google.api.http) = {
      get: "/api/v1/sysstatus/chains"
    };
  }
}

enum ChainType {
  EVM = 0;
  APTOS2 = 1;
  SUI = 2;
  FUEL = 3;
  BTC = 4;
  SOL = 5;
}

message ChainStatus {
  ChainType type = 1;
  string network = 2;
  string chain_id = 3;
  bool hidden = 5;

  message Pool {
    string name = 1;

    enum Category {
      NODE = 0;                          // * chain-master-config.chains.{CHAIN_NAME}.storage.node.{POOL_NAME}
      NODE_STD = 1;                      //   chain-master-config.chains.{CHAIN_NAME}.storage.standard-external-node
      PERSISTENT = 2;                    // * chain-master-config.chains.{CHAIN_NAME}.storage.persistent.[POOL_NAME].[bucket|folder|dsn|rangeTable]
      PROXY = 3;                         // * chain-master-config.chains.{CHAIN_NAME}.proxy.[app|port]
      PROXY_SOURCE = 4;                  //   chain-master-config.chains.{CHAIN_NAME}.proxy.[forwarder.endpointPool]
      JSONRPC = 5;                       // * chain-master-config.chains.{CHAIN_NAME}.process.jsonrpc.port
      JSONRPC_LATEST_SOURCE = 6;         //   chain-master-config.chains.{CHAIN_NAME}.process.jsonrpc.dataSource
      JSONRPC_HISTORY_SOURCE = 7;        //   chain-master-config.chains.{CHAIN_NAME}.storage.persistent.gcs-parquet.[bucket|folder]
      SUPER_NODE = 8;                    // * chain-master-config.chains.{CHAIN_NAME}.process.superNode.port
      SUPER_NODE_LATEST_SOURCE = 12;     //   chain-master-config.chains.{CHAIN_NAME}.process.superNode.dataSource
      SUPER_NODE_HISTORY_SOURCE = 13;    //   chain-master-config.chains.{CHAIN_NAME}.process.superNode.historyDataSource
      TX_SEARCH_SOURCE = 14;             //   chain-master-config.chains.{CHAIN_NAME}.process.transactionSearch.dataSource
      DRIVER_SOURCE = 9;                 //   {ENV}.values.sentio.chainsConfig.{CHAIN_ID}.[Https|ChainServer|TraceServer|IndexerServer]
      SOLIDITY = 10;                     // * {ENV}.values.solidityServer.chainConfig.endpoint
      SOLIDITY_DEBUG = 11;               // * {ENV}.values.solidityServer.chainConfig.debug_endpoint
    }
    repeated Category categories = 2;

    message EndpointStatus {
      string endpoint = 1;
      uint64 latest_slot_number = 2;
      bool fall_behind = 4;
      string error = 3;
    }
    repeated EndpointStatus endpoints = 3;
  }
  repeated Pool pools = 4;
}

message ChainStatusSimple {
  ChainType type = 1;
  string network = 2;
  string chain_id = 3;

  message Pool {
    enum Type {
      STD_EXTERNAL = 0;
      DRIVER_SOURCE = 1;
      JSONRPC = 2;
      TX_INDEXER = 3;
      SIMULATION = 10;
      DEBUGGER = 11;
    }
    Type type = 1;

    uint64 latest_slot_number = 2;

    enum Status {
      OK = 0;
      FALL_BEHIND = 1;
      INVALID = 2;
    }
    Status status = 3;
  }
  repeated Pool pools = 4;
}

message GetChainsStatusRequest {
  string chain_id = 1;
}

message GetChainsStatusAdminResponse {
  repeated ChainStatus chains_status = 1;
  google.protobuf.Timestamp timestamp = 2;
}

message GetChainsStatusSimpleResponse {
  repeated ChainStatusSimple chains_status = 1;
  google.protobuf.Timestamp timestamp = 2;
}
