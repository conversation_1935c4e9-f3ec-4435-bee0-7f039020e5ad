package usermv

import (
	"context"
	"fmt"
	"time"

	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/clients"
	protosanalytic "sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/query"
	"sentioxyz/sentio/service/analytic/repository"
	"sentioxyz/sentio/service/analytic/repository/cache"
	"sentioxyz/sentio/service/analytic/repository/models"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	"sentioxyz/sentio/service/common/auth"
	commonmodels "sentioxyz/sentio/service/common/models"
	protoscommon "sentioxyz/sentio/service/common/protos"
	processormodel "sentioxyz/sentio/service/processor/models"

	shell "github.com/ipfs/go-ipfs-api"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

type Controller interface {
	Init() error
	Delete(ctx context.Context, mv *models.SQLRefreshableMaterializedView) error
	DeleteAll(ctx context.Context, projectID string) error
	Watch(ctx context.Context) error
	WatchOnce(ctx context.Context) error
	Transform(ctx context.Context, mv *models.SQLRefreshableMaterializedView) (*models.SQLRefreshableMaterializedView, error)
	CreateOrReplace(ctx context.Context, mv *models.SQLRefreshableMaterializedView) error
}

type controller struct {
	ctx             context.Context
	logger          *log.SentioLogger
	repository      *repository.Repository
	chMultiSharding event.MultiSharding
	redisClient     *redis.Client
	rewriterClient  clients.RewriterServiceClient
	shell           *shell.Shell
	cache           cache.Cache
	seaMapper       *sqllib.TableMappers
	authManager     auth.AuthManager
	intervalSeconds int
	engines         sqllib.Setting
}

func NewController(ctx context.Context, repository *repository.Repository,
	chMultiSharding event.MultiSharding, redisClient *redis.Client,
	rewriterClient clients.RewriterServiceClient, shell *shell.Shell, cache cache.Cache,
	seaMapper *sqllib.TableMappers, authManager auth.AuthManager,
	watchIntervalSeconds int, setting sqllib.Setting) Controller {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("component", "user_mv_controller")
	return &controller{
		ctx:             ctx,
		logger:          logger,
		repository:      repository,
		chMultiSharding: chMultiSharding,
		intervalSeconds: watchIntervalSeconds,
		redisClient:     redisClient,
		rewriterClient:  rewriterClient,
		shell:           shell,
		cache:           cache,
		seaMapper:       seaMapper,
		authManager:     authManager,
		engines:         setting,
	}
}

func (c *controller) Init() error {
	const userMvDatabaseStmt = `CREATE DATABASE IF NOT EXISTS {database}`
	for _, sharding := range c.chMultiSharding.All() {
		cluster := sharding.GetSentioConn().GetCluster()
		var stmt string
		switch cluster {
		case "":
			stmt = builder.FormatSQLTemplate(userMvDatabaseStmt, map[string]any{
				"database": *mapper.UserMvDatabaseName,
			})
		default:
			stmt = builder.FormatSQLTemplate(userMvDatabaseStmt, map[string]any{
				"database": *mapper.UserMvDatabaseName,
			})
		}
		for _, conn := range sharding.GetSentioReplicas() {
			if err := conn.GetClickhouseConn().Exec(c.ctx, stmt); err != nil {
				c.logger.Errorf("failed to create database %s: %v", *mapper.UserMvDatabaseName, err)
				return err
			}
		}
	}
	return nil
}

func (c *controller) latestProcessor(ctx context.Context, projectID string) (*processormodel.Processor, error) {
	logger := c.logger.With("project_id", projectID)
	processors, err := c.repository.GetProcessorsByProjectAndVersion(ctx, projectID, 0)
	if err != nil {
		logger.Errorf("failed to get processors by project and version: %v", err)
		return nil, err
	}
	if len(processors) == 0 {
		logger.Errorf("no processors found for project %s", projectID)
		return nil, nil
	}
	latestProcessor := processors[0]
	return latestProcessor, nil
}

func (c *controller) getAllRefreshableMaterializedViews(ctx context.Context, projectID string) (
	[]*models.SQLRefreshableMaterializedView, error) {
	logger := c.logger.With("project_id", projectID)
	var mvs []*models.SQLRefreshableMaterializedView
	switch {
	case projectID == "":
		logger.Infof("getting all refreshable materialized views")
		if err := c.repository.DB.WithContext(ctx).Model(&models.SQLRefreshableMaterializedView{}).
			Find(&mvs).Error; err != nil {
			logger.Errorf("failed to get all refreshable materialized views: %v", err)
			return nil, err
		}
		return mvs, nil
	default:
		if err := c.repository.DB.WithContext(ctx).Model(&models.SQLRefreshableMaterializedView{}).
			Where("project_id = ?", projectID).Find(&mvs).Error; err != nil {
			logger.Errorf("failed to get all refreshable materialized views: %v", err)
			return nil, err
		}
		return mvs, nil
	}
}

func (c *controller) pickConn(shardingConn event.ShardingConn, projectType string) []event.Conn {
	switch projectType {
	case commonmodels.ProjectTypeSentio:
		return shardingConn.GetSentioReplicas()
	case commonmodels.ProjectTypeSubgraph:
		return shardingConn.GetSubgraphReplicas()
	default:
		return shardingConn.GetSentioReplicas()
	}
}

func (c *controller) delete(ctx context.Context, logger *log.SentioLogger,
	sharding int32, mv *models.SQLRefreshableMaterializedView) error {
	shardingConn := c.chMultiSharding.GetShard(sharding)
	if shardingConn == nil {
		logger.Errorf("failed to get sharding connection for %d", sharding)
		return errors.Errorf("failed to get sharding connection for %d", sharding)
	}
	conns := c.pickConn(shardingConn, mv.ProjectType)
	var lastErr error
	for _, c := range conns {
		if err := c.GetClickhouseConn().Exec(ctx, builder.FormatSQLTemplate("DROP VIEW IF EXISTS {database}.{name}", map[string]any{
			"database": *mapper.UserMvDatabaseName,
			"name":     mv.EncryptName(),
		})); err != nil {
			log.Errorf("failed to drop materialized view: %v", err)
			lastErr = err
		}
	}
	return lastErr
}

func (c *controller) pickSharding(mv *models.SQLRefreshableMaterializedView,
	latestProcessor *processormodel.Processor) int32 {
	var sharding = mv.ProcessorSharding
	if latestProcessor != nil {
		if latestProcessor.ID != mv.ProcessorID {
			c.logger.Infof("processor changed, deleting mv with latest processor: %s", latestProcessor.ID)
		}
		sharding = latestProcessor.ClickhouseShardingIndex
	}
	return sharding
}

func (c *controller) Delete(ctx context.Context, mv *models.SQLRefreshableMaterializedView) error {
	logger := c.logger.With("project_id", mv.ProjectID, "mv_id", mv.UUID, "name", mv.Name)
	latestProcessor, err := c.latestProcessor(ctx, mv.ProjectID)
	if err != nil {
		logger.Errorf("failed to get latest processor: %v", err)
		return err
	}
	return c.delete(ctx, logger, c.pickSharding(mv, latestProcessor), mv)
}

func (c *controller) DeleteAll(ctx context.Context, projectID string) error {
	logger := c.logger.With("project_id", projectID)

	mvs, err := c.getAllRefreshableMaterializedViews(ctx, projectID)
	if err != nil {
		logger.Errorf("failed to get all refreshable materialized views: %v", err)
		return err
	}
	if len(mvs) == 0 {
		return nil
	}
	latestProcessor, err := c.latestProcessor(ctx, projectID)
	if err != nil {
		logger.Errorf("failed to get latest processor: %v", err)
		return err
	}

	for _, mv := range mvs {
		if err := c.delete(ctx, logger, c.pickSharding(mv, latestProcessor), mv); err != nil {
			logger.Errorf("failed to delete materialized view: %v", err)
			return err
		}
	}
	return nil
}

func (c *controller) watchOnce(ctx context.Context) error {
	allMvs, err := c.getAllRefreshableMaterializedViews(ctx, "")
	if err != nil {
		c.logger.Errorf("failed to get all refreshable materialized views: %v", err)
		return err
	}

	var updatedMVs []string
	for _, mv := range allMvs {
		latestProcessor, err := c.latestProcessor(ctx, mv.ProjectID)
		if err != nil {
			c.logger.Errorf("failed to get latest processor: %v", err)
			return err
		}
		if latestProcessor == nil {
			c.logger.Infof("no latest processor found for project %s", mv.ProjectID)
			_ = c.delete(ctx, c.logger, mv.ProcessorSharding, mv)
			continue
		}

		if latestProcessor.ID != mv.ProcessorID {
			c.logger.Infof("processor changed, deleting mv with latest processor: %s", latestProcessor.ID)
			if err := c.delete(ctx, c.logger, mv.ProcessorSharding, mv); err != nil {
				c.logger.Errorf("failed to delete materialized view: %v", err)
				return err
			}
			mv.ProcessorID = latestProcessor.ID
			mv.ProcessorVersion = latestProcessor.Version
			mv.ProcessorSharding = latestProcessor.ClickhouseShardingIndex
			mv, err = c.Transform(ctx, mv)
			if err != nil {
				c.logger.Errorf("failed to transform materialized view: %v", err)
				return err
			}
			if err := c.CreateOrReplace(ctx, mv); err != nil {
				c.logger.Errorf("failed to create or replace materialized view: %v", err)
				return err
			}
			updatedMVs = append(updatedMVs, mv.UUID)
		}
	}
	if len(updatedMVs) > 0 {
		c.logger.Infof("updated materialized views: %v", updatedMVs)
	}
	return nil
}

func (c *controller) Watch(ctx context.Context) error {
	if c.intervalSeconds <= 0 {
		return nil
	}

	logger := c.logger.With("watch_interval", c.intervalSeconds)
	d, _ := time.ParseDuration(fmt.Sprintf("%ds", c.intervalSeconds))
	ticker := time.NewTicker(d)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			logger.Debugf("watching for user materialized views")
			if err := c.watchOnce(ctx); err != nil {
				logger.Errorf("failed to watch user materialized views: %v", err)
			}
		}
	}
}

func (c *controller) WatchOnce(ctx context.Context) error {
	if err := c.watchOnce(ctx); err != nil {
		log.Errorf("failed to watch user materialized views: %v", err)
		return err
	}
	return nil
}

func (c *controller) transform(ctx context.Context,
	mv *models.SQLRefreshableMaterializedView,
	processor *processormodel.Processor,
	project *commonmodels.Project) (*models.SQLRefreshableMaterializedView, error) {
	logger := c.logger.With("project_id", mv.ProjectID, "mv_id", mv.UUID, "name", mv.Name)
	newMv := mv.Clone()
	newMv.ProcessorID = processor.ID
	newMv.ProcessorSharding = processor.ClickhouseShardingIndex
	newMv.ProcessorVersion = processor.Version
	newMv.UpdatedAt = time.Now()

	args, err := query.NewArgs(ctx, processor.ID, project.ID,
		c.chMultiSharding, c.repository, c.redisClient, c.rewriterClient, c.shell, c.cache)
	if err != nil {
		logger.Errorf("failed to create query args: %v", err)
		return nil, err
	}
	mapping := sqllib.NewMapping()
	if err := mapping.ApplyExternals(ctx, c.redisClient, args); err != nil {
		logger.Errorf("failed to apply externals: %v", err)
		return nil, err
	}
	mapping.ApplyMapper(int(processor.ClickhouseShardingIndex), nil, mv.UserID, c.repository,
		sqllib.GetTableMappers(args, c.seaMapper))
	mapping.ApplyEntities(ctx, args)
	mapping.AppendUserMappers(mapper.NewUserRefreshableViews(c.repository.DB, args.Project, args.RWConn))
	mapping.ApplyQueries(ctx, c.repository.MustGetNamedQueriesByProject(args.Project.ID))

	var executor event.ClickhouseViewer
	switch project.Type {
	case commonmodels.ProjectTypeSentio:
		executor = args.Viewer
	case commonmodels.ProjectTypeSubgraph:
		executor = args.SgViewer
	default:
		return nil, errors.Errorf("unknown project type: %s", project.Type)
	}
	var settings = clickhouse.NewConnSettingsTemplate()
	c.engines.OverwriteClickhouseSettings("user_mv", settings)
	result := executor.Execute(ctx, clickhouse.NewExecuteArgs(
		mv.SQL, "", &protoscommon.RichStruct{},
		mapping.GetDatabaseTableMapping(), mapping.GetRemoteArgsMapping(), mapping.GetCommonTableExprArgsMapping(),
		1,
		c.repository, c.authManager, sqllib.ProcessorAccessValidator,
		c.rewriterClient, true, settings,
		"", "", true, protosanalytic.ExecuteEngine_DEFAULT))
	if err := result.Error(); err != nil {
		logger.Errorf("failed to transform query: %v", err)
		return nil, err
	}
	newMv.RawSQL = result.SQL()
	return newMv, nil
}

func (c *controller) Transform(ctx context.Context,
	mv *models.SQLRefreshableMaterializedView) (*models.SQLRefreshableMaterializedView, error) {
	logger := c.logger.With("project_id", mv.ProjectID, "mv_id", mv.UUID, "name", mv.Name)
	project, err := c.repository.GetProjectByID(c.repository.DB, mv.ProjectID)
	if err != nil {
		logger.Errorf("failed to get project by ID: %v", err)
		return nil, err
	}
	if project == nil {
		logger.Errorf("project not found for ID: %s", mv.ProjectID)
		return nil, errors.Errorf("project not found for ID: %s", mv.ProjectID)
	}

	latestProcessor, err := c.latestProcessor(ctx, mv.ProjectID)
	if err != nil {
		logger.Errorf("failed to get latest processor: %v", err)
		return nil, err
	}
	if latestProcessor == nil {
		logger.Infof("no latest processor found for project %s", mv.ProjectID)
		return nil, errors.Errorf("no latest running processor found for project %s", mv.ProjectID)
	}

	newMv, err := c.transform(ctx, mv, latestProcessor, project)
	if err != nil {
		logger.Errorf("failed to transform materialized view: %v", err)
		return nil, err
	}
	return newMv, nil
}

func (c *controller) CreateOrReplace(ctx context.Context,
	mv *models.SQLRefreshableMaterializedView) error {
	logger := c.logger.With("project_id", mv.ProjectID, "mv_id", mv.UUID, "name", mv.Name)
	project, err := c.repository.GetProjectByID(c.repository.DB, mv.ProjectID)
	if err != nil {
		logger.Errorf("failed to get project by ID: %v", err)
		return err
	}
	if project == nil {
		logger.Errorf("project not found for ID: %s", mv.ProjectID)
		return errors.Errorf("project not found for ID: %s", mv.ProjectID)
	}

	latestProcessor, err := c.latestProcessor(ctx, mv.ProjectID)
	if err != nil {
		logger.Errorf("failed to get latest processor: %v", err)
		return err
	}
	if latestProcessor == nil {
		logger.Infof("no latest processor found for project %s", mv.ProjectID)
		return errors.Errorf("no latest running processor found for project %s", mv.ProjectID)
	}

	if err := c.Delete(ctx, mv); err != nil {
		logger.Errorf("failed to delete materialized view: %v", err)
		return err
	}

	var conns []event.Conn
	shardingConn := c.chMultiSharding.GetShard(latestProcessor.ClickhouseShardingIndex)
	if shardingConn == nil {
		return errors.Errorf("failed to get sharding connection for %d", latestProcessor.ClickhouseShardingIndex)
	}
	switch project.Type {
	case commonmodels.ProjectTypeSentio:
		conns = shardingConn.GetSentioReplicas()
	case commonmodels.ProjectTypeSubgraph:
		conns = shardingConn.GetSubgraphReplicas()
	default:
		return errors.Errorf("invalid project type")
	}
	createStmt := mv.CreateViewStmt(*mapper.UserMvDatabaseName)
	logger = logger.With("create", createStmt)
	logger.Debugf("create refreshable materialized view statement completed")

	for _, stmt := range createStmt {
		for idx, c := range conns {
			if err := c.GetClickhouseConn().Exec(ctx, stmt); err != nil {
				logger.Warnf("failed to create mv to clickhouse, err: %v", err)
				return err
			} else {
				logger.Infof("success to create mv to clickhouse index: %d", idx)
			}
		}
	}
	return nil
}
