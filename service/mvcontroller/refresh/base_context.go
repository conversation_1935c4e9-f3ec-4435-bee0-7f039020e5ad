package refresh

import (
	"context"
	"database/sql"
	"fmt"
	"hash/fnv"
	"sort"
	"strings"
	"sync"
	"time"

	settings "sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/clickhouse/builder"
	chmodels "sentioxyz/sentio/common/clickhouse/models"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/common/repository"
	"sentioxyz/sentio/service/processor/models"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

var (
	ErrPostgresInternal = errors.New("postgres internal error")
	pgConnPool          = map[string]*gorm.DB{}
	pgMutex             sync.Mutex
	insertBatchSize     = 20000
)

func connectPostgres(dsn string) (*gorm.DB, error) {
	pgMutex.Lock()
	defer pgMutex.Unlock()
	if conn, ok := pgConnPool[dsn]; ok {
		return conn, nil
	}
	conn, err := repository.Connect(dsn)
	if err != nil {
		return nil, err
	}
	pgConnPool[dsn] = conn
	return conn, nil
}

type PointsData struct {
	ID         string
	PointsJSON string
}

type baseContext struct {
	name               string
	db                 repository.Repository
	connOpt            *clickhouse.Options
	conn               clickhouse.Conn
	replicaConns       []clickhouse.Conn
	clusterArgv        string
	mvHash             string
	config             WatcherConfig
	results            map[string]map[string]map[string]float64
	chRemotePatternMap CHRemotePatternMap
	allowSkip          bool
}

func (b *baseContext) clickhouseExec(ctx context.Context, f string, v ...any) error {
	ctx = clickhouse.Context(ctx, clickhouse.WithSettings(clickhouse.Settings{
		"allow_experimental_lightweight_delete":            "true",
		"allow_experimental_refreshable_materialized_view": 1,
		"max_query_size": 10000000,
		"secondary_indices_enable_bulk_filtering": 0,
	}))
	stmt := fmt.Sprintf(f, v...)
	startTime := time.Now()
	err := b.conn.Exec(ctx, stmt)
	_, logger := log.FromContext(ctx)
	if err == nil {
		logger.Debugf("clickHouse exec: %s (%v)", stmt, time.Since(startTime))
	} else {
		logger.Warnfe(err, "clickHouse exec failed (%v): %s", err, stmt)
	}
	return err
}

type ExecuteMode int

const (
	ExecuteModeMaster ExecuteMode = iota
	ExecuteModeReplica
	ExecuteModeBoth
)

func (w *baseContext) processBatchData(ctx context.Context, tmpTableName string, datas []PointsData) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name, "table", tmpTableName)

	batch, err := w.conn.PrepareBatch(ctx, "INSERT INTO "+tmpTableName)
	if err != nil {
		logger.Errorfe(err, "failed to prepare batch")
		return err
	}
	defer batch.Close()

	for _, data := range datas {
		if err := batch.Append(data.ID, data.PointsJSON); err != nil {
			logger.Errorfe(err, "failed to append batch")
			return err
		}
	}
	return batch.Send()
}

func (b *baseContext) DynamicTableName(processor *models.Processor) string {
	var l = &chmodels.LogEvent{}
	return l.TableName(processor)
}

func (b *baseContext) clickhouseManualExec(ctx context.Context, mode ExecuteMode, f string, v ...any) error {
	ctx = clickhouse.Context(ctx, clickhouse.WithSettings(clickhouse.Settings{
		"allow_experimental_lightweight_delete":            "true",
		"allow_experimental_refreshable_materialized_view": 1,
		"max_query_size": 10000000,
		"secondary_indices_enable_bulk_filtering": 0,
	}))
	stmt := fmt.Sprintf(f, v...)
	_, rLogger := log.FromContext(ctx)
	startTime := time.Now()
	switch mode {
	case ExecuteModeMaster:
		err := b.conn.Exec(ctx, stmt)
		if err == nil {
			rLogger.Debugf("clickHouse exec: %s (%v)", stmt, time.Since(startTime))
		} else {
			rLogger.Warnfe(err, "clickHouse exec failed (%v): %s", err, stmt)
			return err
		}
	case ExecuteModeReplica:
		if len(b.replicaConns) == 0 {
			return nil
		}
		for i, conn := range b.replicaConns {
			rLogger := rLogger.With("replica", i)
			err := conn.Exec(ctx, stmt)
			if err == nil {
				rLogger.Debugf("clickHouse exec: %s (%v)", stmt, time.Since(startTime))
			} else {
				rLogger.Warnfe(err, "clickHouse exec failed (%v): %s", err, stmt)
				return err
			}
		}
	case ExecuteModeBoth:
		err := b.conn.Exec(ctx, stmt)
		if err == nil {
			rLogger.Debugf("clickHouse exec: %s (%v)", stmt, time.Since(startTime))
		} else {
			rLogger.Warnfe(err, "clickHouse exec failed (%v): %s", err, stmt)
		}
		for i, conn := range b.replicaConns {
			rLogger = rLogger.With("replica", i)
			err := conn.Exec(ctx, stmt)
			if err == nil {
				rLogger.Debugf("clickHouse exec: %s (%v)", stmt, time.Since(startTime))
			} else {
				rLogger.Warnfe(err, "clickHouse exec failed (%v): %s", err, stmt)
				return err
			}
		}
	}
	return nil
}

func (b *baseContext) setupClickhouseInViewMode(ctx context.Context, dsn string) {
	_, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	var err error
	b.connOpt, err = clickhouse.ParseDSN(dsn)
	if err != nil {
		logger.Errorfe(err, "parse clickhouse dsn failed: %s", err)
	}
}

func (b *baseContext) setupClickhouse(ctx context.Context, dsn string) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	var err error
	b.connOpt, err = clickhouse.ParseDSN(dsn)
	if err != nil {
		logger.Errorfe(err, "parse clickhouse dsn failed: %s", err)
		return err
	}
	b.connOpt.Settings = settings.NewConnSettingsMacro()
	b.conn, err = clickhouse.Open(b.connOpt)
	if err != nil {
		logger.Errorfe(err, "open clickhouse failed: %s", err)
		return err
	}
	b.setupClickhouseClusterArgv(ctx)
	return nil
}

func (b *baseContext) setupClickhouseReplica(ctx context.Context, dsns []string) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	for _, dsn := range dsns {
		opt, err := clickhouse.ParseDSN(dsn)
		if err != nil {
			logger.Errorfe(err, "parse clickhouse dsn failed: %s", err)
			return err
		}
		conn, err := clickhouse.Open(opt)
		if err != nil {
			logger.Errorfe(err, "open clickhouse failed: %s", err)
			return err
		}
		b.replicaConns = append(b.replicaConns, conn)
	}
	return nil
}

func (b *baseContext) createIfNotExists(ctx context.Context, argv ...[]string) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	for _, arg := range argv {
		for _, stmt := range arg {
			if err := b.clickhouseManualExec(ctx, ExecuteModeBoth, stmt); err != nil {
				logger.Errorf("clickhouse manual replica exec failed: %s", err)
				return err
			}
		}
	}
	return nil
}

func (b *baseContext) setupPostgres(ctx context.Context, dsn string) error {
	_, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	db, err := connectPostgres(dsn)
	if err != nil {
		logger.Errorfe(err, "setup postgres failed: %s", err)
		return err
	}
	b.db = repository.NewRepository(db)
	return nil
}

func (b *baseContext) getCurrentMvHash(ctx context.Context, mvMaintainerTable, mvTable string) (string, error) {
	ctx, logger := log.FromContext(ctx)
	const tpl = "SELECT argMax(schema_hash, create_time) FROM `%s` WHERE mv_name = '%s'"
	stmt := fmt.Sprintf(tpl, mvMaintainerTable, mvTable)
	row := b.conn.QueryRow(ctx, stmt)
	if err := row.Err(); err != nil {
		logger.Warnfe(err, "failed to query clickhouse, sql: %s", stmt)
		return "", err
	}
	var mvHash string
	if err := row.Scan(&mvHash); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return "", nil
		}
		logger.Warnfe(err, "failed to scan clickhouse, sql: %s", stmt)
		return "", err
	}
	return mvHash, nil
}

func (b *baseContext) fetchData(ctx context.Context, projectName string) (
	slug string, version int32, processorID string, sharding int32, processor *models.Processor, err error) {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	parts := strings.Split(strings.TrimSpace(projectName), "/")
	if len(parts) != 2 {
		logger.Fatalf("invalid project: %s", projectName)
		err = errors.Errorf("invalid project: %s", projectName)
		return
	}
	project, err := b.db.GetProjectBySlug(ctx, parts[0], parts[1])
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Warnf("project not found: %s, will skip it", projectName)
			return
		}
		logger.Warnf("failed to get project: %s, will skip it", projectName)
		err = errors.Wrap(ErrPostgresInternal, fmt.Sprintf("failed to get project: %s", err.Error()))
		return
	}
	if project == nil {
		logger.Infof("project not found: %s, will skip it", projectName)
		err = gorm.ErrRecordNotFound
		return
	}
	processors, err := b.db.GetProcessorsByProjectAndVersion(ctx, project.ID, -1)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Warnf("processors not found: %s, will skip it", projectName)
			return
		}
		logger.Warnf("failed to get processors, will skip it: %s", projectName)
		err = errors.Wrap(ErrPostgresInternal, fmt.Sprintf("failed to get processors: %s", err.Error()))
		return
	}
	if len(processors) == 0 {
		logger.Infof("no processors found: %s, will skip it", projectName)
		err = gorm.ErrRecordNotFound
		return
	}
	p := processors[0]
	slug = project.Slug
	version = p.Version
	processorID = p.ID
	sharding = p.ClickhouseShardingIndex
	processor = p
	return
}

func (b *baseContext) calculateHash(ctx context.Context, argv []string) string {
	_, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	sort.Strings(argv)
	h := fnv.New64a()
	h.Write([]byte(b.config.TargetTable))
	h.Write([]byte(b.config.TargetHoldingsTable))
	h.Write([]byte(b.config.MvMaintainerTable))
	h.Write([]byte(b.config.RefreshInterval))
	h.Write([]byte(b.config.HoldingRefreshInterval))
	h.Write([]byte(b.config.RandomInterval))
	for _, arg := range argv {
		h.Write([]byte(arg))
	}
	b.mvHash = fmt.Sprintf("%x", h.Sum64())
	logger.Infof("mv hash: %s", b.mvHash)
	return b.mvHash
}

func (b *baseContext) setupClickhouseClusterArgv(ctx context.Context) {
	_, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	row := b.conn.QueryRow(ctx, "SELECT cluster FROM ("+
		"SELECT cluster, count(*) AS rs, SUM(host_address = '127.0.0.1') AS cl "+
		"FROM system.clusters "+
		"WHERE cluster not like 'all-%' "+
		"GROUP BY cluster"+
		") WHERE cl > 0 AND rs > 1")
	if row.Err() != nil {
		log.Errorf("get cluster failed: %v", row.Err())
		b.clusterArgv = ""
		return
	}
	var cluster string
	err := row.Scan(&cluster)
	if err != nil {
		b.clusterArgv = ""
	}
	if cluster == "" {
		b.clusterArgv = ""
	} else {
		b.clusterArgv = fmt.Sprintf(" ON CLUSTER '%s' ", cluster)
	}
	logger.Infof("cluster argv: %s", b.clusterArgv)
}

func (b *baseContext) formatCreateStmtTpl(tpl, tableName string, replicated bool) []string {
	var (
		engine  = "MergeTree()"
		cluster = ""
	)
	if replicated {
		cluster = b.clusterArgv
		if cluster != "" {
			engine = "ReplicatedMergeTree('/clickhouse/tables/{cluster}/{database}/{shard}/{table}/{uuid}', '{replica}')"
		}
	}
	return []string{builder.FormatSQLTemplate(tpl, map[string]any{
		"name":    tableName,
		"cluster": cluster,
		"engine":  engine,
	})}
}

func (b *baseContext) dropView(ctx context.Context, viewNames []string) error {
	_, logger := log.FromContext(ctx)
	logger = logger.With("name", b.name)
	var cluster = ""
	for _, viewName := range viewNames {
		stmt := builder.FormatSQLTemplate("DROP VIEW IF EXISTS {name} {cluster}", map[string]any{
			"name":    viewName,
			"cluster": cluster,
		})
		if err := b.clickhouseManualExec(ctx, ExecuteModeBoth, stmt); err != nil {
			logger.Errorf("drop view failed: %s", err)
			return err
		}
	}
	return nil
}

type ProjectWithSharding interface {
	GetSharding() int32
	GetProcessor() *models.Processor
}

func (b *baseContext) getSentioEventsTable(baseSharding int32, project ProjectWithSharding) string {
	if project == nil {
		return fmt.Sprintf("`%s`.sentio_events", b.connOpt.Auth.Database)
	}

	processor := project.GetProcessor()
	tableName := b.DynamicTableName(processor)
	if baseSharding == project.GetSharding() {
		return fmt.Sprintf("`%s`.%s", b.connOpt.Auth.Database, tableName)
	}
	base, ok := b.chRemotePatternMap[int(baseSharding)]
	if !ok {
		return fmt.Sprintf("`%s`.%s", b.connOpt.Auth.Database, tableName)
	}
	to, ok := b.chRemotePatternMap[int(project.GetSharding())]
	if !ok {
		return fmt.Sprintf("`%s`.%s", b.connOpt.Auth.Database, tableName)
	}
	if base.Cluster != to.Cluster {
		return to.ToRemoteSpecificTable(true, tableName)
	}
	return to.ToRemoteSpecificTable(false, tableName)
}

func (b *baseContext) getSubgraphEntityTable(baseSharding int32, project ProjectWithSharding, entity string) string {
	if project == nil {
		return fmt.Sprintf("prod_subgraph.%s_latestView_%s", "", entity)
	}

	var processorID string
	if processor := project.GetProcessor(); processor != nil {
		processorID = processor.ID
	}
	if baseSharding == project.GetSharding() {
		return fmt.Sprintf("prod_subgraph.%s_latestView_%s", processorID, entity)
	}
	base, ok := b.chRemotePatternMap[int(baseSharding)]
	if !ok {
		return fmt.Sprintf("prod_subgraph.%s_latestView_%s", processorID, entity)
	}
	to, ok := b.chRemotePatternMap[int(project.GetSharding())]
	if !ok {
		return fmt.Sprintf("prod_subgraph.%s_latestView_%s", processorID, entity)
	}
	if base.Cluster != to.Cluster {
		return to.ToRemoteSpecificDatabaseTable(true, "prod_subgraph", fmt.Sprintf("%s_latestView_%s", processorID, entity))
	} else {
		return to.ToRemoteSpecificDatabaseTable(false, "prod_subgraph", fmt.Sprintf("%s_latestView_%s", processorID, entity))
	}
}

func (b *baseContext) randomIntervalArgv() string {
	if b.config.RandomInterval == "" {
		return ""
	}
	return "RANDOMIZE FOR " + b.config.RandomInterval
}

func (b *baseContext) RunLocal(_ context.Context) error {
	return errors.Errorf("not implemented")
}

func (b *baseContext) Cleanup(ctx context.Context, prefix string) error {
	rows, err := b.conn.Query(ctx, fmt.Sprintf("show tables like '%s%%'", prefix))
	if err != nil {
		return errors.Wrap(err, "failed to query tables")
	}
	defer func() {
		_ = rows.Close()
	}()

	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			return errors.Wrap(err, "failed to scan table name")
		}
		switch {
		case strings.HasPrefix(tableName, "autogen"):
			log.Infof("not dropping table: %s", tableName)
		case strings.HasPrefix(tableName, "sentio_events"):
			log.Infof("not dropping table: %s", tableName)
		default:
			if err := b.clickhouseManualExec(ctx, ExecuteModeBoth, fmt.Sprintf("DROP TABLE IF EXISTS `%s`", tableName)); err != nil {
				return errors.Wrap(err, "failed to drop table")
			}
			log.Infof("dropped table: %s", tableName)
		}

	}
	if err := rows.Err(); err != nil {
		return errors.Wrap(err, "failed to iterate tables")
	}
	return nil
}
