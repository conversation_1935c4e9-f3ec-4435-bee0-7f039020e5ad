package refresh

import (
	"fmt"
	"hash/fnv"
	"strings"
)

type SchemaAggregation struct {
	Column   string `yaml:"column"`
	Type     string `yaml:"type"`
	<PERSON><PERSON>    string `yaml:"alias"`
	IsPoints bool   `yaml:"is_points"`
}

func (a SchemaAggregation) Copy() SchemaAggregation {
	return SchemaAggregation{
		Column:   a.Column,
		Type:     a.Type,
		Alias:    a.<PERSON>,
		IsPoints: a.<PERSON>,
	}
}

type SchemaFilter struct {
	Column string `yaml:"column"`
	Value  string `yaml:"value"`
	Func   string `yaml:"func"`
	Op     string `yaml:"op"`
	Raw    string `yaml:"raw"`
}

func (f SchemaFilter) Copy() SchemaFilter {
	return SchemaFilter{
		Column: f.Column,
		Value:  f.Value,
		Func:   f.Func,
		Op:     f.Op,
		Raw:    f.Raw,
	}
}

type SchemaFilters []SchemaFilter

func (fs SchemaFilters) String() string {
	var filters []string
	var filter string
	for _, f := range fs {
		switch {
		case f.Raw != "":
			filters = append(filters, f.Raw)
		default:
			var v string
			if f.Value != "" {
				v = "'" + f.Value + "'"
			} else if f.Func != "" {
				v = f.Func
			}

			if v == "" {
				continue
			}

			switch strings.ToLower(f.Op) {
			case "eq":
				filters = append(filters, "("+f.Column+"="+v+")")
			case "ne":
				filters = append(filters, "("+f.Column+"!="+v+")")
			case "gt":
				filters = append(filters, "("+f.Column+">"+v+")")
			case "lt":
				filters = append(filters, "("+f.Column+"<"+v+")")
			case "gte":
				filters = append(filters, "("+f.Column+">="+v+")")
			case "lte":
				filters = append(filters, "("+f.Column+"<="+v+")")
			}
		}
	}
	if len(filters) > 0 {
		filter = " AND " + strings.Join(filters, " AND ")
	}
	return filter
}

type SchemaMultiAggregation struct {
	Table        string              `yaml:"table"`
	Aggregations []SchemaAggregation `yaml:"aggregations"`
}

type Schema struct {
	Projects          []string                 `yaml:"projects"`
	Table             string                   `yaml:"table"`
	IDColumn          string                   `yaml:"id_column"`
	SubProjectColumn  string                   `yaml:"sub_project_column"`
	SymbolColumn      string                   `yaml:"symbol_column"`
	Aggregations      []SchemaAggregation      `yaml:"aggregations"`
	Filters           []SchemaFilter           `yaml:"filters"`
	MultiAggregations []SchemaMultiAggregation `yaml:"multi_aggregations"`

	// for swell org
	ComplexQueryEnable string `yaml:"complex_query_enable,omitempty"`
	SwellV2Enable      bool   `yaml:"swell_v2_enable,omitempty"`

	// for lombard org
	WeightedPointsByTierEnable bool `yaml:"weighted_points_by_tier_enable,omitempty"`
}

type WatcherConfig struct {
	PgDSN                   string   `yaml:"pg_dsn"`
	ClickhouseDSN           string   `yaml:"clickhouse_dsn"`
	ClickhouseReplicaDSNs   []string `yaml:"clickhouse_replica_dsns"`
	ClickhouseShardingIndex int      `yaml:"clickhouse_sharding_index"`
	TargetTable             string   `yaml:"target_table"`
	TargetHoldingsTable     string   `yaml:"target_holdings_table"`
	MvMaintainerTable       string   `yaml:"mv_maintainer_table"`
	RefreshInterval         string   `yaml:"refresh_interval"`
	HoldingRefreshInterval  string   `yaml:"holding_refresh_interval"`
	RandomInterval          string   `yaml:"random_interval"`
	Schema                  []Schema `yaml:"schema"`
	AllowSkip               bool     `yaml:"allow_skip"`
}

const encryptKey = "sentio_mvcontroller_v1"

func (c *WatcherConfig) EncryptTargetTable(name string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetTable))
	return fmt.Sprintf("%s_%s", c.TargetTable, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) OriginalTargetTableMv() string {
	return c.TargetTable + "_mv"
}

func (c *WatcherConfig) EncryptTargetTableMv(name string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetTable))
	return fmt.Sprintf("%s_mv_%s", c.TargetTable, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) EncryptTargetStatusTable(name string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetTable))
	return fmt.Sprintf("%s_status_%s", c.TargetTable, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) OriginalTargetStatusTableMv() string {
	return c.TargetTable + "_status_mv"
}

func (c *WatcherConfig) EncryptTargetStatusTableMv(name string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetTable))
	return fmt.Sprintf("%s_status_mv_%s", c.TargetTable, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) EncryptTargetHoldingsTable(name string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetHoldingsTable))
	return fmt.Sprintf("%s_%s", c.TargetHoldingsTable, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) OriginalTargetHoldingsTableMv() string {
	return c.TargetHoldingsTable + "_mv"
}

func (c *WatcherConfig) EncryptTargetHoldingsTableMv(name string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetHoldingsTable))
	return fmt.Sprintf("%s_mv_%s", c.TargetHoldingsTable, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) EncryptTableBySuffix(name, suffix string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetTable))
	f.Write([]byte(suffix))
	return fmt.Sprintf("%s_%s_%s", c.TargetTable, suffix, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) EncryptTableMvBySuffix(name, suffix string) string {
	f := fnv.New64a()
	f.Write([]byte(name))
	f.Write([]byte(encryptKey))
	f.Write([]byte(c.TargetTable))
	f.Write([]byte(suffix))
	return fmt.Sprintf("%s_%s_mv_%s", c.TargetTable, suffix, fmt.Sprintf("%x", f.Sum64()))
}

func (c *WatcherConfig) OriginalTableMvBySuffix(suffix string) string {
	return c.TargetTable + "_" + suffix + "_mv"
}

func (c *WatcherConfig) OriginalTableBySuffix(suffix string) string {
	return c.TargetTable + "_" + suffix
}
