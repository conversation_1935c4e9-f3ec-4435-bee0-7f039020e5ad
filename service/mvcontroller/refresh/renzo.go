package refresh

import (
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/timer"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	"sentioxyz/sentio/service/processor/models"

	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

//go:embed sql/renzo_project_query.sql
var renzoProjectQuery string

//go:embed sql/renzo_project_status_query.sql
var renzoProjectStatusQuery string

//go:embed sql/renzo_points.sql
var renzoPointsCreateStmt string

//go:embed sql/mv_maintainer.sql
var mvMaintainerCreateStmt string

//go:embed sql/renzo_union_query.sql
var renzoUnionQuery string

//go:embed sql/renzo_status_union_query.sql
var renzoStatusUnionQuery string

//go:embed sql/renzo_points_status.sql
var renzoPointsStatusCreateStmt string

//go:embed sql/renzo_contango_points_table.sql
var renzoContangoPointsTable string

//go:embed sql/renzo_contango_pzeth_ethereum_points_table.sql
var renzoContangoPzethEthereumPointsTable string

//go:embed sql/renzo_beefy_pendlesept26_magpie_points_table.sql
var renzoBeefyPendleSept26MagpiePointsTable string

//go:embed sql/renzo_beefy_pendlesept26_points_points_table.sql
var renzoBeefyPendleSept26PointsPointsTable string

//go:embed sql/renzo_beefy_pendle_27jun24_points_points_table.sql
var renzoBeefyPendle27Jun24PointsPointsTable string

//go:embed sql/renzo_cian_points_table.sql
var renzoCianPointsTable string

//go:embed sql/renzo_project_cte_query.sql
var renzoProjectCTEQuery string

//go:embed sql/renzo_project_status_cte_query.sql
var renzoProjectStatusCTEQuery string

//go:embed sql/renzo_fuel_points_table.sql
var renzoFuelPointsTable string

//go:embed sql/v2/renzo_project_query_v2.sql
var renzoProjectQueryV2 string

//go:embed sql/v2/renzo_points_v2.sql
var renzoPointsCreateStmtV2 string

type RenzoProject struct {
	FullName          string
	Version           int32
	ProcessorID       string
	ProcessorSharding int32
	Schema            *Schema
	SubProjects       map[string]RenzoProject
	Processor         *models.Processor `json:"-"`
}

func (r RenzoProject) GetSharding() int32 {
	return r.ProcessorSharding
}

func (r RenzoProject) GetProcessor() *models.Processor {
	return r.Processor
}

func (r RenzoProject) ToCustomQuery(w *renzoWatcher) string {
	var aggregations []string
	for _, aggr := range r.Schema.Aggregations {
		aggregations = append(aggregations, "'"+aggr.Alias+"'")
		switch strings.ToLower(aggr.Type) {
		case "sum":
			aggregations = append(aggregations, "sum("+aggr.Column+")")
		}
	}

	switch r.Schema.Table {
	case "renzo_contango_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoContangoPointsTable, map[string]any{
			"morpho_processor_id":           r.SubProjects["morpho"].ProcessorID,
			"morpho_base_weth_processor_id": r.SubProjects["morpho_base_weth"].ProcessorID,
			"morpho_base_usdc_processor_id": r.SubProjects["morpho_base_usdc"].ProcessorID,
			"silo_arb_processor_id":         r.SubProjects["silo_arb"].ProcessorID,
			"silo_main_processor_id":        r.SubProjects["silo_main"].ProcessorID,
			"silo_op_processor_id":          r.SubProjects["silo_op"].ProcessorID,
			"dolomite_arb_processor_id":     r.SubProjects["dolomite_arb"].ProcessorID,
			"compound_eth_processor_id":     r.SubProjects["compound_eth"].ProcessorID,
			"compound_arb_processor_id":     r.SubProjects["compound_arb"].ProcessorID,
			"compound_base_processor_id":    r.SubProjects["compound_base"].ProcessorID,
			"compound_op_processor_id":      r.SubProjects["compound_op"].ProcessorID,
			"euler_eth_processor_id":        r.SubProjects["euler_eth"].ProcessorID,
			"zerolend_eth_processor_id":     r.SubProjects["zerolend_eth"].ProcessorID,
			"zerolend_line_processor_id":    r.SubProjects["zerolend_line"].ProcessorID,
			"processor_id":                  r.ProcessorID,

			"sentio_events":                  w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"morpho_sentio_events":           w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["morpho"]),
			"morpho_base_weth_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["morpho_base_weth"]),
			"morpho_base_usdc_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["morpho_base_usdc"]),
			"silo_arb_sentio_events":         w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_arb"]),
			"silo_main_sentio_events":        w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_main"]),
			"silo_op_sentio_events":          w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_op"]),
			"dolomite_arb_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["dolomite_arb"]),
			"compound_eth_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_eth"]),
			"compound_arb_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_arb"]),
			"compound_base_sentio_events":    w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_base"]),
			"compound_op_sentio_events":      w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_op"]),
			"euler_eth_sentio_events":        w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["euler_eth"]),
			"zerolend_eth_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["zerolend_eth"]),
			"zerolend_line_sentio_events":    w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["zerolend_line"]),
		})
		return builder.FormatSQLTemplate(renzoProjectCTEQuery, map[string]any{
			"id_column":    r.Schema.IDColumn,
			"project":      "'" + r.FullName + "'",
			"aggregations": strings.Join(aggregations, ", "),
			"cte_query":    cteQuery,
		})
	case "renzo_contango_pzeth_ethereum_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoContangoPzethEthereumPointsTable, map[string]any{
			"zerolend_pzeth_processor_id": r.SubProjects["zerolend_pzeth"].ProcessorID,
			"euler_pzeth_processor_id":    r.SubProjects["euler_pzeth"].ProcessorID,
			"silo_pzeth_processor_id":     r.SubProjects["silo_pzeth"].ProcessorID,
			"processor_id":                r.ProcessorID,

			"sentio_events":                w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"zerolend_pzeth_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["zerolend_pzeth"]),
			"euler_pzeth_sentio_events":    w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["euler_pzeth"]),
			"silo_pzeth_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_pzeth"]),
		})
		return builder.FormatSQLTemplate(renzoProjectCTEQuery, map[string]any{
			"id_column":    r.Schema.IDColumn,
			"project":      "'" + r.FullName + "'",
			"aggregations": strings.Join(aggregations, ", "),
			"cte_query":    cteQuery,
		})
	case "renzo_beefy_pendlesept26_magpie_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoBeefyPendleSept26MagpiePointsTable, map[string]any{
			"processor_id":                          r.ProcessorID,
			"pendle_arbitrum_sept2024_processor_id": r.SubProjects["pendle_arbitrum_sept2024"].ProcessorID,
			"sentio_events":                         w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
		return builder.FormatSQLTemplate(renzoProjectCTEQuery, map[string]any{
			"id_column":    r.Schema.IDColumn,
			"project":      "'" + r.FullName + "'",
			"aggregations": strings.Join(aggregations, ", "),
			"cte_query":    cteQuery,
		})
	case "renzo_beefy_pendlesept26_points_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoBeefyPendleSept26PointsPointsTable, map[string]any{
			"processor_id":                          r.ProcessorID,
			"pendle_arbitrum_sept2024_processor_id": r.SubProjects["pendle_arbitrum_sept2024"].ProcessorID,
			"sentio_events":                         w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
		return builder.FormatSQLTemplate(renzoProjectCTEQuery, map[string]any{
			"id_column":    r.Schema.IDColumn,
			"project":      "'" + r.FullName + "'",
			"aggregations": strings.Join(aggregations, ", "),
			"cte_query":    cteQuery,
		})
	case "renzo_beefy_pendle_27jun24_points_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoBeefyPendle27Jun24PointsPointsTable, map[string]any{
			"processor_id":                        r.ProcessorID,
			"pendle_points_arbitrum_processor_id": r.SubProjects["pendle_points_arbitrum"].ProcessorID,
			"sentio_events":                       w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
		return builder.FormatSQLTemplate(renzoProjectCTEQuery, map[string]any{
			"id_column":    r.Schema.IDColumn,
			"project":      "'" + r.FullName + "'",
			"aggregations": strings.Join(aggregations, ", "),
			"cte_query":    cteQuery,
		})
	case "renzo_cian_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoCianPointsTable, map[string]any{
			"processor_id":               r.ProcessorID,
			"aave_ethereum_processor_id": r.SubProjects["aave_ethereum"].ProcessorID,

			"sentio_events":               w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"aave_ethereum_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["aave_ethereum"]),
		})
		return builder.FormatSQLTemplate(renzoProjectCTEQuery, map[string]any{
			"id_column":    r.Schema.IDColumn,
			"project":      "'" + r.FullName + "'",
			"aggregations": strings.Join(aggregations, ", "),
			"cte_query":    cteQuery,
		})
	case "renzo_fuel_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoFuelPointsTable, map[string]any{
			"processor_id":           r.ProcessorID,
			"sentio_events":          w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"fuel_evm_mapping_table": w.getSubgraphEntityTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["fuel_evm_mapping"], "Account"),
		})
		return builder.FormatSQLTemplate(renzoProjectCTEQuery, map[string]any{
			"id_column":    r.Schema.IDColumn,
			"project":      "'" + r.FullName + "'",
			"aggregations": strings.Join(aggregations, ", "),
			"cte_query":    cteQuery,
		})
	}
	return ""
}

func (r RenzoProject) ToCustomStatusQuery(w *renzoWatcher) string {
	var elPointsColumn, ezPointsColumn, melPointsColumn, symPointsColum = "0", "0", "0", "0"
	for _, aggr := range r.Schema.Aggregations {
		switch aggr.Alias {
		case "el":
			elPointsColumn = aggr.Column
		case "ez":
			ezPointsColumn = aggr.Column
		case "mel":
			melPointsColumn = aggr.Column
		case "sym":
			symPointsColum = aggr.Column
		}
	}

	switch r.Schema.Table {
	case "renzo_contango_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoContangoPointsTable, map[string]any{
			"morpho_processor_id":           r.SubProjects["morpho"].ProcessorID,
			"morpho_base_weth_processor_id": r.SubProjects["morpho_base_weth"].ProcessorID,
			"morpho_base_usdc_processor_id": r.SubProjects["morpho_base_usdc"].ProcessorID,
			"silo_arb_processor_id":         r.SubProjects["silo_arb"].ProcessorID,
			"silo_main_processor_id":        r.SubProjects["silo_main"].ProcessorID,
			"silo_op_processor_id":          r.SubProjects["silo_op"].ProcessorID,
			"dolomite_arb_processor_id":     r.SubProjects["dolomite_arb"].ProcessorID,
			"compound_eth_processor_id":     r.SubProjects["compound_eth"].ProcessorID,
			"compound_arb_processor_id":     r.SubProjects["compound_arb"].ProcessorID,
			"compound_base_processor_id":    r.SubProjects["compound_base"].ProcessorID,
			"compound_op_processor_id":      r.SubProjects["compound_op"].ProcessorID,
			"euler_eth_processor_id":        r.SubProjects["euler_eth"].ProcessorID,
			"zerolend_eth_processor_id":     r.SubProjects["zerolend_eth"].ProcessorID,
			"zerolend_line_processor_id":    r.SubProjects["zerolend_line"].ProcessorID,
			"processor_id":                  r.ProcessorID,

			"sentio_events":                  w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"morpho_sentio_events":           w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["morpho"]),
			"morpho_base_weth_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["morpho_base_weth"]),
			"morpho_base_usdc_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["morpho_base_usdc"]),
			"silo_arb_sentio_events":         w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_arb"]),
			"silo_main_sentio_events":        w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_main"]),
			"silo_op_sentio_events":          w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_op"]),
			"dolomite_arb_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["dolomite_arb"]),
			"compound_eth_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_eth"]),
			"compound_arb_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_arb"]),
			"compound_base_sentio_events":    w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_base"]),
			"compound_op_sentio_events":      w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["compound_op"]),
			"euler_eth_sentio_events":        w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["euler_eth"]),
			"zerolend_eth_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["zerolend_eth"]),
			"zerolend_line_sentio_events":    w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["zerolend_line"]),
		})
		return builder.FormatSQLTemplate(renzoProjectStatusCTEQuery, map[string]any{
			"project":           "'" + r.FullName + "'",
			"processor_version": uint32(r.Version),
			"el_points_column":  elPointsColumn,
			"ez_points_column":  ezPointsColumn,
			"mel_points_column": melPointsColumn,
			"sym_points_column": symPointsColum,
			"cte_query":         cteQuery,
		})
	case "renzo_contango_pzeth_ethereum_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoContangoPzethEthereumPointsTable, map[string]any{
			"zerolend_pzeth_processor_id": r.SubProjects["zerolend_pzeth"].ProcessorID,
			"euler_pzeth_processor_id":    r.SubProjects["euler_pzeth"].ProcessorID,
			"silo_pzeth_processor_id":     r.SubProjects["silo_pzeth"].ProcessorID,
			"processor_id":                r.ProcessorID,

			"sentio_events":                w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"zerolend_pzeth_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["zerolend_pzeth"]),
			"euler_pzeth_sentio_events":    w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["euler_pzeth"]),
			"silo_pzeth_sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["silo_pzeth"]),
		})
		return builder.FormatSQLTemplate(renzoProjectStatusCTEQuery, map[string]any{
			"project":           "'" + r.FullName + "'",
			"processor_version": uint32(r.Version),
			"el_points_column":  elPointsColumn,
			"ez_points_column":  ezPointsColumn,
			"mel_points_column": melPointsColumn,
			"sym_points_column": symPointsColum,
			"cte_query":         cteQuery,
		})
	case "renzo_beefy_pendlesept26_magpie_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoBeefyPendleSept26MagpiePointsTable, map[string]any{
			"processor_id":                          r.ProcessorID,
			"pendle_arbitrum_sept2024_processor_id": r.SubProjects["pendle_arbitrum_sept2024"].ProcessorID,
			"sentio_events":                         w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
		return builder.FormatSQLTemplate(renzoProjectStatusCTEQuery, map[string]any{
			"project":           "'" + r.FullName + "'",
			"processor_version": uint32(r.Version),
			"el_points_column":  elPointsColumn,
			"ez_points_column":  ezPointsColumn,
			"mel_points_column": melPointsColumn,
			"sym_points_column": symPointsColum,
			"cte_query":         cteQuery,
		})
	case "renzo_beefy_pendlesept26_points_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoBeefyPendleSept26PointsPointsTable, map[string]any{
			"processor_id":                          r.ProcessorID,
			"pendle_arbitrum_sept2024_processor_id": r.SubProjects["pendle_arbitrum_sept2024"].ProcessorID,
			"sentio_events":                         w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
		return builder.FormatSQLTemplate(renzoProjectStatusCTEQuery, map[string]any{
			"project":           "'" + r.FullName + "'",
			"processor_version": uint32(r.Version),
			"el_points_column":  elPointsColumn,
			"ez_points_column":  ezPointsColumn,
			"mel_points_column": melPointsColumn,
			"sym_points_column": symPointsColum,
			"cte_query":         cteQuery,
		})
	case "renzo_beefy_pendle_27jun24_points_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoBeefyPendle27Jun24PointsPointsTable, map[string]any{
			"processor_id":                        r.ProcessorID,
			"pendle_points_arbitrum_processor_id": r.SubProjects["pendle_points_arbitrum"].ProcessorID,
			"sentio_events":                       w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
		return builder.FormatSQLTemplate(renzoProjectStatusCTEQuery, map[string]any{
			"project":           "'" + r.FullName + "'",
			"processor_version": uint32(r.Version),
			"el_points_column":  elPointsColumn,
			"ez_points_column":  ezPointsColumn,
			"mel_points_column": melPointsColumn,
			"sym_points_column": symPointsColum,
			"cte_query":         cteQuery,
		})
	case "renzo_cian_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoCianPointsTable, map[string]any{
			"processor_id":                r.ProcessorID,
			"aave_ethereum_processor_id":  r.SubProjects["aave_ethereum"].ProcessorID,
			"sentio_events":               w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"aave_ethereum_sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["aave_ethereum"]),
		})
		return builder.FormatSQLTemplate(renzoProjectStatusCTEQuery, map[string]any{
			"project":           "'" + r.FullName + "'",
			"processor_version": uint32(r.Version),
			"el_points_column":  elPointsColumn,
			"ez_points_column":  ezPointsColumn,
			"mel_points_column": melPointsColumn,
			"sym_points_column": symPointsColum,
			"cte_query":         cteQuery,
		})
	case "renzo_fuel_points_table.sql":
		cteQuery := builder.FormatSQLTemplate(renzoFuelPointsTable, map[string]any{
			"processor_id":           r.ProcessorID,
			"sentio_events":          w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
			"fuel_evm_mapping_table": w.getSubgraphEntityTable(int32(w.config.ClickhouseShardingIndex), r.SubProjects["fuel_evm_mapping"], "Account"),
		})
		return builder.FormatSQLTemplate(renzoProjectStatusCTEQuery, map[string]any{
			"project":           "'" + r.FullName + "'",
			"processor_version": uint32(r.Version),
			"el_points_column":  elPointsColumn,
			"ez_points_column":  ezPointsColumn,
			"mel_points_column": melPointsColumn,
			"sym_points_column": symPointsColum,
			"cte_query":         cteQuery,
		})
	}
	return ""
}

func (r RenzoProject) ToQuery(w *renzoWatcher, version int) string {
	if strings.HasSuffix(r.Schema.Table, ".sql") {
		return r.ToCustomQuery(w)
	}

	var tables []string
	var aggregations []string
	if len(r.Schema.MultiAggregations) > 0 {
		for _, multiAggr := range r.Schema.MultiAggregations {
			tables = append(tables, "'"+multiAggr.Table+"'")
			for _, aggr := range multiAggr.Aggregations {
				aggregations = append(aggregations, "'"+aggr.Alias+"'")
				switch strings.ToLower(aggr.Type) {
				case "sum":
					aggregations = append(aggregations, "sum("+aggr.Column+")")
				case "last":
					aggregations = append(aggregations, "argMax("+aggr.Column+", tuple(block_number,log_index))")
				case "any":
					aggregations = append(aggregations, "any("+aggr.Column+")")
				}
			}
		}
	} else {
		if r.Schema.Table == "" || len(r.Schema.Aggregations) == 0 {
			return ""
		}
		tables = append(tables, "'"+r.Schema.Table+"'")
		for _, aggr := range r.Schema.Aggregations {
			aggregations = append(aggregations, "'"+aggr.Alias+"'")
			switch strings.ToLower(aggr.Type) {
			case "sum":
				aggregations = append(aggregations, "sum("+aggr.Column+")")
			case "last":
				aggregations = append(aggregations, "argMax("+aggr.Column+", tuple(block_number,log_index))")
			case "any":
				aggregations = append(aggregations, "any("+aggr.Column+")")
			}
		}
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	var stmt string
	switch version {
	case 1:
		stmt = renzoProjectQuery
	case 2:
		stmt = renzoProjectQueryV2
	}
	return builder.FormatSQLTemplate(stmt, map[string]any{
		"id_column":     r.Schema.IDColumn,
		"project":       "'" + r.FullName + "'",
		"aggregations":  strings.Join(aggregations, ", "),
		"processor_id":  r.ProcessorID,
		"table":         strings.Join(tables, ","),
		"filters":       filter,
		"sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r RenzoProject) ToStatusQuery(w *renzoWatcher) string {
	if strings.HasSuffix(r.Schema.Table, ".sql") {
		return r.ToCustomStatusQuery(w)
	}

	var tables []string
	var elPointsColumn, ezPointsColumn, melPointsColumn, symPointsColumn = "0", "0", "0", "0"
	if len(r.Schema.MultiAggregations) > 0 {
		for _, multiAggr := range r.Schema.MultiAggregations {
			tables = append(tables, "'"+multiAggr.Table+"'")
			for _, aggr := range multiAggr.Aggregations {
				switch aggr.Alias {
				case "el":
					elPointsColumn = aggr.Column
				case "ez":
					ezPointsColumn = aggr.Column
				case "mel":
					melPointsColumn = aggr.Column
				case "sym":
					symPointsColumn = aggr.Column
				}
			}
		}
	} else {
		if r.Schema.Table == "" || len(r.Schema.Aggregations) == 0 {
			return ""
		}
		tables = append(tables, "'"+r.Schema.Table+"'")
		for _, aggr := range r.Schema.Aggregations {
			switch aggr.Alias {
			case "el":
				elPointsColumn = aggr.Column
			case "ez":
				ezPointsColumn = aggr.Column
			case "mel":
				melPointsColumn = aggr.Column
			case "sym":
				symPointsColumn = aggr.Column
			}
		}
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	return builder.FormatSQLTemplate(renzoProjectStatusQuery, map[string]any{
		"project":           "'" + r.FullName + "'",
		"processor_version": uint32(r.Version),
		"processor_id":      r.ProcessorID,
		"table":             strings.Join(tables, ","),
		"el_points_column":  elPointsColumn,
		"ez_points_column":  ezPointsColumn,
		"mel_points_column": melPointsColumn,
		"sym_points_column": symPointsColumn,
		"filters":           filter,
		"sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r RenzoProject) Run(ctx context.Context, w *renzoWatcher, indexMap map[string]map[string]map[string]float64) (map[string]map[string]float64, error) {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("project", r.FullName)
	stmt := r.ToQuery(w, 2)
	if stmt == "" {
		logger.Infof("project query is empty, will skip it")
		return nil, nil
	}

	logger = logger.With("stmt", stmt)
	tm := timer.NewTimer()
	totalTimer := tm.Start("total")
	// logger.Infof("start to run project fetcher, stmt: %s", stmt)

	queryTimer := tm.Start("query")
	result := make(map[string]map[string]float64)
	logger = logger.With("stmt", stmt)
	rows, err := w.conn.Query(ctx, stmt)
	if err != nil {
		logger.Warnfe(err, "failed to run project fetcher")
		if w.allowSkip {
			return result, nil
		}
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()
	_ = queryTimer.End()

	indexTimer := tm.Start("index")
	for rows.Next() {
		var (
			id, placeHolder string
			aggregateMap    map[string]decimal.Decimal
		)
		if strings.HasSuffix(r.Schema.Table, ".sql") {
			if err := rows.Scan(&id, &placeHolder, &aggregateMap); err != nil {
				logger.Fatalfe(err, "failed to scan project fetcher result")
				return nil, err
			}
		} else {
			if err := rows.Scan(&id, &aggregateMap); err != nil {
				logger.Fatalfe(err, "failed to scan project fetcher result")
				return nil, err
			}
		}

		if result[id] == nil {
			result[id] = make(map[string]float64)
		}
		if indexMap != nil && indexMap[id] == nil {
			indexMap[id] = make(map[string]map[string]float64)
		}
		for k, v := range aggregateMap {
			result[id][k], _ = v.Float64()
			if indexMap[id][r.FullName] == nil {
				indexMap[id][r.FullName] = make(map[string]float64)
			}
			indexMap[id][r.FullName][k] = result[id][k]
		}
	}
	if err := rows.Err(); err != nil {
		logger.Fatalfe(err, "failed to iterate project fetcher result")
		return nil, err
	}
	_ = indexTimer.End()
	totalUsed := totalTimer.End()
	logger.Infof("project run completed, total user: %v, total used: %s, analytics: %s",
		len(result), totalUsed.String(), tm.ReportDistribution("total", "query,index"))
	return result, nil
}

type renzoWatcher struct {
	*baseContext
	projects map[string]RenzoProject
}

func NewRenzoWatcher(ctx context.Context, chRemotePatternMap CHRemotePatternMap,
	renzoConfig WatcherConfig, viewMode bool) (MvWatcher, error) {
	r := &renzoWatcher{
		baseContext: &baseContext{
			name:               "renzo",
			config:             renzoConfig,
			mvHash:             "",
			results:            make(map[string]map[string]map[string]float64),
			chRemotePatternMap: chRemotePatternMap,
			allowSkip:          renzoConfig.AllowSkip,
		},
	}
	if viewMode {
		r.setupClickhouseInViewMode(ctx, r.config.ClickhouseDSN)
		return r, nil
	}
	if err := r.setupClickhouse(ctx, r.config.ClickhouseDSN); err != nil {
		return nil, err
	}
	if err := r.baseContext.setupClickhouseReplica(ctx, r.config.ClickhouseReplicaDSNs); err != nil {
		return nil, err
	}
	if err := r.createIfNotExists(ctx,
		r.formatCreateStmtTpl(renzoPointsCreateStmt, r.config.EncryptTargetTable(r.name), false),
		r.formatCreateStmtTpl(renzoPointsStatusCreateStmt, r.config.EncryptTargetStatusTable(r.name), false),
		r.formatCreateStmtTpl(renzoPointsCreateStmtV2, r.config.EncryptTableBySuffix(r.name, "v2"), false),
		r.formatCreateStmtTpl(mvMaintainerCreateStmt, r.config.MvMaintainerTable, true)); err != nil {
		return nil, err
	}
	if err := r.setupPostgres(ctx, r.config.PgDSN); err != nil {
		return nil, err
	}
	if err := r.refreshProjects(ctx); err != nil {
		return nil, err
	}
	return r, nil
}

func (w *renzoWatcher) refreshProjects(ctx context.Context) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name)
	projects := make(map[string]RenzoProject)
	var projectArgs []string
	start := time.Now()
	for _, pattern := range w.config.Schema {
		for _, projectName := range pattern.Projects {
			slug, version, processorID, sharding, processor, err := w.fetchData(ctx, projectName)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					logger.Fatalfe(err, "failed to fetch project: %s", projectName)
					return err
				}
				logger.Infof("project not found: %s, will skip it", projectName)
				continue
			}
			projects[projectName] = RenzoProject{
				FullName:          slug,
				Version:           version,
				ProcessorID:       processorID,
				ProcessorSharding: sharding,
				Schema: &Schema{
					Table:    pattern.Table,
					IDColumn: pattern.IDColumn,
				},
				SubProjects: make(map[string]RenzoProject),
				Processor:   processor,
			}
			if subProjects, ok := kSubProjects[pattern.Table]; ok {
				for name, subProject := range subProjects {
					slug, version, processorID, sharding, processor, err := w.fetchData(ctx, subProject)
					if err != nil {
						if !errors.Is(err, gorm.ErrRecordNotFound) {
							logger.Fatalfe(err, "failed to fetch project: %s", projectName)
							return err
						}
						return err
					}
					projects[projectName].SubProjects[name] = RenzoProject{
						FullName:          slug,
						Version:           version,
						ProcessorID:       processorID,
						ProcessorSharding: sharding,
						Processor:         processor,
					}
				}
			}
			for _, aggr := range pattern.Aggregations {
				projects[projectName].Schema.Aggregations = append(projects[projectName].Schema.Aggregations, aggr.Copy())
			}
			for _, filter := range pattern.Filters {
				projects[projectName].Schema.Filters = append(projects[projectName].Schema.Filters, filter.Copy())
			}
			for _, multiAggr := range pattern.MultiAggregations {
				m := SchemaMultiAggregation{
					Table: multiAggr.Table,
				}
				for _, aggr := range multiAggr.Aggregations {
					m.Aggregations = append(m.Aggregations, aggr.Copy())
				}
				projects[projectName].Schema.MultiAggregations = append(projects[projectName].Schema.MultiAggregations, m)
			}
			args, err := json.Marshal(projects[projectName])
			if err != nil {
				logger.Fatalfe(err, "failed to marshal project: %s", projectName)
				return err
			}
			projectArgs = append(projectArgs, string(args))
		}
	}
	w.calculateHash(ctx, projectArgs)
	w.projects = projects
	logger = logger.With("mv_hash", w.mvHash)
	if len(projects) == 0 {
		logger.Warnf("no projects found, suspend the watcher")
		return errors.Errorf("no projects found")
	}
	logger.Infof("renzo refresh project completed, count: %d, cost: %.2fs",
		len(projects), time.Since(start).Seconds())
	return nil
}

func (w *renzoWatcher) ToQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		queries = append(queries, project.ToQuery(w, 1))
	}
	return builder.FormatSQLTemplate(renzoUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *renzoWatcher) ToQueryV2() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		queries = append(queries, project.ToQuery(w, 2))
	}
	return strings.Join(queries, " UNION ALL ")
}

func (w *renzoWatcher) ToStatusQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		queries = append(queries, project.ToStatusQuery(w))
	}
	return builder.FormatSQLTemplate(renzoStatusUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *renzoWatcher) Replace(ctx context.Context) (bool, error) {
	if err := w.refreshProjects(ctx); err != nil {
		return false, errors.Wrap(err, "failed to refresh projects")
	}

	if len(w.projects) == 0 {
		return false, nil
	}

	ctx, logger := log.FromContext(ctx)
	now := time.Now()
	tmpSuffix := fmt.Sprintf("tmp_%d", time.Now().Unix())
	tmpTableName := w.config.EncryptTableBySuffix(w.name, tmpSuffix)
	if err := w.createIfNotExists(ctx,
		w.formatCreateStmtTpl(renzoPointsCreateStmtV2, tmpTableName, true)); err != nil {
		return false, err
	}
	logger = logger.With("name", w.name, "table", tmpTableName)

	tm := timer.NewTimer()
	totalTimer := tm.Start("total")
	readTimer := tm.Start("read")
	var aggregateMap = make(map[string]map[string]map[string]float64)
	for _, project := range w.projects {
		_, err := project.Run(ctx, w, aggregateMap)
		if err != nil {
			logger.Errorfe(err, "failed to run project: %s", project.FullName)
			return false, err
		}
	}
	readUsed := readTimer.End()
	logger.Infof("read used: %s", readUsed.String())

	writeTimer := tm.Start("write")
	var round int
	datas := lo.MapToSlice(aggregateMap, func(id string, points map[string]map[string]float64) PointsData {
		if points == nil {
			points = make(map[string]map[string]float64)
		}
		if points["__sentio_capture_time__"] == nil {
			points["__sentio_capture_time__"] = map[string]float64{
				"timestamp": float64(now.UnixMilli()),
			}
		} else {
			points["__sentio_capture_time__"]["timestamp"] = float64(now.UnixMilli())
		}
		pointsJSON, err := sonic.Marshal(points)
		if err != nil {
			logger.Errorfe(err, "failed to marshal points")
			return PointsData{}
		}
		return PointsData{
			ID:         id,
			PointsJSON: string(pointsJSON),
		}
	})
	chunks := lo.Chunk(datas, insertBatchSize)
	for _, chunk := range chunks {
		round++
		if err := w.processBatchData(ctx, tmpTableName, chunk); err != nil {
			logger.Errorfe(err, "failed to process batch data")
			return false, err
		} else {
			if round%100 == 0 {
				logger.Infof("inserted %d users", round*insertBatchSize)
			}
		}
	}
	writeUsed := writeTimer.End()
	totalUsed := totalTimer.End()
	logger.Infof("write used: %s", writeUsed.String())

	replaceTimer := tm.Start("replace")
	if err := w.conn.Exec(ctx, "EXCHANGE TABLES "+w.config.EncryptTableBySuffix(w.name, "v2")+" AND "+tmpTableName+" "+w.clusterArgv); err != nil {
		logger.Errorfe(err, "failed to exchange table")
		return false, err
	}
	if err := w.conn.Exec(ctx, "DROP TABLE IF EXISTS "+tmpTableName+w.clusterArgv); err != nil {
		logger.Errorfe(err, "failed to drop table")
		return false, err
	}
	_ = replaceTimer.End()

	cleanupTimer := tm.Start("cleanup")
	_ = w.Cleanup(ctx, w.config.TargetTable+"_tmp_")
	_ = cleanupTimer.End()

	logger.Infof("run local completed, total users: %d, total used: %s, analytics: %s",
		len(aggregateMap), totalUsed.String(), tm.ReportDistribution("total", "read,write,replace,cleanup"))
	return true, nil
}

func (w *renzoWatcher) Mapper() []sqllib.TableMapper {
	return []sqllib.TableMapper{
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetTableMv(),
			w.config.EncryptTargetTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetStatusTableMv(),
			w.config.EncryptTargetStatusTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("v2"),
			w.config.EncryptTableBySuffix(w.name, "v2"), w.config.ClickhouseShardingIndex, w.connOpt),
	}
}
