package refresh

import (
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/timer"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	"sentioxyz/sentio/service/processor/models"

	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

const (
	holdingBalanceColumn = "holding_balance"
)

type lombardWeighted struct {
	balanceThreshold  float64
	durationThreshold float64
	weighted          float64
}

var lombardTierWeighted = map[string]lombardWeighted{
	"silver": {
		balanceThreshold:  0.002,
		durationThreshold: 864000,
		weighted:          1.1,
	},
	"gold": {
		balanceThreshold:  0.01,
		durationThreshold: 1728000,
		weighted:          1.2,
	},
	"platinum": {
		balanceThreshold:  0.02,
		durationThreshold: 2592000,
		weighted:          1.3,
	},
	"diamond": {
		balanceThreshold:  0.1,
		durationThreshold: 2592000,
		weighted:          1.4,
	},
}

//go:embed sql/lombard_project_query.sql
var lombardProjectQuery string

//go:embed sql/lombard_project_status_query.sql
var lombardProjectStatusQuery string

//go:embed sql/lombard_points.sql
var lombardPointsCreateStmt string

//go:embed sql/lombard_union_query.sql
var lombardUnionQuery string

//go:embed sql/lombard_status_union_query.sql
var lombardStatusUnionQuery string

//go:embed sql/lombard_points_status.sql
var lombardPointsStatusCreateStmt string

//go:embed sql/lombard_holding_time.sql
var lombardHoldingsCreateStmt string

//go:embed sql/lombard_project_holding_query.sql
var lombardProjectHoldingQuery string

//go:embed sql/lombard_holding_time_query.sql
var lombardHoldingsQuery string

//go:embed sql/lombard_tier_query.sql
var lombardTierQuery string

//go:embed sql/lombard_project_tier_balance_query.sql
var lombardProjectTierBalanceQuery string

//go:embed sql/lombard_project_query_with_tier.sql
var lombardProjectQueryWithTier string

//go:embed sql/v2/lombard_project_query_v2.sql
var lombardProjectQueryV2 string

//go:embed sql/v2/lombard_points_v2.sql
var lombardPointsCreateStmtV2 string

type LombardProject struct {
	FullName          string
	Version           int32
	ProcessorID       string
	ProcessorSharding int32
	Schema            *Schema
	SubProjects       map[string]LombardProject
	Processor         *models.Processor `json:"-"`
}

func (r LombardProject) GetSharding() int32 {
	return r.ProcessorSharding
}

func (r LombardProject) GetProcessor() *models.Processor {
	return r.Processor
}

func (r LombardProject) ToHoldingQuery(w *lombardWatcher) string {
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}
	var idColumn, balanceColumn = r.Schema.IDColumn, ""
	for _, aggr := range r.Schema.Aggregations {
		if strings.ToLower(aggr.Alias) == holdingBalanceColumn {
			balanceColumn = aggr.Column
			break
		}
	}
	if balanceColumn == "" {
		return ""
	}
	return builder.FormatSQLTemplate(lombardProjectHoldingQuery, map[string]any{
		"id_column":      idColumn,
		"balance_column": balanceColumn,
		"processor_id":   r.ProcessorID,
		"table":          r.Schema.Table,
		"sentio_events":  w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r LombardProject) ToQuery(w *lombardWatcher, version int) string {
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}

	var aggregations []string
	for _, aggr := range r.Schema.Aggregations {
		if aggr.Alias == holdingBalanceColumn {
			continue
		}
		aggregations = append(aggregations, "'"+aggr.Alias+"'")
		switch strings.ToLower(aggr.Type) {
		case "sum":
			aggregations = append(aggregations, "sum("+aggr.Column+")")
		case "last":
			aggregations = append(aggregations, "argMax("+aggr.Column+", tuple(block_number,log_index))")
		case "any":
			aggregations = append(aggregations, "any("+aggr.Column+")")
		}
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	var stmt string
	switch version {
	case 1:
		stmt = lombardProjectQuery
	case 2:
		stmt = lombardProjectQueryV2
	}
	return builder.FormatSQLTemplate(stmt, map[string]any{
		"id_column":     r.Schema.IDColumn,
		"project":       "'" + r.FullName + "'",
		"aggregations":  strings.Join(aggregations, ", "),
		"processor_id":  r.ProcessorID,
		"table":         r.Schema.Table,
		"filters":       filter,
		"sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r LombardProject) ToStatusQuery(w *lombardWatcher) string {
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}

	var bPointsColumn, lPointsColumn, multiplierColumn, holdingBalanceIntegrated = "0", "0", "1", "'N'"
	for _, aggr := range r.Schema.Aggregations {
		switch aggr.Alias {
		case "bpoints":
			bPointsColumn = aggr.Column
		case "lpoints":
			lPointsColumn = aggr.Column
		case "multiplier":
			multiplierColumn = aggr.Column
		case holdingBalanceColumn:
			holdingBalanceIntegrated = "'Y'"
		}
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	return builder.FormatSQLTemplate(lombardProjectStatusQuery, map[string]any{
		"project":                    "'" + r.FullName + "'",
		"processor_version":          uint32(r.Version),
		"processor_id":               r.ProcessorID,
		"table":                      r.Schema.Table,
		"bpoints_column":             bPointsColumn,
		"lpoints_column":             lPointsColumn,
		"multiplier_column":          multiplierColumn,
		"holding_balance_integrated": holdingBalanceIntegrated,
		"filters":                    filter,
		"sentio_events":              w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r LombardProject) Run(ctx context.Context, w *lombardWatcher, indexMap map[string]map[string]map[string]float64) (map[string]map[string]float64, error) {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("project", r.FullName)
	stmt := r.ToQuery(w, 2)
	if stmt == "" {
		logger.Infof("project query is empty, will skip it")
		return nil, nil
	}

	logger = logger.With("stmt", stmt)
	tm := timer.NewTimer()
	totalTimer := tm.Start("total")
	// logger.Infof("start to run project fetcher, stmt: %s", stmt)

	queryTimer := tm.Start("query")
	result := make(map[string]map[string]float64)
	logger = logger.With("stmt", stmt)
	rows, err := w.conn.Query(ctx, stmt)
	if err != nil {
		logger.Warnfe(err, "failed to run project fetcher")
		if w.allowSkip {
			return result, nil
		}
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()
	_ = queryTimer.End()

	indexTimer := tm.Start("index")
	for rows.Next() {
		var (
			id           string
			aggregateMap map[string]*decimal.Decimal
		)
		if err := rows.Scan(&id, &aggregateMap); err != nil {
			logger.Fatalfe(err, "failed to scan project fetcher result")
			return nil, err
		}

		if result[id] == nil {
			result[id] = make(map[string]float64)
		}
		if indexMap != nil && indexMap[id] == nil {
			indexMap[id] = make(map[string]map[string]float64)
		}
		for k, v := range aggregateMap {
			if v == nil {
				continue
			}
			result[id][k], _ = v.Float64()
			if indexMap[id][r.FullName] == nil {
				indexMap[id][r.FullName] = make(map[string]float64)
			}
			indexMap[id][r.FullName][k] = result[id][k]
		}
	}
	if err := rows.Err(); err != nil {
		logger.Fatalfe(err, "failed to iterate project fetcher result")
		return nil, err
	}
	_ = indexTimer.End()
	totalUsed := totalTimer.End()
	logger.Infof("project run completed, total user: %v, total used: %s, analytics: %s",
		len(result), totalUsed.String(), tm.ReportDistribution("total", "query,index"))
	return result, nil
}

func (r LombardProject) ToTierBalanceQuery(w *lombardWatcher) string {
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}
	var idColumn, balanceColumn = r.Schema.IDColumn, ""
	for _, aggr := range r.Schema.Aggregations {
		if strings.ToLower(aggr.Alias) == holdingBalanceColumn {
			balanceColumn = aggr.Column
			break
		}
	}
	if balanceColumn == "" {
		return ""
	}
	return builder.FormatSQLTemplate(lombardProjectTierBalanceQuery, map[string]any{
		"id_column":      idColumn,
		"balance_column": balanceColumn,
		"processor_id":   r.ProcessorID,
		"table":          r.Schema.Table,
		"sentio_events":  w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r LombardProject) ToWeightedQueryWithTier(w *lombardWatcher) string {
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}
	if !r.Schema.WeightedPointsByTierEnable {
		return r.ToQuery(w, 1)
	}

	var aggregations []string
	var haveWeightedPoints bool
	for _, aggr := range r.Schema.Aggregations {
		if aggr.Alias == holdingBalanceColumn {
			continue
		}
		aggregations = append(aggregations, "'"+aggr.Alias+"'")
		switch strings.ToLower(aggr.Type) {
		case "sum":
			var pointsTpl = "toDecimal256(SUM(multiply({column}," +
				"multiIf(and(mapContains(user_tier.tier, 'diamond')," +
				"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['diamond'])),{diamond_weight}," +
				"and(mapContains(user_tier.tier, 'platinum')," +
				"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['platinum'])),{platinum_weight}," +
				"and(mapContains(user_tier.tier, 'gold')," +
				"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['gold'])),{gold_weight}," +
				"and(mapContains(user_tier.tier, 'silver')," +
				"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['silver'])),{silver_weight},1))),12)"
			aggregations = append(aggregations, builder.FormatSQLTemplate(pointsTpl, map[string]any{
				"column":          aggr.Column,
				"silver_weight":   lombardTierWeighted["silver"].weighted,
				"gold_weight":     lombardTierWeighted["gold"].weighted,
				"platinum_weight": lombardTierWeighted["platinum"].weighted,
				"diamond_weight":  lombardTierWeighted["diamond"].weighted,
			}))
			haveWeightedPoints = true
		case "unweighted_sum":
			aggregations = append(aggregations, "sum("+aggr.Column+")")
		case "last":
			aggregations = append(aggregations, "argMax("+aggr.Column+", tuple(block_number,log_index))")
		case "any":
			aggregations = append(aggregations, "any("+aggr.Column+")")
		}
	}
	if haveWeightedPoints {
		aggregations = append(aggregations, "'user_tier'")
		var tierTpl = "argMax(multiIf(and(mapContains(user_tier.tier, 'diamond')," +
			"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['diamond'])),toDecimal256(4, 12)," +
			"and(mapContains(user_tier.tier, 'platinum')," +
			"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['platinum'])),toDecimal256(3, 12)," +
			"and(mapContains(user_tier.tier, 'gold')," +
			"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['gold'])),toDecimal256(2, 12)," +
			"and(mapContains(user_tier.tier, 'silver')," +
			"toUnixTimestamp(_timestamp) > toUnixTimestamp(user_tier.tier['silver'])),toDecimal256(1, 12)," +
			"toDecimal256(0, 12)), block_number)"
		aggregations = append(aggregations, builder.FormatSQLTemplate(tierTpl, map[string]any{}))
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	return builder.FormatSQLTemplate(lombardProjectQueryWithTier, map[string]any{
		"id_column":     r.Schema.IDColumn,
		"project":       "'" + r.FullName + "'",
		"aggregations":  strings.Join(aggregations, ", "),
		"processor_id":  r.ProcessorID,
		"table":         r.Schema.Table,
		"filters":       filter,
		"sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

type lombardWatcher struct {
	*baseContext
	projects map[string]LombardProject
}

func NewLombardWatcher(ctx context.Context, chRemotePatternMap CHRemotePatternMap,
	lombardConfig WatcherConfig, viewMode bool) (MvWatcher, error) {
	r := &lombardWatcher{
		baseContext: &baseContext{
			name:               "lombard",
			config:             lombardConfig,
			mvHash:             "",
			results:            make(map[string]map[string]map[string]float64),
			chRemotePatternMap: chRemotePatternMap,
			allowSkip:          lombardConfig.AllowSkip,
		},
	}
	if viewMode {
		r.setupClickhouseInViewMode(ctx, r.config.ClickhouseDSN)
		return r, nil
	}
	if err := r.setupClickhouse(ctx, r.config.ClickhouseDSN); err != nil {
		return nil, err
	}
	if err := r.baseContext.setupClickhouseReplica(ctx, r.config.ClickhouseReplicaDSNs); err != nil {
		return nil, err
	}
	if err := r.createIfNotExists(ctx, r.formatCreateStmtTpl(lombardPointsCreateStmt, r.config.EncryptTargetTable(r.name), false),
		r.formatCreateStmtTpl(lombardPointsStatusCreateStmt, r.config.EncryptTargetStatusTable(r.name), false),
		r.formatCreateStmtTpl(lombardHoldingsCreateStmt, r.config.EncryptTargetHoldingsTable(r.name), false),
		r.formatCreateStmtTpl(lombardPointsCreateStmtV2, r.config.EncryptTableBySuffix(r.name, "v2"), false),
		r.formatCreateStmtTpl(lombardPointsCreateStmt, r.config.EncryptTableBySuffix(r.name, "weighted"), false),
		r.formatCreateStmtTpl(mvMaintainerCreateStmt, r.config.MvMaintainerTable, true),
	); err != nil {
		return nil, err
	}
	if err := r.setupPostgres(ctx, r.config.PgDSN); err != nil {
		return nil, err
	}
	if err := r.refreshProjects(ctx); err != nil {
		return nil, err
	}
	return r, nil
}

func (w *lombardWatcher) refreshProjects(ctx context.Context) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name)
	projects := make(map[string]LombardProject)
	var projectArgs []string
	start := time.Now()
	for _, pattern := range w.config.Schema {
		for _, projectName := range pattern.Projects {
			slug, version, processorID, sharding, processor, err := w.fetchData(ctx, projectName)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					logger.Fatalfe(err, "failed to fetch project: %s", projectName)
					return err
				}
				logger.Infof("project not found: %s, will skip it", projectName)
				continue
			}
			projects[projectName] = LombardProject{
				FullName:          slug,
				Version:           version,
				ProcessorID:       processorID,
				ProcessorSharding: sharding,
				Schema: &Schema{
					Table:                      pattern.Table,
					IDColumn:                   pattern.IDColumn,
					WeightedPointsByTierEnable: pattern.WeightedPointsByTierEnable,
				},
				SubProjects: make(map[string]LombardProject),
				Processor:   processor,
			}
			if subProjects, ok := kSubProjects[pattern.Table]; ok {
				for name, subProject := range subProjects {
					slug, version, processorID, sharding, processor, err = w.fetchData(ctx, subProject)
					if err != nil {
						if !errors.Is(err, gorm.ErrRecordNotFound) {
							logger.Fatalfe(err, "failed to fetch project: %s", projectName)
							return err
						}
						return err
					}
					projects[projectName].SubProjects[name] = LombardProject{
						FullName:          slug,
						Version:           version,
						ProcessorID:       processorID,
						ProcessorSharding: sharding,
						Processor:         processor,
					}
				}
			}
			for _, aggr := range pattern.Aggregations {
				projects[projectName].Schema.Aggregations = append(projects[projectName].Schema.Aggregations, aggr.Copy())
			}
			for _, filter := range pattern.Filters {
				projects[projectName].Schema.Filters = append(projects[projectName].Schema.Filters, filter.Copy())
			}
			args, err := json.Marshal(projects[projectName])
			if err != nil {
				logger.Fatalfe(err, "failed to marshal project: %s", projectName)
				return err
			}
			projectArgs = append(projectArgs, string(args))
		}
	}
	w.calculateHash(ctx, projectArgs)
	w.projects = projects
	logger = logger.With("mv_hash", w.mvHash)
	if len(projects) == 0 {
		logger.Warnf("no projects found, suspend the watcher")
		return errors.Errorf("no projects found")
	}
	logger.Infof("lombard refresh project completed, count: %d, cost: %.2fs",
		len(projects), time.Since(start).Seconds())
	return nil
}

func (w *lombardWatcher) ToQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		queries = append(queries, project.ToQuery(w, 1))
	}
	return builder.FormatSQLTemplate(lombardUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *lombardWatcher) ToStatusQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		queries = append(queries, project.ToStatusQuery(w))
	}
	return builder.FormatSQLTemplate(lombardStatusUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *lombardWatcher) ToHoldingQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		if projectHoldingQuery := project.ToHoldingQuery(w); projectHoldingQuery != "" {
			queries = append(queries, project.ToHoldingQuery(w))
		}
	}
	unionQuery := strings.Join(queries, " UNION ALL ")
	return builder.FormatSQLTemplate(lombardHoldingsQuery, map[string]any{
		"union_query": unionQuery,
	})
}

func (w *lombardWatcher) ToWeightedQueryWithTier() string {
	if len(w.projects) == 0 {
		return ""
	}

	var balanceQueries []string
	var weightedPointsQueries []string
	for _, project := range w.projects {
		balanceQueries = append(balanceQueries, project.ToTierBalanceQuery(w))
		weightedPointsQueries = append(weightedPointsQueries, project.ToWeightedQueryWithTier(w))
	}
	balanceQuery := strings.Join(balanceQueries, " UNION ALL ")
	weightedPointsQuery := strings.Join(weightedPointsQueries, " UNION ALL ")
	return builder.FormatSQLTemplate(lombardTierQuery, map[string]any{
		"tier_balance_query":          balanceQuery,
		"silver_balance_threshold":    lombardTierWeighted["silver"].balanceThreshold,
		"silver_duration_threshold":   lombardTierWeighted["silver"].durationThreshold,
		"gold_balance_threshold":      lombardTierWeighted["gold"].balanceThreshold,
		"gold_duration_threshold":     lombardTierWeighted["gold"].durationThreshold,
		"platinum_balance_threshold":  lombardTierWeighted["platinum"].balanceThreshold,
		"platinum_duration_threshold": lombardTierWeighted["platinum"].durationThreshold,
		"diamond_balance_threshold":   lombardTierWeighted["diamond"].balanceThreshold,
		"diamond_duration_threshold":  lombardTierWeighted["diamond"].durationThreshold,
		"project_query": builder.FormatSQLTemplate(lombardUnionQuery, map[string]any{
			"queries": weightedPointsQuery,
		}),
	})
}

func (w *lombardWatcher) RunLocal(ctx context.Context) error {
	if err := w.refreshProjects(ctx); err != nil {
		return errors.Wrap(err, "failed to refresh projects")
	}

	if len(w.projects) == 0 {
		return nil
	}

	ctx, logger := log.FromContext(ctx)
	tmpSuffix := fmt.Sprintf("tmp_%d", time.Now().Unix())
	tmpTableName := w.config.EncryptTableBySuffix(w.name, tmpSuffix)
	if err := w.createIfNotExists(ctx,
		w.formatCreateStmtTpl(lombardPointsCreateStmtV2, tmpTableName, false)); err != nil {
		return err
	}
	logger = logger.With("name", w.name, "table", tmpTableName)

	tm := timer.NewTimer()
	totalTimer := tm.Start("total")
	readTimer := tm.Start("read")
	var aggregateMap = make(map[string]map[string]map[string]float64)
	for _, project := range w.projects {
		_, err := project.Run(ctx, w, aggregateMap)
		if err != nil {
			logger.Errorfe(err, "failed to run project: %s", project.FullName)
			return err
		}
	}
	readUsed := readTimer.End()
	logger.Infof("read used: %s", readUsed.String())

	writeTimer := tm.Start("write")
	var round int
	datas := lo.MapToSlice(aggregateMap, func(id string, points map[string]map[string]float64) PointsData {
		pointsJSON, err := sonic.Marshal(points)
		if err != nil {
			logger.Errorfe(err, "failed to marshal points")
			return PointsData{}
		}
		return PointsData{
			ID:         id,
			PointsJSON: string(pointsJSON),
		}
	})
	chunks := lo.Chunk(datas, insertBatchSize)
	for _, chunk := range chunks {
		round++
		if err := w.processBatchData(ctx, tmpTableName, chunk); err != nil {
			logger.Errorfe(err, "failed to process batch data")
			return err
		} else {
			if round%100 == 0 {
				logger.Infof("inserted %d users", round*insertBatchSize)
			}
		}
	}
	writeUsed := writeTimer.End()
	totalUsed := totalTimer.End()
	logger.Infof("write used: %s", writeUsed.String())

	replaceTimer := tm.Start("replace")
	if err := w.conn.Exec(ctx, "DROP TABLE IF EXISTS "+w.config.EncryptTableBySuffix(w.name, "v2")); err != nil {
		logger.Errorfe(err, "failed to drop table")
		return err
	}
	if err := w.conn.Exec(ctx, "RENAME TABLE "+tmpTableName+" TO "+w.config.EncryptTableBySuffix(w.name, "v2")); err != nil {
		logger.Errorfe(err, "failed to rename table")
		return err
	}
	_ = replaceTimer.End()

	cleanupTimer := tm.Start("cleanup")
	_ = w.Cleanup(ctx, w.config.TargetTable+"_tmp_")
	_ = w.Cleanup(ctx, w.config.TargetTable+"_174")
	_ = cleanupTimer.End()

	logger.Infof("run local completed, total users: %d, total used: %s, analytics: %s",
		len(aggregateMap), totalUsed.String(), tm.ReportDistribution("total", "read,write,replace,cleanup"))
	return nil
}

func (w *lombardWatcher) Replace(ctx context.Context) (bool, error) {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name)
	if err := w.refreshProjects(ctx); err != nil {
		logger.Errorfe(err, "failed to refresh projects")
		return false, err
	}
	mvHash, err := w.getCurrentMvHash(ctx, w.config.MvMaintainerTable, w.config.EncryptTargetTableMv(w.name))
	if err != nil {
		logger.Errorfe(err, "failed to get current mv hash")
		return false, err
	}
	if mvHash == w.mvHash {
		logger.Infof("mv hash matched, no need to refresh")
		return false, nil
	}

	targetMV := w.config.EncryptTargetTableMv(w.name)
	targetWeightedMV := w.config.EncryptTableMvBySuffix(w.name, "weighted")
	targetTable := w.config.EncryptTargetTable(w.name)
	// targetWeightedTable := w.config.EncryptTableBySuffix(w.name, "weighted")
	targetStatusMV := w.config.EncryptTargetStatusTableMv(w.name)
	targetHoldingsMV := w.config.EncryptTargetHoldingsTableMv(w.name)
	if err := w.dropView(ctx, []string{targetMV, targetStatusMV, targetHoldingsMV, targetWeightedMV}); err != nil {
		logger.Errorfe(err, "failed to drop clickhouse view")
		return false, err
	}
	if err := w.clickhouseManualExec(ctx, ExecuteModeBoth,
		"CREATE MATERIALIZED VIEW IF NOT EXISTS `%s` REFRESH AFTER %s %s TO `%s` AS %s",
		targetMV, w.config.RefreshInterval, w.randomIntervalArgv(),
		targetTable, w.ToQuery(),
	); err != nil {
		logger.Errorfe(err, "failed to create clickhouse view: %s", targetMV)
		return false, err
	}
	if err := w.clickhouseManualExec(ctx, ExecuteModeBoth,
		"CREATE MATERIALIZED VIEW IF NOT EXISTS `%s` REFRESH EVERY 1 MINUTE DEPENDS ON `%s` TO `%s` AS %s",
		targetStatusMV, targetMV, w.config.EncryptTargetStatusTable(w.name), w.ToStatusQuery(),
	); err != nil {
		logger.Errorfe(err, "failed to create clickhouse view: %s", targetStatusMV)
		return false, err
	}
	if err := w.clickhouseManualExec(ctx, ExecuteModeMaster,
		"INSERT INTO `%s` VALUES ('%s', '%s', now())",
		w.config.MvMaintainerTable, targetMV, w.mvHash); err != nil {
		logger.Errorfe(err, "failed to insert clickhouse table: %s", w.config.MvMaintainerTable)
		return false, err
	}
	return true, nil
}

func (w *lombardWatcher) Mapper() []sqllib.TableMapper {
	return []sqllib.TableMapper{
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetTableMv(),
			w.config.EncryptTargetTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetStatusTableMv(),
			w.config.EncryptTargetStatusTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetHoldingsTableMv(),
			w.config.EncryptTargetHoldingsTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("weighted"),
			w.config.EncryptTableBySuffix(w.name, "weighted"), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("v2"),
			w.config.EncryptTableBySuffix(w.name, "v2"), w.config.ClickhouseShardingIndex, w.connOpt),
	}
}
