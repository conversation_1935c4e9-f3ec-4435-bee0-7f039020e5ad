package refresh

import (
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/timer"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	"sentioxyz/sentio/service/processor/models"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

//go:embed sql/swell_project_query.sql
var swellProjectQuery string

//go:embed sql/swell_project_status_query.sql
var swellProjectStatusQuery string

//go:embed sql/swell_points.sql
var swellPointsCreateStmt string

//go:embed sql/swell_union_query.sql
var swellUnionQuery string

//go:embed sql/swell_status_union_query.sql
var swellStatusUnionQuery string

//go:embed sql/swell_points_status.sql
var swellPointsStatusCreateStmt string

//go:embed sql/swell_project_complex_query_a.sql
var swellProjectComplexQueryA string

//go:embed sql/swell_project_complex_query_b.sql
var swellProjectComplexQueryB string

//go:embed sql/swell_project_complex_query_c.sql
var swellProjectComplexQueryC string

//go:embed sql/swell_project_complex_query_d.sql
var swellProjectComplexQueryD string

//go:embed sql/swell_project_complex_query_e.sql
var swellProjectComplexQueryE string

//go:embed sql/swell_project_complex_query_g.sql
var swellProjectComplexQueryG string

//go:embed sql/swell_project_complex_query_pendle.sql
var swellProjectComplexQueryPendle string

//go:embed sql/swell_project_complex_query_balancer.sql
var swellProjectComplexQueryBalancer string

//go:embed sql/swell_project_complex_query_curve.sql
var swellProjectComplexQueryCurve string

//go:embed sql/swell_project_complex_query_curve_symbiotic.sql
var swellProjectComplexQueryCurveSymbiotic string

//go:embed sql/swell_project_complex_query_pendle_merge.sql
var swellProjectComplexQueryPendleMerge string

//go:embed sql/swell_project_complex_query_pendle_merge_l2.sql
var swellProjectComplexQueryPendleMergeL2 string

//go:embed sql/swell_project_complex_query_h.sql
var swellProjectComplexQueryH string

//go:embed sql/swell_weekly_points.sql
var swellWeeklyPointsCreateStmt string

//go:embed sql/swell_weekly_query.sql
var swellWeeklyQuery string

//go:embed sql/swell_weekly_union.sql
var swellWeeklyUnionQuery string

//go:embed sql/v2/swell_project_query_v2.sql
var swellProjectQueryV2 string

//go:embed sql/v2/swell_points_v2.sql
var swellPointsCreateStmtV2 string

//go:embed sql/v2/swell_weekly_account.sql
var swellWeeklyAccountCreateStmt string

//go:embed sql/v2/swell_weekly_account_query.sql
var swellWeeklyAccountQuery string

//go:embed sql/v2/swell_protocol.sql
var swellProtocolCreateStmt string

//go:embed sql/v2/swell_protocol_query.sql
var swellProtocolQuery string

type SwellProject struct {
	FullName          string
	Version           int32
	ProcessorID       string
	ProcessorSharding int32
	Schema            *Schema
	SubProjects       map[string]SwellProject
	External          bool
	Processor         *models.Processor `json:"-"`
}

type SwellProjects []SwellProject

func (r SwellProjects) Len() int {
	return len(r)
}

func (r SwellProjects) Less(i, j int) bool {
	if r[i].External && !r[j].External {
		return true
	}
	return false
}

func (r SwellProjects) Swap(i, j int) {
	r[i], r[j] = r[j], r[i]
}

func (r SwellProject) GetSharding() int32 {
	return r.ProcessorSharding
}

func (r SwellProject) GetProcessor() *models.Processor {
	return r.Processor
}

func (r SwellProject) ToQueryV2(w *swellWatcher, category string) string {
	if !r.Schema.SwellV2Enable {
		return ""
	}

	var (
		tpl  string
		args = make(map[string]any)
	)
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}
	switch category {
	case "weekly_account":
		tpl = swellWeeklyAccountQuery
		var (
			aggregations    []string
			aggregatePoints bool
			pointsColumn    []string
			pointsAlias     string
		)
		for _, aggr := range r.Schema.Aggregations {
			var formula string
			switch strings.ToLower(aggr.Type) {
			case "sum":
				formula = "sum(" + aggr.Column + ")"
			case "last":
				formula = "argMax(" + aggr.Column + ", tuple(block_number,log_index))"
			case "any":
				formula = "any(" + aggr.Column + ")"
			case "aggregate_points":
				aggregatePoints = true
				pointsAlias = aggr.Alias
			}
			if aggr.IsPoints {
				pointsColumn = append(pointsColumn, "'"+aggr.Alias+"'")
				pointsColumn = append(pointsColumn, formula)
			}
			if formula != "" {
				aggregations = append(aggregations, formula+" AS "+aggr.Alias)
			}
		}
		if aggregatePoints {
			aggregations = append(aggregations, fmt.Sprintf("map(%s)::JSON AS %s", strings.Join(pointsColumn, ", "), pointsAlias))
		}
		args["id_column"] = r.Schema.IDColumn
		args["sub_project_column"] = r.Schema.SubProjectColumn
		args["project"] = "'" + r.FullName + "'"
		args["aggregations"] = strings.Join(aggregations, ", ")
		args["processor_id"] = r.ProcessorID
		args["table"] = r.Schema.Table
		args["sentio_events"] = w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r)
		args["filters"] = SchemaFilters(r.Schema.Filters).String()
	case "protocol":
		tpl = swellProtocolQuery
		args["project"] = "'" + r.FullName + "'"
		args["processor_id"] = r.ProcessorID
		args["table"] = "protocolStatus"
		args["sentio_events"] = w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r)
		args["filters"] = SchemaFilters(r.Schema.Filters).String()
	default:
		return ""
	}
	return builder.FormatSQLTemplate(tpl, args)
}

func (r SwellProject) ToQuery(w *swellWatcher, category string) string {
	switch {
	case r.Schema.SwellV2Enable:
		return r.ToQueryV2(w, category)
	case r.Schema.ComplexQueryEnable != "":
		var tpl string
		switch r.Schema.ComplexQueryEnable {
		case "swell_project_complex_query_a":
			tpl = swellProjectComplexQueryA
		case "swell_project_complex_query_b":
			tpl = swellProjectComplexQueryB
		case "swell_project_complex_query_c":
			tpl = swellProjectComplexQueryC
		case "swell_project_complex_query_d":
			tpl = swellProjectComplexQueryD
		case "swell_project_complex_query_e":
			tpl = swellProjectComplexQueryE
		case "swell_project_complex_query_g":
			tpl = swellProjectComplexQueryG
		case "swell_project_complex_query_pendle":
			tpl = swellProjectComplexQueryPendle
		case "swell_project_complex_query_balancer":
			tpl = swellProjectComplexQueryBalancer
		case "swell_project_complex_query_curve":
			tpl = swellProjectComplexQueryCurve
		case "swell_project_complex_query_curve_symbiotic":
			tpl = swellProjectComplexQueryCurveSymbiotic
		case "swell_project_complex_query_pendle_merge":
			tpl = swellProjectComplexQueryPendleMerge
		case "swell_project_complex_query_pendle_merge_l2":
			tpl = swellProjectComplexQueryPendleMergeL2
		case "swell_project_complex_query_h":
			tpl = swellProjectComplexQueryH
		}
		return builder.FormatSQLTemplate(tpl, map[string]any{
			"project":       "'" + r.FullName + "'",
			"processor_id":  r.ProcessorID,
			"table":         r.Schema.Table,
			"sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
	default:
		if len(r.Schema.Aggregations) == 0 {
			return ""
		}
		var aggregations []string
		for _, aggr := range r.Schema.Aggregations {
			aggregations = append(aggregations, "'"+aggr.Alias+"'")
			switch strings.ToLower(aggr.Type) {
			case "sum":
				aggregations = append(aggregations, "sum("+aggr.Column+")")
			case "last":
				aggregations = append(aggregations, "argMax("+aggr.Column+", tuple(block_number,log_index))")
			case "any":
				aggregations = append(aggregations, "any("+aggr.Column+")")
			}
		}
		var filter = SchemaFilters(r.Schema.Filters).String()
		var stmt string
		switch category {
		case "run_local":
			stmt = swellProjectQueryV2
		default:
			stmt = swellProjectQuery
		}
		return builder.FormatSQLTemplate(stmt, map[string]any{
			"id_column":     r.Schema.IDColumn,
			"project":       "'" + r.FullName + "'",
			"aggregations":  strings.Join(aggregations, ", "),
			"processor_id":  r.ProcessorID,
			"table":         r.Schema.Table,
			"filters":       filter,
			"sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
		})
	}
}

func (r SwellProject) ToWeeklyQuery(w *swellWatcher) string {
	if r.Schema.SwellV2Enable {
		return ""
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	return builder.FormatSQLTemplate(swellWeeklyQuery, map[string]any{
		"id_column":     r.Schema.IDColumn,
		"project":       "'" + r.FullName + "'",
		"points_column": "number_values[indexOf(number_names, 'el')]",
		"processor_id":  r.ProcessorID,
		"table":         r.Schema.Table,
		"filters":       filter,
		"sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r SwellProject) ToStatusQuery(w *swellWatcher) string {
	if len(r.Schema.Aggregations) == 0 || r.Schema.SwellV2Enable {
		return ""
	}

	var elPointsColumn, pearlPointsColumn = "0", "0"
	for _, aggr := range r.Schema.Aggregations {
		switch aggr.Alias {
		case "el":
			elPointsColumn = aggr.Column
		case "pearl":
			pearlPointsColumn = aggr.Column
		}
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	return builder.FormatSQLTemplate(swellProjectStatusQuery, map[string]any{
		"project":           "'" + r.FullName + "'",
		"processor_version": uint32(r.Version),
		"processor_id":      r.ProcessorID,
		"table":             r.Schema.Table,
		"el_column":         elPointsColumn,
		"pearl_column":      pearlPointsColumn,
		"filters":           filter,
		"sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

type SwellWeeklyAccountData struct {
	Week             time.Time       `ch:"week"`
	Account          string          `ch:"account"`
	Project          string          `ch:"project"`
	SubProject       string          `ch:"sub_project"`
	Balance          decimal.Decimal `ch:"balance"`
	SwellBalance     decimal.Decimal `ch:"swell_balance"`
	BalanceUSD       decimal.Decimal `ch:"balance_usd"`
	BalanceETH       decimal.Decimal `ch:"balance_eth"`
	BalanceBreakdown string          `ch:"balance_breakdown"`
	EL               decimal.Decimal `ch:"el"`
	Symbiotic        decimal.Decimal `ch:"symbiotic"`
	PointsJSON       string          `ch:"points_json"`
	APR              string          `ch:"apr"`
	DeltaSeconds     decimal.Decimal `ch:"delta_seconds"`
	BalanceHours     decimal.Decimal `ch:"balance_hours"`
}

type SwellProtocolData struct {
	Week            time.Time       `ch:"week"`
	Project         string          `ch:"project"`
	Supply          decimal.Decimal `ch:"supply"`
	SupplyBreakdown string          `ch:"supply_breakdown"`
	APR             string          `ch:"apr"`
	SupplyUSD       decimal.Decimal `ch:"supply_usd"`
	SupplyETH       decimal.Decimal `ch:"supply_eth"`
}

func (r SwellProject) Run(ctx context.Context, w *swellWatcher) ([]*SwellWeeklyAccountData, []*SwellProtocolData, error) {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("project", r.FullName)
	var (
		weeklyAccountStmt = r.ToQueryV2(w, "weekly_account")
		protocolStmt      = r.ToQueryV2(w, "protocol")
		allProtocols      = make([]*SwellProtocolData, 0)
		allAccounts       = make([]*SwellWeeklyAccountData, 0)
	)
	if weeklyAccountStmt == "" || protocolStmt == "" {
		logger.Infof("project not enable v2, will skip it")
		return nil, nil, nil
	}

	logger = logger.With("weekly", weeklyAccountStmt, "protocol", protocolStmt)
	tm := timer.NewTimer()
	totalTimer := tm.Start("total")

	{
		queryAccountTimer := tm.Start("query_account")
		rows, err := w.conn.Query(ctx, weeklyAccountStmt)
		if err != nil {
			logger.Warnfe(err, "failed to run project account fetcher")
			if w.allowSkip {
				return nil, nil, nil
			}
			return nil, nil, err
		}
		defer func() {
			_ = rows.Close()
		}()
		for rows.Next() {
			var accountData SwellWeeklyAccountData
			if err := rows.ScanStruct(&accountData); err != nil {
				logger.Fatalfe(err, "failed to scan project account fetcher result")
				return nil, nil, err
			}
			if accountData.Account == "" {
				continue
			}
			allAccounts = append(allAccounts, &accountData)
		}
		if err := rows.Err(); err != nil {
			logger.Fatalfe(err, "failed to iterate project account fetcher result")
			return nil, nil, err
		}
		_ = queryAccountTimer.End()
	}

	{
		queryProtocolTimer := tm.Start("query_protocol")
		rows, err := w.conn.Query(ctx, protocolStmt)
		if err != nil {
			logger.Warnfe(err, "failed to run project protocol fetcher")
			if w.allowSkip {
				return nil, nil, nil
			}
			return nil, nil, err
		}
		defer func() {
			_ = rows.Close()
		}()
		for rows.Next() {
			var protocolData SwellProtocolData
			if err := rows.ScanStruct(&protocolData); err != nil {
				logger.Fatalfe(err, "failed to scan project protocol fetcher result")
				return nil, nil, err
			}
			allProtocols = append(allProtocols, &protocolData)
		}
		if err := rows.Err(); err != nil {
			logger.Fatalfe(err, "failed to iterate project protocol fetcher result")
			return nil, nil, err
		}
		_ = queryProtocolTimer.End()
	}

	totalUsed := totalTimer.End()
	logger.Infof("project run completed, total rows: %v, total used: %s, analytics: %s",
		len(allAccounts)+len(allProtocols), totalUsed.String(), tm.ReportDistribution("total", "query_account,query_protocol"))
	return allAccounts, allProtocols, nil
}

type swellWatcher struct {
	*baseContext
	projects map[string]SwellProject
}

func NewSwellWatcher(ctx context.Context, chRemotePatternMap CHRemotePatternMap,
	swellConfig WatcherConfig, viewMode bool) (MvWatcher, error) {
	r := &swellWatcher{
		baseContext: &baseContext{
			name:               "swell",
			config:             swellConfig,
			mvHash:             "",
			results:            make(map[string]map[string]map[string]float64),
			chRemotePatternMap: chRemotePatternMap,
			allowSkip:          swellConfig.AllowSkip,
		},
	}
	if viewMode {
		r.setupClickhouseInViewMode(ctx, r.config.ClickhouseDSN)
		return r, nil
	}
	if err := r.setupClickhouse(ctx, r.config.ClickhouseDSN); err != nil {
		return nil, err
	}
	if err := r.baseContext.setupClickhouseReplica(ctx, r.config.ClickhouseReplicaDSNs); err != nil {
		return nil, err
	}
	if err := r.createIfNotExists(ctx,
		r.formatCreateStmtTpl(swellPointsCreateStmt, r.config.EncryptTargetTable(r.name), false),
		r.formatCreateStmtTpl(swellWeeklyPointsCreateStmt, r.config.EncryptTableBySuffix(r.name, "weekly"), false),
		r.formatCreateStmtTpl(swellPointsCreateStmtV2, r.config.EncryptTableBySuffix(r.name, "v2"), false),
		r.formatCreateStmtTpl(swellPointsStatusCreateStmt, r.config.EncryptTargetStatusTable(r.name), false),
		r.formatCreateStmtTpl(swellWeeklyAccountCreateStmt, r.config.EncryptTableBySuffix(r.name, "weekly_account_v2"), false),
		r.formatCreateStmtTpl(swellProtocolCreateStmt, r.config.EncryptTableBySuffix(r.name, "protocol_v2"), false),
		r.formatCreateStmtTpl(mvMaintainerCreateStmt, r.config.MvMaintainerTable, true)); err != nil {
		return nil, err
	}
	if err := r.setupPostgres(ctx, r.config.PgDSN); err != nil {
		return nil, err
	}
	if err := r.refreshProjects(ctx); err != nil {
		return nil, err
	}
	return r, nil
}

func (w *swellWatcher) refreshProjects(ctx context.Context) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name)
	projects := make(map[string]SwellProject)
	var projectArgs []string
	start := time.Now()
	for _, pattern := range w.config.Schema {
		for _, projectName := range pattern.Projects {
			slug, version, processorID, sharding, processor, err := w.fetchData(ctx, projectName)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					logger.Fatalfe(err, "failed to fetch project: %s", projectName)
					return err
				}
				logger.Infof("project not found: %s, will skip it", projectName)
				continue
			}
			projects[projectName] = SwellProject{
				FullName:          slug,
				Version:           version,
				ProcessorID:       processorID,
				ProcessorSharding: sharding,
				Schema: &Schema{
					Table:              pattern.Table,
					IDColumn:           pattern.IDColumn,
					SubProjectColumn:   pattern.SubProjectColumn,
					ComplexQueryEnable: pattern.ComplexQueryEnable,
					SwellV2Enable:      pattern.SwellV2Enable,
				},
				SubProjects: make(map[string]SwellProject),
				External:    sharding != int32(w.config.ClickhouseShardingIndex),
				Processor:   processor,
			}
			if subProjects, ok := kSubProjects[pattern.Table]; ok {
				for name, subProject := range subProjects {
					slug, version, processorID, sharding, processor, err = w.fetchData(ctx, subProject)
					if err != nil {
						if !errors.Is(err, gorm.ErrRecordNotFound) {
							logger.Fatalfe(err, "failed to fetch project: %s", projectName)
							return err
						}
						return err
					}
					projects[projectName].SubProjects[name] = SwellProject{
						FullName:          slug,
						Version:           version,
						ProcessorID:       processorID,
						ProcessorSharding: sharding,
						Processor:         processor,
					}
				}
			}
			for _, aggr := range pattern.Aggregations {
				projects[projectName].Schema.Aggregations = append(projects[projectName].Schema.Aggregations, aggr.Copy())
			}
			for _, filter := range pattern.Filters {
				projects[projectName].Schema.Filters = append(projects[projectName].Schema.Filters, filter.Copy())
			}
			args, err := json.Marshal(projects[projectName])
			if err != nil {
				logger.Fatalfe(err, "failed to marshal project: %s", projectName)
				return err
			}
			projectArgs = append(projectArgs, string(args))
		}
	}
	w.calculateHash(ctx, projectArgs)
	w.projects = projects
	logger = logger.With("mv_hash", w.mvHash)
	if len(projects) == 0 {
		logger.Warnf("no projects found, suspend the watcher")
		return errors.Errorf("no projects found")
	}
	logger.Infof("swell refresh project completed, count: %d, cost: %.2fs",
		len(projects), time.Since(start).Seconds())
	return nil
}

func (w *swellWatcher) ToQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var projects []SwellProject
	var queries []string
	for _, project := range w.projects {
		projects = append(projects, project)
	}
	sort.Sort(SwellProjects(projects))
	for _, project := range projects {
		if stmt := project.ToQuery(w, ""); stmt != "" {
			queries = append(queries, stmt)
		}
	}
	return builder.FormatSQLTemplate(swellUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *swellWatcher) ToWeeklyQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var projects []SwellProject
	var queries []string
	for _, project := range w.projects {
		projects = append(projects, project)
	}
	sort.Sort(SwellProjects(projects))
	for _, project := range projects {
		if stmt := project.ToWeeklyQuery(w); stmt != "" {
			queries = append(queries, stmt)
		}
	}
	return builder.FormatSQLTemplate(swellWeeklyUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *swellWatcher) ToStatusQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		if stmt := project.ToStatusQuery(w); stmt != "" {
			queries = append(queries, stmt)
		}
	}
	return builder.FormatSQLTemplate(swellStatusUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *swellWatcher) processAccountChunk(ctx context.Context, chunk []*SwellWeeklyAccountData, tmpWeeklyAccountTable string) error {
	if len(chunk) == 0 {
		return nil
	}
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name, "weekly_account", tmpWeeklyAccountTable)
	batch, err := w.conn.PrepareBatch(ctx, "INSERT INTO "+tmpWeeklyAccountTable)
	if err != nil {
		logger.Errorfe(err, "failed to prepare batch")
		return err
	}
	defer batch.Close()
	for _, account := range chunk {
		if err := batch.AppendStruct(account); err != nil {
			logger.Errorfe(err, "failed to append batch")
			return err
		}
	}
	return batch.Send()
}

func (w *swellWatcher) processProtocolChunk(ctx context.Context, chunk []*SwellProtocolData, tmpProtocolTable string) error {
	if len(chunk) == 0 {
		return nil
	}
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name, "protocol", tmpProtocolTable)
	batch, err := w.conn.PrepareBatch(ctx, "INSERT INTO "+tmpProtocolTable)
	if err != nil {
		logger.Errorfe(err, "failed to prepare batch")
		return err
	}
	defer batch.Close()
	for _, protocol := range chunk {
		if err := batch.AppendStruct(protocol); err != nil {
			logger.Errorfe(err, "failed to append batch")
			return err
		}
	}
	return batch.Send()
}

func (w *swellWatcher) Replace(ctx context.Context) (bool, error) {
	if err := w.refreshProjects(ctx); err != nil {
		return false, errors.Wrap(err, "failed to refresh projects")
	}

	if len(w.projects) == 0 {
		return false, nil
	}

	ctx, logger := log.FromContext(ctx)
	tmpSuffix := fmt.Sprintf("%d", time.Now().Unix())
	tmpWeeklyAccountTable := w.config.EncryptTableBySuffix(w.name, "tmp_account_"+tmpSuffix)
	tmpProtocolTable := w.config.EncryptTableBySuffix(w.name, "tmp_protocol_"+tmpSuffix)
	if err := w.createIfNotExists(ctx,
		w.formatCreateStmtTpl(swellWeeklyAccountCreateStmt, tmpWeeklyAccountTable, false),
		w.formatCreateStmtTpl(swellProtocolCreateStmt, tmpProtocolTable, false)); err != nil {
		return false, err
	}
	logger = logger.With("name", w.name, "weekly_account", tmpWeeklyAccountTable, "protocol", tmpProtocolTable)

	tm := timer.NewTimer()
	totalTimer := tm.Start("total")
	var projectNames []string
	for _, project := range w.projects {
		projectNames = append(projectNames, project.FullName)
		projectTimer := tm.Start(project.FullName)
		accounts, protocols, err := project.Run(ctx, w)
		if err != nil {
			logger.Errorfe(err, "failed to run project: %s", project.FullName)
			return false, err
		}
		if accounts == nil && protocols == nil {
			_ = projectTimer.End()
			continue
		}
		var (
			accountRound  = 0
			protocolRound = 0
		)
		accountChunks := lo.Chunk(accounts, insertBatchSize)
		protocolChunks := lo.Chunk(protocols, insertBatchSize)
		for _, chunk := range accountChunks {
			accountRound++
			if err := w.processAccountChunk(ctx, chunk, tmpWeeklyAccountTable); err != nil {
				logger.Errorfe(err, "failed to process account chunk")
				return false, err
			} else {
				if accountRound%100 == 0 {
					logger.Infof("inserted %d accounts", accountRound*insertBatchSize)
				}
			}
		}
		for _, chunk := range protocolChunks {
			protocolRound++
			if err := w.processProtocolChunk(ctx, chunk, tmpProtocolTable); err != nil {
				logger.Errorfe(err, "failed to process protocol chunk")
				return false, err
			} else {
				if protocolRound%100 == 0 {
					logger.Infof("inserted %d protocols", protocolRound*insertBatchSize)
				}
			}
		}
		projectUsed := projectTimer.End()
		logger.Infof("project %s run completed, total accounts: %d, total protocols: %d, total used: %s",
			project.FullName, len(accounts), len(protocols), projectUsed.String())
	}
	replaceTimer := tm.Start("replace")
	if err := w.conn.Exec(ctx, "EXCHANGE TABLES "+w.config.EncryptTableBySuffix(w.name, "weekly_account_v2")+" AND "+tmpWeeklyAccountTable); err != nil {
		logger.Errorfe(err, "failed to exchange table")
		return false, err
	}
	if err := w.conn.Exec(ctx, "EXCHANGE TABLES "+w.config.EncryptTableBySuffix(w.name, "protocol_v2")+" AND "+tmpProtocolTable); err != nil {
		logger.Errorfe(err, "failed to exchange table")
		return false, err
	}
	if err := w.conn.Exec(ctx, "DROP TABLE IF EXISTS "+tmpWeeklyAccountTable); err != nil {
		logger.Errorfe(err, "failed to drop table")
		return false, err
	}
	if err := w.conn.Exec(ctx, "DROP TABLE IF EXISTS "+tmpProtocolTable); err != nil {
		logger.Errorfe(err, "failed to drop table")
		return false, err
	}
	_ = replaceTimer.End()

	cleanupTimer := tm.Start("cleanup")
	_ = w.Cleanup(ctx, w.config.TargetTable+"_tmp_")
	_ = cleanupTimer.End()

	totalUsed := totalTimer.End()
	logger.Infof("run local completed, total used: %s, analytics: %s",
		totalUsed.String(), tm.ReportDistribution("total", strings.Join(projectNames, ",")+",replace"))
	return true, nil
}

func (w *swellWatcher) Mapper() []sqllib.TableMapper {
	return []sqllib.TableMapper{
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetTableMv(),
			w.config.EncryptTargetTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetStatusTableMv(),
			w.config.EncryptTargetStatusTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("weekly"),
			w.config.EncryptTableBySuffix(w.name, "weekly"), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("weekly_account_v2"),
			w.config.EncryptTableBySuffix(w.name, "weekly_account_v2"), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("protocol_v2"),
			w.config.EncryptTableBySuffix(w.name, "protocol_v2"), w.config.ClickhouseShardingIndex, w.connOpt),
	}
}
