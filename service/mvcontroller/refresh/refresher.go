package refresh

import (
	"context"
	_ "embed"
	"fmt"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/sqllib"

	"github.com/samber/lo"
	"gopkg.in/yaml.v3"
)

//go:embed data/sea_config.yaml
var defaultSeaConfig []byte

type Task struct {
	Name   string        `yaml:"name"`
	Config WatcherConfig `yaml:"config"`
	Orgs   []string      `yaml:"orgs"`
}

type Sharding struct {
	Index        int    `yaml:"index"`
	Host         string `yaml:"host"`
	ExternalHost string `yaml:"external_host"`
	Database     string `yaml:"database"`
	Table        string `yaml:"table"`
	Username     string `yaml:"username"`
	Password     string `yaml:"password"`
	Cluster      string `yaml:"cluster"`
}

type ShardingSlice []Sharding

type RemotePattern struct {
	Host         string `yaml:"host"`
	ExternalHost string `yaml:"external_host"`
	Database     string `yaml:"database"`
	Table        string `yaml:"table"`
	Username     string `yaml:"username"`
	Password     string `yaml:"password"`
	Cluster      string `yaml:"cluster"`
}

func (r RemotePattern) ToRemoteTable(crossCluster bool) string {
	if !crossCluster {
		return fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
			r.Host, r.Database, r.Table, r.Username, r.Password)
	} else {
		return fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
			r.ExternalHost, r.Database, r.Table, r.Username, r.Password)
	}
}

func (r RemotePattern) ToRemoteSpecificTable(crossCluster bool, table string) string {
	if !crossCluster {
		return fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
			r.Host, r.Database, table, r.Username, r.Password)
	} else {
		return fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
			r.ExternalHost, r.Database, table, r.Username, r.Password)
	}
}

func (r RemotePattern) ToRemoteSpecificDatabaseTable(crossCluster bool, database, table string) string {
	if !crossCluster {
		return fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
			r.Host, database, table, r.Username, r.Password)
	} else {
		return fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
			r.ExternalHost, database, table, r.Username, r.Password)
	}
}

type CHRemotePatternMap map[int]RemotePattern

func (s ShardingSlice) ToCHRemotePatternMap() CHRemotePatternMap {
	m := make(map[int]RemotePattern)
	for _, sharding := range s {
		m[sharding.Index] = RemotePattern{
			Host:         sharding.Host,
			Database:     sharding.Database,
			Table:        sharding.Table,
			Username:     sharding.Username,
			Password:     sharding.Password,
			ExternalHost: sharding.ExternalHost,
			Cluster:      sharding.Cluster,
		}
	}
	return m
}

type Config struct {
	Tasks              []Task     `yaml:"tasks"`
	ScanInterval       string     `yaml:"scan_interval"`
	RunLocalInterval   string     `yaml:"run_local_interval"`
	RunLocalTaskNames  []string   `yaml:"run_local_task_names"`
	ClickhouseSharding []Sharding `yaml:"clickhouse_sharding"`
}

var kSubProjects = map[string]map[string]string{
	"renzo_contango_points_table.sql": {
		"morpho":           "renzo/morpho-points",
		"morpho_base_weth": "renzo/morpho-ezeth-weth-points-base",
		"morpho_base_usdc": "renzo/morpho-ezeth-usdc-points-base",
		"silo_arb":         "renzo/silo-points-newdb-arbitrum",
		"silo_main":        "renzo/silo-points-newdb-ethereum",
		"silo_op":          "renzo/silo-points-newdb-optimism",
		"dolomite_arb":     "renzo/dolomite-points-arbitrum",
		"compound_eth":     "renzo/compound-points-ethereum",
		"compound_arb":     "renzo/compound-points-arbitrum",
		"compound_base":    "renzo/compound-points-base",
		"compound_op":      "renzo/compound-points-optimism",
		"euler_eth":        "renzo/euler-ezeth-ethereum",
		"zerolend_eth":     "renzo/zerolend-points-eth",
		"zerolend_line":    "renzo/zerolend-points-linea",
	},
	"renzo_contango_pzeth_ethereum_points_table.sql": {
		"zerolend_pzeth": "renzo/zerolend-points-pzeth",
		"silo_pzeth":     "renzo/silo-pz-ethereum",
		"euler_pzeth":    "renzo/euler-pzeth-ethereum",
	},
	"renzo_beefy_pendlesept26_magpie_points_table.sql": {
		"pendle_arbitrum_sept2024": "renzo/pendle-arbitrum-sept2024",
	},
	"renzo_beefy_pendlesept26_points_points_table.sql": {
		"pendle_arbitrum_sept2024": "renzo/pendle-arbitrum-sept2024",
	},
	"renzo_beefy_pendle_27jun24_points_points_table.sql": {
		"pendle_points_arbitrum": "renzo/pendle-points-arbitrum",
	},
	"renzo_cian_points_table.sql": {
		"aave_ethereum": "renzo/aave-ethereum",
	},
	"renzo_fuel_points_table.sql": {
		"fuel_evm_mapping": "renzo/fuel-evm-mapping-2",
	},
}

type MvRefresher interface {
	Run() error
	AllMappers() []sqllib.TableMapper
	MappersByName(name string) []sqllib.TableMapper
	Mappers() map[string][]sqllib.TableMapper
}

type MvWatcher interface {
	Mapper() []sqllib.TableMapper
	Replace(ctx context.Context) (bool, error)
}

type mvRefresher struct {
	ctx             context.Context
	config          Config
	replaceCount    map[string]*atomic.Uint64
	watchers        map[string]MvWatcher
	tableMappers    map[string][]sqllib.TableMapper
	allTableMappers []sqllib.TableMapper
	dryRun          bool
	mutex           sync.Mutex
}

type Options struct {
	DryRun            bool
	ForceAtStart      bool
	ViewMode          bool
	ForceAtStartTasks string
}

func NewMvRefresher(ctx context.Context, configPath string, options Options) MvRefresher {
	var data []byte
	var err error
	switch configPath {
	case "sentio-sea":
		data = defaultSeaConfig
	default:
		data, err = os.ReadFile(configPath)
		if err != nil {
			panic(err)
		}
	}
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		panic(err)
	}
	mvRefresher := &mvRefresher{
		ctx:          ctx,
		config:       config,
		replaceCount: make(map[string]*atomic.Uint64),
		watchers:     make(map[string]MvWatcher),
		tableMappers: make(map[string][]sqllib.TableMapper),
		dryRun:       options.DryRun,
	}
	var failedInitial []string
	remotePatternMap := ShardingSlice(config.ClickhouseSharding).ToCHRemotePatternMap()
	for _, task := range config.Tasks {
		var watcher MvWatcher
		switch task.Name {
		case "renzo":
			watcher, err = NewRenzoWatcher(ctx, remotePatternMap, task.Config, options.ViewMode)
		case "lombard":
			watcher, err = NewLombardWatcher(ctx, remotePatternMap, task.Config, options.ViewMode)
		case "swell":
			watcher, err = NewSwellWatcher(ctx, remotePatternMap, task.Config, options.ViewMode)
		case "resolv":
			watcher, err = NewResolvWatcher(ctx, remotePatternMap, task.Config, options.ViewMode)
		default:
			panic("unknown watcher")
		}
		if err != nil {
			failedInitial = append(failedInitial, fmt.Sprintf("(%s: %s)", task.Name, err.Error()))
			continue
		}
		mvRefresher.watchers[task.Name] = watcher
		mvRefresher.replaceCount[task.Name] = new(atomic.Uint64)
		mvRefresher.replaceCount[task.Name].Store(0)
		mvRefresher.allTableMappers = append(mvRefresher.allTableMappers, watcher.Mapper()...)
		for _, org := range task.Orgs {
			mvRefresher.tableMappers[org] = append(mvRefresher.tableMappers[org], watcher.Mapper()...)
		}
	}
	if len(failedInitial) > 0 {
		log.Warnf("failed to initialize watchers: %v, skip them", failedInitial)
	}
	if options.ViewMode {
		log.Infof("view mode, skip initialization")
	}
	return mvRefresher
}

func (r *mvRefresher) runTaskOnce(ctx context.Context, name string, watcher MvWatcher) error {
	success, err := watcher.Replace(ctx)
	if err != nil {
		return err
	}
	if success {
		r.mutex.Lock()
		r.replaceCount[name].Add(1)
		r.mutex.Unlock()
	}
	return nil
}

func (r *mvRefresher) Run() error {
	ctx, logger := log.FromContext(r.ctx)
	scanInterval, err := time.ParseDuration(r.config.ScanInterval)
	if err != nil {
		return err
	}
	var runLocalInterval *time.Duration
	var runLocalTasks = lo.SliceToMap(r.config.RunLocalTaskNames, func(s string) (string, bool) {
		return s, true
	})
	if len(r.config.RunLocalInterval) > 0 {
		ri, err := time.ParseDuration(r.config.RunLocalInterval)
		if err != nil {
			return err
		}
		runLocalInterval = &ri
	}

	go func() {
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				var debugInfo []string
				for name, count := range r.replaceCount {
					debugInfo = append(debugInfo, fmt.Sprintf("%s: %d", name, count.Load()))
				}
				logger.Infof("sync count: %v", debugInfo)
			case <-r.ctx.Done():
				logger.Infof("context done, exit sync loop")
			default:
				time.Sleep(time.Second * 10)
			}

		}
	}()
	for name, watcher := range r.watchers {
		name := name
		watcher := watcher
		logger.Infof("starting watcher for task %s", name)
		go func() {
			interval := scanInterval
			_, local := runLocalTasks[name]
			if local {
				if runLocalInterval != nil {
					interval = *runLocalInterval
				}
			}
			_ = r.runTaskOnce(ctx, name, watcher)
			ticker := time.NewTicker(interval)
			for {
				select {
				case <-r.ctx.Done():
					logger.Infof("context done, exit watcher %s loop", name)
					return
				case <-ticker.C:
					if err := r.runTaskOnce(ctx, name, watcher); err != nil {
						logger.Errorf("failed to run task %s: %v", name, err)
					} else {
						logger.Infof("task %s run successfully", name)
					}
				}
			}
		}()
	}

	for {
		time.Sleep(time.Minute)
		logger.Infof("running...")
	}
}

func (r *mvRefresher) MappersByName(name string) []sqllib.TableMapper {
	return r.tableMappers[name]
}

func (r *mvRefresher) AllMappers() []sqllib.TableMapper {
	return r.allTableMappers
}

func (r *mvRefresher) Mappers() map[string][]sqllib.TableMapper {
	return r.tableMappers
}
