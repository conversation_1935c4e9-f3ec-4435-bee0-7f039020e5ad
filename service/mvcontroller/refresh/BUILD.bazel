load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "refresh",
    srcs = [
        "base_context.go",
        "lombard.go",
        "models.go",
        "refresher.go",
        "renzo.go",
        "resolv.go",
        "swell.go",
    ],
    embedsrcs = [
        "data/sea_config.yaml",
        "sql/lombard_holding_time.sql",
        "sql/lombard_holding_time_query.sql",
        "sql/lombard_points.sql",
        "sql/lombard_points_status.sql",
        "sql/lombard_project_holding_query.sql",
        "sql/lombard_project_query.sql",
        "sql/lombard_project_status_query.sql",
        "sql/lombard_status_union_query.sql",
        "sql/lombard_union_query.sql",
        "sql/mv_maintainer.sql",
        "sql/renzo_beefy_pendle_27jun24_points_points_table.sql",
        "sql/renzo_beefy_pendlesept26_magpie_points_table.sql",
        "sql/renzo_beefy_pendlesept26_points_points_table.sql",
        "sql/renzo_contango_points_table.sql",
        "sql/renzo_points.sql",
        "sql/renzo_points_status.sql",
        "sql/renzo_project_cte_query.sql",
        "sql/renzo_project_query.sql",
        "sql/renzo_project_status_cte_query.sql",
        "sql/renzo_project_status_query.sql",
        "sql/renzo_status_union_query.sql",
        "sql/renzo_union_query.sql",
        "sql/swell_points.sql",
        "sql/swell_points_status.sql",
        "sql/swell_project_complex_query_a.sql",
        "sql/swell_project_complex_query_b.sql",
        "sql/swell_project_complex_query_c.sql",
        "sql/swell_project_query.sql",
        "sql/swell_project_status_query.sql",
        "sql/swell_status_union_query.sql",
        "sql/swell_union_query.sql",
        "sql/lombard_project_query_with_tier.sql",
        "sql/lombard_project_tier_balance_query.sql",
        "sql/lombard_tier_query.sql",
        "sql/swell_project_complex_query_d.sql",
        "sql/renzo_contango_pzeth_ethereum_points_table.sql",
        "sql/swell_project_complex_query_e.sql",
        "sql/swell_project_complex_query_pendle_merge.sql",
        "sql/swell_project_complex_query_g.sql",
        "sql/swell_project_complex_query_pendle.sql",
        "sql/swell_project_complex_query_balancer.sql",
        "sql/swell_project_complex_query_curve.sql",
        "sql/swell_project_complex_query_curve_symbiotic.sql",
        "sql/swell_project_complex_query_pendle_merge_l2.sql",
        "sql/renzo_cian_points_table.sql",
        "sql/renzo_fuel_points_table.sql",
        "sql/swell_project_complex_query_h.sql",
        "sql/resolv_points.sql",
        "sql/resolv_points_status.sql",
        "sql/resolv_project_query.sql",
        "sql/resolv_project_status_query.sql",
        "sql/resolv_status_union_query.sql",
        "sql/resolv_union_query.sql",
        "sql/swell_weekly_points.sql",
        "sql/swell_weekly_query.sql",
        "sql/swell_weekly_union.sql",
        "sql/v2/renzo_points_v2.sql",
        "sql/v2/renzo_project_query_v2.sql",
        "sql/v2/lombard_points_v2.sql",
        "sql/v2/lombard_project_query_v2.sql",
        "sql/v2/resolv_points_v2.sql",
        "sql/v2/resolv_project_query_v2.sql",
        "sql/v2/swell_points_v2.sql",
        "sql/v2/swell_project_query_v2.sql",
        "sql/v2/swell_protocol.sql",
        "sql/v2/swell_protocol_query.sql",
        "sql/v2/swell_weekly_account.sql",
        "sql/v2/swell_weekly_account_query.sql",
    ],
    importpath = "sentioxyz/sentio/service/mvcontroller/refresh",
    visibility = ["//visibility:public"],
    deps = [
        "//common/clickhouse",
        "//common/clickhouse/builder",
        "//common/clickhouse/models",
        "//common/log",
        "//common/timer",
        "//service/analytic/sqllib",
        "//service/analytic/sqllib/mapper",
        "//service/common/repository",
        "//service/processor/models",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_pkg_errors//:errors",
        "@com_github_samber_lo//:lo",
        "@com_github_shopspring_decimal//:decimal",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_gorm_gorm//:gorm",
    ],
)

go_test(
    name = "refresh_test",
    srcs = [
        "refresher_test.go",
        "swell_test.go",
    ],
    embed = [":refresh"],
    deps = [
        "//common/log",
        "@com_github_stretchr_testify//assert",
    ],
)
