package refresh

import (
	"context"
	_ "embed"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/timer"
	"sentioxyz/sentio/service/analytic/sqllib"
	"sentioxyz/sentio/service/analytic/sqllib/mapper"
	"sentioxyz/sentio/service/processor/models"

	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

//go:embed sql/resolv_project_query.sql
var resolvProjectQuery string

//go:embed sql/resolv_project_status_query.sql
var resolvProjectStatusQuery string

//go:embed sql/resolv_points.sql
var resolvPointsCreateStmt string

//go:embed sql/resolv_union_query.sql
var resolvUnionQuery string

//go:embed sql/resolv_status_union_query.sql
var resolvStatusUnionQuery string

//go:embed sql/resolv_points_status.sql
var resolvPointsStatusCreateStmt string

//go:embed sql/v2/resolv_points_v2.sql
var resolvPointsCreateStmtV2 string

//go:embed sql/v2/resolv_project_query_v2.sql
var resolvProjectQueryV2 string

type ResolvProject struct {
	FullName          string
	Version           int32
	ProcessorID       string
	ProcessorSharding int32
	Schema            *Schema
	SubProjects       map[string]ResolvProject
	Processor         *models.Processor `json:"-"`
}

func (r ResolvProject) GetSharding() int32 {
	return r.ProcessorSharding
}

func (r ResolvProject) GetProcessor() *models.Processor {
	return r.Processor
}

type queryType string

const (
	defaultQuery  queryType = "default"
	dailySnapshot queryType = "daily_snapshot"
	to20250509    queryType = "to_20250509"
	from20250509  queryType = "from_20250509"
)

func (r ResolvProject) ToQuery(w *resolvWatcher, queryType queryType, version int) string {
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}

	var aggregations []string
	var lastAggrOnly = true
	for _, aggr := range r.Schema.Aggregations {
		aggregations = append(aggregations, "'"+aggr.Alias+"'")
		switch strings.ToLower(aggr.Type) {
		case "sum":
			aggregations = append(aggregations, "sum("+aggr.Column+")")
			lastAggrOnly = false
		case "last":
			aggregations = append(aggregations, "argMax("+aggr.Column+", tuple(block_number,log_index))")
		case "any":
			aggregations = append(aggregations, "any("+aggr.Column+")")
			lastAggrOnly = false
		case "cnt":
			aggregations = append(aggregations, "count()")
			lastAggrOnly = false
		}
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	switch queryType {
	case dailySnapshot:
		if lastAggrOnly {
			filter += " AND toTimezone(_timestamp, 'UTC') <= timestamp_add(toTimezone(toDateTime(today()), 'UTC'), interval 270 minute)"
		} else {
			filter += " AND toTimezone(_timestamp, 'UTC') > timestamp_add(toTimezone(toDateTime(yesterday()), 'UTC'), interval 270 minute) AND toTimezone(_timestamp, 'UTC') <= timestamp_add(toTimezone(toDateTime(today()), 'UTC'), interval 270 minute)"
		}
	case from20250509:
		filter += " AND _timestamp >= toDateTime64('2025-05-10 00:00:00', 3, 'UTC')"
	case to20250509:
		filter += " AND _timestamp < toDateTime64('2025-05-10 00:00:00', 3, 'UTC')"
	default:
		// do nothing
	}
	var stmt string
	switch version {
	case 1:
		stmt = resolvProjectQuery
	case 2:
		stmt = resolvProjectQueryV2
	}
	return builder.FormatSQLTemplate(stmt, map[string]any{
		"id_column":     r.Schema.IDColumn,
		"project":       "'" + r.FullName + "'",
		"aggregations":  strings.Join(aggregations, ", "),
		"processor_id":  r.ProcessorID,
		"table":         r.Schema.Table,
		"filters":       filter,
		"sentio_events": w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r ResolvProject) ToStatusQuery(w *resolvWatcher) string {
	if len(r.Schema.Aggregations) == 0 {
		return ""
	}

	var pointsColumn = "0"
	for _, aggr := range r.Schema.Aggregations {
		switch aggr.Alias {
		case "points":
			pointsColumn = aggr.Column
		}
	}
	var filter = SchemaFilters(r.Schema.Filters).String()
	return builder.FormatSQLTemplate(resolvProjectStatusQuery, map[string]any{
		"project":           "'" + r.FullName + "'",
		"processor_version": uint32(r.Version),
		"processor_id":      r.ProcessorID,
		"table":             r.Schema.Table,
		"points_column":     pointsColumn,
		"filters":           filter,
		"sentio_events":     w.getSentioEventsTable(int32(w.config.ClickhouseShardingIndex), r),
	})
}

func (r ResolvProject) Run(ctx context.Context, w *resolvWatcher, queryType queryType,
	indexMap map[string]map[string]map[string]float64) (map[string]map[string]float64, error) {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("project", r.FullName)
	stmt := r.ToQuery(w, queryType, 2)
	if stmt == "" {
		logger.Infof("project query is empty, will skip it")
		return nil, nil
	}

	logger = logger.With("stmt", stmt)
	tm := timer.NewTimer()
	totalTimer := tm.Start("total")
	// logger.Infof("start to run project fetcher, stmt: %s", stmt)

	queryTimer := tm.Start("query")
	logger = logger.With("stmt", stmt)
	result := make(map[string]map[string]float64)
	rows, err := w.conn.Query(ctx, stmt)
	if err != nil {
		logger.Warnfe(err, "failed to run project fetcher")
		if w.allowSkip {
			return result, nil
		}
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()
	_ = queryTimer.End()

	indexTimer := tm.Start("index")
	for rows.Next() {
		var (
			id           string
			aggregateMap map[string]decimal.Decimal
		)
		if err := rows.Scan(&id, &aggregateMap); err != nil {
			logger.Fatalfe(err, "failed to scan project fetcher result")
			return nil, err
		}

		if result[id] == nil {
			result[id] = make(map[string]float64)
		}
		if indexMap != nil && indexMap[id] == nil {
			indexMap[id] = make(map[string]map[string]float64)
		}
		for k, v := range aggregateMap {
			result[id][k], _ = v.Float64()
			if indexMap[id][r.FullName] == nil {
				indexMap[id][r.FullName] = make(map[string]float64)
			}
			indexMap[id][r.FullName][k] = result[id][k]
		}
	}
	if err := rows.Err(); err != nil {
		logger.Fatalfe(err, "failed to iterate project fetcher result")
		return nil, err
	}
	_ = indexTimer.End()
	totalUsed := totalTimer.End()
	logger.Infof("project run completed, total user: %v, total used: %s, analytics: %s",
		len(result), totalUsed.String(), tm.ReportDistribution("total", "query,index"))
	return result, nil
}

type resolvWatcher struct {
	*baseContext
	projects map[string]ResolvProject
}

func NewResolvWatcher(ctx context.Context, chRemotePatternMap CHRemotePatternMap,
	resolvConfig WatcherConfig, viewMode bool) (MvWatcher, error) {
	r := &resolvWatcher{
		baseContext: &baseContext{
			name:               "resolv",
			config:             resolvConfig,
			mvHash:             "",
			results:            make(map[string]map[string]map[string]float64),
			chRemotePatternMap: chRemotePatternMap,
			allowSkip:          resolvConfig.AllowSkip,
		},
	}
	if viewMode {
		r.setupClickhouseInViewMode(ctx, r.config.ClickhouseDSN)
		return r, nil
	}
	if err := r.setupClickhouse(ctx, r.config.ClickhouseDSN); err != nil {
		return nil, err
	}
	if err := r.baseContext.setupClickhouseReplica(ctx, r.config.ClickhouseReplicaDSNs); err != nil {
		return nil, err
	}
	if err := r.createIfNotExists(ctx, r.formatCreateStmtTpl(resolvPointsCreateStmt, r.config.EncryptTargetTable(r.name), false),
		r.formatCreateStmtTpl(resolvPointsStatusCreateStmt, r.config.EncryptTargetStatusTable(r.name), false),
		r.formatCreateStmtTpl(mvMaintainerCreateStmt, r.config.MvMaintainerTable, true),
	); err != nil {
		return nil, err
	}
	if err := r.setupPostgres(ctx, r.config.PgDSN); err != nil {
		return nil, err
	}
	if err := r.refreshProjects(ctx); err != nil {
		return nil, err
	}
	return r, nil
}

func (w *resolvWatcher) refreshProjects(ctx context.Context) error {
	ctx, logger := log.FromContext(ctx)
	logger = logger.With("name", w.name)
	projects := make(map[string]ResolvProject)
	var projectArgs []string
	start := time.Now()
	for _, pattern := range w.config.Schema {
		for _, projectName := range pattern.Projects {
			slug, version, processorID, sharding, processor, err := w.fetchData(ctx, projectName)
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					logger.Fatalfe(err, "failed to fetch project: %s", projectName)
					return err
				}
				logger.Infof("project not found: %s, will skip it", projectName)
				continue
			}
			projects[projectName] = ResolvProject{
				FullName:          slug,
				Version:           version,
				ProcessorID:       processorID,
				ProcessorSharding: sharding,
				Schema: &Schema{
					Table:    pattern.Table,
					IDColumn: pattern.IDColumn,
				},
				SubProjects: make(map[string]ResolvProject),
				Processor:   processor,
			}
			if subProjects, ok := kSubProjects[pattern.Table]; ok {
				for name, subProject := range subProjects {
					slug, version, processorID, sharding, processor, err = w.fetchData(ctx, subProject)
					if err != nil {
						if !errors.Is(err, gorm.ErrRecordNotFound) {
							logger.Fatalfe(err, "failed to fetch project: %s", projectName)
							return err
						}
						return err
					}
					projects[projectName].SubProjects[name] = ResolvProject{
						FullName:          slug,
						Version:           version,
						ProcessorID:       processorID,
						ProcessorSharding: sharding,
						Processor:         processor,
					}
				}
			}
			for _, aggr := range pattern.Aggregations {
				projects[projectName].Schema.Aggregations = append(projects[projectName].Schema.Aggregations, aggr.Copy())
			}
			for _, filter := range pattern.Filters {
				projects[projectName].Schema.Filters = append(projects[projectName].Schema.Filters, filter.Copy())
			}
			args, err := json.Marshal(projects[projectName])
			if err != nil {
				logger.Fatalfe(err, "failed to marshal project: %s", projectName)
				return err
			}
			projectArgs = append(projectArgs, string(args))
		}
	}
	w.calculateHash(ctx, projectArgs)
	w.projects = projects
	logger = logger.With("mv_hash", w.mvHash)
	if len(projects) == 0 {
		logger.Warnf("no projects found, suspend the watcher")
		return errors.Errorf("no projects found")
	}
	logger.Infof("resolv refresh project completed, count: %d, cost: %.2fs",
		len(projects), time.Since(start).Seconds())
	return nil
}

func (w *resolvWatcher) ToQuery(queryType queryType) string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		queries = append(queries, project.ToQuery(w, queryType, 1))
	}
	return builder.FormatSQLTemplate(resolvUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *resolvWatcher) ToStatusQuery() string {
	if len(w.projects) == 0 {
		return ""
	}

	var queries []string
	for _, project := range w.projects {
		queries = append(queries, project.ToStatusQuery(w))
	}
	return builder.FormatSQLTemplate(resolvStatusUnionQuery, map[string]any{
		"queries": strings.Join(queries, " UNION ALL "),
	})
}

func (w *resolvWatcher) runWithType(ctx context.Context, queryType queryType, suffix string) error {
	ctx, logger := log.FromContext(ctx)
	now := time.Now()
	var tmpSuffix, finalTableName string
	switch {
	case suffix != "":
		tmpSuffix = fmt.Sprintf("tmp_%s_%d", suffix, time.Now().Unix())
		finalTableName = w.config.EncryptTableBySuffix(w.name, suffix)
	default:
		tmpSuffix = fmt.Sprintf("tmp_%d", time.Now().Unix())
		finalTableName = w.config.EncryptTargetTable(w.name)
	}
	tmpTableName := w.config.EncryptTableBySuffix(w.name, tmpSuffix)
	if err := w.createIfNotExists(ctx,
		w.formatCreateStmtTpl(resolvPointsCreateStmtV2, tmpTableName, true)); err != nil {
		return err
	}
	logger = logger.With("name", w.name, "table", tmpTableName)

	tm := timer.NewTimer()
	totalTimer := tm.Start("total")
	readTimer := tm.Start("read")
	var aggregateMap = make(map[string]map[string]map[string]float64)
	for _, project := range w.projects {
		_, err := project.Run(ctx, w, queryType, aggregateMap)
		if err != nil {
			logger.Errorfe(err, "failed to run project: %s", project.FullName)
			return err
		}
	}
	readUsed := readTimer.End()
	logger.Infof("read used: %s", readUsed.String())

	writeTimer := tm.Start("write")
	var round int
	datas := lo.MapToSlice(aggregateMap, func(id string, points map[string]map[string]float64) PointsData {
		if points == nil {
			points = make(map[string]map[string]float64)
		}
		if points["__sentio_capture_time__"] == nil {
			points["__sentio_capture_time__"] = map[string]float64{
				"timestamp": float64(now.UnixMilli()),
			}
		} else {
			points["__sentio_capture_time__"]["timestamp"] = float64(now.UnixMilli())
		}
		pointsJSON, err := sonic.Marshal(points)
		if err != nil {
			logger.Errorfe(err, "failed to marshal points")
			return PointsData{}
		}
		return PointsData{
			ID:         id,
			PointsJSON: string(pointsJSON),
		}
	})
	chunks := lo.Chunk(datas, insertBatchSize)
	for _, chunk := range chunks {
		round++
		if err := w.processBatchData(ctx, tmpTableName, chunk); err != nil {
			logger.Errorfe(err, "failed to process batch data")
			return err
		} else {
			if round%100 == 0 {
				logger.Infof("inserted %d users", round*insertBatchSize)
			}
		}
	}
	writeUsed := writeTimer.End()
	totalUsed := totalTimer.End()
	logger.Infof("write used: %s", writeUsed.String())

	replaceTimer := tm.Start("replace")
	if err := w.conn.Exec(ctx, "EXCHANGE TABLES "+finalTableName+" AND "+tmpTableName+" "+w.clusterArgv); err != nil {
		logger.Errorfe(err, "failed to exchange table")
		return err
	}
	if err := w.conn.Exec(ctx, "DROP TABLE IF EXISTS "+tmpTableName+" "+w.clusterArgv); err != nil {
		logger.Errorfe(err, "failed to drop table")
		return err
	}
	_ = replaceTimer.End()

	cleanupTimer := tm.Start("cleanup")
	_ = w.Cleanup(ctx, w.config.TargetTable+"_tmp_")
	_ = cleanupTimer.End()

	logger.Infof("run local completed, total users: %d, total used: %s, analytics: %s",
		len(aggregateMap), totalUsed.String(), tm.ReportDistribution("total", "read,write,replace,cleanup"))
	return nil
}

func (w *resolvWatcher) Replace(ctx context.Context) (bool, error) {
	if err := w.refreshProjects(ctx); err != nil {
		return false, errors.Wrap(err, "failed to refresh projects")
	}

	if len(w.projects) == 0 {
		return true, nil
	}
	if err := w.runWithType(ctx, defaultQuery, ""); err != nil {
		log.Errorfe(err, "failed to run local with type: %s", defaultQuery)
	}
	if err := w.runWithType(ctx, dailySnapshot, "daily_snapshot"); err != nil {
		log.Errorfe(err, "failed to run local with type: %s", dailySnapshot)
	}
	if err := w.runWithType(ctx, from20250509, "from_20250509"); err != nil {
		log.Errorfe(err, "failed to run local with type: %s", from20250509)
	}
	if err := w.runWithType(ctx, to20250509, "to_20250509"); err != nil {
		log.Errorfe(err, "failed to run local with type: %s", to20250509)
	}
	return true, nil
}

func (w *resolvWatcher) Mapper() []sqllib.TableMapper {
	return []sqllib.TableMapper{
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetTableMv(),
			w.config.EncryptTargetTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTargetStatusTableMv(),
			w.config.EncryptTargetStatusTable(w.name), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("daily_snapshot"),
			w.config.EncryptTableBySuffix(w.name, "daily_snapshot"), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("from_20250509"),
			w.config.EncryptTableBySuffix(w.name, "from_20250509"), w.config.ClickhouseShardingIndex, w.connOpt),
		mapper.NewReservedMaterializedViewMapper(w.name,
			w.config.OriginalTableMvBySuffix("to_20250509"),
			w.config.EncryptTableBySuffix(w.name, "to_20250509"), w.config.ClickhouseShardingIndex, w.connOpt),
	}
}
