syntax = "proto3";

package solidity_service;

option go_package = "sentioxyz/sentio/service/solidity/protos";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/api/visibility.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/api/httpbody.proto";
import "service/solidity/protos/common.proto";
import "chain/evm/protos/evm.proto";
import "scip.proto";
import "service/common/protos/common.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

service SolidityService {
  rpc FetchAndCompile (FetchAndCompileRequest) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/fetch_and_compile"
    };
  }

  rpc GetTransactionInfo(GetTransactionInfoRequest) returns (GetTransactionInfoResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/transaction_info",
    };
  }

  rpc GetLatestBlockNumber(GetLatestBlockNumberRequest) returns (GetLatestBlockNumberResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/block_number",
    };
  }

  rpc GetBlockSummary(GetBlockSummaryRequest) returns (GetBlockSummaryResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/block_summary",
    };
  }

  rpc GetStorageInfo(GetStorageInfoRequest) returns (GetStorageInfoResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/storage_info",
    };
  }

  rpc GetCode(GetCodeRequest) returns (GetCodeResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/code",
    };
  }

  rpc GetCallTrace(GetCallTraceRequest) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (common.track_usage) = {
      api_sku: "api_debugger"
      webui_sku: "webui_debugger"
    };
    option (google.api.http) = {
      get: "/api/v1/solidity/call_trace"
    };
  }

  rpc SentioTraceTransaction(SentioTraceTransactionRequest) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/sentio_trace_transaction"
    };
  }


  rpc GetAffectedContract(GetAffectedContractRequest) returns (GetAffectedContractResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/affected_contract"
    };
  }

  rpc SimulateTransaction(SimulateTransactionRequest) returns (SimulateTransactionResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (common.track_usage) = {
      api_sku: "api_debugger"
      webui_sku: "webui_debugger"
    };
    option (google.api.http) = {
      post: "/api/v1/solidity/simulate",
      body: "*"
    };
  }

  rpc SimulateTransactionBundle(SimulateTransactionBundleRequest) returns (SimulateTransactionBundleResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (common.track_usage) = {
      api_sku: "api_debugger"
      webui_sku: "webui_debugger"
    };
    option (google.api.http) = {
      post: "/api/v1/solidity/simulate_bundle",
      body: "*"
    };
  }

  // Get a bundle simulation by id
  rpc GetSimulationBundle(GetSimulationBundleRequest) returns (GetSimulationBundleResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (common.track_usage) = {
      api_sku: "api_debugger"
      webui_sku: "webui_debugger"
    };
    option (google.api.http) = {
      get: "/api/v1/solidity/simulate_bundle/{bundle_id}"
    };
  }

  // Get existing transaction simulations
  rpc GetSimulations(GetSimulationsRequest) returns (GetSimulationsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (common.track_usage) = {
      api_sku: "api_debugger"
      webui_sku: "webui_debugger"
    };
    option (google.api.http) = {
      get: "/api/v1/solidity/simulate"
    };
  }

  // Get a simulation by id
  rpc GetSimulation(GetSimulationRequest) returns (GetSimulationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (common.track_usage) = {
      api_sku: "api_debugger"
      webui_sku: "webui_debugger"
    };
    option (google.api.http) = {
      get: "/api/v1/solidity/simulate/{simulation_id}"
    };
  }

  // Search for transactions
  rpc SearchTransactions(EvmSearchTransactionsRequest) returns (EvmSearchTransactionsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/search_transactions",
      body: "*"
      additional_bindings {
        get: "/api/v1/solidity/search_transactions"
      }
    };
  }

  rpc SyncContracts(SyncContractsRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/sync_contracts",
      body: "*"
    };
  }

  rpc UniversalSearch(UniversalSearchRequest) returns (UniversalSearchResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/universal_search"
    };
  }

  rpc GetContractIndex(GetContractIndexRequest) returns (GetContractIndexResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/index"
    };
  }

  rpc GetDebugTrace(GetDebugTraceRequest) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/debug_trace"
    };
  }

  rpc LookupSignature(LookupSignatureRequest) returns (LookupSignatureResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/signature"
    };
  }

  rpc GetABI(GetABIRequest) returns (GetABIResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/abi"
    };
  }

  rpc GetContractName(GetContractNameRequest) returns (GetContractNameResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/contract_name"
    };
  }

  rpc StateDiff(StateDiffRequest) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/state_diff"
    };
  }

  rpc UploadUserCompilation(UploadUserCompilationRequest) returns (UploadUserCompilationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/user_compilation",
      body: "*"
    };
  }

  // no gcs read
  rpc GetUserCompilations(GetUserCompilationsRequest) returns (GetUserCompilationsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/user_compilation",
    };
  }

  // do read sources from gcs
  rpc GetUserCompilation(GetUserCompilationRequest) returns (GetUserCompilationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/user_compilation/{user_compilation_id}",
    };
  }

  rpc UpdateUserCompilation(UpdateUserCompilationRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/user_compilation/{user_compilation_id}",
      body: "*"
    };
  }

  rpc DeleteUserCompilation(DeleteUserCompilationRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/solidity/user_compilation/{user_compilation_id}",
    };
  }

  rpc GetDeployedCode(GetDeployedCodeRequest) returns (GetDeployedCodeResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/user_compilation/{user_compilation_id}/deployed_code",
    };
  }

  rpc VerifyContract(VerifyContractRequest) returns (VerifyContractResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/verification"
      body: "*"
    };
  }

  rpc GetVerifications(GetVerificationsRequest) returns (GetVerificationsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/verification"
    };
  }

  rpc GetVerificationByContract(GetVerificationByContractRequest) returns (GetVerificationByContractResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/verification_by_contract"
    };
  }

  rpc DeleteVerification(DeleteVerificationRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/solidity/verification/{verification_id}"
    };
  }

  rpc ParseContracts(ParseContractsRequest) returns (ParseContractsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/parse_contracts"
      body: "*"
    };
  }

  rpc GetTransactions(GetTransactionsRequest) returns (GetTransactionsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/transactions"
    };
  }

  rpc GetMEVInfo(GetMEVInfoRequest) returns (GetMEVInfoResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/mev_info"
    };
  }

  rpc BatchGetMEVInfo(BatchGetMEVInfoRequest) returns (BatchGetMEVInfoResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/mev_info/batch"
    };
  }

  rpc GetStorageSummary(GetStorageSummaryRequest) returns (GetStorageSummaryResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/storage_summary"
    };
  }

  rpc DumpSimulation(DumpSimulationRequest) returns (DumpSimulationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/dump_simulation"
    };
  }

  rpc DumpUserCompilation(DumpUserCompilationRequest) returns (DumpUserCompilationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/dump_user_compilation"
    };
  }

  rpc SimulateDeployment(SimulateDeploymentRequest) returns (SimulateDeploymentResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/simulate_deployment"
    };
  }

  rpc CreateShareSimulation(CreateShareSimulationRequest) returns (CreateShareSimulationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/solidity/share_simulation",
      body: "*"
    };
  }

  rpc GetSharedSimulation(GetShareSimulationRequest) returns (GetShareSimulationResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/share_simulation/{id}"
    };
  }

  rpc DownloadContract(DownloadContractRequest) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/download_contract"
    };
  }

  rpc GetForkState(GetForkStateRequestDeprecated) returns (google.api.HttpBody) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/fork_state"
    };
  }

  rpc GetRecentTransactions(GetRecentTransactionsRequest) returns (GetRecentTransactionsResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/solidity/recent_transactions"
    };
  }

  rpc GetEstimatedGasPrice(GetEstimatedGasPriceRequest) returns (GetEstimatedGasPriceResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";
  }
}

message GetStorageSummaryRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 7;
  string address = 4;
  optional string block_number = 5;
  optional string variable_path = 6;
}

message GetStorageSummaryResponse {
  string address = 1;
  optional string impl_address = 2;
  repeated StorageSummaryResult results = 3;
  string block_number = 4;
}

message FetchAndCompileRequest {
  // Required if fork id is specified
  optional string project_owner = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  // Required if fork id is specified
  optional string project_slug = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
  optional string share_id = 9;

  string network_id = 4 [deprecated = true];
  ChainIdentifier chain_spec = 8;

  TxIdentifier tx_id = 1;
  repeated string addresses = 7;
  bool disable_optimizer = 2;
  bool source_only = 3;
}

message GetTransactionInfoRequest {
  // Required if fork id is specified
  optional string project_owner = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  // Required if fork id is specified
  optional string project_slug = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];

  string network_id = 5 [deprecated = true];
  ChainIdentifier chain_spec = 8;


  TxIdentifier tx_id = 1;
//  bool with_trace = 2;
//  bool with_state_diff = 3;
//  bool decode = 4;
}

message GetTransactionInfoResponse {
  ChainIdentifier chain_spec = 9;

  google.protobuf.Struct block = 1;
  evm.Transaction transaction = 2;
  evm.TransactionReceipt transaction_receipt = 3;
//  google.protobuf.Struct trace = 4;
//  StateDiffResponse state_diff = 7;
//  map<string, string> code = 5;
  string latestBlockNumber = 6;

  repeated evm.Transaction transactions = 7;
  repeated evm.TransactionReceipt transaction_receipts = 8;
}

message GetTransactionsRequest {
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 3;
  repeated string tx_hash = 2;
}

message GetTransactionsResponse {
  map<string, evm.Transaction> transactions = 1;
}

//message StateDiffResponse {
//  oneof state_diff {
//    google.protobuf.Struct raw = 1;
//  }
//}

message GetLatestBlockNumberRequest {
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 2;
}

message GetLatestBlockNumberResponse {
  // the result of eth_blockNumber, an integer value of the latest block number encoded as hexadecimal
  string block_number = 1;
}

message GetBlockSummaryRequest {
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 3;
  // used for calling eth_getBlockByNumber, empty means "latest"
  string block_number = 2;
}

message GetBlockSummaryResponse {
  string block_number = 1;
  uint32 transaction_count = 2;
  string base_fee_per_gas = 3;
}

message GetStorageInfoRequest {
  string network_id = 6 [deprecated = true];
  ChainIdentifier chain_spec = 7;

  string block_hash = 1;
  int64 tx_idx = 2;
  string contract_address = 3;
  string key_start = 4;
  int64 max_result = 5;
}

message GetStorageInfoResponse {
  google.protobuf.Struct result = 1;
}

message GetCodeRequest {
  string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 4;

  string contract_address = 1;
  string block_number = 2;
}

message GetCodeResponse {
  string code = 5;
}

message GetCallTraceRequest {
  // Required if fork id is specified
  optional string project_owner = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  // Required if fork id is specified
  optional string project_slug = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
  optional string share_id = 9;

  string network_id = 5 [deprecated = true];
  ChainIdentifier chain_spec = 8;

  TxIdentifier tx_id = 1 [(google.api.field_visibility).restriction = "INTERNAL"];;
  bool disable_optimizer = 2;
  // Fetch the decoded trace, which will give you the function info, decoded parameters of both external and internal call trace.
  bool with_internal_calls = 3;
  // Only effective when disableOptimizer=true
  bool ignore_gas_cost = 4;
}

message SentioTraceTransactionRequest {
  optional string project_owner = 7;
  optional string project_slug = 8;
  string network_id = 6 [deprecated = true];
  ChainIdentifier chain_spec = 9;

  TxIdentifier tx_id = 1;
  bool disable_optimizer = 2;
  bool with_internal_calls = 3;
  bool debug = 4;
  bool ignore_gas_cost = 5;
}

message GetCallTraceResponse {
  google.protobuf.ListValue result = 1;
  google.protobuf.ListValue outputs = 2;
}

message GetAffectedContractRequest {
  optional string project_owner = 3;
  optional string project_slug = 4;
  optional string share_id = 6;
  string network_id = 2 [deprecated = true];
  ChainIdentifier chain_spec = 5;
  TxIdentifier tx_id = 1;
}

message GetAffectedContractResponse {
  repeated string addresses = 1;
}

message SimulateTransactionRequest {
  optional string project_owner = 1 [(google.api.field_behavior) = REQUIRED];
  optional string project_slug = 2 [(google.api.field_behavior) = REQUIRED];
  Simulation simulation = 3 [(google.api.field_behavior) = REQUIRED];
//  bool save = 4;
//  bool save_if_fails = 5;
}

message SimulateTransactionResponse {
  Simulation simulation = 1;
}

message SimulateTransactionBundleRequest {
  optional string project_owner = 1 [(google.api.field_behavior) = REQUIRED];
  optional string project_slug = 2 [(google.api.field_behavior) = REQUIRED];
  // For blockNumber, transactionIndex, networkId, stateOverrides and blockOverrides fields, only the first simulation takes effect.
  repeated Simulation simulations = 3 [(google.api.field_behavior) = REQUIRED];
}

message SimulateTransactionBundleResponse {
  string bundle_id = 1;
  repeated Simulation simulations = 2;
  string error = 3;
}

message GetSimulationBundleRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  string bundle_id = 3;
}

message GetSimulationBundleResponse {
  repeated Simulation simulations = 1;
  string error = 2;
}

message GetSimulationsRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  optional string label_contains = 5;
  int32 page = 3;
  int32 page_size = 4;
}

message GetSimulationsResponse {
  repeated Simulation simulations = 1;
  int64 count = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message GetSimulationRequest {
  optional string project_owner = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "owner"}}];
  optional string project_slug = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {field_configuration: {path_param_name: "slug"}}];
  string simulation_id = 3;
}

message GetSimulationResponse {
  Simulation simulation = 1;
}

message SyncContractsRequest {
  string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 4;
  repeated string addresses = 1;
  bool disable_optimizer = 2;
}

message UniversalSearchRequest {
  string q = 1;
  int32 limit = 2;
}

message UniversalSearchResultTransaction {
  string hash = 1;
  uint64 block_number = 2;
  string block_hash = 3;
  string from = 4;
  string to = 5;
  string method_id = 6;
  int32 status = 7;
  bool is_trace = 8;
}

message UniversalSearchResultContract {
  string address = 1;
  string contract_name = 2;
}

message UniversalSearchResult {
  string chain_id = 1;
  uint64 timestamp_ms = 2;

  oneof result {
    UniversalSearchResultTransaction transaction = 3;
    UniversalSearchResultContract contract = 4;
  }
}

message UniversalSearchResponse {
  repeated UniversalSearchResult results = 1;
}

message GetContractIndexRequest {
  optional string project_owner = 3;
  optional string project_slug = 4;
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 7;

  // see if it's an address with the source overridden in simulation
  // for on-chain txns, this field is not necessary
  optional TxIdentifier tx_id = 5;
  optional string user_compilation_id = 6;

  string address = 2;
}

message GetContractIndexResponse {
  scip.Index index = 1;
}

message GetDebugTraceRequest {
  optional string project_owner = 6;
  optional string project_slug = 7;
  optional string share_id = 9;
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 8;
  TxIdentifier tx_id = 2;
  int32 memory_compression_window = 3;
  bool disable_optimizer = 4;
  bool ignore_gas_cost = 5;
}

message LookupSignatureRequest {
  optional ChainIdentifier chain_spec = 6;
  optional string address = 7;
  string hex_signature = 1;
  SignatureType type = 2;
  optional string data = 3;
  optional string output = 4;
  repeated string topics = 5;
}

message LookupSignatureResponse {
  string text_signature = 1;
  optional string abi_item = 2;
}

enum SignatureType {
  FUNCTION = 0;
  EVENT = 1;
  ERROR = 2;
}

message GetABIRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 6;
  optional TxIdentifier tx_id = 4;
  string address = 5;
}

message GetABIResponse {
  string ABI = 1;
}

message GetContractNameRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 6;
  optional TxIdentifier tx_id = 4;
  string address = 5;
}

message GetContractNameResponse {
  string contract_name = 1;
}

message StateDiffRequest {
  optional string project_owner = 3;
  optional string project_slug = 4;
  optional string share_id = 7;
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 6;
  TxIdentifier tx_id = 2;
  optional bool decode = 5;
}

// TODO support uploading by standard json input and metadata
message UploadUserCompilationRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  SourceSpec compile_spec = 3;
  optional FromContract from_contract = 6;
  optional FromUserCompilation from_user_compilation = 7;
  optional Verification verify_spec = 4;
  optional string name = 5;

  message FromContract {
    string network_id = 1 [deprecated = true];
    ChainIdentifier chain_spec = 4;
    string address = 2;
    map<string, string> override_source = 3;
  }

  message FromUserCompilation {
    string user_compilation_id = 1;
    map<string, string> override_source = 2;
  }
}

message UploadUserCompilationResponse {
  string user_compilation_id = 1;
  bool verified = 2;
}

message GetUserCompilationsRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  optional string name = 5;
  int32 page = 3;
  int32 page_size = 4;
}

message GetUserCompilationsResponse {
  repeated UserCompilation results = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message GetUserCompilationRequest {
  string user_compilation_id = 1;
  bool brief = 2;
}

message GetDeployedCodeResponse {
  string deployed_code = 1;
}

message UpdateUserCompilationRequest {
  string user_compilation_id = 1;
  string name = 2;
}

message DeleteUserCompilationRequest {
  string user_compilation_id = 1;
}

message GetUserCompilationResponse {
  UserCompilation result = 1;
  string project_owner = 2;
  string project_slug = 3;
}

message GetDeployedCodeRequest {
  ChainIdentifier chain_spec = 1;
  string user_compilation_id = 2;
  string address = 3;
}

message VerifyContractRequest {
  Verification verify_spec = 1;
}

message VerifyContractResponse {
  bool verified = 1;
}

message GetVerificationsRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  optional string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 6;
  int32 page = 4;
  int32 page_size = 5;
}

message GetVerificationsResponse {
  repeated Verification results = 1;
  int64 total = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message GetVerificationByContractRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 6;
  repeated string addresses = 4;
  bool check_internal = 5;
}

message GetVerificationByContractResponse {
  message Contract {
    bool internal = 1;
    Verification user_verification = 2;
  }

  map<string, Contract> result = 1;
}

message DeleteVerificationRequest {
  string verification_id = 1;
}

message ParseContractsRequest {
  SourceSpec compile_spec = 1;
}

message ParseContractsResponse {
  repeated string contracts = 1;
}

message UserCompilation {
  message Origin {
    string chain_id = 1;
    string address = 2;
    string creation_tx = 3;
  }
  string id = 1;
  string name = 2;
  string contract_name = 3;
  string ABI = 4;
  SourceSpec source = 5;
  google.protobuf.Struct compilation = 6;
  repeated Verification verifications = 7;
  google.protobuf.Timestamp create_time = 8;
  optional Origin origin = 9;
}

message GetMEVInfoRequest {
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 3;
  string tx_hash = 2;
}

message GetMEVInfoResponse {
  string tx_hash = 1;
  int32 tx_index = 2;
  string block_number = 3;
  MEVTxType type = 4;
  oneof related_mev {
    SandwichResult sandwich = 5;
    ArbitrageResult arbitrage = 6;
  }
  repeated SandwichResult block_sandwiches = 7;
  repeated ArbitrageResult block_arbitrages = 8;
}

message BatchGetMEVInfoRequest {
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 3;
  repeated string tx_hash = 2;
}

message BatchGetMEVInfoResponse {
  repeated GetMEVInfoResponse results = 1;
}

enum MEVTxType {
  NONE = 0;
  SANDWICH = 1;
  ARBITRAGE = 2;
  VICTIM = 3;
}

message SandwichResult {
  string front_tx_hash = 1 [deprecated = true];
  string back_tx_hash = 2 [deprecated = true];
  int32 front_tx_index = 3 [deprecated = true];
  int32 back_tx_index = 4 [deprecated = true];

  repeated TxHashAndIndex txs = 12;
  repeated TxHashAndIndex victims = 13;
  string mev_contract = 5;
  Assets revenues = 6;
  Assets costs = 7;
  Assets profits = 8;
  repeated TokenInfo tokens = 9;
  repeated Pool traders = 10;
  repeated string tags = 11;
}

message TxHashAndIndex {
  string tx_hash = 1;
  int32 tx_index = 2;
}

message ArbitrageResult {
  string tx_hash = 1;
  int32 tx_index = 2;
  string mev_contract = 3;
  Assets revenues = 4;
  Assets costs = 5;
  Assets profits = 6;
  repeated TokenInfo tokens = 7;
  repeated Pool traders = 8;
  repeated string tags = 9;
}

message Assets {
  message Token {
    string address = 1;
    string symbol = 2;
    double value = 3;
    optional double value_usd = 4;
  }

  repeated Token tokens = 1;
  double total_usd = 2;
}

message TokenInfo {
  string address = 1;
  optional string symbol = 2;
  optional google.protobuf.Timestamp timestamp = 3;
  optional double price = 4;
}

message Pool {
  string address = 1;
  optional string protocol = 2;
  repeated TokenInfo tokens = 3;
}

message DumpSimulationRequest {
  string simulation_id = 1;
}

message DumpSimulationResponse {
  SimulateTransactionRequest simulation_req = 1;
  map<string, UploadUserCompilationRequest> compilation_req = 2;
}

message DumpUserCompilationRequest {
  string user_compilation_id = 1;
}

message DumpUserCompilationResponse {
   UploadUserCompilationRequest compilation_req = 1;
}

message SimulateDeploymentRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  string network_id = 3 [deprecated = true];
  ChainIdentifier chain_spec = 7;
  string address = 4;
  string user_compilation_id = 5;
  optional string block_number = 6;
}

message SimulateDeploymentResponse {
  Simulation simulation = 1;
}

message DownloadContractRequest {
  string network_id = 1 [deprecated = true];
  ChainIdentifier chain_spec = 3;
  string address = 2;
}

message GetForkStateRequestDeprecated {
  string network_id = 1 [deprecated = true];
}

message GetRecentTransactionsRequest {
  optional string project_owner = 1;
  optional string project_slug = 2;
  ChainIdentifier chain_spec = 3;
  optional int32 limit = 4;
}

message GetRecentTransactionsResponse {
  repeated string tx_hashes = 1;
}

message GetEstimatedGasPriceRequest {
  // Current support chain id: 1
  string chain_id = 1;
}

message GetEstimatedGasPriceResponse {
  string system = 1;
  string network = 2;
  string unit = 3;
  double max_price = 4;
  int32 current_block_number = 5;
  int32 ms_since_last_block = 6;
  repeated BlockPrice block_prices = 7;
}

message BlockPrice {
  int32 block_number = 1;
  int32 estimated_transaction_count = 2;
  double base_fee_per_gas = 3;
  double blob_base_fee_per_gas = 4;
  repeated EstimatedPrice estimated_prices = 5;
}

message EstimatedPrice {
  int32 confidence = 1;
  double price = 2;
  double max_priority_fee_per_gas = 3;
  double max_fee_per_gas = 4;
}

message EvmSearchTransactionsRequest {
  repeated string chain_id = 1;
  repeated string address = 2;
  bool include_direct = 3;
  bool include_trace = 4;
  bool include_in = 5;
  bool include_out = 6;

  optional int64 start_block = 7;
  optional int64 end_block = 8;
  optional int64 start_timestamp = 9;
  optional int64 end_timestamp = 10;

  repeated int32 transaction_status = 11;
  optional string method_signature = 12;

  int32 limit = 32;
  bytes page_token = 33;
}

message EvmRawTransaction {
  string hash = 1;
  int64 block_number = 2;
  bool is_in = 3;
  bool trace = 4;
  evm.Transaction tx = 5;
  string json = 6;
  int64 timestamp = 7;
  int32 transaction_status = 8;
  string method_signature = 9;
  optional string method_signature_text = 10;
  optional string abi_item = 11;
}

message EvmSearchTransactionsResponse {
  repeated EvmRawTransaction transactions = 1;
  bytes next_page_token = 2;
}

message CreateShareSimulationRequest {
  string simulation_id = 1;
  bool public = 2;
}

message CreateShareSimulationResponse {
  string id = 1;
  bool public = 2;
}

message GetShareSimulationRequest {
  string id = 1;
}

message GetShareSimulationResponse {
  Simulation simulation = 1;
}
