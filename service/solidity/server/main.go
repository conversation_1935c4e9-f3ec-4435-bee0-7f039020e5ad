package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"syscall"
	"time"

	"cloud.google.com/go/storage"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"sentioxyz/sentio/k8s/client"
	rediscommon "sentioxyz/sentio/service/common/redis"
	rpcproto "sentioxyz/sentio/service/rpcnode/protos"

	"sentioxyz/sentio/common/flags"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/monitoring"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/rpc"
	priceprotos "sentioxyz/sentio/service/price/protos"
	"sentioxyz/sentio/service/solidity"
	"sentioxyz/sentio/service/solidity/protos"
	"sentioxyz/sentio/service/solidity/repository"
	usageUtils "sentioxyz/sentio/service/usage/utils"
)

var ready = false

func main() {
	var (
		port = flag.Int(
			"port",
			10060,
			"The server port",
		)
		authIssuerURL = flag.String("auth-issuer-url", "https://sentio-dev.us.auth0.com/", "The auth0 issue url")
		authAudience  = flag.String("auth-audience", "http://localhost:8080/v1", "The auth0 audience")
		enableProf    = flag.Bool("enable-pprof", false, "Enable pprof")
		syncDBURL     = flag.String(
			"db-url",
			"postgres://postgres:postgres@localhost:5432/postgres",
			"sync db url",
		)
		externalDebuggerServer = flag.String(
			"external-debugger-server",
			"",
			"External debugger server",
		)
		gcsBucket = flag.String(
			"gcs-bucket",
			"sentio-solidity-data",
			"GCS bucket for contract code",
		)
		storeCompilations = flag.Bool(
			"store-compilations",
			false,
			"Store compilations",
		)
		activeSyncWorkers = flag.Int(
			"active-sync-workers",
			6,
			"worker count for active sync",
		)
		envDBURL = flag.String(
			"simulation-db",
			"postgres://postgres:postgres@localhost:5432/postgres",
			"env db url",
		)
		chainConfig = flag.String(
			"chain-config",
			"service/solidity/server/nodes.yaml",
			"node config file, include all endpoint of each network",
		)

		internalPort = flag.Int(
			"internal-port",
			20060,
			"internal grpc server port",
		)
		priceService = flag.String(
			"price-service",
			"localhost:10070",
			"the address of price service",
		)
		usageService = flag.String(
			"usage-service",
			"",
			"Usage service address",
		)
		rpcNodeService = flag.String(
			"rpc-node-service",
			"",
			"Rpc node service address",
		)
		rpcNodeDomain = flag.String(
			"rpc-node-domain",
			"",
			"Rpc node domain",
		)
		k8sClustersConfig = flag.String(
			"k8s-clusters-file",
			"",
			"The config file of multi k8s clusters",
		)
		k8sClusterID = flag.Int(
			"k8s-cluster-id",
			1,
			"The id of the k8s cluster for managed forks",
		)
		anvilImage = flag.String(
			"anvil-image",
			"ghcr.io/sentioxyz/anvil:v1.0.0",
			"The anvil image to use for managed forks",
		)
	)
	flags.ParseAndInitLogFlag()

	debuggerServer := *externalDebuggerServer
	if debuggerServer == "" {
		cmd, port, err := solidity.StartDebuggerServer(*chainConfig)
		if err != nil {
			log.Fatale(err)
		}
		debuggerServer = fmt.Sprintf("localhost:%d", port)

		c := make(chan os.Signal)
		defer close(c)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM)
		go func(process *os.Process) {
			<-c
			if process != nil {
				log.Info("shutting down debugger server")
				process.Kill()
			}
		}(cmd.Process)
	}

	conn, err := rpc.DialInsecureWithTimeout(5*time.Minute, debuggerServer, rpc.RetryDialOption)
	if err != nil {
		log.Fatale(err)
	}
	defer conn.Close()
	debuggerClient := protos.NewDebuggerServiceClient(conn)

	redisClient := rediscommon.NewClientWithDefaultOptions()

	syncDB, err := repository.SetupSyncDB(*syncDBURL)
	if err != nil {
		log.Fatale(err)
	}
	envDB, err := repository.SetupEnvDB(*envDBURL)
	if err != nil {
		log.Fatale(err)
	}

	ethClients, chainConfigs, err := solidity.LoadChainClients(*chainConfig, envDB, redisClient, true)
	if err != nil {
		log.Fatale(err)
	}

	authManager := auth.NewAuthManager(&auth.AuthConfig{
		IssuerURL: *authIssuerURL,
		Audience:  *authAudience,
	}, envDB)

	ctx := context.Background()
	gcsClient, err := storage.NewClient(ctx)
	if err != nil {
		log.Fatale(err)
	}
	if _, err := gcsClient.Bucket(*gcsBucket).Attrs(ctx); err != nil {
		log.Fatale(err)
	}

	monitoring.StartMonitoring()
	defer monitoring.StopMonitoring()

	usageClient := usageUtils.MustNewClient(*usageService)
	grpcServer := rpc.NewServer(true, grpc.ChainUnaryInterceptor(
		usageUtils.UsageInterceptor(authManager, usageClient),
	))

	priceConn, err := rpc.DialInsecure(*priceService)
	if err != nil {
		log.Fatale(err)
	}
	defer conn.Close()
	priceClient := priceprotos.NewPriceServiceClient(priceConn)

	k8sMultiClient, err := client.NewMultiClientFromFile(*k8sClustersConfig)
	if err != nil {
		log.Fatale(err)
	}
	k8sClient := k8sMultiClient[*k8sClusterID]

	rpcNodeConn, err := rpc.Dial(*rpcNodeService)
	if err != nil {
		log.Fatale(err)
	}
	defer conn.Close()
	rpcNodeClient := rpcproto.NewRPCNodeServiceClient(rpcNodeConn)

	solidityService, err := solidity.NewService(
		syncDB,
		envDB,
		redisClient,
		ethClients,
		*gcsBucket,
		debuggerClient,
		priceClient,
		gcsClient,
		*storeCompilations,
		*activeSyncWorkers,
		authManager,
		chainConfigs,
	)
	if err != nil {
		log.Fatale(err)
	}
	solidityAPIService := solidity.NewSolidityAPIService(solidityService)
	forkService := solidity.NewForkService(k8sClient, solidityService, rpcNodeClient, *rpcNodeDomain, *anvilImage)
	protos.RegisterSolidityServiceServer(grpcServer, solidityService)
	protos.RegisterSolidityAPIServiceServer(grpcServer, solidityAPIService)
	protos.RegisterForkServiceServer(grpcServer, forkService)
	reflection.Register(grpcServer)

	// not using server.NewServeMux() since it's messing up with google.api.HttpBody
	matcherFn := func(header string) (string, bool) {
		if header == "content-disposition" {
			return header, true
		}
		return "", false
	}
	mux := runtime.NewServeMux(
		runtime.WithMetadata(rpc.WithAuthAndTraceMetadata),
		runtime.WithOutgoingHeaderMatcher(matcherFn),
	)
	err = mux.HandlePath("GET", "/healthz", healthz)
	if err != nil {
		log.Fatale(err)
	}
	err = mux.HandlePath("POST", "/api/v1/solidity/{owner}/{slug}/fork/{fork_id}/rpc", forkService.ServeForkJsonRPCRequest)
	if err != nil {
		log.Fatale(err)
	}

	err = protos.RegisterSolidityServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptionsForLargeMsg)
	if err != nil {
		log.Fatale(err)
	}
	err = protos.RegisterSolidityAPIServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptionsForLargeMsg)
	if err != nil {
		log.Fatale(err)
	}
	err = protos.RegisterForkServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptionsForLargeMsg)
	if err != nil {
		log.Fatale(err)
	}

	if *enableProf {
		go func() {
			log.Fatale(http.ListenAndServe(":6060", nil))
		}()
	}

	internalGrpcServer := rpc.NewServer(false)
	protos.RegisterSolidityServiceServer(internalGrpcServer, solidityService)
	reflection.Register(internalGrpcServer)
	go bindAndServeInternal(internalGrpcServer, *internalPort)

	rpc.BindAndServeWithHTTP(mux, grpcServer, *port, nil)

	ready = true
}

func healthz(w http.ResponseWriter, r *http.Request, pathParams map[string]string) {
	if ready {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
	}
}

func bindAndServeInternal(grpcServer *grpc.Server, port int) {
	l, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		log.Fatale(err)
	}
	go func() {
		err := grpcServer.Serve(l)
		if err != nil {
			log.Info("internal grpc server shut down: ", err)
		}
	}()

	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM)

	_ = <-sigCh
	grpcServer.GracefulStop()
}
