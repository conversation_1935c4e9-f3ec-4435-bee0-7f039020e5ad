package solidity

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
	"google.golang.org/api/iterator"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"sentioxyz/sentio/common/ethclient"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/protojson"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/k8s/client"
	"sentioxyz/sentio/service/common/auth"
	rpcmodels "sentioxyz/sentio/service/rpcnode/model"
	rpcproto "sentioxyz/sentio/service/rpcnode/protos"
	"sentioxyz/sentio/service/solidity/models"
	"sentioxyz/sentio/service/solidity/protos"

	"google.golang.org/protobuf/types/known/emptypb"
)

type ForkService struct {
	protos.UnimplementedForkServiceServer

	solidityService *Service
	k8sClient       *client.Client
	rpcNodeClient   rpcproto.RPCNodeServiceClient
	rpcNodeDomain   string
	anvilImage      string
}

func NewForkService(
	k8sClient *client.Client,
	solidityService *Service,
	rpcNodeClient rpcproto.RPCNodeServiceClient,
	rpcNodeDomain string,
	anvilImage string,
) *ForkService {
	return &ForkService{
		solidityService: solidityService,
		k8sClient:       k8sClient,
		rpcNodeClient:   rpcNodeClient,
		rpcNodeDomain:   rpcNodeDomain,
		anvilImage:      anvilImage,
	}
}

func (s *ForkService) CreateFork(
	ctx context.Context,
	req *protos.CreateForkRequest,
) (*protos.CreateForkResponse, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("CreateFork, req: %v", req)

	identity, project, err := s.solidityService.authManager.RequiredLoginForProjectOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.WRITE)
	if err != nil {
		return nil, err
	}
	fork := req.Fork
	if fork == nil {
		return nil, status.Errorf(codes.InvalidArgument, "missing fork")
	}
	if fork.Type == protos.ForkType_EXTERNAL {
		return s.createExternalFork(ctx, fork, project.ID, identity.UserID)
	} else if fork.Type == protos.ForkType_MANAGED {
		return s.createManagedFork(ctx, fork, project.ID, identity.UserID)
	} else {
		return nil, status.Errorf(codes.InvalidArgument, "invalid fork type: %s", fork.Type)
	}
}

func (s *ForkService) createManagedFork(ctx context.Context, fork *protos.Fork, projectID string, UserID string) (*protos.CreateForkResponse, error) {
	logger := log.WithContext(ctx)
	managed := fork.GetManagedFork()
	if managed == nil {
		return nil, status.Errorf(codes.InvalidArgument, "missing managed fork")
	}
	if managed.ChainId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "missing chain id")
	}
	if managed.ParentNetwork == nil {
		return nil, status.Errorf(codes.InvalidArgument, "missing parent network")
	}
	parentRpcEndpoint := ""
	chainConfig := models.BaseChainConfig{}
	switch managed.ParentNetwork.(type) {
	case *protos.ManagedFork_ParentRpcEndpoint:
		parentRpcEndpoint = managed.GetParentRpcEndpoint()
	case *protos.ManagedFork_ParentChainSpec:
		chainSpec := models.ChainIdentifier{}
		chainSpec.FromPB(managed.GetParentChainSpec())
		cli, err := s.solidityService.chainClients.GetChainClient(ctx, chainSpec)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		parentRpcEndpoint = cli.Config.DebugEndpoint
		// inherit parent's config, except endpoint
		chainConfig = cli.Config.BaseChainConfig
	}
	if managed.ParentBlockNumber <= 0 {
		cli, err := ethclient.NewClient(parentRpcEndpoint, nil, 0, false)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		bn, err := cli.GetLatestBlockNumber(ctx)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		blockNumber := utils.MustParseBigInt(bn).Int64()
		managed.ParentBlockNumber = blockNumber
	}

	var m models.CustomChain
	var err error
	m.FromForkPB(fork, projectID, UserID)
	m.ID = ""
	err = s.solidityService.envRepository.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&m).Error; err != nil {
			logger.Errore(err)
			return err
		}
		anvilSpec := m.ManagedFork.ToStatefulSetSpec(s.k8sClient.Namespace, parentRpcEndpoint, s.anvilImage)
		serviceSpec := m.ManagedFork.ToServiceSpec(s.k8sClient.Namespace)

		internalEndpoint := fmt.Sprintf("http://%s.%s:%d", m.ManagedFork.ServiceID(), anvilSpec.Namespace, models.Port)

		outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
		ctx = metadata.NewOutgoingContext(ctx, outgoingMD)
		resp, err := s.rpcNodeClient.SaveRPCNode(ctx, &rpcproto.RPCNode{
			ProjectId:   projectID,
			Enabled:     true,
			ForkId:      m.ID,
			ForkNodeUrl: internalEndpoint,
		})
		if err != nil {
			logger.Errore(err)
			return err
		}
		nodes := resp.GetRpcNodes()
		if len(nodes) != 1 {
			err = status.Errorf(codes.Internal, "failed to save rpc node, length mismatch: %d", len(nodes))
		}
		externalEndpoint := fmt.Sprintf("https://%s/%s", s.rpcNodeDomain, nodes[0].Code)

		chainConfig.Endpoint = externalEndpoint
		chainConfig.DebugEndpoint = externalEndpoint
		m.ManagedFork.ChainConfig = chainConfig

		if err := tx.Session(&gorm.Session{FullSaveAssociations: true}).Save(&m).Error; err != nil {
			logger.Errore(err)
			return err
		}

		// TODO wait this
		if err := s.k8sClient.Create(ctx, anvilSpec); err != nil {
			logger.Errore(err)
			return err
		}
		if err := s.k8sClient.Create(ctx, serviceSpec); err != nil {
			logger.Errore(err)
			return err
		}
		return nil
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to create fork")
	}
	return &protos.CreateForkResponse{Fork: m.ToForkPB()}, nil
}

func (s *ForkService) createExternalFork(ctx context.Context, fork *protos.Fork, projectID string, UserID string) (*protos.CreateForkResponse, error) {
	logger := log.WithContext(ctx)
	ext := fork.GetExternalFork()
	if ext == nil {
		return nil, status.Errorf(codes.InvalidArgument, "missing external fork")
	}
	cfg := ext.ChainConfig
	if cfg == nil {
		return nil, status.Errorf(codes.InvalidArgument, "missing chain config")
	}
	if cfg.Endpoint == "" {
		return nil, status.Errorf(codes.InvalidArgument, "missing endpoint")
	}
	if cfg.DebugEndpoint == "" {
		cfg.DebugEndpoint = ext.ChainConfig.Endpoint
	}
	var sourceFetcherType models.SourceFetcherType
	sourceFetcherType.FromPB(ext.ChainConfig.SourceFetcherType)
	if !lo.Contains(models.SourceFetcherTypes, sourceFetcherType) {
		return nil, status.Errorf(codes.InvalidArgument, "invalid source fetcher type: %s", ext.ChainConfig.SourceFetcherType)
	}
	if cfg.SourceFetcherTimeout == 0 {
		cfg.SourceFetcherTimeout = int64(2 * time.Second)
	} else if cfg.SourceFetcherTimeout > int64(5*time.Second) {
		return nil, status.Errorf(codes.InvalidArgument, "source fetcher timeout too long: %d", cfg.SourceFetcherTimeout)
	}

	cli, err := ethclient.NewClient(cfg.Endpoint, nil, 0, false)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "failed to create eth client")
	}
	nodeInfo, err := cli.GetAnvilNodeInfo(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get node info: %v", err)
	}
	logger.Infof("node info: %v", nodeInfo)

	var m models.CustomChain
	m.FromForkPB(fork, projectID, UserID)
	m.ID = ""
	m.ExternalFork.ParentBlockNumber = nodeInfo.ForkConfig.ForkBlockNumber
	if err = s.solidityService.envRepository.DB.WithContext(ctx).Create(&m).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save fork: %v", err)
	}
	return &protos.CreateForkResponse{Fork: m.ToForkPB()}, nil
}

func (s *ForkService) GetFork(
	ctx context.Context,
	req *protos.GetForkRequest,
) (*protos.GetForkResponse, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("GetFork, req: %v", req)
	fork, _, _, err := s.authFork(ctx, req.ProjectOwner, req.ProjectSlug, req.Id, auth.READ)
	if err != nil {
		return nil, err
	}
	return &protos.GetForkResponse{Fork: fork.ToForkPB()}, nil
}

func (s *ForkService) ListForks(
	ctx context.Context,
	req *protos.ListForksRequest,
) (*protos.ListForksResponse, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("ListForks, req: %v", req)

	_, project, err := s.solidityService.authManager.RequiredLoginForProjectOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.WRITE)
	if err != nil {
		return nil, err
	}
	var forks []models.CustomChain
	if err = s.solidityService.envRepository.DB.WithContext(ctx).
		Preload(clause.Associations).
		Where(&models.CustomChain{ProjectID: project.ID}).
		Find(&forks).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list forks: %v", err)
	}
	var pbs []*protos.Fork
	for _, fork := range forks {
		pbs = append(pbs, fork.ToForkPB())
	}
	return &protos.ListForksResponse{Forks: pbs}, nil
}

func (s *ForkService) UpdateFork(
	ctx context.Context,
	req *protos.UpdateForkRequest,
) (*protos.UpdateForkResponse, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("UpdateFork, req: %v", req)

	fork, _, _, err := s.authFork(ctx, req.ProjectOwner, req.ProjectSlug, req.Id, auth.WRITE)
	if err != nil {
		return nil, err
	}
	newFork := req.Fork
	// update note and name
	if newFork.Name != "" {
		fork.Name = newFork.Name
	}
	if newFork.Extra != "" {
		fork.Extra = newFork.Extra
	}
	if err = s.solidityService.envRepository.DB.WithContext(ctx).Save(&fork).Error; err != nil {
		return nil, fmt.Errorf("failed to update fork: %v", err)
	}
	return &protos.UpdateForkResponse{Fork: newFork}, nil
}

func (s *ForkService) DeleteFork(ctx context.Context,
	req *protos.DeleteForkRequest) (*emptypb.Empty, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("DeleteFork, req: %v", req)

	fork, _, _, err := s.authFork(ctx, req.ProjectOwner, req.ProjectSlug, req.Id, auth.WRITE)
	if err != nil {
		return nil, err
	}
	// TODO call rpc node server
	if err = s.solidityService.envRepository.DB.WithContext(ctx).
		Delete(rpcmodels.RPCNode{}, "fork_id = ?", req.Id).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, status.Errorf(codes.Internal, "delete rpc node failed: %v", err)
		}
	}
	if err = s.solidityService.envRepository.DB.WithContext(ctx).
		Delete(&fork).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "delete custom chain failed: %v", err)
	}
	s.solidityService.chainClients.InvalidateCustomChain(req.Id)
	return &emptypb.Empty{}, nil
}

func (s *ForkService) GetForkInfo(
	ctx context.Context,
	req *protos.GetForkInfoRequest,
) (*protos.GetForkInfoResponse, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("GetForkInfo, req: %v", req)

	fork, _, _, err := s.authFork(ctx, req.ProjectOwner, req.ProjectSlug, req.Id, auth.READ)
	if err != nil {
		return nil, err
	}
	chainSpec, _ := models.NewForkChainIdentifier(fork.ID)
	client, err := s.solidityService.chainClients.GetCustomEVMChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}
	return getAnvilNodeInfo(ctx, client.Client.Client)
}

func (s *ForkService) GetForkState(
	ctx context.Context,
	req *protos.GetForkStateRequest,
) (*httpbody.HttpBody, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("GetForkState, req: %v", req)

	fork, _, _, err := s.authFork(ctx, req.ProjectOwner, req.ProjectSlug, req.Id, auth.READ)
	if err != nil {
		return nil, err
	}
	chainSpec, _ := models.NewForkChainIdentifier(fork.ID)
	client, err := s.solidityService.chainClients.GetCustomEVMChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}
	return getAnvilNodeState(ctx, client.Client.Client)
}

// CleanupForks called by admin service only
func (s *ForkService) CleanupForks(
	ctx context.Context,
	_ *emptypb.Empty,
) (*emptypb.Empty, error) {
	logger := log.WithContext(ctx)
	logger.Infof("cleaning data for forks")
	var chains []models.CustomChain
	envDB := s.solidityService.envRepository.DB.WithContext(ctx)
	syncDB := s.solidityService.syncRepository.DB.WithContext(ctx)
	err := envDB.
		Unscoped().
		Preload("ManagedFork").
		Where("deleted_at is not null AND cleaned = false").
		Find(&chains).
		Error
	if err != nil {
		logger.Errore(err, "failed to find uncleaned forks")
		return nil, err
	}
	logger.Infof("%d forks to clean", len(chains))
	for _, chain := range chains {
		logger.Infof("clean up fork, id: %s", chain.ID)
		filter := map[string]string{
			"fork_id": chain.ID,
		}
		if chain.Type == models.CustomChainTypeManagedFork {
			err := s.cleanupK8sResource(ctx, *chain.ManagedFork)
			if err != nil {
				logger.Errore(err)
				return nil, err
			}
		}
		// for both managed forks and external forks
		// cleanup db
		// cleanup compilations
		err := syncDB.Where(filter).Delete(&models.ContractFetchRecord{}).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		err = syncDB.Where(filter).Delete(&models.ContractFingerprint{}).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		err = syncDB.Where(filter).Delete(&models.ContractMetadata{}).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		err = syncDB.Where(filter).Delete(&models.SimilarContract{}).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}

		// cleanup simulations
		err = envDB.Where(filter).Delete(&models.Simulation{}).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		err = envDB.Where(filter).Delete(&models.Bundle{}).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}

		// cleanup verifications
		err = envDB.Where(filter).Delete(&models.VerifiedContract{}).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}

		// cleanup gcs
		bkt := s.solidityService.gcsClient.Bucket(s.solidityService.gcsBucket)
		objs := bkt.Objects(ctx, &storage.Query{
			Prefix: "custom_chains/chain_" + chain.ID,
		})
		g, gCtx := errgroup.WithContext(ctx)
		for {
			objAttr, err := objs.Next()
			if err != nil {
				if errors.Is(err, iterator.Done) {
					break
				}
				logger.Errore(err)
				return nil, err
			}
			g.Go(func() error {
				return bkt.Object(objAttr.Name).Delete(gCtx)
			})
		}
		err = g.Wait()
		if err != nil {
			logger.Errore(err)
			return nil, err
		}

		chain.Cleaned = true
		err = envDB.Unscoped().Save(&chain).Error
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		logger.Infof("clean up custom chain ok, id: %s", chain.ID)
	}
	return &emptypb.Empty{}, nil
}

func (s *ForkService) ServeForkJsonRPCRequest(w http.ResponseWriter, r *http.Request, pathParams map[string]string) {
	ctx, logger := log.FromContextWithTrace(r.Context())

	w.Header().Set("Content-Type", "application/json")

	var err error
	defer func() {
		if err != nil {
			logger.Errore(err)
			resp := struct {
				Message string `json:"message"`
			}{
				Message: err.Error(),
			}
			data, _ := json.Marshal(resp)
			w.WriteHeader(http.StatusInternalServerError)
			_, _ = w.Write(data)
		}
	}()

	// TODO do we need auth for json rpc?
	_, ok := pathParams["owner"]
	if !ok {
		err = status.Errorf(codes.InvalidArgument, "owner is required")
		return
	}
	_, ok = pathParams["slug"]
	if !ok {
		err = status.Errorf(codes.InvalidArgument, "slug is required")
		return
	}
	forkID, ok := pathParams["fork_id"]
	if !ok {
		err = status.Errorf(codes.InvalidArgument, "fork id is required")
		return
	}

	// TODO cache clients
	var fork models.CustomChain
	err = s.solidityService.envRepository.DB.WithContext(ctx).
		Preload(clause.Associations).
		Where(&models.CustomChain{ID: forkID}).
		Take(&fork).Error
	if err != nil {
		err = status.Errorf(codes.Internal, "failed to get fork, err: %v", err)
		return
	}
	endpoint := ""
	if fork.Type == models.CustomChainTypeManagedFork {
		if fork.ManagedFork.Version > 0 {
			err = status.Errorf(codes.InvalidArgument, "this rpc endpoint supports legacy forks only")
			return
		}
		endpoint = fork.ManagedFork.ChainConfig.DebugEndpoint
	} else {
		err = status.Errorf(codes.InvalidArgument, "fork type not supported: %v", fork.Type)
		return
	}

	c := http.Client{
		Timeout: 5 * time.Second,
	}
	r.RequestURI = ""
	r.URL, _ = url.Parse(endpoint)
	resp, err := c.Do(r)
	if err != nil {
		return
	}
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return
	}
	_, _ = w.Write(data)
}

func (s *ForkService) cleanupK8sResource(ctx context.Context, managedFork models.ManagedFork) error {
	logger := log.WithContext(ctx)
	opts := []k8sclient.ListOption{
		k8sclient.InNamespace(s.k8sClient.Namespace),
		k8sclient.MatchingLabels{
			"app.kubernetes.io/instance": managedFork.InstanceID(),
		},
	}
	{
		// delete StatefulSet
		var lst appsv1.StatefulSetList
		err := s.k8sClient.List(ctx, &lst, opts...)
		if err != nil {
			logger.Errore(err)
			return err
		}
		for _, item := range lst.Items {
			err := s.k8sClient.Delete(ctx, &item)
			if err != nil {
				logger.Errore(err)
				return err
			}
		}
	}
	{
		// delete Service
		var lst corev1.ServiceList
		err := s.k8sClient.List(ctx, &lst, opts...)
		if err != nil {
			logger.Errore(err)
			return err
		}
		for _, item := range lst.Items {
			err := s.k8sClient.Delete(ctx, &item)
			if err != nil {
				logger.Errore(err)
				return err
			}
		}
	}
	{
		// delete PersistentVolumeClaim
		var lst corev1.PersistentVolumeClaimList
		err := s.k8sClient.List(ctx, &lst, opts...)
		if err != nil {
			logger.Errore(err)
			return err
		}
		for _, item := range lst.Items {
			err := s.k8sClient.Delete(ctx, &item)
			if err != nil {
				logger.Errore(err)
				return err
			}
		}
	}
	return nil
}

func (s *ForkService) authFork(ctx context.Context, owner string, slug string, forkID string, action auth.Action) (*models.CustomChain, string, string, error) {
	identity, project, err := s.solidityService.authManager.RequiredLoginForProjectOwnerAndSlug(ctx, owner, slug, action)
	if err != nil {
		return nil, "", "", err
	}
	var fork models.CustomChain
	err = s.solidityService.envRepository.DB.WithContext(ctx).
		Preload(clause.Associations).
		Where(&models.CustomChain{ID: forkID}).
		Take(&fork).Error
	if err != nil {
		return nil, "", "", status.Errorf(codes.Internal, "failed to get fork, err: %v", err)
	}
	if fork.ProjectID != project.ID {
		return nil, "", "", status.Errorf(codes.PermissionDenied, "permission denied")
	}
	return &fork, identity.UserID, project.ID, nil
}

func getAnvilNodeInfo(ctx context.Context, c *ethclient.Client) (*protos.GetForkInfoResponse, error) {
	logger := log.WithContext(ctx)
	var data json.RawMessage
	_, err := c.RawCall(ctx, &data, "anvil_nodeInfo")
	if err != nil {
		msg := strings.ToLower(err.Error())
		if strings.Contains(msg, "method not found") {
			return nil, status.Errorf(codes.InvalidArgument, "not an anvil endpoint")
		}
		return nil, err
	}
	var resp protos.GetForkInfoResponse
	err = protojson.Unmarshal(data, &resp)
	if err != nil {
		logger.Errore(err)
		return nil, status.Errorf(codes.InvalidArgument, "invalid node info response from the endpoint")
	}
	return &resp, nil
}

func getAnvilNodeState(ctx context.Context, c *ethclient.Client) (*httpbody.HttpBody, error) {
	logger := log.WithContext(ctx)
	var raw string
	_, err := c.RawCall(ctx, &raw, "anvil_dumpState")
	if err != nil {
		msg := strings.ToLower(err.Error())
		if strings.Contains(msg, "method not found") {
			return nil, status.Errorf(codes.InvalidArgument, "not an anvil endpoint")
		}
		return nil, err
	}
	rawBytes, err := hex.DecodeString(strings.TrimPrefix(raw, "0x"))
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	rd, err := gzip.NewReader(bytes.NewReader(rawBytes))
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	data, err := io.ReadAll(rd)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &httpbody.HttpBody{
		ContentType: "application/json",
		Data:        data,
	}, nil
}
