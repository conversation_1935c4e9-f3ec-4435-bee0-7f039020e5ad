load("@rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "repository",
    srcs = [
        "db.go",
        "evm_respository.go",
        "repository.go",
    ],
    importpath = "sentioxyz/sentio/service/solidity/repository",
    visibility = ["//visibility:public"],
    deps = [
        "//chain/evm/ch",
        "//chain/evm/protos",
        "//common/db",
        "//common/log",
        "//service/common/repository",
        "//service/solidity/models",
        "//service/solidity/protos",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_ethereum_go_ethereum//common/hexutil",
        "@com_github_pkg_errors//:errors",
        "@com_github_samber_lo//:lo",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//clause",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_protobuf//proto",
        "@org_golang_x_net//context",
    ],
)

go_test(
    name = "repository_test",
    srcs = ["evm_repository_test.go"],
    embed = [":repository"],
    deps = [
        "//service/solidity/models",
        "//service/solidity/protos",
        "@com_github_stretchr_testify//assert",
        "@org_golang_x_net//context",
    ],
)
