package repository

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/pkg/errors"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/proto"

	"sentioxyz/sentio/chain/evm/ch"
	evmprotos "sentioxyz/sentio/chain/evm/protos"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/solidity/models"
	"sentioxyz/sentio/service/solidity/protos"
)

type EvmRepository struct {
	chainConfigs map[string]models.ChainConfig
}

func NewEvmRepository(chainConfigs map[string]models.ChainConfig) *EvmRepository {
	return &EvmRepository{
		chainConfigs: chainConfigs,
	}
}

func (e *EvmRepository) SearchTransactions(ctx context.Context, req *protos.EvmSearchTransactionsRequest, opts ...grpc.CallOption) (*protos.EvmSearchTransactionsResponse, error) {
	resp := &protos.EvmSearchTransactionsResponse{
		Transactions: make([]*protos.EvmRawTransaction, 0),
	}
	pageTokens, err := NewPageTokens(req.PageToken)
	if err != nil {
		return nil, err
	}

	chainAddresses := make(map[string][]string)

	for idx, address := range req.Address {
		if len(req.ChainId) > idx {
			chain := req.ChainId[idx]
			chainAddresses[chain] = append(chainAddresses[chain], address)
		} else {
			chain := req.ChainId[len(req.ChainId)-1]
			chainAddresses[chain] = append(chainAddresses[chain], address)
		}
	}

	for chain, addresses := range chainAddresses {
		req.Address = addresses
		req.ChainId = []string{chain}
		pageToken, err := pageTokens.GetChain(chain)
		if err != nil {
			return nil, err
		}

		if conn, table, err := e.connect(chain); err == nil {
			defer conn.Close()
			r := proto.Clone(req).(*protos.EvmSearchTransactionsRequest)
			r.ChainId = []string{chain}
			r.Address = req.Address

			result, err := e.searchTransactionsFromClickhouse(ctx, conn, r, table, chain, pageToken)
			if err != nil {
				return nil, err
			}
			if err = mergeResult(resp, result, chain); err != nil {
				return nil, err
			}
		}
	}
	return resp, nil
}

func (e *EvmRepository) getBlockNumberFromTimestamp(ctx context.Context, conn clickhouse.Conn, tablePrefix string, ts int64, desc bool) (uint64, error) {
	var order, op string
	if desc {
		op = "<="
		order = "DESC"
	} else {
		op = ">="
		order = "ASC"
	}

	sql := fmt.Sprintf("SELECT block_number FROM `%s.blocks` WHERE block_timestamp %s toDateTime(%d, 'UTC') ORDER BY block_number %s LIMIT 1",
		tablePrefix, op, ts, order)
	var maxBlock uint64
	result, err := conn.Query(ctx, sql)
	if err != nil {
		return 0, err
	}
	defer result.Close()
	for result.Next() {
		if err := result.Scan(&maxBlock); err == nil {
			return maxBlock, err
		} else {
			return 0, err
		}
	}

	return 0, errors.New("failed to get block number from timestamp")
}

func (e *EvmRepository) getLatestBlock(ctx context.Context, conn clickhouse.Conn, prefix string) (uint64, error) {
	sql := fmt.Sprintf("SELECT block_number FROM `%s.blocks` ORDER BY block_number DESC LIMIT 1", prefix)
	var blockNumber uint64
	result, err := conn.Query(ctx, sql)
	if err != nil {
		return 0, err
	}
	defer result.Close()
	for result.Next() {
		if err := result.Scan(&blockNumber); err == nil {
			return blockNumber, nil
		} else {
			return 0, err
		}
	}
	return 0, errors.New("failed to get latest block number")
}

func (e *EvmRepository) searchTransactionsFromClickhouse(ctx context.Context, conn clickhouse.Conn, req *protos.EvmSearchTransactionsRequest, tablePrefix string, chain string, token *PageToken) (*protos.EvmSearchTransactionsResponse, error) {
	fromBlock := uint64(0)
	toBlock := uint64(0)

	if req.StartBlock != nil {
		fromBlock = uint64(*req.StartBlock)
	}
	if req.EndBlock != nil && token == nil {
		toBlock = uint64(*req.EndBlock)
	} else if token != nil {
		toBlock = token.NextBlockNumber
	}

	if req.StartTimestamp != nil && fromBlock == 0 {
		blockNumber, err := e.getBlockNumberFromTimestamp(ctx, conn, tablePrefix, *req.StartTimestamp, false)
		if err != nil {
			return nil, err
		}
		fromBlock = blockNumber
	}
	if req.EndTimestamp != nil && req.StartBlock == nil && token == nil {
		blockNumber, err := e.getBlockNumberFromTimestamp(ctx, conn, tablePrefix, *req.EndTimestamp, true)
		if err != nil {
			return nil, err
		}
		toBlock = blockNumber
	}
	response := &protos.EvmSearchTransactionsResponse{
		Transactions: make([]*protos.EvmRawTransaction, 0),
	}
	limit := int(req.Limit)
	if limit == 0 {
		limit = 50
	}

	if toBlock == 0 && fromBlock == 0 {
		// do a search without block range
		err := e.doSearch(ctx, conn, req, tablePrefix, chain, token, fromBlock, toBlock, limit, func(transaction *protos.EvmRawTransaction, row *ch.Transaction) error {
			if len(response.Transactions) > 0 {
				lastTx := response.Transactions[len(response.Transactions)-1]
				if lastTx.Hash == transaction.Hash {
					// duplicate transaction
					return nil
				}
			}
			response.Transactions = append(response.Transactions, transaction)
			if len(response.Transactions) >= limit {
				pageToken := &PageToken{
					NextBlockNumber:      row.BlockNumber,
					NextTransactionIndex: row.TransactionIndex,
				}
				response.NextPageToken = pageToken.ToBytes()
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
		return response, nil
	}

	// if toBlock is 0, then we need to get the latest block number
	if toBlock == 0 {
		blockNumber, err := e.getLatestBlock(ctx, conn, tablePrefix)
		if err != nil {
			return nil, err
		}
		toBlock = blockNumber
	}

	var err error

	log.Debugw("searching block for transactions ", "from", fromBlock, "to", toBlock)
	// do search in page / with block range / without limit
	err = e.doSearch(ctx, conn, req, tablePrefix, chain, token, fromBlock, toBlock, limit, func(transaction *protos.EvmRawTransaction, row *ch.Transaction) error {
		if len(response.Transactions) > 0 {
			lastTx := response.Transactions[len(response.Transactions)-1]
			if lastTx.Hash == transaction.Hash {
				// duplicate transaction
				return nil
			}
		}
		response.Transactions = append(response.Transactions, transaction)
		if len(response.Transactions) >= limit {
			pageToken := &PageToken{
				NextBlockNumber:      row.BlockNumber,
				NextTransactionIndex: row.TransactionIndex,
			}
			response.NextPageToken = pageToken.ToBytes()
			return errors.New("reached limit")
		}
		return nil
	})
	if err != nil && err.Error() != "reached limit" {
		return nil, err
	}

	return response, nil
}

func (e *EvmRepository) doSearch(ctx context.Context, conn clickhouse.Conn, req *protos.EvmSearchTransactionsRequest, tablePrefix string, chain string, token *PageToken,
	fromBlock, toBlock uint64, limit int,
	handleResult func(*protos.EvmRawTransaction, *ch.Transaction) error) error {
	var txWheres []string
	var trWheres []string
	var blockWhere []string

	if token != nil {
		blockWhere = append(blockWhere, fmt.Sprintf("block_number < %d OR (block_number = %d AND transaction_index < %d)",
			token.NextBlockNumber, token.NextBlockNumber, token.NextTransactionIndex))
	} else if toBlock > 0 {
		blockWhere = append(blockWhere, fmt.Sprintf("block_number <= %d", toBlock))
	}

	if fromBlock > 0 {
		blockWhere = append(blockWhere, fmt.Sprintf("block_number >= %d", fromBlock))
	}

	if len(req.Address) > 0 {
		addresses := make([]string, 0, len(req.Address))
		for _, a := range req.Address {
			addresses = append(addresses, strings.ToLower(a))
		}

		// req.IncludeIn && req.IncludeOut are no longer used
		trWheres = append(trWheres, "to_address IN ('"+strings.Join(addresses, "', '")+"')")
		/*if req.IncludeIn && req.IncludeOut {
			//trWheres = append(trWheres, fmt.Sprintf("(from_address IN ('%s') OR to_address IN ('%s'))", strings.Join(addresses, "', '"), strings.Join(addresses, "', '")))
			trWheres = append(trWheres, "to_address IN ('"+strings.Join(addresses, "', '")+"')")
		} else if req.IncludeIn {
			trWheres = append(trWheres, "from_address IN ('"+strings.Join(addresses, "', '")+"')")
		} else if req.IncludeOut {
			trWheres = append(trWheres, "to_address IN ('"+strings.Join(addresses, "', '")+"')")
		}*/
	}
	if req.IncludeDirect && req.IncludeTrace {
		// include all
	} else if req.IncludeDirect {
		trWheres = append(trWheres, "trace_index = 0")
	} else if req.IncludeTrace {
		trWheres = append(trWheres, "trace_index > 0")
	}
	if req.MethodSignature != nil {
		trWheres = append(trWheres, fmt.Sprintf("method_sig = '%s'", *req.MethodSignature))
	}

	var sql string

	// stores (block_number, transaction_index) -> (trace_index, method_sig)
	extraValueMap := make(map[[2]uint64]map[string]any)

	if len(req.TransactionStatus) > 0 {
		var status []string
		for _, s := range req.TransactionStatus {
			status = append(status, fmt.Sprintf("'%d'", s))
		}
		txWheres = append(txWheres, "receipt_status IN ("+strings.Join(status, ",")+")")
		innerTable := fmt.Sprintf("SELECT transaction_hash, min(tuple(trace_index, method_sig)) as pair "+
			" FROM `%s.traces` where (%s) GROUP BY transaction_hash",
			tablePrefix, strings.Join(append(trWheres, blockWhere...), ") AND ("))

		sql = fmt.Sprintf("SELECT tx.*, pair.1 AS trace_index, pair.2 AS method_sig "+
			"FROM `%s.transactions` tx JOIN (%s) tr on tx.transaction_hash = tr.transaction_hash ",
			tablePrefix,
			innerTable)
		allWheres := append(txWheres, blockWhere...)
		if len(allWheres) > 0 {
			sql += fmt.Sprintf(" WHERE (%s) ", strings.Join(allWheres, ") AND ("))
		}

	} else {
		// faster
		// query inner table first, and then join with transactions table using where
		innerTable := fmt.Sprintf("SELECT block_number, transaction_index, min(tuple(trace_index, method_sig)) as pair "+
			" FROM `%s.traces` where (%s) GROUP BY block_number, transaction_index"+
			" ORDER BY block_number DESC, transaction_index DESC",
			tablePrefix, strings.Join(append(trWheres, blockWhere...), ") AND ("))
		if limit > 0 {
			innerTable += fmt.Sprintf(" LIMIT %d", limit)
		}
		rows, err := conn.Query(ctx, innerTable)
		if err != nil {
			return err
		}

		txIDs := make([][2]uint64, 0)

		for rows.Next() {
			var blockNumber uint64
			var txIdx uint64
			var pair []any
			if err := rows.Scan(&blockNumber, &txIdx, &pair); err != nil {
				return err
			}
			txID := [2]uint64{blockNumber, txIdx}
			txIDs = append(txIDs, txID)
			m := make(map[string]any)
			traceIndex := pair[0].(uint64)
			m["trace_index"] = &traceIndex
			methodSig := pair[1].(string)
			if len(methodSig) > 0 {
				m["method_sig"] = &methodSig
			}
			extraValueMap[txID] = m
		}

		if len(txIDs) == 0 {
			return nil
		}

		tupleArr := ""
		for idx, txID := range txIDs {
			if idx > 0 {
				tupleArr += ", "
			}
			tupleArr += fmt.Sprintf("(%d, %d)", txID[0], txID[1])
		}

		sql = fmt.Sprintf("SELECT * FROM `%s.transactions` WHERE (block_number, transaction_index) IN ([%s])", tablePrefix, tupleArr)
	}

	sql += " ORDER BY block_number DESC, transaction_index DESC"

	return ch.QueryWithSQL[ch.Transaction](ctx, conn, sql, func(row *ch.Transaction, values map[string]any) error {
		var to string
		if row.ToAddress != nil {
			to = *row.ToAddress
		}
		tx := &evmprotos.Transaction{
			BlockNumber:          strconv.FormatUint(row.BlockNumber, 10),
			BlockHash:            row.BlockHash,
			TransactionIndex:     strconv.FormatUint(row.TransactionIndex, 10),
			Hash:                 row.TransactionHash,
			ChainId:              chain,
			Type:                 hexutil.Uint64(row.TransactionType).String(),
			From:                 row.FromAddress,
			To:                   to,
			Input:                fmt.Sprintf("0x%x", row.Input),
			Value:                row.Value,
			Nonce:                hexutil.Uint64(row.Nonce).String(),
			Gas:                  row.Gas.String(),
			GasPrice:             row.GasPrice.String(),
			MaxFeePerGas:         row.MaxFeePerGas.String(),
			MaxPriorityFeePerGas: row.MaxPriorityFeePerGas.String(),
		}

		rpcTx, _ := row.ToRPCTransaction()
		j, err := json.Marshal(rpcTx)
		if err != nil {
			return err
		}
		extraValue := values
		if m, ok := extraValueMap[[2]uint64{row.BlockNumber, row.TransactionIndex}]; ok {
			extraValue = m
		}

		traceIndex := extraValue["trace_index"].(*uint64)

		transaction := &protos.EvmRawTransaction{
			Hash:        row.TransactionHash,
			BlockNumber: int64(row.BlockNumber),
			IsIn:        false,
			Trace:       *traceIndex > 0,
			Json:        string(j),
			Tx:          tx,
			Timestamp:   row.BlockTimestamp.Unix(),
		}

		if sig, ok := extraValue["method_sig"]; ok {
			if s, ok := sig.(*string); ok {
				transaction.MethodSignature = *s
			}
		}

		if row.ReceiptStatus {
			transaction.TransactionStatus = 1
		} else {
			transaction.TransactionStatus = 0
		}

		return handleResult(transaction, row)
	})

}

func (e *EvmRepository) connect(chain string) (clickhouse.Conn, string, error) {
	if config, ok := e.chainConfigs[chain]; ok {
		if len(config.EVMClickhouseDNS) > 0 {
			options, err := clickhouse.ParseDSN(config.EVMClickhouseDNS)
			if err != nil {
				return nil, "", err
			}
			conn, err := clickhouse.Open(options)
			if err != nil {
				return nil, "", err
			}
			return conn, config.EVMTablePrefix, nil
		}
	}
	return nil, "", errors.New("no clickhouse config")
}

func mergeResult(resp *protos.EvmSearchTransactionsResponse, result *protos.EvmSearchTransactionsResponse, chain string) error {
	resp.Transactions = append(resp.Transactions, result.Transactions...)

	pageTokens, err := NewPageTokens(resp.NextPageToken)
	if err != nil {
		return err
	}
	pageTokens.Merge(result.NextPageToken, chain)
	resp.NextPageToken, err = pageTokens.ToJSON()
	return err
}

type PageTokens map[string][]byte

func NewPageTokens(bytes []byte) (PageTokens, error) {
	ret := make(map[string][]byte)
	if len(bytes) > 0 {
		err := json.Unmarshal(bytes, &ret)
		return ret, err
	}
	return ret, nil
}

func (p PageTokens) Merge(nextPageToken []byte, chain string) {
	p[chain] = nextPageToken
}

func (p PageTokens) ToJSON() ([]byte, error) {
	return json.Marshal(p)
}

func (p PageTokens) GetChain(chain string) (*PageToken, error) {
	if token, ok := p[chain]; ok {
		return NewPageTokenFromBytes(token)
	}
	return nil, nil
}

type PageToken struct {
	NextBlockNumber      uint64
	NextTransactionIndex uint64
}

func NewPageTokenFromBytes(buf []byte) (*PageToken, error) {
	if len(buf) == 0 {
		return nil, nil
	}
	reader := bytes.NewReader(buf)
	blockNumber, err := binary.ReadUvarint(reader)
	if err != nil {
		return nil, err
	}
	transactionIndex, err := binary.ReadUvarint(reader)
	if err != nil {
		return nil, err
	}
	return &PageToken{
		NextBlockNumber:      blockNumber,
		NextTransactionIndex: transactionIndex,
	}, nil

}

func (p *PageToken) ToBytes() []byte {
	buf := make([]byte, 0)
	buf = binary.AppendUvarint(buf, p.NextBlockNumber)
	buf = binary.AppendUvarint(buf, p.NextTransactionIndex)

	return buf
}
