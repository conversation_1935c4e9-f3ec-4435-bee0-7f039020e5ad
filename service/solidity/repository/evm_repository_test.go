package repository

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"golang.org/x/net/context"

	"sentioxyz/sentio/service/solidity/models"
	"sentioxyz/sentio/service/solidity/protos"
)

func TestEvmRepository_SearchTransactions(t *testing.T) {
	t.SkipNow()
	chainConfig := models.ChainConfig{
		EVMClickhouseDNS: "clickhouse://sentio:@localhost:39000/chain_db",
		EVMTablePrefix:   "base",
	}
	configs := map[string]models.ChainConfig{
		"8453": chainConfig,
	}
	repo := NewEvmRepository(configs)

	//startBlock := int64(20510200)
	//endBlock := int64(20532200)
	//startTime := int64(1723689122)
	//endTime := int64(1726281122)
	//method := "0x095ea7b3"
	req := &protos.EvmSearchTransactionsRequest{
		ChainId:       []string{"8453"},
		Address:       []string{"******************************************"},
		IncludeDirect: true,
		IncludeTrace:  true,
		IncludeIn:     true,
		IncludeOut:    true,
		//StartBlock: &startBlock,
		//EndBlock:   &endBlock,
		//StartTimestamp: &startTime,
		//EndTimestamp:   &endTime,
		Limit: 50,
		//TransactionStatus: []int32{1},
		//MethodSignature: &method,
	}

	ctx := context.Background()
	result, err := repo.SearchTransactions(ctx, req)
	assert.NoError(t, err)

	assert.Equal(t, 50, len(result.Transactions))
	assert.NotNil(t, result.NextPageToken)

	req.PageToken = result.NextPageToken
	result2, err := repo.SearchTransactions(ctx, req)
	assert.NoError(t, err)
	assert.True(t, len(result2.Transactions) > 0)
}
