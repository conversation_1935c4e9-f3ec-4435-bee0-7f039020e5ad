package contract

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"

	"cloud.google.com/go/storage"
	"github.com/samber/lo"

	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/protojson"
	"sentioxyz/sentio/service/solidity/common"
	"sentioxyz/sentio/service/solidity/models"
	"sentioxyz/sentio/service/solidity/protos"
	"sentioxyz/sentio/service/solidity/types"
)

type UserCompilation struct {
	*Processor
	compilationID string
	dbRec         *models.UserCompilation
	compilation   *types.Compilation
}

func (p *Processor) UserCompilationByID(compilationID string) *UserCompilation {
	return &UserCompilation{
		Processor:     p,
		compilationID: compilationID,
	}
}

func (p *Processor) UserCompilationByRec(dbRec *models.UserCompilation) *UserCompilation {
	return &UserCompilation{
		Processor:     p,
		compilationID: dbRec.ID,
		dbRec:         dbRec,
	}
}

func (p *Processor) UserCompilationByContent(dbRec *models.UserCompilation, compilation *types.Compilation) *UserCompilation {
	// FIXME seems weird
	return &UserCompilation{
		Processor:     p,
		compilationID: dbRec.ID,
		dbRec:         dbRec,
		compilation:   compilation,
	}
}

func (c *UserCompilation) GetLocalCompilationPath() string {
	return common.GetWorkingDir() + "/" + c.gcsBucket + "/user/compilation/" + c.compilationID
}

func (c *UserCompilation) GetGcsCompilationHandle() *storage.ObjectHandle {
	return c.gcsClient.Bucket(c.gcsBucket).Object("user/compilation/" + c.compilationID)
}

func (c *UserCompilation) GetLocalPreprocessPath() string {
	return common.GetWorkingDir() + "/" + c.gcsBucket + "/user/preprocess/" + c.compilationID
}

func (c *UserCompilation) GetGcsPreprocessHandle() *storage.ObjectHandle {
	return c.gcsClient.Bucket(c.gcsBucket).Object("user/preprocess/" + c.compilationID)
}

func (c *UserCompilation) GetGcsSourceHandle() *storage.ObjectHandle {
	return c.gcsClient.Bucket(c.gcsBucket).Object("user/source/" + c.compilationID)
}

func (c *UserCompilation) GetDBRec(ctx context.Context) (*models.UserCompilation, error) {
	if c.dbRec == nil {
		var rec models.UserCompilation
		err := c.envRepository.DB.WithContext(ctx).
			Where("id = ?", c.compilationID).
			Take(&rec).Error
		if err != nil {
			return nil, err
		}
		c.dbRec = &rec
	}
	return c.dbRec, nil
}

func (c *UserCompilation) UseDebugBuild(ctx context.Context) (*UserCompilation, error) {
	logger := log.WithContext(ctx)

	rec, err := c.GetDBRec(ctx)
	if err != nil {
		return nil, err
	}
	if rec.DebugBuildID != nil {
		return c.Processor.UserCompilationByID(*rec.DebugBuildID), nil
	}
	if rec.RawBuildID != nil {
		return nil, fmt.Errorf("already debug build")
	}

	id, err := gonanoid.GenerateID()
	if err != nil {
		return nil, err
	}
	obj := c.GetGcsSourceHandle()
	rd, err := obj.NewReader(ctx)
	if err != nil {
		return nil, err
	}
	data, err := io.ReadAll(rd)
	if err != nil {
		return nil, err
	}
	var compileSpec protos.SourceSpec
	err = protojson.Unmarshal(data, &compileSpec)
	if err != nil {
		return nil, err
	}
	for _, preset := range optimizerPresets {
		ret, err := c.Processor.CreateUserCompilation(ctx, id, rec.UserID, rec.ProjectID,
			rec.Name+"_debug_build", &compileSpec, optimizerOverrides[preset], true, &c.compilationID, rec.Origin)
		if err == nil {
			rec.DebugBuildID = &ret.compilationID
			err := c.envRepository.DB.WithContext(ctx).Save(rec).Error
			if err != nil {
				return nil, err
			}
			return ret, nil
		}
		logger.Warnf("failed to compile debug build for user source, preset: %s, err: %v", preset, err)
	}
	return nil, err
}

func (c *UserCompilation) GetCompilation(ctx context.Context) (*types.Compilation, error) {
	if c.compilation != nil {
		return c.compilation, nil
	}

	logger := log.WithContext(ctx)
	rec, err := c.GetDBRec(ctx)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	obj := c.gcsClient.Bucket(c.gcsBucket).Object(rec.CompilationPath)
	rd, err := obj.NewReader(ctx)
	if err != nil {
		return nil, err
	}
	defer rd.Close()

	var compilation types.Compilation
	err = json.NewDecoder(rd).Decode(&compilation)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	c.compilation = &compilation
	return &compilation, nil
}

func (c *UserCompilation) GetSource(ctx context.Context) (*protos.SourceSpec, error) {
	logger := log.WithContext(ctx)
	rec, err := c.GetDBRec(ctx)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	obj := c.gcsClient.Bucket(c.gcsBucket).Object(rec.SourcePath)
	rd, err := obj.NewReader(ctx)
	if err != nil {
		return nil, err
	}
	defer rd.Close()

	var source protos.SourceSpec
	data, err := io.ReadAll(rd)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	err = protojson.Unmarshal(data, &source)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &source, nil
}

func (c *UserCompilation) GetCreationCode(ctx context.Context) (constructorCode string, constructorArgs string, err error) {
	logger := log.WithContext(ctx)

	compilation, err := c.GetCompilation(ctx)
	if err != nil {
		logger.Errore(err)
		return "", "", err
	}

	constructorCode, err = compilation.GetConstructorCode()
	if err != nil {
		logger.Errore(err)
		return "", "", err
	}
	sourceInfo := compilation.SourceInfo

	rec, err := c.GetDBRec(ctx)
	if err != nil {
		logger.Errore(err)
		return "", "", err
	}
	constructorArgs = ""
	if rec.ConstructorArgs != "" {
		constructorArgs = rec.ConstructorArgs
	} else if sourceInfo.Options != nil && sourceInfo.Options.Specializations != nil {
		constructorArgs = sourceInfo.Options.Specializations.ConstructorArguments
	}
	return constructorCode, constructorArgs, nil
}

func (c *UserCompilation) GetDeployedCode(
	ctx context.Context,
	chainSpec models.ChainIdentifier,
	address string,
) (ret string, err error) {
	logger := log.WithContext(ctx)

	path := fmt.Sprintf("user/deployed_code/%s_%s_%s", c.compilationID, chainSpec.ToString(), address)
	obj := c.gcsClient.Bucket(c.gcsBucket).Object(path)
	_, err = obj.Attrs(ctx)
	if err == nil {
		rd, err := obj.NewReader(ctx)
		if err != nil {
			logger.Errore(err)
			return "", err
		}
		defer rd.Close()
		data, err := io.ReadAll(rd)
		if err != nil {
			logger.Errore(err)
			return "", err
		}
		return string(data), nil
	}
	defer func() {
		if err == nil {
			w := obj.NewWriter(ctx)
			gz := gzip.NewWriter(w)
			w.ContentType = "text/plain"
			w.ContentEncoding = "gzip"
			_, err = gz.Write([]byte(ret))
			if err != nil {
				logger.Errore(err)
			}
			_ = gz.Close()
			_ = w.Close()
		}
	}()
	ret, err = c.getDeployedCodeWithOriginCreationTx(ctx, chainSpec, address)
	if err == nil {
		logger.Infof("successfully got deployed code for user compilation %s with origin creation tx", c.compilationID)
		return ret, nil
	}
	ret, err = c.getDeployedCodeWithTraceCall(ctx, chainSpec, address)
	if err == nil {
		logger.Infof("successfully got deployed code for user compilation %s with trace call", c.compilationID)
		return ret, nil
	}
	return "", fmt.Errorf("failed to get deployed code for user compilation")
}

// use original creation tx to get deployed code
// requires debug_traceTransaction with createOverrides
func (c *UserCompilation) getDeployedCodeWithOriginCreationTx(
	ctx context.Context,
	chainSpec models.ChainIdentifier,
	address string,
) (string, error) {
	logger := log.WithContext(ctx)

	client, err := c.chainClients.GetChainClient(ctx, chainSpec)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	rec, err := c.GetDBRec(ctx)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	if rec.Origin == nil {
		return "", fmt.Errorf("no origin for user compilation %s", rec.ID)
	}

	constructorCode, constructorArgs, err := c.GetCreationCode(ctx)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	deploymentCode := constructorCode + constructorArgs
	opt := common.TraceTransactionOption{
		Tracer: lo.ToPtr("callTracer"),
		TracerConfig: common.CallTracerConfig{
			OnlyTopCall: false,
		},
		CreationOverrides: map[string]common.CreationOverride{
			rec.Origin.Address: {
				NewAddress: &address,
				NewCode:    lo.ToPtr("0x" + deploymentCode),
			},
		},
	}
	raw, err := client.DebugClient.Client.TraceTx(ctx, rec.Origin.CreationTx, opt)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	var trace common.CallTrace
	err = json.Unmarshal(raw, &trace)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	_, ret := trace.FindCreateCall(address)
	if ret == "" {
		err := fmt.Errorf("create call not found")
		logger.Errore(err)
		return "", err
	}
	return ret, nil
}

// use debug_traceCall to get deployed code
// requires debug_traceCall with createAddressOverride
func (c *UserCompilation) getDeployedCodeWithTraceCall(ctx context.Context, networkID models.ChainIdentifier, address string) (string, error) {
	logger := log.WithContext(ctx)

	constructorCode, constructorArgs, err := c.GetCreationCode(ctx)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	deploymentCode := constructorCode + constructorArgs

	client, err := c.chainClients.GetChainClient(ctx, networkID)
	if err != nil {
		logger.Errore(err)
		return "", err
	}

	opt := common.TraceTransactionOption{
		Tracer:                lo.ToPtr("callTracer"),
		CreateAddressOverride: &address,
		StateOverrides: map[string]*common.Account{
			"0x0000000000000000000000000000000000000000": {
				Balance: lo.ToPtr("0xffffffffffffffffffffffffffffffffffff"),
			},
		},
	}
	rec, err := c.GetDBRec(ctx)
	if err != nil {
		return "", err
	}
	if rec.RawBuildID != nil {
		opt.IgnoreCodeSizeLimit = lo.ToPtr(true)
		opt.IgnoreGas = lo.ToPtr(true)
	}

	var callTrace common.CallTrace
	_, err = client.DebugClient.RawCall(ctx, &callTrace, "debug_traceCall",
		common.TransactionParam{
			Data: "0x" + deploymentCode,
		},
		"latest", // TODO should be able to be specified
		opt)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	if callTrace.Error != "" {
		return "", errors.New(callTrace.Error)
	}
	return callTrace.Output, nil
}

// use eth_call to get deployed code
// unable to specify deployment address
func (c *UserCompilation) GetDeployedCodeWithEthCall(ctx context.Context, networkID models.ChainIdentifier) (string, error) {
	logger := log.WithContext(ctx)

	constructorCode, constructorArgs, err := c.GetCreationCode(ctx)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	deploymentCode := constructorCode + constructorArgs

	client, err := c.chainClients.GetChainClient(ctx, networkID)
	if err != nil {
		logger.Errore(err)
		return "", err
	}

	rec, err := c.GetDBRec(ctx)
	if err != nil {
		return "", err
	}
	if rec.RawBuildID != nil {
		return "", fmt.Errorf("cannot get deployed code for debug build")
	}

	var ret string
	_, err = client.Client.RawCall(ctx, &ret, "eth_call",
		common.TransactionParam{
			Data: "0x" + deploymentCode,
		},
		"latest", // TODO should be able to be specified
	)
	if err != nil {
		logger.Errore(err)
		return "", err
	}
	return ret, nil
}

func (p *Processor) CreateUserCompilation(
	ctx context.Context,
	id string,
	userID string,
	projectID string,
	name string,
	compileSpec *protos.SourceSpec,
	optimizerOverride *protos.Optimizer,
	attachDebugInfo bool,
	rawBuildID *string,
	origin *models.UserCompilationOrigin,
) (*UserCompilation, error) {
	logger := log.WithContext(ctx)

	if origin != nil {
		// determine constructor args from original creation tx
		// TODO shall also check and store constructor args when compiling on-chain contracts
		chainSpec := models.ChainIdentifier{
			ChainID: origin.ChainID,
		}
		client, err := p.chainClients.GetChainClient(ctx, chainSpec)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		originContract := p.WithContract(chainSpec, origin.Address, false)
		originCompilation, _, _, _, err, _ := originContract.FetchAndStore(ctx)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		opt := common.TraceTransactionOption{
			Tracer: lo.ToPtr("callTracer"),
			TracerConfig: common.CallTracerConfig{
				OnlyTopCall: false,
			},
		}
		raw, err := client.DebugClient.Client.TraceTx(ctx, origin.CreationTx, opt)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		var trace common.CallTrace
		err = json.Unmarshal(raw, &trace)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		input, _ := trace.FindCreateCall(origin.Address)
		if input == "" {
			err := fmt.Errorf("create call not found")
			logger.Errore(err)
			return nil, err
		}
		constructorCode, err := originCompilation.GetConstructorCode()
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		constructorCode = "0x" + constructorCode

		err = fmt.Errorf("original creation code mismatch with the compilation")
		if len(input) < len(constructorCode) {
			logger.Errore(err)
			return nil, err
		}
		originConstructorCode := input[:len(constructorCode)]
		constructorArgs := input[len(constructorCode):]
		if constructorCode != originConstructorCode {
			constructorCode, _ = common.TrimMetadata(constructorCode)
			originConstructorCode, _ = common.TrimMetadata(originConstructorCode)
		}
		if constructorCode != originConstructorCode {
			logger.Errore(err)
			return nil, err
		}
		compileSpec.ConstructorArgs = constructorArgs
	}

	c := p.UserCompilationByID(id)
	compileSpec.Id = fmt.Sprintf("userSourceFor(%s)", id)

	localPath := c.GetLocalCompilationPath()
	resp, err := p.debuggerClient.CompileSourceInternal(ctx, &protos.CompileSourceInternalRequest{
		Spec:              compileSpec,
		CompilationKey:    localPath,
		OptimizerOverride: optimizerOverride,
		AttachDebugInfo:   attachDebugInfo,
	})
	if err != nil {
		logger.Errorf("failed to compile user source, err: %v", err)
		return nil, err
	}
	compileSpec.Id = id // only return id in response

	if resp.Failure != nil {
		err := resp.Failure.Error
		if len(err) > 200 {
			err = err[:200]
		}
		logger.Errorf("failed to compile user source, reason: %s, err: %s", resp.Failure.Reason, err)
		return nil, fmt.Errorf("compile error: %s", resp.Failure.Error)
	}

	// store compilation and source
	data, err := os.ReadFile(localPath)
	if err != nil {
		logger.Errorf("failed to read local user compilation, err: %v", err)
		return nil, err
	}
	var compilation types.Compilation
	err = json.Unmarshal(data, &compilation)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	_ = compilation.Trim()
	if len(compilation.Compilations) != 1 || len(compilation.Compilations[0].Contracts) != 1 {
		return nil, fmt.Errorf("malformed compilation")
	}
	compilation.SourceInfo.Type = types.SourceTypeUser

	data, _ = json.Marshal(compilation.Compilations[0].Contracts[0].ABI)
	abi := string(data)

	data, _ = json.Marshal(compilation)

	obj := c.GetGcsCompilationHandle()
	compilationPath := obj.ObjectName()
	w := obj.NewWriter(ctx)
	gz := gzip.NewWriter(w)
	w.ContentType = "application/json"
	w.ContentEncoding = "gzip"
	_, err = gz.Write(data)
	if err != nil {
		return nil, err
	}
	_ = gz.Close()
	_ = w.Close()

	data, err = protojson.Marshal(compileSpec)
	if err != nil {
		return nil, err
	}
	obj = c.GetGcsSourceHandle()
	sourcePath := obj.ObjectName()
	w = obj.NewWriter(ctx)
	gz = gzip.NewWriter(w)
	w.ContentType = "application/json"
	w.ContentEncoding = "gzip"
	_, err = gz.Write(data)
	if err != nil {
		return nil, err
	}
	_ = gz.Close()
	_ = w.Close()

	if name == "" {
		name = compileSpec.Id
	}
	userCompilation := &models.UserCompilation{
		ID:              compileSpec.Id,
		UserID:          userID,
		ProjectID:       projectID,
		Name:            name,
		SourcePath:      sourcePath,
		CompilationPath: compilationPath,
		ContractName:    compileSpec.ContractName,
		ABI:             abi,
		SolidityVersion: compileSpec.SolidityVersion,
		ConstructorArgs: compileSpec.ConstructorArgs,
		RawBuildID:      rawBuildID,
		Origin:          origin,
	}
	err = p.envRepository.DB.WithContext(ctx).Create(userCompilation).Error
	if err != nil {
		return nil, err
	}
	return c, nil
}
