package contract

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel"

	"cloud.google.com/go/storage"
	"github.com/cenkalti/backoff/v4"
	gethcommon "github.com/ethereum/go-ethereum/common"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/sourcefetcher"
	"sentioxyz/sentio/service/common/requestcache/scripts"
	"sentioxyz/sentio/service/solidity/common"
	"sentioxyz/sentio/service/solidity/common/clients"
	"sentioxyz/sentio/service/solidity/models"
	"sentioxyz/sentio/service/solidity/protos"
	"sentioxyz/sentio/service/solidity/repository"
	"sentioxyz/sentio/service/solidity/types"
)

const (
	LockTTL                 = 60 * time.Second
	ProxyValidationInterval = 24 * time.Hour
	TagEIP1167              = "EIP1167"
)

var (
	tracer = otel.Tracer("solidity-service")

	ErrNoStoredData       = fmt.Errorf("no contract data stored")
	ClauseContractAddress = clause.OnConflict{
		Columns: []clause.Column{
			{Name: "chain_id"},
			{Name: "fork_id"},
			{Name: "address"},
		},
		UpdateAll: true,
	}
)

type Processor struct {
	redisClient    *redis.Client
	chainClients   *clients.ChainClients
	debuggerClient protos.DebuggerServiceClient
	gcsClient      *storage.Client
	syncRepository *repository.SyncRepository
	envRepository  *repository.EnvRepository
	retryPolicy    backoff.BackOff
	gcsBucket      string
	overrideBefore int64
}

func NewProcessor(
	syncRepository *repository.SyncRepository,
	envRepository *repository.EnvRepository,
	redisClient *redis.Client,
	chainClients *clients.ChainClients,
	debuggerClient protos.DebuggerServiceClient,
	gcsClient *storage.Client,
	gcsBucket string,
) *Processor {
	return &Processor{
		syncRepository: syncRepository,
		envRepository:  envRepository,
		redisClient:    redisClient,
		chainClients:   chainClients,
		debuggerClient: debuggerClient,
		gcsClient:      gcsClient,
		gcsBucket:      gcsBucket,
		retryPolicy:    backoff.WithMaxRetries(backoff.NewConstantBackOff(time.Second*2), 3),
		overrideBefore: 0,
	}
}

func (p *Processor) WithContract(chainSpec models.ChainIdentifier, address string, tryToDisableOptimizer bool) *Contract {
	return &Contract{
		Processor:             p,
		ChainID:               chainSpec,
		address:               strings.ToLower(address),
		tryToDisableOptimizer: tryToDisableOptimizer,
	}
}

func (p *Processor) WithOverrideBefore(timestamp int64) *Processor {
	p.overrideBefore = timestamp
	return p
}

type Contract struct {
	*Processor

	ChainID               models.ChainIdentifier
	address               string
	tryToDisableOptimizer bool

	creationInfo     *sourcefetcher.ContractCreation
	compileResult    *types.Compilation
	abi              []json.RawMessage
	compileDBMiss    bool
	compileError     error
	compileJSON      []byte
	fingerprint      string
	fingerprintError error
	optimizerPreset  string
	contractSource   *sourcefetcher.ContractSource
}

func (c *Contract) Address() string {
	return c.address
}

func (c *Contract) ChecksumAddress() string {
	return gethcommon.HexToAddress(c.address).Hex()
}

func (c *Contract) FetchAndStore(ctx context.Context) (
	compilation *types.Compilation,
	compilationData []byte,
	compileDBMiss bool,
	fingerprintDBMiss bool,
	compileErr error,
	fingerprintErr error,
) {
	// TODO check sourcefetcher first, and pass source into truffle
	compileDBMiss, compileErr, _ = c.CheckPermanentCompileError(ctx)
	if compileErr == nil {
		compilation, compilationData, compileDBMiss, compileErr = c.FetchAndStoreCompileResult(ctx)
	}
	_, _ = c.FetchAndStoreMetadata(ctx)
	_ = c.StoreSignatures(ctx)
	_, fingerprintDBMiss, fingerprintErr = c.FetchAndStoreFingerprint(ctx)
	return
}

func (c *Contract) FindMetadataByFingerprint(ctx context.Context) (*models.ContractMetadata, error) {
	fingerprint, err := c.Fingerprint(ctx)
	if err != nil {
		return nil, err
	}

	var contractFingerprints []models.ContractFingerprint
	err = c.syncRepository.DB.WithContext(ctx).Where(&models.ContractFingerprint{
		ChainID:             c.ChainID.ChainID,
		ForkID:              c.ChainID.ForkID,
		BytecodeFingerprint: fingerprint,
	}).Find(&contractFingerprints).Error
	if err != nil {
		return nil, err
	}

	for _, contractFingerprint := range contractFingerprints {
		if contractFingerprint.Address == c.address {
			continue
		}
		similarContract := c.Processor.WithContract(c.ChainID, contractFingerprint.Address, c.tryToDisableOptimizer)
		record, err := similarContract.FindMetadataRecord(ctx)
		if err != nil {
			continue
		}
		return record, nil
	}
	return nil, ErrNoStoredData
}

func (c *Contract) FindCompileResultByFingerprint(ctx context.Context) (*types.Compilation, []byte, error) {
	fingerprint, err := c.Fingerprint(ctx)
	if err != nil {
		return nil, nil, err
	}

	var contractFingerprints []models.ContractFingerprint
	err = c.syncRepository.DB.WithContext(ctx).Where(&models.ContractFingerprint{
		ChainID:             c.ChainID.ChainID,
		ForkID:              c.ChainID.ForkID,
		BytecodeFingerprint: fingerprint,
	}).Find(&contractFingerprints).Error
	if err != nil {
		return nil, nil, err
	}

	for _, contractFingerprint := range contractFingerprints {
		if contractFingerprint.Address == c.address {
			continue
		}
		similarContract := c.Processor.WithContract(c.ChainID, contractFingerprint.Address, c.tryToDisableOptimizer)
		record, _, err := similarContract.FindCompileRecord(ctx)
		if err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, nil, err
			}
			continue
		}
		if record.Status != models.StatusSuccess {
			continue
		}
		result, _, err := similarContract.FindCompileResult(ctx)
		if err != nil {
			if !errors.Is(err, ErrNoStoredData) {
				return nil, nil, err
			}
			// this shall not happen
			log.Warnf("compile result not found in gcs, while successful record exists in db, address: %s", c.address)
			_ = c.syncRepository.DB.WithContext(ctx).Delete(record)
			continue
		}

		sourceIDMap := map[string]string{}
		for _, compilation := range result.Compilations {
			if compilation.Address == similarContract.Address() {
				// replace compilation id and address
				oldCompilationID := compilation.ID
				newCompilationID := strings.Replace(
					oldCompilationID,
					gethcommon.HexToAddress(similarContract.Address()).Hex(),
					gethcommon.HexToAddress(c.Address()).Hex(),
					1,
				)
				compilation.Address = c.Address()
				compilation.ID = newCompilationID

				// replace source ids for user sources
				for i := range compilation.Sources {
					oldSourceID := makeSourceID(oldCompilationID, nil, i)
					newSourceID := makeSourceID(newCompilationID, nil, i)
					sourceIDMap[oldSourceID] = newSourceID
				}

				// replace source ids for generated sources
				// not tested yet, and may not work with link references
				// TODO test this with similar contract + generated source
				for _, contract := range compilation.Contracts {
					if contract.GeneratedSources != nil && len(contract.GeneratedSources) > 0 {
						contextHash := common.SoliditySHA3(contract.Bytecode.Bytes)
						for i := range contract.GeneratedSources {
							oldSourceID := makeSourceID(oldCompilationID, &contextHash, i)
							newSourceID := makeSourceID(newCompilationID, &contextHash, i)
							sourceIDMap[oldSourceID] = newSourceID
						}
					}
					if contract.DeployedGeneratedSources != nil && len(contract.DeployedGeneratedSources) > 0 {
						contextHash := common.SoliditySHA3(contract.DeployedBytecode.Bytes)
						for i := range contract.DeployedGeneratedSources {
							oldSourceID := makeSourceID(oldCompilationID, &contextHash, i)
							newSourceID := makeSourceID(newCompilationID, &contextHash, i)
							sourceIDMap[oldSourceID] = newSourceID
						}
					}
				}
			}
		}
		if result.ProcessedAST != nil {
			// replace source ids
			for oldSourceID, newSourceID := range sourceIDMap {
				if scopeMap, ok := result.ProcessedAST.Scopes.BySourceID[oldSourceID]; ok {
					for _, scope := range scopeMap.ByAstRef {
						scope.SourceID = newSourceID
						for _, variable := range scope.Variables {
							variable.SourceID = newSourceID
						}
					}
					result.ProcessedAST.Scopes.BySourceID[newSourceID] = scopeMap
					delete(result.ProcessedAST.Scopes.BySourceID, oldSourceID)
				}
			}
			for _, i := range result.ProcessedAST.UserDefinedTypes {
				if v, ok := sourceIDMap[i.SourceID]; ok {
					i.SourceID = v
				}
			}
			for _, i := range result.ProcessedAST.TaggedOutputs {
				if v, ok := sourceIDMap[i.SourceID]; ok {
					i.SourceID = v
				}
			}
		}

		// TODO constructor args substitution
		// TDODO substibiution better at js part

		result.SourceInfo.Parent = similarContract.Address()
		bytes, _ := json.Marshal(result)

		log.Infof("found compile result for [%s] by similar contract [%s]", c.Address(), similarContract.Address())
		return result, bytes, nil
	}
	return nil, nil, ErrNoStoredData
}

func (c *Contract) FindConstructorArgs(ctx context.Context) (string, error) {
	var similarContract models.SimilarContract
	err := c.syncRepository.DB.WithContext(ctx).
		Where(&models.SimilarContract{
			ChainID: c.ChainID.ChainID,
			ForkID:  c.ChainID.ForkID,
			Address: c.address,
		}).
		Take(&similarContract).
		Error
	if err == nil {
		return similarContract.ConstructorArgs, nil
	}
	// FIXME constructor args may not exist for old records
	rec, _, err := c.FindCompileRecord(ctx)
	if err == nil {
		return rec.ConstructorArgs, nil
	}
	return "", err
}

func (c *Contract) FindConstructorArgsByFingerprint(ctx context.Context) (string, error) {
	fingerprint, err := c.Fingerprint(ctx)
	if err != nil {
		return "", err
	}
	var contractFingerprints []models.ContractFingerprint
	err = c.syncRepository.DB.WithContext(ctx).Where(&models.ContractFingerprint{
		ChainID:             c.ChainID.ChainID,
		ForkID:              c.ChainID.ForkID,
		BytecodeFingerprint: fingerprint,
	}).Find(&contractFingerprints).Error
	if err != nil {
		return "", err
	}

	for _, contractFingerprint := range contractFingerprints {
		// hack: allow to find constructor args from itself,
		// because constructor args fetched directly can be unreliable
		//if contractFingerprint.Address == c.address {
		//	continue
		//}
		similarContract := c.Processor.WithContract(c.ChainID, contractFingerprint.Address, c.tryToDisableOptimizer)
		parentConstructorArgs, err := similarContract.FindConstructorArgs(ctx)
		if err != nil {
			continue
		}

		bytes, _, err := c.getDeploymentBytecode(ctx)
		if err != nil {
			continue
		}
		bytecode := gethcommon.Bytes2Hex(bytes)

		if len(bytecode) < len(parentConstructorArgs) {
			return "", fmt.Errorf("bytecode length less than the parent's constructor args, address: %s, parent: %s",
				c.address, similarContract.address)
		}
		ret := bytecode[len(bytecode)-len(parentConstructorArgs):]

		rec := &models.SimilarContract{
			ChainID:         c.ChainID.ChainID,
			ForkID:          c.ChainID.ForkID,
			Address:         c.address,
			ParentAddress:   similarContract.address,
			ConstructorArgs: ret,
		}
		if err := c.syncRepository.DB.WithContext(ctx).
			Clauses(ClauseContractAddress).
			Create(rec).Error; err != nil {
			return "", err
		}
		return ret, nil
	}
	return "", fmt.Errorf("not found")
}

func (c *Contract) GetCompilationBytecode(ctx context.Context) (string, string, error) {
	logger := log.WithContext(ctx)
	compilation, _, _, err := c.Compile(ctx)
	if err != nil {
		logger.Warnf("failed to compile contract directly, addr: %s, disableOptimizer: %v, err: %v",
			c.address, c.tryToDisableOptimizer, err)
		compilation, _, err = c.FindCompileResultByFingerprint(ctx)
	}
	if err != nil {
		logger.Warnf("failed to compile contract by fingerprint, addr: %s, disableOptimizer: %v, err: %v",
			c.address, c.tryToDisableOptimizer, err)
		return "", "", err
	}
	// find creation bytecode (constructor code + contract code)
	// similar contracts share the same creation bytecode
	bytecode, err := compilation.GetConstructorCode()
	if err != nil {
		logger.Errore(err)
		return "", "", err
	}

	bytecodeWithoutMetadata := bytecode
	hasMetadata := true
	options := compilation.SourceInfo.Options
	if options != nil && options.Settings != nil && options.Settings.Metadata != nil {
		metadataSettings := options.Settings.Metadata
		if lo.FromPtr(metadataSettings.AppendCBOR) ||
			metadataSettings.BytecodeHash == nil ||
			lo.FromPtr(metadataSettings.BytecodeHash) != "none" { // if bytecodeHash is omitted, "ipfs"is used by default
			hasMetadata = false
		}
	}
	if hasMetadata {
		bytecodeWithoutMetadata, err = common.TrimMetadata(bytecode)
		if err != nil {
			return "", "", err
		}
	}
	return bytecode, bytecodeWithoutMetadata, nil
}

func (c *Contract) GetDeployedCodeOverride(ctx context.Context) (string, error) {
	logger := log.WithContext(ctx)
	if !c.tryToDisableOptimizer {
		return "", fmt.Errorf("no need to override bytecode")
	}
	obj := c.getGCSDeployedCodeHandle()
	var err error
	if _, err = obj.Attrs(ctx); err == nil {
		ctx, span := tracer.Start(ctx, "gcs/read/deployed_code")
		defer span.End()
		rd, err := obj.NewReader(ctx)
		if err != nil {
			return "", err
		}
		defer rd.Close()
		data, err := io.ReadAll(rd)
		if err != nil {
			return "", err
		}
		return string(data), nil
	}
	if !errors.Is(err, storage.ErrObjectNotExist) {
		return "", err
	}
	logger.Infof("deployed code missing in gcs, address: %s", c.address)

	if err := c.acquireLock(ctx, "deployedCode"); err != nil {
		return "", err
	}
	defer c.releaseLock(ctx, "deployedCode")
	// creation bytecode: constructor code + contract code
	// deployment bytecode: creation bytecode + constructor args
	// constructor args can differ between similar contracts

	// notice: constructor args from polygonscan can be unreliable (not sure for sourcefetcher)
	// sometimes it just returns the data for its similar contract
	// and length of constructor args can vary between similar contracts

	// get original creation bytecode from compilations
	rawContract := c.Processor.WithContract(c.ChainID, c.address, false)
	rawCreationBytecode, rawCreationBytecodeWithoutMetadata, err := rawContract.GetCompilationBytecode(ctx)
	if err != nil {
		return "", err
	}

	// get original creation bytecode from creation txn
	rawDeploymentBytecodeBytes, rawDeployedBytecodeBytes, err := rawContract.getDeploymentBytecode(ctx)
	if err != nil {
		logger.Errorf("failed to get original creation bytecode, address: %s, err: %v", c.address, err)
		return "", err
	}
	rawDeploymentBytecode := hex.EncodeToString(rawDeploymentBytecodeBytes)
	rawDeployedBytecode := hex.EncodeToString(rawDeployedBytecodeBytes)

	// get non-optimized init bytecode from compilations
	creationBytecode, _, err := c.GetCompilationBytecode(ctx)
	if err != nil {
		return "", err
	}

	// check if creation bytecode on chain matches bytecode in compilation
	var constructorArgs string
	if common.IsEIP1167(rawDeployedBytecode) {
		// in this case, sourcefetcher returns it's proxied contract as result
		// we must not use that
		// keep its original deployed bytecode in convenience
		creationBytecode = rawDeploymentBytecode
		constructorArgs = ""

	} else if strings.HasPrefix(rawDeploymentBytecode, rawCreationBytecodeWithoutMetadata) {
		constructorArgs = rawDeploymentBytecode[len(rawCreationBytecode):]

	} else {
		logger.Warnf("creation bytecode on chain mismatched with compilations, address: %s", c.address)
		// FIXME some contracts seem to break the metadata pattern, why?
		// for example 0xf8956e715b9aa5897c6e81ce50b4c7256f43df21

		// try to handle broken metadata pattern in a dumb way
		// assume metadata is in the last 200 bytes
		fallback := true
		if len(rawCreationBytecode) >= 400 {
			rawCreationBytecodeWithoutMetadata = rawCreationBytecode[:len(rawCreationBytecode)-400]
			if strings.HasPrefix(rawDeploymentBytecode, rawCreationBytecodeWithoutMetadata) {
				logger.Infof("creation bytecode on chain matched with compilations after truncating, address: %s", c.address)
				constructorArgs = rawDeploymentBytecode[len(rawCreationBytecode):]
				fallback = false
			}
		}

		if fallback {
			// find constructor args by fingerprint first
			constructorArgs, err = rawContract.FindConstructorArgsByFingerprint(ctx)
			if err != nil {
				logger.Warnf("failed to find constructor args by fingerprint, address: %s, err: %v", c.address, err)
				constructorArgs, err = rawContract.FindConstructorArgs(ctx)
			}
			if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Infof("original version for %s is missing, compiling", c.address)
				rawContract.FetchAndStore(ctx)
				constructorArgs, err = rawContract.FindConstructorArgs(ctx)
			}
			if err != nil {
				logger.Warnf("failed to find constructor args directly, address: %s, err: %v", c.address, err)
				return "", err
			}
		}
	}
	logger.Infof("got constructor args, address: %s, args: %s", c.address, constructorArgs)

	deploymentBytecode := creationBytecode + constructorArgs

	// override the creation bytecode in the original creation txn to get the final contract code on chain
	deployedBytecode, err := rawContract.getDeployedCode(ctx, c.ChainID, deploymentBytecode)
	if err != nil {
		logger.Warnf("failed to get deployed bytecode override, address: %s, err: %v", c.address, err)
		return "", err
	}
	logger.Infof("successfully got deployed bytecode override, address: %s", c.address)

	deployedBytecode = strings.TrimPrefix(deployedBytecode, "0x")

	w := obj.NewWriter(ctx)
	defer w.Close()
	if _, err := w.Write([]byte(deployedBytecode)); err != nil {
		return "", err
	}
	return deployedBytecode, nil
}

func (c *Contract) getDeployedCode(ctx context.Context, chainSpec models.ChainIdentifier, creationCodeOverride string) (string, error) {
	if !strings.HasPrefix(creationCodeOverride, "0x") {
		creationCodeOverride = "0x" + creationCodeOverride
	}

	logger := log.WithContext(ctx)
	rec, _, err := c.FindFingerprintRecord(ctx)
	if err != nil {
		logger.Warnf("failed to find fingerprint record, address: %s, err: %v", c.address, err)
		return "", err
	}
	txHash := rec.CreationTxnHash
	if len(txHash) == 0 {
		logger.Warnf("got empty creation txn hash, address: %s", c.address)
		return "", fmt.Errorf("empty creation txn hash")
	}

	// rerun creation txn with no gas limit and creation bytecode override
	var trace common.CallTrace

	chainClient, err := c.chainClients.GetChainClient(ctx, chainSpec)
	if err != nil {
		return "", err
	}

	_, err = chainClient.DebugClient.RawCall(ctx, &trace, "debug_traceTransaction", txHash,
		common.TraceTransactionOption{
			Tracer:              lo.ToPtr("callTracer"),
			IgnoreGas:           lo.ToPtr(true),
			IgnoreCodeSizeLimit: lo.ToPtr(true),
			CreationOverrides: map[string]common.CreationOverride{
				c.address: {
					NewCode: lo.ToPtr(creationCodeOverride),
				},
			},
			TracerConfig: common.CallTracerConfig{
				OnlyTopCall: false,
			},
		})
	if err != nil {
		logger.Warnf("failed to call debug_traceTransaction, txHash: %s, err: %v", txHash, err)
		return "", err
	}
	if _, output := trace.FindCreateCall(c.address); output != "" {
		return output, nil
	}
	return "", fmt.Errorf("CREATE/CREATE2 not found")
}

var DeployedCodeVersion = 6

func (c *Contract) getGCSDeployedCodeHandle() *storage.ObjectHandle {
	prefix := ""
	if c.ChainID.IsFork() {
		prefix = "custom_chains/fork_" + c.ChainID.ForkID
	} else if c.ChainID.IsBuiltin() {
		prefix = "chain_" + c.ChainID.ChainID
	}
	name := fmt.Sprintf("%s/deployed_code/version_%d/address_%s", prefix, DeployedCodeVersion, c.address)
	return c.gcsClient.Bucket(c.gcsBucket).Object(name)
}

func (c *Contract) lockKey(name string) string {
	return fmt.Sprintf("solidity_contract_lock_%s_%s_%s", c.ChainID.ToString(), c.address, name)
}

func (c *Contract) acquireLock(ctx context.Context, name string) error {
	if c.redisClient == nil {
		// skip this check in sync
		return nil
	}
	ctxNew, span := tracer.Start(ctx, "acquireLock")
	defer span.End()

	deadline := time.Now().Add(LockTTL)
	for time.Now().Before(deadline) {
		ok, err := c.tryAcquireLock(ctxNew, name)
		if err != nil {
			return err
		}
		if ok {
			return nil
		}
		interval := time.Duration(800+rand.Intn(200)) * time.Millisecond
		time.Sleep(interval)
	}
	return fmt.Errorf("failed to acquire lock")
}

func (c *Contract) tryAcquireLock(ctx context.Context, name string) (bool, error) {
	key := c.lockKey(name)
	statusCode, err := c.redisClient.Eval(ctx, scripts.CASTemplate, []string{key},
		"processing", "processing", int(LockTTL.Seconds())).Result()
	if err != nil {
		return false, err
	}
	return statusCode.(int64) == 0, nil
}

func (c *Contract) releaseLock(ctx context.Context, name string) error {
	if c.redisClient == nil {
		// skip this check in sync
		return nil
	}
	return c.redisClient.Del(ctx, c.lockKey(name)).Err()
}

func (c *Contract) FetchSource(ctx context.Context) (*sourcefetcher.ContractSource, error) {
	logger := log.WithContext(ctx)
	if c.contractSource != nil {
		return c.contractSource, nil
	}
	chainClient, err := c.chainClients.GetChainClient(ctx, c.ChainID)
	if err != nil {
		return nil, err
	}
	source, err := chainClient.SourceFetcher.GetSourceCode(ctx, c.address)
	if err != nil {
		if types.ErrorContains([]error{sourcefetcher.ErrNotVerified, sourcefetcher.ErrRequestFailed}, err) {
			return nil, err
		}
		return nil, fmt.Errorf("sourcefetcher error: %v", err)
	}
	// sourcefetcher returns code of proxied contract for eip1167
	// we don't want that
	code, err := chainClient.Client.GetCode(ctx, c.address, "latest")
	if err != nil {
		return nil, err
	}
	if common.IsEIP1167(code) {
		return nil, types.ErrEIP1167
	}
	// lineascan returns wrong result for EOAs
	if code == "0x" {
		logger.Warnf("got source code but no code form the node, chainID: %v, address: %v", c.ChainID, c.address)
		return nil, sourcefetcher.ErrNotVerified
	}
	c.contractSource = source
	return source, nil
}

func (c *Contract) FindMetadataRecord(ctx context.Context) (*models.ContractMetadata, error) {
	var rec models.ContractMetadata
	err := c.syncRepository.DB.WithContext(ctx).
		Where(&models.ContractFetchRecord{
			ChainID: c.ChainID.ChainID,
			ForkID:  c.ChainID.ForkID,
			Address: c.address,
		}).
		Take(&rec).Error
	if err != nil {
		return nil, err
	}
	if rec.Error != nil { // only permanent errors are recorded here
		return nil, rec.Error
	}
	return &rec, nil
}

func (c *Contract) FetchAndStoreMetadata(ctx context.Context) (*models.ContractMetadata, error) {
	rec, err := c.FindMetadataRecord(ctx)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	} else {
		if !rec.IsProxy {
			return rec, nil
		}
		if time.Now().Sub(rec.UpdatedAt) < ProxyValidationInterval {
			return rec, nil
		}
		log.WithContext(ctx).Infof("validate metadata for proxy contract, address: %s", c.address)
	}
	source, err := c.FetchSource(ctx)
	if err != nil {
		if types.IsPermanentError(err) {
			rec = &models.ContractMetadata{
				ChainID:        c.ChainID.ChainID,
				ForkID:         c.ChainID.ForkID,
				Address:        c.address,
				ModelWithError: models.ModelWithError{Error: err},
			}
			if types.ErrorEquals(err, types.ErrEIP1167) {
				chainClient, err := c.chainClients.GetChainClient(ctx, c.ChainID)
				if err != nil {
					return nil, err
				}
				code, err := chainClient.Client.GetCode(ctx, c.address, "latest")
				if err != nil {
					return nil, err
				}
				impl, err := common.ExtractEIP1167Implementation(code)
				if err != nil {
					return nil, err
				}
				rec.Tag = TagEIP1167
				rec.IsProxy = true
				rec.ImplAddress = impl
				rec.ModelWithError = models.ModelWithError{}
			}
			_ = c.syncRepository.DB.WithContext(ctx).
				Clauses(ClauseContractAddress).
				Create(rec).Error
			return rec, nil
		}
		return nil, err
	}
	runs, _ := strconv.ParseInt(source.Runs, 10, 64)
	rec = &models.ContractMetadata{
		ChainID:          c.ChainID.ChainID,
		ForkID:           c.ChainID.ForkID,
		Address:          c.address,
		ContractName:     source.ContractName,
		ABI:              source.ABI,
		IsProxy:          source.Proxy == "1",
		ImplAddress:      source.Implementation,
		CompilerVersion:  source.CompilerVersion,
		EVMVersion:       source.EVMVersion,
		OptimizationUsed: source.OptimizationUsed == "1",
		Runs:             runs,
		ConstructorArgs:  source.ConstructorArguments,
		Library:          source.Library,
	}
	err = c.syncRepository.DB.WithContext(ctx).
		Clauses(ClauseContractAddress).
		Create(rec).Error
	if err != nil {
		return nil, err
	}
	if len(c.abi) == 0 {
		if err := json.Unmarshal([]byte(source.ABI), &c.abi); err == nil {
			_ = c.StoreSignatures(ctx)
		}
	}
	return rec, nil
}

func (c *Contract) ValidateVerification(ctx context.Context) {
	if c.compileError == nil || !types.ErrorEquals(c.compileError, sourcefetcher.ErrNotVerified) {
		return
	}
	if err := c.acquireLock(ctx, "compile"); err != nil {
		return
	}
	defer c.releaseLock(ctx, "compile")

	if _, err := c.FetchSource(ctx); err != nil {
		return
	}
	_, _, _, err := c.Compile(ctx)
	if err != nil {
		return
	}
	log.WithContext(ctx).Infof("found recently verified contract, address: %s", c.address)

	cond := map[string]interface{}{
		"chain_id": c.ChainID.ChainID,
		"fork_id":  c.ChainID.ForkID,
		"address":  c.address,
	}
	db := c.syncRepository.DB.WithContext(ctx)
	_ = db.Where(cond).Delete(&models.ContractFetchRecord{}).Error
	_ = db.Where(cond).Delete(&models.ContractFingerprint{}).Error
	_ = db.Where(cond).Delete(&models.ContractMetadata{}).Error
}

func makeSourceID(compilationID string, contextHash *string, index int) string {
	_contextHash := `null`
	if contextHash != nil {
		_contextHash = `"` + *contextHash + `"`
	}
	return common.SoliditySHA3(fmt.Sprintf(`{"compilationId":"%s","contextHash":%s,"index":%d}`,
		compilationID, _contextHash, index))
}
