package solidity

import (
	"archive/zip"
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/fs"
	"math"
	"math/big"
	"net"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/unpackdev/solgo"
	"github.com/unpackdev/solgo/parser"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm/clause"

	evmprotos "sentioxyz/sentio/chain/evm/protos"
	"sentioxyz/sentio/common/ethclient"
	"sentioxyz/sentio/common/sourcefetcher"
	"sentioxyz/sentio/common/utils"
	priceprotos "sentioxyz/sentio/service/price/protos"
	"sentioxyz/sentio/service/solidity/common/txpool"
	"sentioxyz/sentio/service/solidity/models"
	"sentioxyz/sentio/service/solidity/semantic"
	"sentioxyz/sentio/service/solidity/types"

	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/structpb"

	"cloud.google.com/go/storage"
	"github.com/gammazero/workerpool"
	"github.com/pkg/errors"
	redis "github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/protojson"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/solidity/common"
	"sentioxyz/sentio/service/solidity/common/clients"
	"sentioxyz/sentio/service/solidity/common/contract"
	"sentioxyz/sentio/service/solidity/protos"
	"sentioxyz/sentio/service/solidity/repository"
)

const (
	RedisTTL = 30 * time.Minute
)

var (
	tracer = otel.Tracer("solidity-service")

	gasEstimationSupportChainIds = []string{"1", "137", "8453"}
)

type Service struct {
	protos.UnimplementedSolidityServiceServer

	gcsBucket         string
	storeCompilations bool
	authManager       auth.AuthManager
	redisClient       *redis.Client
	debuggerClient    protos.DebuggerServiceClient
	priceClient       priceprotos.PriceServiceClient
	gcsClient         *storage.Client

	chainClients *clients.ChainClients
	ethSigClient *clients.EthSignatureClient

	syncRepository *repository.SyncRepository
	envRepository  *repository.EnvRepository
	evmRepository  *repository.EvmRepository

	contractProcessor *contract.Processor
	LocalCacheCleaner *common.LocalCacheCleaner
	activeSyncPool    *workerpool.WorkerPool

	txPoolPolls map[string]*txpool.Poll

	compileMissCounter       metric.Int64Counter
	activeSyncPendingGauge   metric.Int64ObservableGauge
	activeSyncSuccessCounter metric.Int64Counter
	activeSyncErrorCounter   metric.Int64Counter

	u UniversalSearch
}

func NewService(
	syncDB *gorm.DB,
	envDB *gorm.DB,
	redisClient *redis.Client,
	chainClients *clients.ChainClients,
	gcsBucket string,
	debuggerClient protos.DebuggerServiceClient,
	priceClient priceprotos.PriceServiceClient,
	gcsClient *storage.Client,
	storeCompilations bool,
	activeSyncWorkers int,
	authManager auth.AuthManager,
	chainConfigs map[string]models.ChainConfig,
) (*Service, error) {
	syncRepo := repository.NewSyncRepository(syncDB)
	envRepo := repository.NewEnvRepository(envDB)
	contractProcessor := contract.NewProcessor(syncRepo, envRepo, redisClient, chainClients, debuggerClient, gcsClient, gcsBucket)

	localCacheCleaner := common.NewLocalCacheCleaner(common.GetWorkingDir(), 10*time.Minute)
	localCacheCleaner.Run(context.Background())

	evmRepo := repository.NewEvmRepository(chainConfigs)

	activeSyncPool := workerpool.New(activeSyncWorkers)

	meter := otel.Meter("solidity-service")
	compileMissCounter, _ := meter.Int64Counter("compile_miss")
	activeSyncPendingGauge, _ := meter.Int64ObservableGauge("active_sync_pending")
	activeSyncSuccessCounter, _ := meter.Int64Counter("active_sync_success")
	activeSyncErrorCounter, _ := meter.Int64Counter("active_sync_error")

	_, err := meter.RegisterCallback(func(ctx context.Context, o metric.Observer) error {
		o.ObserveInt64(activeSyncPendingGauge, int64(activeSyncPool.WaitingQueueSize()))
		return nil
	}, activeSyncPendingGauge)
	if err != nil {
		return nil, err
	}

	ethSigClient := clients.NewEthSignatureClient()

	txPoolPolls := map[string]*txpool.Poll{}
	for _, chainId := range gasEstimationSupportChainIds {
		client, _ := chainClients.GetBuiltinChainClient(models.ChainIdentifier{ChainID: chainId})
		txPoolPolls[chainId] = txpool.NewPoll(chainId, client.Client.Client, time.Second)
		txPoolPolls[chainId].Start()
	}

	return &Service{
		authManager:              authManager,
		gcsBucket:                gcsBucket,
		priceClient:              priceClient,
		debuggerClient:           debuggerClient,
		gcsClient:                gcsClient,
		contractProcessor:        contractProcessor,
		LocalCacheCleaner:        localCacheCleaner,
		activeSyncPool:           activeSyncPool,
		storeCompilations:        storeCompilations,
		chainClients:             chainClients,
		ethSigClient:             ethSigClient,
		syncRepository:           syncRepo,
		envRepository:            envRepo,
		evmRepository:            evmRepo,
		redisClient:              redisClient,
		txPoolPolls:              txPoolPolls,
		compileMissCounter:       compileMissCounter,
		activeSyncPendingGauge:   activeSyncPendingGauge,
		activeSyncSuccessCounter: activeSyncSuccessCounter,
		activeSyncErrorCounter:   activeSyncErrorCounter,
		u: UniversalSearch{
			chainConfigs: chainClients,
		},
	}, nil
}

func getCompileRedisPrefix(projectID string) string {
	return "solidity_fetch_and_compile_" + projectID
}

func getCompileRedisKey(projectID string, req *protos.FetchAndCompileRequest) (string, error) {
	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s_%s_%v_%v_%v_%v", getCompileRedisPrefix(projectID),
		chainSpec.ToString(), req.TxId, req.Addresses, req.DisableOptimizer, req.SourceOnly), nil
}

func (s *Service) FetchAndCompile(
	ctx context.Context,
	req *protos.FetchAndCompileRequest,
) (*httpbody.HttpBody, error) {
	s.LocalCacheCleaner.Acquire()
	defer s.LocalCacheCleaner.Release()

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	useCache := true
	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		v := md.Get("cache")
		if len(v) > 0 && v[0] == "disabled" {
			useCache = false
		}
	}

	logger := log.WithContext(ctx)
	logger.Infof("FetchAndCompile, req: %v", req)
	var projectID string
	// skip check if passing in addresses
	if req.Addresses == nil {
		projectID, err = s.authTxRead(ctx, req.ProjectOwner, req.ProjectSlug, req.TxId, req.ShareId)
	} else {
		_, projectID, err = s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	}
	if err != nil {
		return nil, err
	}

	key, err := getCompileRedisKey(projectID, req)
	if err != nil {
		return nil, err
	}
	if useCache {
		if ret, err := s.redisClient.Get(ctx, key).Bytes(); err == nil {
			data, err := utils.DecompressBytes(ret)
			if err != nil {
				return nil, err
			}

			logger.Infof(
				"got fetch and compile result in redis, networkId: %s, txID: %s, disableOptimizer: %v, sourceOnly: %v",
				chainSpec.ToString(),
				req.TxId,
				req.DisableOptimizer,
				req.SourceOnly,
			)
			return &httpbody.HttpBody{
				ContentType: "application/json",
				Data:        data,
			}, nil
		}
	}

	var userCompilationModelMap map[string]*models.UserCompilation
	var contractAddresses []string
	if req.Addresses == nil {
		userCompilationModelMap, contractAddresses, err = s.findUserCompilations(ctx, projectID, *chainSpec, req.TxId, req.DisableOptimizer)
	} else {
		userCompilationModelMap, contractAddresses, err = s.findUserCompilationsByAddresses(ctx, projectID, *chainSpec, req.TxId, req.Addresses, req.DisableOptimizer)
	}
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	userCompilationMap, err := s.readUserCompilations(ctx, userCompilationModelMap)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	if req.DisableOptimizer {
		// ensure we already have original version of artifacts
		if _, _, err := s.fetchAndCompile(ctx, *chainSpec, contractAddresses, false); err != nil {
			return nil, err
		}
	}
	_, resp, err := s.fetchAndCompile(ctx, *chainSpec, contractAddresses, req.DisableOptimizer)
	if err != nil {
		return nil, err
	}
	for _, compilation := range userCompilationMap {
		resp.Result = append(resp.Result, compilation.Compilations...)
		resp.SourceInfo[compilation.Compilations[0].Address] = compilation.SourceInfo
		mergeProcessedAST(resp, compilation)
	}

	if req.DisableOptimizer {
		codeOverrides, err := s.getCodeOverrides(ctx, *chainSpec, contractAddresses)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		if len(resp.Result) < len(codeOverrides) {
			err := fmt.Errorf("compile result mismatch with code override")
			logger.Errore(err)
			return nil, err
		}
		resp.CodeOverrides = codeOverrides
	}

	if req.SourceOnly {
		// drop codeOverrides, processedAST
		newResp := &types.FetchAndCompileResponse{
			Result:     []*types.FileCompilation{},
			SourceInfo: resp.SourceInfo,
			Proxy:      resp.Proxy,
		}
		for _, file := range resp.Result {
			// drop contracts, settings
			newFile := &types.FileCompilation{
				ID:                    file.ID,
				Address:               file.Address,
				Compiler:              file.Compiler,
				UnreliableSourceOrder: file.UnreliableSourceOrder,
				Sources:               []*types.Source{},
			}
			for _, source := range file.Sources {
				// drop ast
				newSource := &types.Source{
					ID:         source.ID,
					Language:   source.Language,
					Compiler:   source.Compiler,
					SourcePath: source.SourcePath,
					Source:     source.Source,
				}
				newFile.Sources = append(newFile.Sources, newSource)
			}
			newResp.Result = append(newResp.Result, newFile)
		}
		resp = newResp
	} else {
		if resp.CodeOverrides == nil {
			resp.CodeOverrides = map[string]string{}
		}
		for address, compilation := range userCompilationMap {
			c := s.contractProcessor.UserCompilationByContent(userCompilationModelMap[address], compilation)
			code, err := c.GetDeployedCode(ctx, *chainSpec, address)
			if err != nil {
				logger.Errore(err)
				return nil, status.Errorf(codes.Internal, "unable to get code for user sources")
			}
			resp.CodeOverrides[address] = strings.TrimPrefix(code, "0x")
		}

		// return codes if not requesting debug build
		// also return on-chain code if failed to get debug build
		var missingAddrs []string
		for _, addr := range contractAddresses {
			if _, ok := resp.CodeOverrides[addr]; !ok {
				missingAddrs = append(missingAddrs, addr)
			}
		}
		chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
		if err != nil {
			return nil, err
		}
		if len(missingAddrs) > 0 {
			blockNumber := "latest"
			if req.TxId != nil {
				txs, err := chainClient.Client.GetTransactions(ctx, req.TxId)
				if err != nil {
					logger.Errore(err)
					return nil, err
				}
				blockNumber = txs[0].BlockNumber
			}
			g, gCtx := errgroup.WithContext(ctx)
			var m sync.Mutex
			for _, _addr := range missingAddrs {
				addr := _addr
				g.Go(func() error {
					code, err := chainClient.Client.GetCode(gCtx, addr, blockNumber)
					if err != nil {
						return err
					}
					m.Lock()
					defer m.Unlock()
					resp.CodeOverrides[addr] = strings.TrimPrefix(code, "0x")
					return nil
				})
			}
			if err := g.Wait(); err != nil {
				logger.Errore(err)
			}
		}
	}

	// we don't want to replace all those sourceIDs
	// so leave AST processing to truffle for now
	// FIXME avoid compilationID substitution or sourceID substitution?
	if len(userCompilationMap) > 0 {
		resp.ProcessedAST = nil
	}

	_, span := tracer.Start(ctx, "compilation/marshal")
	data, err := json.Marshal(resp)
	if err != nil {
		span.End()
		return nil, err
	}
	span.End()

	if useCache && len(resp.Result) > 0 {
		_, span := tracer.Start(ctx, "redis/compilation/write")
		defer span.End()

		compressed, err := utils.CompressBytes(data)
		if err != nil {
			return nil, err
		}
		if err := s.redisClient.Set(ctx, key, compressed, RedisTTL).Err(); err != nil {
			logger.Warnf("failed to set redis, err: %v", err)
		}
	}
	return &httpbody.HttpBody{
		ContentType: "application/json",
		Data:        data,
	}, nil
}

func (s *Service) fetchAndCompile(
	ctx context.Context,
	chainSpec models.ChainIdentifier,
	contractAddresses []string,
	disableOptimizer bool,
) (map[string]string, *types.FetchAndCompileResponse, error) {
	logger := log.WithContext(ctx)
	var err error

	syncCtx := context.Background()
	var addrMutex, respMutex sync.Mutex
	var missingContractAddresses, failedContractAddresses []string
	compilationIDs := map[string]bool{}
	resp := &types.FetchAndCompileResponse{
		Result:     []*types.FileCompilation{},
		SourceInfo: map[string]*types.SourceInfo{},
		ProcessedAST: &types.ProcessedAST{
			Scopes: &types.Scopes{
				BySourceID: map[string]*types.ByAstRef{},
			},
			UserDefinedTypes: []*types.ASTNodeRef{},
			TaggedOutputs:    []*types.ASTNodeRef{},
		},
		Proxy: map[string]string{},
	}
	respMap := map[string]string{}

	compileContracts := func(contracts map[string]*contract.Contract) error {
		g, gCtx := errgroup.WithContext(ctx)
		for addr := range contracts {
			c := contracts[addr]
			g.Go(func() error {
				md, err := c.FetchAndStoreMetadata(gCtx)
				if err == nil && md.IsProxy {
					respMutex.Lock()
					defer respMutex.Unlock()
					resp.Proxy[c.Address()] = md.ImplAddress
				}
				return nil
			})
			g.Go(func() error {
				// TODO try skip this
				ret, raw, dbMiss, compileErr := c.Compile(gCtx)
				if dbMiss {
					addrMutex.Lock()
					missingContractAddresses = append(missingContractAddresses, c.Address())
					addrMutex.Unlock()
				}
				if compileErr != nil {
					logger.Warnf("failed to compile, address: %s, err: %v", c.Address(), compileErr)

					ret, raw, err = c.FindCompileResultByFingerprint(gCtx)
					if err != nil {
						logger.Warnf("failed to compile by fingerprint, address: %s, err: %v", c.Address(), err)

						if disableOptimizer {
							// compile again with normal optimizer later
							addrMutex.Lock()
							failedContractAddresses = append(failedContractAddresses, c.Address())
							addrMutex.Unlock()
						}
						return nil
					}
					logger.Infof("compile by fingerprint success, address: %s", c.Address())

				} else {
					logger.Infof("compile success, address: %s", c.Address())
				}

				path := c.GetLocalCompilationPath()
				dir := filepath.Dir(path)
				errWrite := os.MkdirAll(dir, 0755)
				if errWrite != nil {
					return errWrite
				}

				errWrite = os.WriteFile(path, raw, 0644)
				if errWrite != nil {
					return errWrite
				}

				respMutex.Lock()
				defer respMutex.Unlock()

				respMap[c.Address()] = path

				resp.SourceInfo[c.Address()] = ret.SourceInfo
				mergeProcessedAST(resp, ret)
				// FIXME will there be any compilationID conflict?
				for _, compilation := range ret.Compilations {
					if _, ok := compilationIDs[compilation.ID]; !ok {
						compilationIDs[compilation.ID] = true
						resp.Result = append(resp.Result, compilation)
					}
				}
				return nil
			})
			contracts[addr] = c
		}
		return g.Wait()
	}
	storeContracts := func(contracts map[string]*contract.Contract) {
		// make sure compilations are ready in gcs
		var wg sync.WaitGroup
		for _, c := range contracts {
			wg.Add(1)
			go func(c *contract.Contract) {
				defer wg.Done()
				c.ValidateVerification(syncCtx)
				_, _, _, _ = c.FetchAndStoreCompileResult(syncCtx)
			}(c)
		}
		wg.Wait()

		// calc fingerprint in background
		for _, c := range contracts {
			go func(c *contract.Contract) {
				_ = c.StoreSignatures(syncCtx)
				_, _, _ = c.FetchAndStoreFingerprint(syncCtx)
			}(c)
		}
	}

	contracts := map[string]*contract.Contract{}
	for _, addr := range contractAddresses {
		contracts[addr] = s.contractProcessor.WithContract(chainSpec, addr, disableOptimizer)
	}
	err = compileContracts(contracts)
	if err != nil {
		logger.Errorf("compile contracts failed, err: %v", err)
		return nil, nil, err
	}
	s.compileMissCounter.Add(ctx, int64(len(missingContractAddresses)),
		metric.WithAttributes(attribute.Bool("disable_optimizer", disableOptimizer)))
	logger.Infof("%d missing contracts: %v", len(missingContractAddresses), missingContractAddresses)

	if len(failedContractAddresses) > 0 && disableOptimizer {
		// try with normal optimizer on failed contracts
		contractsWithNormalOpt := map[string]*contract.Contract{}
		for _, addr := range failedContractAddresses {
			contractsWithNormalOpt[addr] = s.contractProcessor.WithContract(chainSpec, addr, false)
		}
		err = compileContracts(contractsWithNormalOpt)
		if err != nil {
			logger.Errorf("compile contracts failed, err: %v", err)
			return nil, nil, err
		}

		if s.storeCompilations {
			logger.Infof("store compilations (normal opt)")
			storeContracts(contractsWithNormalOpt)
		}
	}

	// we don't want to keep user waiting, so run it async
	// TODO maybe not proper to do it like this, move this to a separate service
	// TODO fuyaoz, ask why this is async in the first place
	if s.storeCompilations {
		logger.Infof("store compilations")
		storeContracts(contracts)
	}
	return respMap, resp, nil
}

func mergeProcessedAST(resp *types.FetchAndCompileResponse, compilation *types.Compilation) {
	if compilation.ProcessedAST == nil {
		// require all compilations having this field, otherwise leave AST processing to truffle
		resp.ProcessedAST = nil
	}
	if resp.ProcessedAST != nil {
		resp.ProcessedAST.Scopes.BySourceID = lo.Assign(resp.ProcessedAST.Scopes.BySourceID, compilation.ProcessedAST.Scopes.BySourceID)
		resp.ProcessedAST.UserDefinedTypes = append(resp.ProcessedAST.UserDefinedTypes, compilation.ProcessedAST.UserDefinedTypes...)
		resp.ProcessedAST.TaggedOutputs = append(resp.ProcessedAST.TaggedOutputs, compilation.ProcessedAST.TaggedOutputs...)
	}
}

func (s *Service) GetTransactionInfo(
	ctx context.Context,
	req *protos.GetTransactionInfoRequest,
) (*protos.GetTransactionInfoResponse, error) {
	s.LocalCacheCleaner.Acquire()
	defer s.LocalCacheCleaner.Release()

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	logger := log.WithContext(ctx)
	logger.Infof("GetTransactionInfo, networkId: %s, txID: %s", chainSpec.ToString(), req.TxId)

	resp := protos.GetTransactionInfoResponse{
		ChainSpec: chainSpec.ToPB(),
	}

	chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}

	// transaction
	resp.Transactions, err = chainClient.Client.GetTransactions(ctx, req.TxId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	// chainId is not included in response for scroll
	if chainSpec.ChainID == "534352" {
		for _, tx := range resp.Transactions {
			tx.ChainId = "0x82750"
		}
	}

	// substitute networkId for fork
	if chainSpec.IsBuiltin() {
		networkID, err := strconv.Atoi(chainSpec.ChainID)
		if err == nil {
			for _, tx := range resp.Transactions {
				tx.ChainId = fmt.Sprintf("0x%x", networkID)
			}
		}
	}

	resp.Transaction = resp.Transactions[0]
	blockNumber := resp.Transaction.BlockNumber

	g, gctx := errgroup.WithContext(ctx)
	// latest block number
	g.Go(func() error {
		var err error
		resp.LatestBlockNumber, err = chainClient.Client.GetLatestBlockNumber(ctx)
		if err != nil {
			logger.Errore(err)
			return err
		}
		return nil
	})

	// transaction receipt
	if blockNumber != "" {
		g.Go(func() error {
			var err error
			resp.TransactionReceipts, err = chainClient.Client.GetTransactionReceipts(gctx, req.TxId)
			if err != nil {
				logger.Errore(err)
				return err
			}
			resp.TransactionReceipt = resp.TransactionReceipts[0]
			return nil
		})
	}

	// block
	if blockNumber != "" {
		g.Go(func() error {
			var err error
			resp.Block, err = chainClient.Client.GetBlockByNumber(ctx, blockNumber, false)
			if err != nil {
				logger.Errore(err)
				return err
			}
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return &resp, nil
}

func (s *Service) GetTransactions(
	ctx context.Context,
	req *protos.GetTransactionsRequest,
) (*protos.GetTransactionsResponse, error) {
	logger := log.WithContext(ctx)

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}

	var mutex sync.Mutex
	resp := &protos.GetTransactionsResponse{
		Transactions: map[string]*evmprotos.Transaction{},
	}
	g, gCtx := errgroup.WithContext(ctx)
	for i := range req.TxHash {
		txHash := req.TxHash[i]
		g.Go(func() error {
			tx, err := chainClient.Client.GetTransactions(gCtx, &protos.TxIdentifier{
				Identifier: &protos.TxIdentifier_TxHash{
					TxHash: txHash,
				},
			})
			if err != nil {
				logger.Errore(err)
				return err
			}
			mutex.Lock()
			defer mutex.Unlock()
			resp.Transactions[txHash] = tx[0]
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *Service) StateDiff(
	ctx context.Context,
	req *protos.StateDiffRequest,
) (*httpbody.HttpBody, error) {
	s.LocalCacheCleaner.Acquire()
	defer s.LocalCacheCleaner.Release()

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	shouldDecode := true
	if req.Decode != nil {
		shouldDecode = *req.Decode
	}
	logger := log.WithContext(ctx)
	logger.Infof("StateDiff, networkId: %s, txHash: %s, shouldDecode: %t", chainSpec.ToString(), req.TxId, shouldDecode)
	projectID, err := s.authTxRead(ctx, req.ProjectOwner, req.ProjectSlug, req.TxId, req.ShareId)
	if err != nil {
		return nil, err
	}

	chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}

	opt, err := s.getTraceTransactionOption(ctx, projectID, *chainSpec, req.TxId, nil, false, false)
	if err != nil {
		return nil, err
	}
	if shouldDecode {
		raws, err := chainClient.Client.TraceTx(
			ctx,
			req.TxId,
			&common.TraceTransactionOption{
				Tracer: lo.ToPtr("sentioPrestateTracer"),
				TracerConfig: common.SentioPrestateTracerConfig{
					DiffMode: true,
				},
				StateOverrides: opt.StateOverrides,
			},
		)
		if err != nil {
			return nil, err
		}

		var results []json.RawMessage
		for _, raw := range raws {
			var stateDiff structpb.Struct
			if err := protojson.Unmarshal(raw, &stateDiff); err != nil {
				return nil, err
			}
			// TODO do we need to consider debug build here?
			preprocessResult, err := s.preprocess(ctx, projectID, *chainSpec, req.TxId, []string{}, false)
			if err != nil {
				logger.Errore(err)
			}
			decodedStateDiff, err := s.debuggerClient.DecodeStateDiffInternal(ctx, &protos.DecodeStateDiffInternalRequest{
				//NetworkId:    chainSpec.ChainID,
				RawStateDiff: &stateDiff,
				ContractKeys: preprocessResult,
				//ChainConfig:  customChainConfig,
			})
			if err != nil {
				results = append(results, raw)
			} else {
				results = append(results, []byte(decodedStateDiff.Result))
			}
		}
		var result json.RawMessage
		if req.TxId.GetBundleId() != "" {
			result, _ = json.Marshal(results)
		} else {
			result = results[0]
		}
		return &httpbody.HttpBody{
			ContentType: "application/json",
			Data:        result,
		}, nil
	} else {
		raws, err := chainClient.Client.TraceTx(
			ctx,
			req.TxId,
			&common.TraceTransactionOption{
				Tracer: lo.ToPtr("prestateTracer"),
				TracerConfig: common.SentioPrestateTracerConfig{
					DiffMode: true,
				},
				StateOverrides: opt.StateOverrides,
			},
		)
		var raw json.RawMessage
		if req.TxId.GetBundleId() != "" {
			raw, _ = json.Marshal(raws)
		} else {
			raw = raws[0]
		}
		if err != nil {
			return nil, err
		} else {
			return &httpbody.HttpBody{
				ContentType: "application/json",
				Data:        raw,
			}, nil
		}
	}
}

func (s *Service) GetLatestBlockNumber(
	ctx context.Context,
	req *protos.GetLatestBlockNumberRequest,
) (*protos.GetLatestBlockNumberResponse, error) {
	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}
	cli, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "unsupported network %q", chainSpec)
	}
	latestBlockNumber, err := cli.Client.GetLatestBlockNumber(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "call eth_blockNumber failed: %v", err)
	}
	return &protos.GetLatestBlockNumberResponse{
		BlockNumber: latestBlockNumber,
	}, nil
}

func (s *Service) GetBlockSummary(
	ctx context.Context,
	req *protos.GetBlockSummaryRequest,
) (*protos.GetBlockSummaryResponse, error) {
	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}
	cli, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err == nil {
		return nil, status.Errorf(codes.InvalidArgument, "unsupported network %q", chainSpec)
	}
	type block struct {
		Number        string   `json:"number"`
		BaseFeePerGas string   `json:"baseFeePerGas"`
		Transactions  []string `json:"transactions"`
	}
	var b block
	number := req.BlockNumber
	if number == "" {
		number = "latest"
	}
	_, err = cli.Client.RawCall(ctx, &b, "eth_getBlockByNumber", number, false)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "call eth_getBlockByNumber failed: %v", err)
	}
	return &protos.GetBlockSummaryResponse{
		BlockNumber:      b.Number,
		TransactionCount: uint32(len(b.Transactions)),
		BaseFeePerGas:    b.BaseFeePerGas,
	}, nil
}

func (s *Service) GetStorageInfo(
	ctx context.Context,
	req *protos.GetStorageInfoRequest,
) (*protos.GetStorageInfoResponse, error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetStorageInfo, req: %s", req.String())

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}

	ret, err := chainClient.DebugClient.Call(ctx, "debug_storageRangeAt",
		req.BlockHash, req.TxIdx, req.ContractAddress, req.KeyStart, req.MaxResult)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.GetStorageInfoResponse{
		Result: ret,
	}, nil
}

func (s *Service) GetCode(
	ctx context.Context,
	req *protos.GetCodeRequest,
) (*protos.GetCodeResponse, error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetCode, req: %s", req.String())

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}

	code, err := chainClient.Client.GetCode(ctx, req.ContractAddress, req.BlockNumber)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.GetCodeResponse{
		Code: code,
	}, nil
}

func (s *Service) GetCallTrace(
	ctx context.Context,
	req *protos.GetCallTraceRequest,
) (*httpbody.HttpBody, error) {
	s.LocalCacheCleaner.Acquire()
	defer s.LocalCacheCleaner.Release()

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	logger := log.WithContext(ctx)
	logger.Infof("GetCallTrace, networkId: %s, txID: %s", chainSpec.ToString(), req.TxId)
	projectID, err := s.authTxRead(ctx, req.ProjectOwner, req.ProjectSlug, req.TxId, req.ShareId)
	if err != nil {
		return nil, err
	}

	opt, err := s.getTraceTransactionOption(ctx, projectID, *chainSpec, req.TxId, nil, req.DisableOptimizer, req.IgnoreGasCost)
	if err != nil {
		return nil, err
	}
	opt.Tracer = lo.ToPtr("sentioTracer")
	opt.TracerConfig = common.SentioTracerConfig{
		WithInternalCalls: req.WithInternalCalls,
	}

	//chainClient, err := s.getChainClient(chainSpec)
	//if err != nil {
	//	return nil, err
	//}

	//if !req.WithInternalCalls {
	//	raws, err := chainClient.DebugClient.TraceTx(ctx, req.TxId, opt)
	//	if err != nil {
	//		return nil, err
	//	}
	//	var raw json.RawMessage
	//	if req.TxId.GetBundleId() != "" {
	//		raw, _ = json.Marshal(raws)
	//	} else {
	//		raw = raws[0]
	//	}
	//	return &httpbody.HttpBody{
	//		ContentType: "application/json",
	//		Data:        raw,
	//	}, nil
	//}

	var preprocessResult map[string]*protos.ContractKeyInfo
	if req.WithInternalCalls {
		preprocessResult, err = s.preprocess(ctx, projectID, *chainSpec, req.TxId, []string{}, req.DisableOptimizer)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
	}

	customChainConfig, err := s.getCustomChainConfig(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}

	// TODO trace opt should be calculated by the debugger server
	optJSON, _ := json.Marshal(opt)
	if req.TxId.GetSimulationId() != "" {
		sim, err := s.getSimulationModel(ctx, req.TxId.GetSimulationId())
		if err != nil {
			return nil, err
		}
		tx, block := sim.GetTraceCallParams()
		resp, err := s.debuggerClient.GetTraceCallInternal(ctx, &protos.GetTraceCallInternalRequest{
			NetworkId:    chainSpec.ChainID,
			Bundles:      []*protos.GetTraceCallInternalRequest_Bundle{tx},
			StateContext: block,
			TraceOpt:     string(optJSON),
			ContractKeys: preprocessResult,
			ChainConfig:  customChainConfig,
		})
		if err != nil {
			return nil, err
		}
		ret := map[string]json.RawMessage{}
		err = json.Unmarshal([]byte(resp.Result[0].GetValues()[0].GetStringValue()), &ret)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		outputsData, _ := protojson.Marshal(resp.Outputs[0].GetValues()[0])
		ret["consoleOutputs"] = outputsData
		data, _ := json.Marshal(ret)
		// Currently only consider single trace
		return &httpbody.HttpBody{
			ContentType: "application/json",
			Data:        data,
		}, nil

	} else if req.TxId.GetBundleId() != "" {
		sims, err := s.getBundleSimulations(ctx, req.TxId.GetBundleId())
		if err != nil {
			return nil, err
		}
		tx, block := models.GetTraceCallParamsForBundle(sims)
		resp, err := s.debuggerClient.GetTraceCallInternal(ctx, &protos.GetTraceCallInternalRequest{
			NetworkId:    chainSpec.ChainID,
			Bundles:      []*protos.GetTraceCallInternalRequest_Bundle{tx},
			StateContext: block,
			TraceOpt:     string(optJSON),
			ContractKeys: preprocessResult,
			ChainConfig:  customChainConfig,
		})
		if err != nil {
			return nil, err
		}
		var ret []json.RawMessage
		for _, v := range resp.Result[0].GetValues() {
			ret = append(ret, []byte(v.GetStringValue()))
		}
		data, err := json.Marshal(ret)
		if err != nil {
			return nil, err
		}
		return &httpbody.HttpBody{
			ContentType: "application/json",
			Data:        data,
		}, nil
	}

	traceReq := &protos.GetTraceTransactionInternalRequest{
		NetworkId:       chainSpec.ChainID,
		TransactionHash: req.TxId.GetTxHash(),
		TraceOpt:        string(optJSON),
		ContractKeys:    preprocessResult,
		ChainConfig:     customChainConfig,
	}
	resp, err := s.debuggerClient.GetTraceTransactionInternal(ctx, traceReq)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	return &httpbody.HttpBody{
		ContentType: "application/json",
		Data:        []byte(resp.Result),
	}, nil
}

func (s *Service) GetAffectedContract(
	ctx context.Context,
	req *protos.GetAffectedContractRequest,
) (*protos.GetAffectedContractResponse, error) {
	logger := log.WithContext(ctx)
	logger.Infof("GetAffectedContract, req: %s", req.String())

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	projectID, err := s.authTxRead(ctx, req.ProjectOwner, req.ProjectSlug, req.TxId, req.ShareId)
	if err != nil {
		return nil, err
	}
	contracts, err := s.getAffectedContract(ctx, projectID, *chainSpec, req.TxId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.GetAffectedContractResponse{
		Addresses: contracts,
	}, nil
}

func (s *Service) getAffectedContract(ctx context.Context, projectID string, chainSpec models.ChainIdentifier, txID *protos.TxIdentifier) ([]string, error) {
	logger := log.WithContext(ctx)
	chainClient, err := s.chainClients.GetChainClient(ctx, chainSpec)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	opt, err := s.getTraceTransactionOption(ctx, projectID, chainSpec, txID, nil, false, false)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	opt.Tracer = lo.ToPtr("sentioTracer")
	raws, err := chainClient.DebugClient.TraceTx(ctx, txID, opt)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	var res []string
	for _, raw := range raws {
		var trace common.SentioPartialCallTrace
		err = json.Unmarshal(raw, &trace)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		res = append(res, lo.Keys(trace.CollectAffectedAddresses())...)
	}
	return res, nil
}

func (s *Service) getCodeOverrides(ctx context.Context, chainSpec models.ChainIdentifier, addresses []string) (map[string]string, error) {
	logger := log.WithContext(ctx)

	g, gCtx := errgroup.WithContext(ctx)
	var mutex sync.Mutex
	ret := map[string]string{}
	for i := range addresses {
		addr := addresses[i]
		g.Go(func() error {
			c := s.contractProcessor.WithContract(chainSpec, addr, true)
			code, err := c.GetDeployedCodeOverride(gCtx)
			if err != nil {
				logger.Warnf("failed to get deployed override, addr: %s, err: %v", addr, err)
				return nil
			}
			mutex.Lock()
			ret[addr] = code
			mutex.Unlock()
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}
	return ret, nil
}

func (s *Service) getTraceTransactionOption(
	ctx context.Context,
	projectID string,
	chainSpec models.ChainIdentifier,
	txID *protos.TxIdentifier,
	memoryCompressionWindow *int,
	disableOptimizer bool,
	ignoreGas bool,
) (ret *common.TraceTransactionOption, err error) {
	ret = &common.TraceTransactionOption{
		EnableMemory:            lo.ToPtr(true),
		MemoryCompressionWindow: memoryCompressionWindow,
		StateOverrides:          map[string]*common.Account{},
		IgnoreGas:               lo.ToPtr(ignoreGas),
	}
	// apply stateOverrides and sourceOverrides for simulations
	// normal transaction don't need any override
	var sim *models.Simulation
	if txID.GetBundleId() != "" {
		sim, err = s.getBundleFirstSimulation(ctx, txID.GetBundleId())
		if err != nil {
			return nil, err
		}
	} else if txID.GetSimulationId() != "" {
		sim, err = s.getSimulationModel(ctx, txID.GetSimulationId())
		if err != nil {
			return nil, err
		}
		if sim.BundleID != nil {
			return nil, errors.New("bundle simulation should be query with bundle id instead of simulation id")
		}
	}

	if sim != nil && sim.DebugDeployment {
		sourceOverrides := map[string]string{}
		if err = json.Unmarshal([]byte(sim.SourceOverrides), &sourceOverrides); err != nil {
			return nil, err
		}
		for k := range sourceOverrides {
			ret.CreateAddressOverride = &k
		}
	}

	if sim != nil && sim.ActualStateOverrides != "" {
		simStateOverrides := map[string]*common.Account{}
		if err = json.Unmarshal([]byte(sim.ActualStateOverrides), &simStateOverrides); err != nil {
			return nil, err
		}
		for address, simStateOverride := range simStateOverrides {
			if _, ok := ret.StateOverrides[address]; !ok {
				ret.StateOverrides[address] = simStateOverride
				continue
			}
			acc := ret.StateOverrides[address]
			if simStateOverride.Code != nil {
				acc.Code = simStateOverride.Code
			}
			if simStateOverride.Balance != nil {
				acc.Balance = simStateOverride.Balance
			}
			if simStateOverride.Nonce != nil {
				acc.Nonce = simStateOverride.Nonce
			}
			acc.State = lo.Assign(acc.State, simStateOverride.State)
			acc.StateDiff = lo.Assign(acc.StateDiff, simStateOverride.StateDiff)
		}
	}

	if disableOptimizer {
		ret.IgnoreCodeSizeLimit = lo.ToPtr(true)
		userCompilationMap, addresses, err := s.findUserCompilations(ctx, projectID, chainSpec, txID, disableOptimizer)
		if err != nil {
			return nil, err
		}
		codeOverrides, err := s.getCodeOverrides(ctx, chainSpec, addresses)
		if err != nil {
			return nil, err
		}
		for address, rec := range userCompilationMap {
			c := s.contractProcessor.UserCompilationByRec(rec)
			code, err := c.GetDeployedCode(ctx, chainSpec, address)
			if err != nil {
				return nil, err
			}
			codeOverrides[address] = code
		}
		for addr, override := range codeOverrides {
			if _, ok := ret.StateOverrides[addr]; !ok {
				ret.StateOverrides[addr] = &common.Account{}
			}
			ret.StateOverrides[addr].Code = lo.ToPtr(common.EnsureHexPrefix(override))
		}

		// if CREATE2 presents in trace, we want the address to be the same as the release build
		opt, err := s.getTraceTransactionOption(ctx, projectID, chainSpec, txID, nil, false, false)
		if err != nil {
			return nil, err
		}
		opt.Tracer = lo.ToPtr("sentioTracer")
		chainClient, err := s.chainClients.GetChainClient(ctx, chainSpec)
		if err != nil {
			return nil, err
		}
		raws, err := chainClient.DebugClient.TraceTx(ctx, txID, opt)
		if err != nil {
			return nil, err
		}
		if len(raws) != 1 {
			return ret, nil
		}
		var trace common.SentioPartialCallTrace
		err = json.Unmarshal(raws[0], &trace)
		if err != nil {
			return nil, err
		}
		create2Calls := trace.CollectCreate2()
		if len(create2Calls) != 1 {
			return ret, nil
		}
		ret.CreateAddressOverride = &create2Calls[0].To
	}
	return ret, nil
}

func (s *Service) SentioTraceTransaction(ctx context.Context, request *protos.SentioTraceTransactionRequest) (*httpbody.HttpBody, error) {
	s.LocalCacheCleaner.Acquire()
	defer s.LocalCacheCleaner.Release()

	chainSpec, err := models.GetChainSpec(request.ChainSpec, request.NetworkId)
	if err != nil {
		return nil, err
	}

	logger := log.WithContext(ctx)
	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, request.ProjectOwner, request.ProjectSlug, auth.READ)
	if err != nil {
		return nil, err
	}

	txID := request.TxId
	debug := request.Debug
	withInternalCalls := request.WithInternalCalls
	disableOptimizer := request.DisableOptimizer
	ignoreGasCost := request.IgnoreGasCost

	opt, err := s.getTraceTransactionOption(ctx, projectID, *chainSpec, txID, nil, disableOptimizer, ignoreGasCost)
	if err != nil {
		return nil, err
	}

	opt.Tracer = lo.ToPtr("sentioTracer")
	opt.TracerConfig = common.SentioTracerConfig{
		Debug:             debug,
		WithInternalCalls: withInternalCalls,
	}

	preprocessResult, err := s.preprocess(ctx, projectID, *chainSpec, txID, []string{}, disableOptimizer)
	if err != nil {
		logger.Errore(err)
	}

	var optJSON []byte
	optJSON, err = json.Marshal(opt)
	if err != nil {
		return nil, err
	}

	customChainConfig, err := s.getCustomChainConfig(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}
	if txID.GetSimulationId() != "" {
		sim, err := s.getSimulationModel(ctx, request.TxId.GetSimulationId())
		if err != nil {
			return nil, err
		}
		tx, block := sim.GetTraceCallParams()
		return s.debuggerClient.DebugSentioTraceCallInternal(ctx, &protos.GetTraceCallInternalRequest{
			NetworkId:    request.NetworkId,
			Bundles:      []*protos.GetTraceCallInternalRequest_Bundle{tx},
			StateContext: block,
			TraceOpt:     string(optJSON),
			ContractKeys: preprocessResult,
			ChainConfig:  customChainConfig,
		})
	}

	traceReq := &protos.GetTraceTransactionInternalRequest{
		NetworkId:       request.NetworkId,
		TransactionHash: txID.GetTxHash(),
		TraceOpt:        string(optJSON),
		ContractKeys:    preprocessResult,
		ChainConfig:     customChainConfig,
	}
	return s.debuggerClient.DebugSentioTraceTransactionInternal(ctx, traceReq)
}

func (s *Service) GetDebugTrace(ctx context.Context, req *protos.GetDebugTraceRequest) (*httpbody.HttpBody, error) {
	s.LocalCacheCleaner.Acquire()
	defer s.LocalCacheCleaner.Release()

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	logger := log.WithContext(ctx)
	logger.Infof("GetDebugTrace, networkId: %s, txID: %v, disableOptimizer: %v, ignoreGasCost: %v",
		chainSpec.ToString(), req.TxId, req.DisableOptimizer, req.IgnoreGasCost)

	projectID, err := s.authTxRead(ctx, req.ProjectOwner, req.ProjectSlug, req.TxId, req.ShareId)
	if err != nil {
		return nil, err
	}
	chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		logger.Errorf("network with id %s is not supported", chainSpec.ToString())
		return nil, err
	}
	opt, err := s.getTraceTransactionOption(ctx, projectID, *chainSpec, req.TxId,
		lo.ToPtr(int(req.MemoryCompressionWindow)), req.DisableOptimizer, req.IgnoreGasCost)
	if err != nil {
		logger.Errorf("failed to get trace transaction option, txID: %v, err: %v", req.TxId, err)
		return nil, err
	}
	raws, err := chainClient.DebugClient.TraceTx(ctx, req.TxId, opt)
	if err != nil {
		logger.Errorf("failed to trace transaction, txID: %v, err: %v", req.TxId, err)
		return nil, err
	}
	var raw json.RawMessage
	if req.TxId.GetBundleId() != "" {
		raw, _ = json.Marshal(raws)
	} else {
		raw = raws[0]
	}
	return &httpbody.HttpBody{
		ContentType: "application/json",
		Data:        raw,
	}, nil
}

func StartDebuggerServer(nodeConfig string) (*exec.Cmd, int, error) {
	port, err := getFreePort()
	if err != nil {
		return nil, 0, err
	}

	cmd := exec.Command(
		"./debugger-server.sh",
		//"--eth-node", ethNode,
		"--port", fmt.Sprintf("%d", port),
		"--node-config", nodeConfig)
	cmd.Dir = "packages/debugger-server"
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Start(); err != nil {
		return nil, 0, err
	}

	return cmd, port, err
}

func getFreePort() (int, error) {
	addr, err := net.ResolveTCPAddr("tcp", "localhost:0")
	if err != nil {
		return 0, err
	}

	l, err := net.ListenTCP("tcp", addr)
	if err != nil {
		return 0, err
	}
	defer func() {
		if err := l.Close(); err != nil {
			log.Fatale(err)
		}
	}()
	return l.Addr().(*net.TCPAddr).Port, nil
}

func (s *Service) SearchTransactions(ctx context.Context,
	req *protos.EvmSearchTransactionsRequest) (*protos.EvmSearchTransactionsResponse, error) {
	var userID string
	if identity, err := s.authManager.RequiredLogin(ctx); err == nil {
		userID = identity.UserID
	} else {
		userID = "anonymous"
	}

	logger := log.WithContext(ctx)
	logger.Infof("SearchTransactions, user: %s, req: %s", userID, req.String())
	if len(req.Address) == 0 {
		req.Address = []string{""}
	}
	resp, err := s.evmRepository.SearchTransactions(ctx, req)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	var sigs []string
	for _, tx := range resp.Transactions {
		sigs = append(sigs, common.EnsureHexPrefix(tx.MethodSignature))
	}
	var recs []models.EthSignature
	sql := `SELECT hex_signature, text_signature, abi_item FROM (
			SELECT hex_signature, text_signature, abi_item,
					ROW_NUMBER() OVER (PARTITION BY hex_signature ORDER BY weight DESC) AS row_num
			FROM ?
			where type = ? and hex_signature in ?
		) AS ranked
		WHERE row_num = 1`
	err = s.syncRepository.DB.WithContext(ctx).
		Session(&gorm.Session{}).
		Raw(sql, gorm.Expr(models.EthSignature{}.TableName()), protos.SignatureType_FUNCTION, sigs).
		Scan(&recs).Error
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	recMap := map[string]models.EthSignature{}
	for _, rec := range recs {
		recMap[rec.HexSignature] = rec
	}
	for _, tx := range resp.Transactions {
		if rec, ok := recMap[common.EnsureHexPrefix(tx.MethodSignature)]; ok {
			tx.MethodSignatureText = &rec.TextSignature
			tx.AbiItem = &rec.ABIItem
		}
	}
	return resp, nil
}

var PreprocessVersion = 21

func (s *Service) getPreprocessStorageName(chainID models.ChainIdentifier, address string, disableOptimizer bool) string {
	prefix := ""
	if chainID.IsFork() {
		prefix = "custom_chains/fork_" + chainID.ForkID
	} else if chainID.IsBuiltin() {
		prefix = "chain_" + chainID.ChainID
	}
	return fmt.Sprintf("%s/preprocess/version_%d/address_%s/debug_build_%v",
		prefix, PreprocessVersion, address, disableOptimizer)
}

func (s *Service) getGcsPreprocessHandle(chainID models.ChainIdentifier, address string, disableOptimizer bool) *storage.ObjectHandle {
	return s.gcsClient.Bucket(s.gcsBucket).Object(s.getPreprocessStorageName(chainID, address, disableOptimizer))
}

func (s *Service) getLocalPreprocessPath(chainID models.ChainIdentifier, address string, disableOptimizer bool) string {
	return common.GetWorkingDir() + "/" + s.gcsBucket + "/" + s.getPreprocessStorageName(chainID, address, disableOptimizer)
}

// findPreprocessForOnChainContracts fetch preprocess results for each contract.
// If preprocess result is missing, fetch compilations for it,
// note that there is no actual preprocessing here, it is done later to GetCallTrace.
// If compilations are missing, compile the contract and write it to gcs.
// Preprocess result and compilations will be written to local storage in convenience of debugger service.
func (s *Service) findPreprocessForOnChainContracts(
	ctx context.Context,
	chainSpec models.ChainIdentifier,
	addresses []string,
	disableOptimizer bool,
) (map[string]*protos.ContractKeyInfo, error) {
	logger := log.WithContext(ctx)
	var mutex sync.Mutex
	result := map[string]*protos.ContractKeyInfo{}
	g, gCtx := errgroup.WithContext(ctx)
	for i := range addresses {
		address := addresses[i]
		g.Go(func() error {
			// First try get preprocess from gcs, otherwise get compilation result from gcs
			obj := s.getGcsPreprocessHandle(chainSpec, address, disableOptimizer)
			processInfo := protos.ContractKeyInfo{
				PreprocessKey: s.getLocalPreprocessPath(chainSpec, address, disableOptimizer),
			}
			var err error
			if _, err = obj.Attrs(ctx); err == nil {
				ctxNew, span := tracer.Start(ctx, "gcs/read/preprocess")

				rd, err := obj.ReadCompressed(true).NewReader(ctxNew)
				if err != nil {
					span.End()
					return err
				}
				defer rd.Close()
				fz, err := gzip.NewReader(rd)
				if err != nil {
					span.End()
					return err
				}
				defer fz.Close()
				data, err := io.ReadAll(fz)
				if err != nil {
					span.End()
					return err
				}
				span.End()

				path := processInfo.PreprocessKey

				dir := filepath.Dir(path)
				errWrite := os.MkdirAll(dir, 0755)
				if errWrite != nil {
					return errWrite
				}

				errWrite = os.WriteFile(path, data, 0644)
				if errWrite != nil {
					return errWrite
				}

				mutex.Lock()
				defer mutex.Unlock()
				result[address] = &processInfo
				return nil
			}
			if !errors.Is(err, storage.ErrObjectNotExist) {
				return err
			}
			logger.Infof("preprocess missing in gcs, address: %s, conf: %+v", address, disableOptimizer)

			resp, _, err := s.fetchAndCompile(gCtx, chainSpec, []string{address}, disableOptimizer)

			if err != nil {
				return err
			}
			mutex.Lock()
			defer mutex.Unlock()

			processInfo.CompilationKey = resp[address]
			if processInfo.CompilationKey == "" {
				logger.Warnf("contract finally failed to compile, address: %s", address)
				return nil
			}

			result[address] = &processInfo
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}
	return result, nil
}

func (s *Service) preprocess(
	ctx context.Context,
	projectID string,
	chainSpec models.ChainIdentifier,
	txID *protos.TxIdentifier,
	addresses []string,
	disableOptimizer bool,
) (map[string]*protos.ContractKeyInfo, error) {
	logger := log.WithContext(ctx)
	logger.Infof("preprocess, txID: %s, disableOptimizer: %v", txID, disableOptimizer)

	var partUser map[string]*protos.ContractKeyInfo
	var userCompilationMap map[string]*models.UserCompilation
	var missingAddresses []string
	var err error

	if txID != nil {
		partUser, userCompilationMap, missingAddresses, err = s.findPreprocessForUserCompilations(ctx, projectID, chainSpec, txID, addresses, disableOptimizer)
		if err != nil {
			return nil, err
		}
	}
	missingAddresses = append(missingAddresses, addresses...)

	partGlobal, err := s.findPreprocessForOnChainContracts(ctx, chainSpec, missingAddresses, disableOptimizer)
	if err != nil {
		return nil, err
	}
	contractKeys := lo.Assign(partGlobal, partUser)

	needPreprocess := false
	for _, key := range contractKeys {
		if key.CompilationKey != "" {
			needPreprocess = true
			break
		}
	}
	if !needPreprocess {
		return contractKeys, nil
	}

	resp, err := s.debuggerClient.PreProcessCompilationInternal(ctx, &protos.PreProcessCompilationInternalRequest{
		ContractKeys: contractKeys,
	})
	if err != nil {
		return nil, err
	}

	// some contracts are missing preprocess result at first
	// debugger service writes them to local storage
	// then we write them to gcs here
	g, gCtx := errgroup.WithContext(ctx)
	for _, address := range resp.PreprocessedAddresses {
		var obj *storage.ObjectHandle
		var path string
		if _, ok := userCompilationMap[address]; ok {
			c := s.contractProcessor.UserCompilationByRec(userCompilationMap[address])
			obj = c.GetGcsPreprocessHandle()
			path = c.GetLocalPreprocessPath()

		} else {
			obj = s.getGcsPreprocessHandle(chainSpec, address, disableOptimizer)
			path = s.getLocalPreprocessPath(chainSpec, address, disableOptimizer)
		}
		contractKeys[address].PreprocessKey = path

		g.Go(func() error {
			data, err := os.Open(path)
			if err != nil {
				logger.Error("can't read preprocess file")
				return nil
			}

			w := obj.NewWriter(gCtx)
			w.ContentType = "text/plain"
			w.ContentEncoding = "gzip"
			gz := gzip.NewWriter(w)
			_, span := tracer.Start(gCtx, "gcs/write/preprocess")
			written, err := io.Copy(gz, data)
			if err != nil {
				span.End()
				return err
			}
			logger.Infof("Successfully wrote %d bytes", written)
			span.End()
			_ = gz.Close()
			_ = w.Close()
			_ = data.Close()

			return nil
		})
	}
	// TODO this is maybe not needed
	if err := g.Wait(); err != nil {
		return nil, err
	}
	return contractKeys, nil
}

func (s *Service) SyncContracts(
	ctx context.Context,
	req *protos.SyncContractsRequest,
) (*emptypb.Empty, error) {
	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	addresses := req.Addresses
	for i := range addresses {
		addresses[i] = strings.ToLower(addresses[i])
	}
	addresses = lo.Uniq(addresses)

	logger := log.WithContext(ctx)
	logger.Infof("sync contracts, total: %d, disableOptimizer: %v", len(addresses), req.DisableOptimizer)

	flags := []bool{false}
	if req.DisableOptimizer {
		flags = append(flags, true)
	}
	syncCtx := context.Background()
	for _, address := range addresses {
		addr := address
		s.activeSyncPool.Submit(func() {
			for _, disableOptimizer := range flags {
				attr := metric.WithAttributes(attribute.Bool("disable_optimizer", disableOptimizer))
				compileDBMiss, fingerprintDBMiss, compileErr, fingerprintErr, otherErr :=
					s.SyncContractInternal(syncCtx, *chainSpec, addr, disableOptimizer)

				if compileErr == nil {
					s.activeSyncSuccessCounter.Add(syncCtx, 1, metric.WithAttributes(
						attribute.Bool("disable_optimizer", disableOptimizer),
						attribute.String("pass", "compile"),
						attribute.Bool("db_miss", compileDBMiss),
					))

				} else {
					errMsg := "unknown"
					// FIXME backoff.Retry seems to unpack error
					if compileErr.Error() == sourcefetcher.ErrNotVerified.Error() {
						errMsg = "not verified"
					}
					s.activeSyncErrorCounter.Add(syncCtx, 1, attr, metric.WithAttributes(
						attribute.Bool("disable_optimizer", disableOptimizer),
						attribute.String("pass", "compile"),
						attribute.Bool("db_miss", compileDBMiss),
						attribute.String("error", errMsg),
					))

				}

				if fingerprintErr == nil {
					s.activeSyncSuccessCounter.Add(syncCtx, 1, metric.WithAttributes(
						attribute.Bool("disable_optimizer", disableOptimizer),
						attribute.String("pass", "fingerprint"),
						attribute.Bool("db_miss", fingerprintDBMiss),
					))

				} else {
					errMsg := "unknown"
					if fingerprintErr.Error() == sourcefetcher.ErrNoCreationData.Error() {
						errMsg = "no creation data"
					}
					if fingerprintErr.Error() == ethclient.ErrEmptyResult.Error() {
						errMsg = "creation txn not found"
					}
					s.activeSyncErrorCounter.Add(syncCtx, 1, attr, metric.WithAttributes(
						attribute.Bool("disable_optimizer", disableOptimizer),
						attribute.String("pass", "fingerprint"),
						attribute.Bool("db_miss", fingerprintDBMiss),
						attribute.String("error", errMsg),
					))
				}

				if otherErr != nil {
					s.activeSyncErrorCounter.Add(syncCtx, 1, attr, metric.WithAttributes(
						attribute.Bool("disable_optimizer", disableOptimizer),
						attribute.String("pass", "other"),
					))
				}
			}
		})
	}
	return &emptypb.Empty{}, nil
}

// SyncContractInternal sync compilations & preprocess result & deployed bytecode (if trying to disable optimizer)
// TODO move all preprocess related logic to common/contract
func (s *Service) SyncContractInternal(ctx context.Context, networkID models.ChainIdentifier, address string, debugBuild bool) (
	compileDBMiss bool, fingerprintDBMiss bool,
	compileErr error, fingerprintErr error, otherErr error) {

	s.LocalCacheCleaner.Acquire()
	defer s.LocalCacheCleaner.Release()

	logger := log.WithContext(ctx)

	c := s.contractProcessor.WithContract(networkID, address, debugBuild)
	var compilationData []byte
	_, compilationData, compileDBMiss, fingerprintDBMiss, compileErr, fingerprintErr = c.FetchAndStore(ctx)
	if compileErr != nil {
		return
	}
	compilationPath := c.GetLocalCompilationPath()
	if err := os.MkdirAll(filepath.Dir(compilationPath), 0755); err != nil {
		otherErr = err
		return
	}
	if err := os.WriteFile(compilationPath, compilationData, 0644); err != nil {
		otherErr = err
		return
	}

	// preprocess if not exists
	flags := []bool{false}
	if debugBuild {
		flags = append(flags, true)
	}
	for _, disableOptimizer := range flags {
		preprocessGCSHandle := s.getGcsPreprocessHandle(networkID, address, disableOptimizer)
		if _, err := preprocessGCSHandle.Attrs(ctx); err != nil {
			preprocessPath := s.getLocalPreprocessPath(networkID, address, disableOptimizer)
			contractKeys := map[string]*protos.ContractKeyInfo{
				address: {
					PreprocessKey:  preprocessPath,
					CompilationKey: compilationPath,
				},
			}
			_, err = s.debuggerClient.PreProcessCompilationInternal(ctx, &protos.PreProcessCompilationInternalRequest{
				ContractKeys: contractKeys,
			})
			if err != nil {
				logger.Warnf("failed to preprocess, addr: %s, err: %v", address, err)
				otherErr = err
				return
			}

			// store preprocess result to GCS
			data, err := os.Open(preprocessPath)
			if err != nil {
				logger.Warnf("failed to read local preprocess file, path: %s, err: %v", preprocessPath, err)
				otherErr = err
				return
			}
			obj := s.getGcsPreprocessHandle(networkID, address, disableOptimizer)
			w := obj.NewWriter(ctx)
			w.ContentType = "text/plain"
			w.ContentEncoding = "gzip"
			gz := gzip.NewWriter(w)
			_, err = io.Copy(gz, data)
			data.Close()
			gz.Close()
			w.Close()
			if err != nil {
				logger.Warnf("failed to write preprocess file to GCS, err: %v", err)
				otherErr = err
				return
			}
		}
	}

	// cache deployed code if trying to disable optimizer
	if debugBuild {
		if _, err := c.GetDeployedCodeOverride(ctx); err != nil {
			logger.Warnf("failed to get deployed code, addr: %s, err: %v", address, err)
			otherErr = err
			return
		}
	}
	return
}

func (s *Service) GetContractIndex(ctx context.Context, req *protos.GetContractIndexRequest) (*protos.GetContractIndexResponse, error) {
	logger := log.WithContext(ctx)

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	var fileCompilation *types.FileCompilation

	if req.GetUserCompilationId() != "" {
		userCompilation := s.contractProcessor.UserCompilationByID(req.GetUserCompilationId())
		if userCompilation == nil {
			return nil, errors.New("compilation not found")
		}
		compilation, err := userCompilation.GetCompilation(ctx)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		if len(compilation.Compilations) == 0 {
			return nil, errors.New("compilation not found")
		}
		fileCompilation = compilation.Compilations[0]
	} else {
		userCompilationModelsMap, _, err := s.findUserCompilationsByAddresses(ctx, projectID, *chainSpec, req.TxId, []string{req.Address}, false)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		userCompilationMap, err := s.readUserCompilations(ctx, userCompilationModelsMap)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		if v, ok := userCompilationMap[req.Address]; ok {
			fileCompilation = v.Compilations[0]
		}

		if fileCompilation == nil {
			_, res, err := s.fetchAndCompile(ctx, *chainSpec, []string{req.Address}, false)
			if err != nil {
				logger.Errore(err)
				return nil, err
			}
			if len(res.Result) > 0 {
				fileCompilation = res.Result[0]
			}
		}
	}

	if fileCompilation == nil {
		return nil, errors.New("compilation not found")
	}

	indexer := semantic.CreateIndexer(*chainSpec, fileCompilation)
	index, err := indexer.Process()
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.GetContractIndexResponse{
		Index: index,
	}, nil
}

func (s *Service) GetABI(ctx context.Context, req *protos.GetABIRequest) (*protos.GetABIResponse, error) {
	owner := req.ProjectOwner
	slug := req.ProjectSlug
	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, owner, slug, auth.READ)
	if err != nil {
		return nil, err
	}

	return s.getABI(ctx, projectID, req.ChainSpec, req.TxId, req.Address)
}

func (s *Service) getABI(
	ctx context.Context,
	projectID string,
	chainSpec *protos.ChainIdentifier,
	txID *protos.TxIdentifier,
	address string,
) (*protos.GetABIResponse, error) {
	address = strings.ToLower(address)
	spec := models.ChainIdentifier{}
	spec.FromPB(chainSpec)

	// TODO should we support user compilations here?
	userCompilationMap, _, err := s.findUserCompilationsByAddresses(ctx, projectID, spec, txID, []string{address}, false)
	if err != nil {
		return nil, err
	}
	if _, ok := userCompilationMap[address]; ok {
		compilationMap, err := s.readUserCompilations(ctx, userCompilationMap)
		if err != nil {
			return nil, err
		}
		compilation := compilationMap[address]
		mainContractName := compilation.SourceInfo.ContractName
		for _, c := range compilation.Compilations[0].Contracts {
			if c.ContractName == mainContractName {
				data, _ := json.Marshal(c.ABI)
				return &protos.GetABIResponse{
					ABI: string(data),
				}, nil
			}
		}
		return nil, status.Errorf(codes.Internal, "main contract not found")
	}
	c := s.contractProcessor.WithContract(spec, address, false)
	md, err := c.FetchAndStoreMetadata(ctx)
	if err != nil {
		return nil, err
	}
	if md.Tag == contract.TagEIP1167 {
		return nil, types.ErrEIP1167
	}
	if md.IsProxy && md.ImplAddress != "" && strings.ToLower(md.ImplAddress) != address {
		log.WithContext(ctx).Infof("GetABI got proxy, proxy: %s, impl: %s", address, md.ImplAddress)
		return s.getABI(ctx, projectID, chainSpec, txID, md.ImplAddress)
	}
	return &protos.GetABIResponse{
		ABI: md.ABI,
	}, nil
}

func (s *Service) GetContractName(ctx context.Context, req *protos.GetContractNameRequest) (*protos.GetContractNameResponse, error) {
	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		return nil, err
	}

	address := strings.ToLower(req.Address)
	userCompilationMap, _, err := s.findUserCompilationsByAddresses(ctx, projectID, *chainSpec, req.TxId, []string{address}, false)
	if err != nil {
		return nil, err
	}
	if v, ok := userCompilationMap[address]; ok {
		return &protos.GetContractNameResponse{
			ContractName: v.ContractName,
		}, nil
	}
	c := s.contractProcessor.WithContract(*chainSpec, req.Address, false)
	rec, err := c.FetchAndStoreMetadata(ctx)
	if err != nil {
		return nil, err
	}
	if rec.Tag == contract.TagEIP1167 {
		return nil, types.ErrEIP1167
	}
	return &protos.GetContractNameResponse{
		ContractName: rec.ContractName,
	}, nil
}

var ErrSignatureNotFound = status.Errorf(codes.NotFound, "no signature matched")

func (s *Service) LookupSignature(ctx context.Context, req *protos.LookupSignatureRequest) (*protos.LookupSignatureResponse, error) {
	logger := log.WithContext(ctx)
	//b, _ := protojson.Marshal(req)
	//logger.Infof("LookupSignature, req: %s", string(b))

	hexSig := common.EnsureHexPrefix(req.HexSignature)
	if req.Type == protos.SignatureType_FUNCTION && len(hexSig) != 10 ||
		req.Type == protos.SignatureType_EVENT && len(hexSig) != 66 {
		return nil, fmt.Errorf("incorrect signature length")
	}

	var sigs []*models.EthSignature
	err := s.syncRepository.DB.WithContext(ctx).
		Where("type = ? AND hex_signature = ?", req.Type, hexSig).
		Order("weight DESC").
		Find(&sigs).Error
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	if len(sigs) == 0 && req.ChainSpec != nil && req.Address != nil {
		_, err := s.getABI(ctx, "", req.ChainSpec, nil, *req.Address)
		if err == nil {
			err := s.syncRepository.DB.WithContext(ctx).
				Where("type = ? AND hex_signature = ?", req.Type, hexSig).
				Order("weight DESC").
				Find(&sigs).Error
			if err != nil {
				logger.Errore(err)
				return nil, err
			}
		}
	}

	// check signatures extracted from ABI first
	var reliableSigs, unreliableSigs []*models.EthSignature
	for _, sig := range sigs {
		if sig.Source == models.SignatureSourceABI {
			reliableSigs = append(reliableSigs, sig)
		} else {
			unreliableSigs = append(unreliableSigs, sig)
		}
	}
	if sig, err := selectSignature(ctx, reliableSigs, req); err == nil {
		return &protos.LookupSignatureResponse{
			TextSignature: sig.TextSignature,
			AbiItem:       &sig.ABIItem,
		}, nil
	}
	logger.Infof("no reliable signature found, hex: %s", req.HexSignature)
	if sig, err := selectSignature(ctx, unreliableSigs, req); err == nil {
		return &protos.LookupSignatureResponse{
			TextSignature: sig.TextSignature,
		}, nil
	}

	//// make 4byte request if not found in db
	//logger.Infof("signature not found in db, looking up external repositories, hex: %s", req.HexSignature)
	//
	//var resSigs []clients.Signature
	//if req.Type == protos.SignatureType_EVENT {
	//	resSigs, err = s.ethSigClient.ListEventSignatures(ctx, req.HexSignature)
	//} else {
	//	resSigs, err = s.ethSigClient.ListFunctionSignatures(ctx, req.HexSignature)
	//}
	//if err != nil {
	//	logger.Errore(err)
	//	return nil, err
	//}

	//if len(resSigs) > 0 {
	//	var newSigs []*models.EthSignature
	//	for _, resSig := range resSigs {
	//		data, err := json.Marshal(resSig)
	//		if err != nil {
	//			logger.Errore(err)
	//			return nil, err
	//		}
	//		sig := &models.EthSignature{
	//			Type:          req.Type,
	//			HexSignature:  resSig.HexSignature,
	//			TextSignature: resSig.TextSignature,
	//			ABIHash:       crypto.Keccak256Hash(data).Hex(),
	//			Source:        models.SignatureSourceExternal,
	//			Weight:        1,
	//		}
	//		newSigs = append(newSigs, sig)
	//	}
	//	err = s.syncRepository.CreateSignatures(ctx, newSigs)
	//	if err != nil {
	//		logger.Errore(err)
	//		return nil, err
	//	}
	//
	//	if ret, err := selectSignature(ctx, newSigs, req); err == nil {
	//		return &protos.LookupSignatureResponse{
	//			TextSignature: ret.TextSignature,
	//		}, nil
	//	}
	//}
	return nil, ErrSignatureNotFound
}

func (s *Service) ParseContracts(
	ctx context.Context,
	req *protos.ParseContractsRequest,
) (*protos.ParseContractsResponse, error) {
	if req.CompileSpec == nil {
		return nil, status.Errorf(codes.InvalidArgument, "empty compileSpec")
	}

	var inputs []*solgo.SourceUnit

	switch req.CompileSpec.Source.(type) {
	case *protos.SourceSpec_MultiFile:
		t := req.CompileSpec.Source.(*protos.SourceSpec_MultiFile)
		for path, content := range t.MultiFile.Source {
			inputs = append(inputs, &solgo.SourceUnit{
				Name:    path,
				Path:    path,
				Content: content,
			})
		}

	case *protos.SourceSpec_StandardJson, *protos.SourceSpec_Metadata:
		// TODO get these typed
		t := req.CompileSpec.Source.(*protos.SourceSpec_StandardJson)
		sources, ok := t.StandardJson.GetFields()["sources"]
		if !ok {
			return nil, fmt.Errorf("missing sources field")
		}
		sourcesStruct := sources.GetStructValue()
		if sourcesStruct == nil {
			return nil, fmt.Errorf("sources field not a list")
		}
		for path, source := range sourcesStruct.GetFields() {
			sourceStruct := source.GetStructValue()
			if sourceStruct == nil {
				return nil, fmt.Errorf("malformed source")
			}
			content, ok := sourceStruct.GetFields()["content"]
			if !ok {
				continue
			}
			contentString := content.GetStringValue()
			inputs = append(inputs, &solgo.SourceUnit{
				Name:    path,
				Path:    path,
				Content: contentString,
			})
		}
	}

	var contracts []string
	p, err := solgo.NewParserFromSources(ctx, &solgo.Sources{
		SourceUnits: inputs,
	})
	if err != nil {
		return nil, err
	}
	_ = p.Parse()
	tokens := p.GetTokenStream().GetAllTokens()
	for i, v := range tokens {
		if v.GetTokenType() == parser.SolidityParserContract {
			contracts = append(contracts, tokens[i+1].GetText())
		}
	}
	return &protos.ParseContractsResponse{
		Contracts: contracts,
	}, nil
}

func (s *Service) GetStorageSummary(
	ctx context.Context,
	req *protos.GetStorageSummaryRequest,
) (*protos.GetStorageSummaryResponse, error) {
	logger := log.WithContext(ctx)

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}
	req.Address = strings.ToLower(req.Address)

	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	addresses := []string{req.Address}

	var implAddress *string
	c := s.contractProcessor.WithContract(*chainSpec, req.Address, false)
	md, err := c.FetchAndStoreMetadata(ctx)
	if err == nil && md.ImplAddress != "" {
		implAddress = lo.ToPtr(strings.ToLower(md.ImplAddress))
		addresses = append(addresses, md.ImplAddress)
	}
	preprocessResult, err := s.preprocess(ctx, projectID, *chainSpec, nil, addresses, false)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	chainClient, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	blockNumber := lo.FromPtr(req.BlockNumber)
	if blockNumber == "" {
		blockNumber, err = chainClient.Client.GetLatestBlockNumber(ctx)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
	}
	block, err := chainClient.Client.GetBlockByNumber(ctx, blockNumber, false)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	blockHash := block.Fields["hash"].GetStringValue()
	customChainConfig, err := s.getCustomChainConfig(ctx, *chainSpec)
	if err != nil {
		return nil, err
	}
	internalResp, err := s.debuggerClient.GetStorageSummaryInternal(ctx, &protos.GetStorageSummaryInternalRequest{
		NetworkId:    chainSpec.ChainID,
		Address:      req.Address,
		ImplAddress:  implAddress,
		BlockHash:    blockHash,
		ContractKeys: preprocessResult,
		VariablePath: req.VariablePath,
		ChainConfig:  customChainConfig,
	})
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.GetStorageSummaryResponse{
		Address:     req.Address,
		ImplAddress: implAddress,
		Results:     internalResp.Results,
		BlockNumber: blockNumber,
	}, nil
}

func (s *Service) DumpSimulation(
	ctx context.Context,
	req *protos.DumpSimulationRequest,
) (*protos.DumpSimulationResponse, error) {
	// TODO admin auth by api key?
	logger := log.WithContext(ctx)

	sim, err := s.getSimulationModel(ctx, req.SimulationId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	simPB := sim.ToPB()
	resp := &protos.DumpSimulationResponse{
		SimulationReq: &protos.SimulateTransactionRequest{
			Simulation: simPB,
		},
		CompilationReq: map[string]*protos.UploadUserCompilationRequest{},
	}
	var m sync.Mutex
	g, gCtx := errgroup.WithContext(ctx)
	for addr := range simPB.SourceOverrides {
		id := simPB.SourceOverrides[addr]
		g.Go(func() error {
			c, err := s.dumpUserCompilation(gCtx, id)
			if err != nil {
				return err
			}
			m.Lock()
			defer m.Unlock()
			resp.CompilationReq[id] = c
			return nil
		})
	}
	err = g.Wait()
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return resp, nil
}

func (s *Service) DumpUserCompilation(
	ctx context.Context,
	req *protos.DumpUserCompilationRequest,
) (*protos.DumpUserCompilationResponse, error) {
	// TODO admin auth by api key?
	logger := log.WithContext(ctx)
	ret, err := s.dumpUserCompilation(ctx, req.UserCompilationId)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.DumpUserCompilationResponse{
		CompilationReq: ret,
	}, nil
}

func (s *Service) dumpUserCompilation(ctx context.Context, id string) (*protos.UploadUserCompilationRequest, error) {
	logger := log.WithContext(ctx)
	c := s.contractProcessor.UserCompilationByID(id)
	rec, err := c.GetDBRec(ctx)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	source, err := c.GetSource(ctx)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.UploadUserCompilationRequest{
		// omit projectOwner, projectSlug
		CompileSpec: source,
		Name:        &rec.Name,
	}, nil
}

type DownloadContractMetadata struct {
	Settings        json.RawMessage `json:"settings"`
	SolidityVersion string          `json:"solidityVersion"`
	ContractName    string          `json:"contractName"`
	ConstructorArgs string          `json:"constructorArgs"`
}

type VSCodeSettings struct {
	SolidityRemappings     []string `json:"solidity.remappings,omitempty"`
	SolidityDefaultDepsDir string   `json:"solidity.packageDefaultDependenciesDirectory,omitempty"`
}

func (s *Service) DownloadContract(
	ctx context.Context,
	req *protos.DownloadContractRequest,
) (*httpbody.HttpBody, error) {
	logger := log.WithContext(ctx)

	chainSpec, err := models.GetChainSpec(req.ChainSpec, req.NetworkId)
	if err != nil {
		return nil, err
	}

	addr := strings.ToLower(req.Address)
	_, compileResp, err := s.fetchAndCompile(ctx, *chainSpec, []string{addr}, false)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	if len(compileResp.Result) != 1 {
		err = fmt.Errorf("malformed compilation")
		return nil, err
	}

	buf := new(bytes.Buffer)
	w := zip.NewWriter(buf)

	// write base files
	assetRoot := "service/solidity/assets"
	err = filepath.WalkDir(assetRoot,
		func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return err
			}
			if d.IsDir() {
				return nil
			}
			data, err := os.ReadFile(path)
			if err != nil {
				return err
			}
			fh := &zip.FileHeader{
				Name:   strings.TrimPrefix(path, assetRoot),
				Method: zip.Deflate,
			}
			if strings.HasSuffix(path, ".sh") {
				fh.SetMode(0777)
			}
			f, err := w.CreateHeader(fh)
			if err != nil {
				return err
			}
			_, err = f.Write(data)
			if err != nil {
				return err
			}
			return nil
		})
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	// write sources
	for _, source := range compileResp.Result[0].Sources {
		f, err := w.Create("src/" + source.SourcePath)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		_, err = f.Write([]byte(source.Source))
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
	}

	// write metadata
	f, err := w.Create("metadata.json")
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	sourceInfo := compileResp.SourceInfo[addr]
	opt := sourceInfo.Options
	if opt == nil {
		err = fmt.Errorf("unable to get compiler options")
		logger.Errore(err)
		return nil, err
	}

	// TODO correct settings field in stored compilations
	settings, err := json.Marshal(opt.Settings)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	c := s.contractProcessor.WithContract(*chainSpec, addr, false)
	remoteSource, err := c.FetchSource(ctx)
	if err == nil {
		compilerInput, err := remoteSource.PrepareCompilerInput()
		if err == nil {
			settings = []byte(compilerInput.Settings)
		}
	}

	md := &DownloadContractMetadata{
		SolidityVersion: "v" + strings.TrimPrefix(opt.Version, "v"), // TODO ensure long version is used
		Settings:        settings,
		ContractName:    sourceInfo.ContractName,
		ConstructorArgs: "",
	}
	if opt.Specializations != nil {
		md.ConstructorArgs = opt.Specializations.ConstructorArguments
	}
	data, err := json.MarshalIndent(md, "", "  ")
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	_, err = f.Write(data)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	// write vscode solidity extension settings
	f, err = w.Create(".vscode/settings.json")
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	var settingsStruct types.Settings
	err = json.Unmarshal(settings, &settingsStruct)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	vscSettings := VSCodeSettings{
		SolidityDefaultDepsDir: "src",
	}
	for _, remapping := range settingsStruct.Remappings {
		parts := strings.Split(remapping, "=")
		if len(parts) != 2 {
			err = fmt.Errorf("invalid remapping")
			logger.Errore(err)
			return nil, err
		}
		vscSettings.SolidityRemappings = append(vscSettings.SolidityRemappings,
			parts[0]+"="+path.Join("src", parts[1]))
	}
	data, err = json.MarshalIndent(vscSettings, "", "  ")
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	_, err = f.Write(data)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}

	err = w.Close()
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	header := metadata.Pairs("Content-Disposition", fmt.Sprintf("attachment; filename=contract_%s.zip", addr))
	err = grpc.SendHeader(ctx, header)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	resp := &httpbody.HttpBody{
		ContentType: "application/zip",
		Data:        buf.Bytes(),
	}
	return resp, nil
}

func (s *Service) GetRecentTransactions(
	ctx context.Context,
	req *protos.GetRecentTransactionsRequest,
) (*protos.GetRecentTransactionsResponse, error) {
	logger := log.WithContext(ctx)
	chainSpec := models.ChainIdentifier{}
	chainSpec.FromPB(req.ChainSpec)
	client, err := s.chainClients.GetChainClient(ctx, chainSpec)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	startBN := int64(0)
	if chainSpec.IsFork() {
		var fork models.CustomChain
		err := s.envRepository.DB.WithContext(ctx).
			Preload(clause.Associations).
			Where(&models.CustomChain{
				ID: chainSpec.ForkID,
			}).Take(&fork).Error
		if err != nil {
			return nil, err
		}
		parentBlockNumber := int64(0)
		if fork.Type == models.CustomChainTypeManagedFork {
			parentBlockNumber = fork.ManagedFork.ParentBlockNumber
		} else {
			if fork.ExternalFork.ParentBlockNumber > 0 {
				parentBlockNumber = fork.ExternalFork.ParentBlockNumber
			} else {
				// compat with old forks
				nodeInfo, err := client.Client.GetAnvilNodeInfo(ctx)
				if err != nil {
					logger.Errore(err)
					return nil, err
				}
				parentBlockNumber = nodeInfo.ForkConfig.ForkBlockNumber
			}
		}
		startBN = parentBlockNumber + 1
	}
	bnStr, err := client.Client.GetLatestBlockNumber(ctx)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	limit := int(lo.FromPtr(req.Limit))
	if limit == 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}
	bn, err := strconv.ParseInt(bnStr[2:], 16, 64)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	var ret []string
	for bn >= startBN {
		s := "0x" + strconv.FormatInt(bn, 16)
		block, err := client.Client.GetBlockByNumber(ctx, s, false)
		if err != nil {
			logger.Errore(err)
			return nil, err
		}
		txs := lo.Reverse(block.Fields["transactions"].GetListValue().Values)
		for _, tx := range txs {
			ret = append(ret, tx.GetStringValue())
			if len(ret) >= limit {
				break
			}
		}
		if len(ret) >= limit {
			break
		}
		bn--
	}
	return &protos.GetRecentTransactionsResponse{
		TxHashes: ret,
	}, nil
}

type ChainParam struct {
	EIP1559Elasticity  int
	EIP1559Denominator int
	BlockIntervalMs    int
	GasLimit           int64
	System             string
	Network            string
}

var chainParams = map[string]ChainParam{
	"1": {
		EIP1559Elasticity:  2,
		EIP1559Denominator: 8,
		BlockIntervalMs:    12000,
		GasLimit:           30000000,
		System:             "ethereum",
		Network:            "mainnet",
	},
	"137": {
		EIP1559Elasticity:  2,
		EIP1559Denominator: 16,
		BlockIntervalMs:    2000,
		GasLimit:           30000000,
		System:             "polygon",
		Network:            "mainnet",
	},
	"8453": {
		EIP1559Elasticity:  6,
		EIP1559Denominator: 50,
		BlockIntervalMs:    2000,
		GasLimit:           240000000,
		System:             "base",
		Network:            "mainnet",
	},
}

var confidences = []int32{99, 95, 90, 80, 70}

func getScaledQuantile(chainId string, confidence int32, msSinceLastBlock int32) float64 {
	var confidenceToQuantile = map[int32]float64{
		99: 0.75,
		95: 0.85,
		90: 0.9,
		80: 0.95,
		70: 0.98,
	}
	scaleF := 0.9
	progress := math.Min(float64(msSinceLastBlock)/float64(chainParams[chainId].BlockIntervalMs), 1)
	progressF := 1 - (1-progress)*(1-progress)
	q := confidenceToQuantile[confidence]
	// progress = 0 -> q * f
	// progress = 1 -> q
	return (1-progressF)*q*scaleF + progressF*q
}

func (s *Service) GetEstimatedGasPrice(
	ctx context.Context,
	req *protos.GetEstimatedGasPriceRequest,
) (*protos.GetEstimatedGasPriceResponse, error) {
	chainId := req.ChainId
	if chainId == "" {
		chainId = "1"
	}
	if !lo.Contains(gasEstimationSupportChainIds, chainId) {
		return nil, status.Errorf(codes.InvalidArgument, "invalid chain id, supported: %v", gasEstimationSupportChainIds)
	}
	chainParam := chainParams[chainId]
	chainClient, err := s.chainClients.GetChainClient(ctx, models.ChainIdentifier{ChainID: chainId})
	if err != nil {
		return nil, err
	}
	latestBN, err := chainClient.Client.GetLatestBlockNumber(ctx)
	if err != nil {
		return nil, err
	}
	latestBlockNumber := utils.MustParseBigInt(latestBN).Int64()
	latestBlock, err := chainClient.Client.GetBlockByNumber(ctx, latestBN, false)
	if err != nil {
		return nil, err
	}

	baseFeeHex := latestBlock.Fields["baseFeePerGas"].GetStringValue()
	gasUsedHex := latestBlock.Fields["gasUsed"].GetStringValue()
	gasLimitHex := latestBlock.Fields["gasLimit"].GetStringValue()
	baseFee := utils.MustParseBigInt(baseFeeHex).Int64()
	gasUsed := utils.MustParseBigInt(gasUsedHex).Int64()
	gasLimit := utils.MustParseBigInt(gasLimitHex).Int64()
	gasTarget := float64(gasLimit) / float64(chainParam.EIP1559Elasticity)
	baseFeeDelta := math.Ceil((float64(gasUsed) - gasTarget) * float64(baseFee) / gasTarget / float64(chainParam.EIP1559Denominator))
	if baseFeeDelta > 0 {
		baseFeeDelta = math.Max(baseFeeDelta, 1)
	}
	nextBaseFee := baseFee + int64(baseFeeDelta)

	latestBlockTimestampHex := latestBlock.Fields["timestamp"].GetStringValue()
	latestBlockTimestamp := utils.MustParseBigInt(latestBlockTimestampHex).Int64()
	msSinceLastBlock := int32(time.Now().UnixMilli() - latestBlockTimestamp*1000)
	if msSinceLastBlock < 0 {
		msSinceLastBlock = 0
	}

	txpoolContent := s.txPoolPolls[chainId].GetContent()
	var priorityFees []int64
	var txs []ethclient.TxPoolTransaction
	var maxPrice int64 = 0
	for _, txBySender := range txpoolContent.Pending {
		for _, tx := range txBySender {
			var p int64 = 0
			if tx.Type.String() == "0x0" {
				p = tx.GasPrice.ToInt().Int64()
				txs = append(txs, tx)
			} else if tx.Type.String() == "0x2" {
				maxFeePerGas := tx.MaxFeePerGas.ToInt().Int64()
				maxPriorityFeePerGas := tx.MaxPriorityFeePerGas.ToInt().Int64()
				priorityFees = append(priorityFees, maxPriorityFeePerGas)
				p = nextBaseFee + maxPriorityFeePerGas
				if p > maxFeePerGas {
					p = maxFeePerGas
				}
				tx.GasPrice = (*hexutil.Big)(big.NewInt(p))
				txs = append(txs, tx)
			}
			if maxPrice < p {
				maxPrice = p
			}
		}
	}
	sort.Slice(txs, func(i, j int) bool {
		return txs[i].GasPrice.ToInt().Cmp(txs[j].GasPrice.ToInt()) == 1
	})
	cnt := 0
	var gas int64 = 0
	for _, tx := range txs {
		g := tx.Gas.ToInt().Int64()
		if gas+g > chainParam.GasLimit {
			break
		}
		//if tx.Type.String() == "0x2" {
		//	priorityFees = append(priorityFees, tx.MaxPriorityFeePerGas.ToInt().Int64())
		//}
		cnt += 1
		gas += g
	}
	sort.Slice(priorityFees, func(i, j int) bool {
		return priorityFees[i] > priorityFees[j]
	})
	if len(priorityFees) == 0 {
		priorityFees = []int64{2e9}
	}

	resp := &protos.GetEstimatedGasPriceResponse{
		System:             chainParam.System,
		Network:            chainParam.Network,
		Unit:               "gwei",
		CurrentBlockNumber: int32(latestBlockNumber),
		MsSinceLastBlock:   msSinceLastBlock,
		MaxPrice:           float64(maxPrice) / 1e9,
		BlockPrices: []*protos.BlockPrice{
			{
				BlockNumber:               int32(latestBlockNumber) + 1,
				EstimatedTransactionCount: int32(cnt),
				BaseFeePerGas:             float64(nextBaseFee) / 1e9,
				BlobBaseFeePerGas:         0, // TODO
				EstimatedPrices:           []*protos.EstimatedPrice{},
			},
		},
	}

	for _, confidence := range confidences {
		q := getScaledQuantile(chainId, confidence, msSinceLastBlock)
		pos := int(math.Ceil(float64(len(priorityFees)) * q))
		if pos >= len(priorityFees) {
			pos = len(priorityFees) - 1
		}
		maxPriorityFee := ceilToPrecision(math.Max(float64(priorityFees[pos])/1e9*1.01, 0.01), 10)
		maxFee := float64(nextBaseFee)*2/1e9 + maxPriorityFee
		price := float64(nextBaseFee)/1e9 + maxPriorityFee

		if chainId == "8453" {
			maxFee = 0.014
			maxPriorityFee = 1e-6
			price = 0.008
		}

		resp.BlockPrices[0].EstimatedPrices = append(resp.BlockPrices[0].EstimatedPrices, &protos.EstimatedPrice{
			Confidence:           confidence,
			MaxPriorityFeePerGas: maxPriorityFee,
			MaxFeePerGas:         maxFee,
			Price:                price,
		})
	}
	return resp, nil
}

func ceilToPrecision(x float64, precision int) float64 {
	return math.Ceil(x*math.Pow10(precision)) / math.Pow10(precision)
}

func (s *Service) authTxRead(ctx context.Context, owner *string, slug *string, txID *protos.TxIdentifier, shareID *string) (string, error) {
	if txID == nil {
		return "", fmt.Errorf("empty txID")
	}
	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, owner, slug, auth.READ)
	if err != nil {
		return "", err
	}
	if txID.GetTxHash() != "" {
		return projectID, nil
	}
	var sim *models.Simulation
	if txID.GetBundleId() != "" {
		sim, err = s.getBundleFirstSimulation(ctx, txID.GetBundleId())
	} else {
		sim, err = s.getSimulationModel(ctx, txID.GetSimulationId())
	}
	if err != nil {
		return "", err
	}
	var shareSim *models.ShareSimulation
	if shareID != nil {
		shareSim, err = s.getSharedSimulation(ctx, *shareID)
		if err != nil {
			return "", err
		}
		if shareSim != nil && shareSim.Public && shareSim.SimulationID == sim.ID {
			return projectID, nil
		}
	}

	if sim.ProjectID != "" && sim.ProjectID != projectID {
		return "", status.Errorf(codes.Unauthenticated, "permission denied")
	}
	return projectID, nil
}

func selectSignature(ctx context.Context, sigs []*models.EthSignature, req *protos.LookupSignatureRequest) (*models.EthSignature, error) {
	logger := log.WithContext(ctx)

	var alt *models.EthSignature
	switch req.Type {
	case protos.SignatureType_FUNCTION:
		if len(sigs) > 0 && req.Data == nil && req.Output == nil {
			return sigs[0], nil
		}
		for _, sig := range sigs {
			inputMatched, outputMatched, err := common.VerifyFunctionSignature(
				sig,
				lo.FromPtrOr(req.Data, "0x"),
				lo.FromPtrOr(req.Output, "0x"),
			)
			if err != nil {
				logger.Warne(err)
			}
			if inputMatched {
				if outputMatched {
					return sig, nil
				} else {
					// accept it if no perfect match found
					alt = sig
				}
			}
		}

	case protos.SignatureType_EVENT:
		if len(sigs) > 0 && req.Data == nil && req.Topics == nil {
			return sigs[0], nil
		}
		// always require perfect match for events
		// otherwise decoded result is likely to be a mess
		for _, sig := range sigs {
			dataMatched, topicsMatched, err := common.VerifyEventSignature(
				sig,
				lo.FromPtrOr(req.Data, "0x"),
				req.Topics,
			)
			if err != nil {
				logger.Warne(err)
			}
			if dataMatched && topicsMatched {
				return sig, nil
			}
		}
	}

	if alt != nil {
		return alt, nil
	}
	return nil, ErrSignatureNotFound
}

func (s *Service) getCustomChainConfig(ctx context.Context, chainSpec models.ChainIdentifier) (*protos.BaseChainConfig, error) {
	if chainSpec.IsBuiltin() {
		return nil, nil
	}
	c, err := s.chainClients.GetCustomEVMChainClient(ctx, chainSpec)
	if err != nil {
		return nil, err
	}
	return c.Config.ToPB(), nil
}
