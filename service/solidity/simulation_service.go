package solidity

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gopkg.in/yaml.v3"

	clickhousev2 "github.com/ClickHouse/clickhouse-go/v2"
	"github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"

	evmprotos "sentioxyz/sentio/chain/evm/protos"
	"sentioxyz/sentio/common/chains"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/solidity/common"
	"sentioxyz/sentio/service/solidity/common/clients"
	"sentioxyz/sentio/service/solidity/models"
	"sentioxyz/sentio/service/solidity/protos"
)

func LoadChainClients(
	path string,
	simDB *gorm.DB,
	redisClient *redis.Client,
	cacheEthCalls bool,
) (*clients.ChainClients, map[string]models.ChainConfig, error) {
	var chainConfigs models.ChainConfigsWithEtherscanV2Config
	file, err := os.ReadFile(path)
	if err != nil {
		log.Fatale(err)
	}
	err = yaml.Unmarshal(file, &chainConfigs)
	if err != nil {
		log.Fatale(err)
	}
	ecv2Conf := chainConfigs.EtherscanV2Config
	for chainID, chain := range chainConfigs.Chains {
		if chain.SourceFetcherType == models.SourceFetcherTypeEtherscanV2 {
			if chain.SourceFetcherEndpoint == "" {
				chain.SourceFetcherEndpoint = ecv2Conf.Endpoint
			}
			if len(chain.SourceFetcherAPIKeys) == 0 {
				chain.SourceFetcherAPIKeys = ecv2Conf.APIKeys
			}
			if chain.SourceFetcherTimeout == 0 {
				chain.SourceFetcherTimeout = ecv2Conf.Timeout
			}
		}
		chainConfigs.Chains[chainID] = chain
	}
	chainClients, err := buildChainClients(chainConfigs.Chains, simDB, redisClient, cacheEthCalls)
	if err != nil {
		log.Fatale(err)
	}
	return chainClients, chainConfigs.Chains, nil
}

func buildChainClients(configs map[string]models.ChainConfig, simDB *gorm.DB, redisClient *redis.Client, cacheEthCalls bool) (*clients.ChainClients, error) {
	cliMap := make(map[string]*clients.ChainClient)
	for network, config := range configs {
		if chains.IsEVMChains(network) {
			if config.Endpoint == "" {
				log.Warnf("endpoint for chain %s is empty", network)
				continue
			}
			cli, err := clients.BuildEVMChainClient(network, config.BaseChainConfig, simDB, redisClient, cacheEthCalls, clients.EVMBuiltin)
			if err != nil {
				log.Errorfe(err, "build evm chain clients for chain %s failed", network)
				return nil, err
			}
			cliMap[network] = cli
		} else if chains.IsSuiChain(network) {
			options, err := clickhousev2.ParseDSN(config.SuiClickHouseDNS)
			if err != nil {
				log.Errore(err)
				return nil, fmt.Errorf("parse clickhouse dsn %q for chain %q failed: %w",
					config.SuiClickHouseDNS, network, err)
			}
			conn, err := clickhousev2.Open(options)
			if err != nil {
				log.Errore(err)
				return nil, fmt.Errorf("connect to clickhouse %q for chain %q failed: %w",
					config.SuiClickHouseDNS, network, err)
			}
			cliMap[network] = &clients.ChainClient{
				Type:              clients.Sui,
				Config:            config,
				SuiClickhouseConn: conn,
			}
		} else {
			cliMap[network] = &clients.ChainClient{
				Type:   clients.Other,
				Config: config,
			}
		}
	}
	return clients.NewChainClients(cliMap, simDB, redisClient), nil
}

func normalizeUint256Hex(val *string, name string) error {
	*val = strings.ToLower(*val)
	if strings.Compare(*val, "latest") == 0 || strings.Compare(*val, "pending") == 0 {
		return nil
	}

	x, ok := utils.ParseBigInt(*val)
	if !ok {
		return status.Errorf(codes.InvalidArgument, "value of %q is %q, not a valid uint256", name, *val)
	}
	*val = utils.FormatBigInt16(x)
	return nil
}

func fetchTransactionIfIncomplete(ctx context.Context, client *clients.EthClientWithSim, sim *protos.Simulation) error {
	if sim.Input == "" && sim.OriginTxHash != "" {
		tx, err := client.GetTransaction(ctx, sim.OriginTxHash)
		if err != nil {
			return err
		}

		if sim.To == "" {
			sim.To = tx.To
		}
		if sim.Input == "" {
			sim.Input = tx.Input
		}
		if sim.BlockNumber == "" {
			sim.BlockNumber = tx.BlockNumber
		}
		if sim.TransactionIndex == "" {
			sim.TransactionIndex = tx.TransactionIndex
		}
		if sim.From == "" {
			sim.From = tx.From
		}
		if sim.Gas == "" {
			sim.Gas = tx.Gas
		}
		if sim.GasPrice == "" {
			sim.GasPrice = tx.GasPrice
		}
		if sim.MaxFeePerGas == "" {
			sim.MaxFeePerGas = tx.MaxFeePerGas
		}
		if sim.MaxPriorityFeePerGas == "" {
			sim.MaxPriorityFeePerGas = tx.MaxPriorityFeePerGas
		}
		if sim.Value == "" {
			sim.Value = tx.Value
		}
		if sim.AccessList == nil {
			sim.AccessList = tx.AccessList
		}
	}
	return nil
}

func trimSimulation(sim *protos.Simulation, isFirst bool) error {
	if isFirst {
		if err := normalizeUint256Hex(&sim.BlockNumber, "blockNumber"); err != nil {
			return err
		}
		if err := normalizeUint256Hex(&sim.TransactionIndex, "transactionIndex"); err != nil {
			return err
		}
	}
	if sim.Gas != "" {
		if err := normalizeUint256Hex(&sim.Gas, "gas"); err != nil {
			return err
		}
	}
	if sim.MaxFeePerGas != "" {
		if err := normalizeUint256Hex(&sim.MaxFeePerGas, "maxFeePerGas"); err != nil {
			return err
		}
	}
	if sim.MaxPriorityFeePerGas != "" {
		if err := normalizeUint256Hex(&sim.MaxPriorityFeePerGas, "maxPriorityFeePerGas"); err != nil {
			return err
		}
	}

	if err := normalizeUint256Hex(&sim.GasPrice, "gasPrice"); err != nil {
		return err
	}
	return normalizeUint256Hex(&sim.Value, "value")
}

func (s *Service) SimulateTransactionBundle(ctx context.Context, req *protos.SimulateTransactionBundleRequest) (*protos.SimulateTransactionBundleResponse, error) {
	userID, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.WRITE)
	if err != nil {
		return nil, err
	}
	if len(req.Simulations) == 0 {
		return nil, errors.New("empty simulations")
	}

	bundle := models.Bundle{
		UserID:    userID,
		ProjectID: projectID,
	}

	// TODO use transaction to control all writes
	if err := s.envRepository.DB.Create(&bundle).Error; err != nil {
		return nil, err
	}

	sims, err := s.simulate(ctx, userID, projectID, bundle.ID, req.Simulations)
	res := &protos.SimulateTransactionBundleResponse{
		BundleId:    bundle.ID,
		Simulations: sims,
	}
	if err != nil {
		if sims == nil {
			return nil, err
		}
		res.Error = err.Error()

		bundle.NetworkID = sims[0].ChainSpec.GetChainId()
		bundle.ForkID = sims[0].ChainSpec.GetForkId()
		bundle.Error = res.Error
		if err = s.envRepository.DB.Model(&bundle).Update("error", res.Error).Error; err != nil {
			return nil, err
		}
	}
	return res, nil
}

func (s *Service) SimulateTransaction(
	ctx context.Context,
	req *protos.SimulateTransactionRequest,
) (*protos.SimulateTransactionResponse, error) {
	logger := log.WithContext(ctx)
	logger.Debugf("SimulateTransaction, req: %s", utils.MustJSONMarshal(req))

	// simulation will be cleaned in a short time if it's anonymous
	userID, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.WRITE)
	if err != nil {
		return nil, err
	}

	sims, err := s.simulate(ctx, userID, projectID, "", []*protos.Simulation{req.Simulation})
	if err != nil {
		return nil, err
	}

	return &protos.SimulateTransactionResponse{Simulation: sims[0]}, nil
}

func (s *Service) simulate(
	ctx context.Context,
	userID string,
	projectID string,
	bundleID string, // could be empty if not from bundle
	sims []*protos.Simulation,
) ([]*protos.Simulation, error) {
	//logger := log.WithContext(ctx)
	var res []*protos.Simulation
	isBundle := len(sims) > 1

	if sims == nil {
		return nil, fmt.Errorf("miss simulations")
	}

	// normalize chain id first so we could create client
	sim := sims[0]
	if sim.BlockNumber == "0x0" {
		return nil, status.Errorf(codes.InvalidArgument, "blockNumber cannot be zero")
	}

	if sim.ChainId == "" {
		sim.ChainId = sim.NetworkId
	}
	var chainSpec *models.ChainIdentifier
	if sim.ChainSpec == nil {
		if err := normalizeUint256Hex(&sim.ChainId, "chainId"); err != nil {
			return nil, err
		}
		networkIDBig, _ := utils.ParseBigInt(sim.ChainId)
		networkID := networkIDBig.String() // in the form of 137
		chainSpec, _ = models.GetChainSpec(nil, networkID)
	} else {
		chainSpec = &models.ChainIdentifier{}
		chainSpec.FromPB(sim.ChainSpec)
	}
	sim.ChainSpec = chainSpec.ToPB()

	ethCli, err := s.chainClients.GetChainClient(ctx, *chainSpec)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "unsupported networkId %q", chainSpec.ToString())
	}

	for idx, sm := range sims {
		if sm == nil {
			return nil, fmt.Errorf("simulation can't be null")
		}
		if isBundle {
			if err := fetchTransactionIfIncomplete(ctx, ethCli.DebugClient, sm); err != nil {
				return nil, err
			}
		}
		if err := trimSimulation(sm, idx == 0); err != nil {
			return nil, err
		}
	}
	sourceOverrides := map[string]string{}
	for k, v := range sim.SourceOverrides {
		sourceOverrides[strings.ToLower(k)] = v
	}
	sim.SourceOverrides = sourceOverrides

	// apply source overrides
	for k, v := range sim.StateOverrides {
		delete(sim.StateOverrides, k)
		sim.StateOverrides[strings.ToLower(k)] = v
	}
	data, err := json.Marshal(sim.StateOverrides)
	if err != nil {
		return nil, err
	}
	actualStateOverrides := map[string]*common.Account{}
	err = json.Unmarshal(data, &actualStateOverrides)
	if err != nil {
		return nil, err
	}
	if actualStateOverrides == nil && len(sim.SourceOverrides) > 0 {
		actualStateOverrides = map[string]*common.Account{}
	}
	for _, acc := range actualStateOverrides {
		// in protos, field *state* actually represents *stateDiff*
		acc.StateDiff = acc.State
		acc.State = nil
	}
	if !sim.DebugDeployment {
		for address, userCompilationID := range sim.SourceOverrides {
			c := s.contractProcessor.UserCompilationByID(userCompilationID)
			deployedCode, err := c.GetDeployedCode(ctx, *chainSpec, address)
			if err != nil {
				return nil, status.Errorf(codes.Internal, "failed to get deployed code for %s: %v", userCompilationID, err)
			}
			if _, ok := actualStateOverrides[address]; !ok {
				actualStateOverrides[address] = &common.Account{}
			}
			actualStateOverrides[address].Code = &deployedCode
		}
	}
	actualStateOverridesStr := utils.MustJSONMarshal(actualStateOverrides)

	// doing traceCall here for transaction receipt
	var results [][]*common.SentioPartialRootCallTrace

	var simModels []*models.Simulation
	for _, sim := range sims {
		var sm models.Simulation
		sm.FromPB(sim, userID, projectID, actualStateOverridesStr)
		simModels = append(simModels, &sm)
	}
	bundlesJSON, blockJSON := simModels[0].GetTraceCallParamsJSON(simModels[1:]...)

	opt := map[string]any{
		"stateOverrides": actualStateOverrides,
		"tracer":         "sentioTracer",
		"tracerConfig": map[string]any{
			"onlyTopCall": false,
			"withLog":     true,
		},
	}
	if sim.DebugDeployment {
		for k := range sim.SourceOverrides {
			opt["createAddressOverride"] = k
			break
		}
	}
	// TODO share code with ethClient.TraceTx
	_, traceError := ethCli.DebugClient.Cached(false).RawCall(
		ctx,
		&results,
		"debug_traceCallMany",
		bundlesJSON,
		blockJSON,
		opt,
	)

	// Don't return err here since, it could be partially success
	if results != nil {
		for idx, result := range results[0] {
			if result == nil {
				continue
			}
			// For go-ethereum where no partial error
			if result.Failed != nil {
				traceError = fmt.Errorf("trace failed: %s", *result.Failed)
				break
			}

			sim := sims[idx]
			succeed := result.Error == "" && result.RevertReason == ""

			blockNumber := sims[0].BlockNumber
			if result.Receipt.BlockNumber != nil {
				blockNumber = *result.Receipt.BlockNumber
				// this will be used in call trace and state diff
			}
			sim.BlockNumber = blockNumber

			sim.ChainId = sims[0].ChainId
			sim.ChainSpec = sims[0].ChainSpec
			//if result.Receipt.TransactionIndex != nil {
			//	transactionIndex = "0x" + strconv.FormatInt(int64(*result.Receipt.TransactionIndex), 16)
			//}
			hash := ""
			if result.Receipt.TxHash != nil {
				hash = *result.Receipt.TxHash
			}
			blockHash := ""
			if result.Receipt.BlockHash != nil {
				blockHash = *result.Receipt.BlockHash
			}
			nonce := "0x0"
			if result.Receipt.Nonce != nil {
				n := int64(*result.Receipt.Nonce)
				if n < 0 { // can happen if it's a CREATE
					n = 0
				}
				nonce = "0x" + strconv.FormatInt(n, 16)
			}

			// https://docs.infura.io/infura/networks/ethereum/concepts/transaction-types
			txType := "0x1"
			effectiveGasPrice := sim.GasPrice
			if sim.MaxFeePerGas != "" || sim.MaxPriorityFeePerGas != "" {
				txType = "0x2"
				effectiveGasPrice = sim.MaxFeePerGas
			}

			transactionIdx := "0x" + strconv.FormatInt(int64(simModels[0].TransactionIndex+idx), 16)
			sim.TransactionIndex = transactionIdx

			sim.Result = &protos.Simulation_Result{
				Transaction: &evmprotos.Transaction{
					BlockNumber:          blockNumber,
					BlockHash:            blockHash,
					TransactionIndex:     transactionIdx,
					Hash:                 hash,
					ChainId:              sim.ChainId,
					Type:                 txType,
					From:                 sim.From,
					To:                   sim.To,
					Input:                sim.Input,
					Value:                sim.Value,
					Nonce:                nonce,
					Gas:                  sim.Gas,
					GasPrice:             sim.GasPrice,
					MaxFeePerGas:         sim.MaxFeePerGas,
					MaxPriorityFeePerGas: sim.MaxPriorityFeePerGas,
					AccessList:           sim.AccessList,
				},
				TransactionReceipt: &evmprotos.TransactionReceipt{
					GasUsed:           result.GasUsed,
					CumulativeGasUsed: result.GasUsed, // TODO
					EffectiveGasPrice: effectiveGasPrice,
					Status:            utils.Select(succeed, "0x1", "0x0"),
					Error:             result.Error,
					RevertReason:      result.RevertReason,
				},
			}
			if bundleID != "" {
				sim.BundleId = bundleID
			}
			if err = utils.ConvertByJSONMarshal(result.CollectLogs(), &sim.Result.TransactionReceipt.Logs); err != nil {
				return nil, fmt.Errorf("set transaction.logs failed: %w", err)
			}

			var simulation models.Simulation
			simulation.FromPB(sim, userID, projectID, actualStateOverridesStr)
			simulation.ID = ""
			if err = s.envRepository.DB.WithContext(ctx).Create(&simulation).Error; err != nil {
				return nil, fmt.Errorf("save simulation failed: %w", err)
			}
			sim = simulation.ToPB()

			res = append(res, sim)
		}
	}
	return res, traceError
}

const (
	DefaultPageSize = 10
	MaxPageSize     = 500
)

func (s *Service) GetSimulations(
	ctx context.Context,
	req *protos.GetSimulationsRequest,
) (*protos.GetSimulationsResponse, error) {
	userID, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		return nil, err
	}

	resp := &protos.GetSimulationsResponse{
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	if resp.Page < 1 {
		resp.Page = 1
	}
	if resp.PageSize <= 0 {
		resp.PageSize = DefaultPageSize
	}
	if resp.PageSize > MaxPageSize {
		return nil, status.Errorf(codes.InvalidArgument, "page_size cannot more than %d", MaxPageSize)
	}

	query := s.envRepository.DB.WithContext(ctx).Model(&models.Simulation{}).Where("project_id = ?", projectID)
	if projectID == "" {
		query = query.Where("user_id = ?", userID)
	}
	if req.LabelContains != nil {
		query = query.Where("label LIKE ?", "%"+*req.LabelContains+"%")
	}

	if err = query.Count(&resp.Count).Error; err != nil {
		return nil, fmt.Errorf("get count of simulations failed: %w", err)
	}

	var simulations []models.Simulation
	err = query.
		Preload("Share").
		Order("created_at DESC").
		Offset(int((resp.Page - 1) * resp.PageSize)).
		Limit(int(resp.PageSize)).
		Scan(&simulations).Error
	if err != nil {
		return nil, fmt.Errorf("list simulations failed: %w", err)
	}
	resp.Simulations = make([]*protos.Simulation, 0, len(simulations))
	for _, sim := range simulations {
		resp.Simulations = append(resp.Simulations, sim.ToPB())
	}
	return resp, nil
}

func (s *Service) GetSimulation(
	ctx context.Context,
	req *protos.GetSimulationRequest,
) (*protos.GetSimulationResponse, error) {
	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		return nil, err
	}

	query := s.envRepository.DB.WithContext(ctx).
		Model(&models.Simulation{}).
		Preload("Share").
		Where("id = ? AND (project_id = ? OR project_id = '')", req.SimulationId, projectID)
	var simulation models.Simulation
	err = query.Take(&simulation).Error
	if err != nil {
		return nil, err
	}
	return &protos.GetSimulationResponse{Simulation: simulation.ToPB()}, nil
}

func (s *Service) GetSimulationBundle(
	ctx context.Context,
	req *protos.GetSimulationBundleRequest,
) (*protos.GetSimulationBundleResponse, error) {
	_, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		return nil, err
	}

	var bundle models.Bundle

	err = s.envRepository.DB.WithContext(ctx).
		Model(&models.Bundle{}).
		Preload("Simulations").
		Where("id = ? AND (project_id = ? OR project_id = '')", req.BundleId, projectID).
		First(&bundle).Error
	if err != nil {
		return nil, err
	}
	var sims []*protos.Simulation
	for _, sim := range bundle.Simulations {
		sims = append(sims, sim.ToPB())
	}
	return &protos.GetSimulationBundleResponse{
		Error:       bundle.Error,
		Simulations: sims,
	}, nil
}

func (s *Service) SimulateDeployment(
	ctx context.Context,
	req *protos.SimulateDeploymentRequest,
) (*protos.SimulateDeploymentResponse, error) {
	logger := log.WithContext(ctx)

	userID, projectID, err := s.authProjectByOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	c := s.contractProcessor.UserCompilationByID(req.UserCompilationId)
	constructorCode, constructorArgs, err := c.GetCreationCode(ctx)
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	blockNumber := lo.FromPtr(req.BlockNumber)
	if blockNumber == "" {
		blockNumber = "latest"
	}
	from := "0x0000000000000000000000000000000000000000"
	sim := &protos.Simulation{
		NetworkId:        req.NetworkId,
		BundleId:         "",
		ChainId:          req.NetworkId,
		From:             from,
		To:               "",
		Value:            "0x0",
		Input:            "0x" + constructorCode + constructorArgs,
		BlockNumber:      blockNumber,
		TransactionIndex: "0x0",
		Gas:              "0xffffffffff",
		GasPrice:         "0xffffffffff",
		SourceOverrides: map[string]string{
			req.Address: req.UserCompilationId,
		},
		StateOverrides: map[string]*protos.StateOverride{
			from: {
				Balance: lo.ToPtr("0xffffffffffffffffffffffffffffffffffff"),
			},
		},
		DebugDeployment: true,
	}
	sims, err := s.simulate(ctx, userID, projectID, "", []*protos.Simulation{sim})
	if err != nil {
		logger.Errore(err)
		return nil, err
	}
	return &protos.SimulateDeploymentResponse{
		Simulation: sims[0],
	}, nil
}

// getSimulation
// TODO for now it has no auth, since requests from truffle debugger are missing access token
func (s *Service) getSimulation(ctx context.Context, simulationID string) (*protos.Simulation, error) {
	sim, err := s.getSimulationModel(ctx, simulationID)
	if err != nil {
		return nil, err
	}
	return sim.ToPB(), nil
}

func (s *Service) getSimulationModel(ctx context.Context, simulationID string) (*models.Simulation, error) {
	var simulation models.Simulation
	err := s.envRepository.DB.WithContext(ctx).
		Where("id = ?", simulationID).
		First(&simulation).Error
	if err != nil {
		return nil, err
	}
	return &simulation, nil
}

func (s *Service) getSharedSimulation(ctx context.Context, shareID string) (*models.ShareSimulation, error) {
	var share models.ShareSimulation
	err := s.envRepository.DB.WithContext(ctx).
		Where("id = ? AND public = true", shareID).
		First(&share).Error
	if err != nil {
		return nil, err
	}
	return &share, nil
}

//func (s *Service) getBundleModel(ctx context.Context, bundleID string) (*models.Bundle, error) {
//	var bundle models.Bundle
//	err := s.envRepository.DB.WithContext(ctx).
//		Where("id = ?", bundleID).
//		First(&bundle).Error
//	if err != nil {
//		return nil, err
//	}
//	return &bundle, nil
//}

func (s *Service) getBundleSimulations(ctx context.Context, bundleID string) ([]*models.Simulation, error) {
	var simulations []*models.Simulation
	err := s.envRepository.DB.WithContext(ctx).
		Where("bundle_id = ?", bundleID).
		Order("transaction_index ASC").
		Find(&simulations).Error
	if err != nil {
		return nil, err
	}
	return simulations, nil
}

func (s *Service) getBundleFirstSimulation(ctx context.Context, bundleID string) (*models.Simulation, error) {
	var simulation models.Simulation
	err := s.envRepository.DB.WithContext(ctx).
		Where("bundle_id = ?", bundleID).
		Order("transaction_index ASC").
		First(&simulation).Error
	if err != nil {
		return nil, err
	}
	return &simulation, nil
}
