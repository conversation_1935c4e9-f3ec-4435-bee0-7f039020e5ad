package models

import (
	"fmt"
	"strings"

	"github.com/samber/lo"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	StateDumpDir             = "/root/data"
	StateDumpIntervalSeconds = 300
	StorageRequirement       = "2Gi"
	Port                     = int32(8545)
	TestAccounts             = 10 // TODO do we need test accounts? if so maybe need to expose to users
)

func (s *ManagedFork) InstanceID() string {
	return "anvil-fork-" + strings.ToLower(s.ID)
}

func (s *ManagedFork) ServiceID() string {
	return s.InstanceID() + "-http"
}

func (s *ManagedFork) ToStatefulSetSpec(namespace string, parentRpcEndpoint string, anvilImage string) *appsv1.StatefulSet {
	storageRequirement, _ := resource.ParseQuantity(StorageRequirement)
	cmd := []string{
		"anvil",
		fmt.Sprintf("--fork-url=%s", parentRpcEndpoint),
		fmt.Sprintf("--fork-block-number=%d", s.ParentBlockNumber),
		fmt.Sprintf("--chain-id=%s", s.ChainID),
		fmt.Sprintf("--accounts=%d", TestAccounts),
		"--host=0.0.0.0",
		fmt.Sprintf("--port=%d", Port),
		fmt.Sprintf("--state=%s/anvil.state", StateDumpDir),
		fmt.Sprintf("--state-interval=%d", StateDumpIntervalSeconds),
		"--steps-tracing",
	}
	return &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s.InstanceID(),
			Namespace: namespace,
			Labels: map[string]string{
				"app.kubernetes.io/instance": s.InstanceID(),
				"app.kubernetes.io/name":     "anvil",
			},
		},
		Spec: appsv1.StatefulSetSpec{
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app.kubernetes.io/instance": s.InstanceID(),
					"app.kubernetes.io/name":     "anvil",
				},
			},
			Replicas: lo.ToPtr(int32(1)),
			VolumeClaimTemplates: []corev1.PersistentVolumeClaim{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name: "data",
						Labels: map[string]string{
							"app.kubernetes.io/instance": s.InstanceID(),
							"app.kubernetes.io/name":     "anvil",
						},
					},
					Spec: corev1.PersistentVolumeClaimSpec{
						AccessModes: []corev1.PersistentVolumeAccessMode{
							corev1.ReadWriteOnce,
						},
						StorageClassName: lo.ToPtr("standard"),
						Resources: corev1.VolumeResourceRequirements{
							Requests: corev1.ResourceList{
								corev1.ResourceStorage: storageRequirement,
							},
						},
					},
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app.kubernetes.io/instance": s.InstanceID(),
						"app.kubernetes.io/name":     "anvil",
					},
				},
				Spec: corev1.PodSpec{
					TerminationGracePeriodSeconds: lo.ToPtr(int64(30)),
					Containers: []corev1.Container{
						{
							Name:            "anvil",
							Image:           anvilImage,
							ImagePullPolicy: "IfNotPresent",
							Command:         cmd,
							Ports: []corev1.ContainerPort{
								{
									Name:          "port",
									Protocol:      corev1.ProtocolTCP,
									ContainerPort: Port,
								},
							},
							Env: []corev1.EnvVar{
								{
									Name:  "RUST_LOG",
									Value: "info",
								},
							},
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      "data",
									MountPath: StateDumpDir,
								},
							},
						},
					},
					Affinity: &corev1.Affinity{
						NodeAffinity: &corev1.NodeAffinity{
							PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{
								{
									Preference: corev1.NodeSelectorTerm{
										MatchExpressions: []corev1.NodeSelectorRequirement{
											{
												Key:      "node-size",
												Operator: corev1.NodeSelectorOpIn,
												Values:   []string{"small"},
											},
										},
									},
									Weight: 100,
								},
							},
						},
					},
					Tolerations: []corev1.Toleration{
						{
							Effect:   corev1.TaintEffectNoSchedule,
							Key:      "sentio.xyz/driver",
							Operator: corev1.TolerationOpEqual,
							Value:    "true",
						},
					},
				},
			},
		},
	}
}

func (s *ManagedFork) ToServiceSpec(namespace string) *corev1.Service {
	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s.ServiceID(),
			Namespace: namespace,
			Labels: map[string]string{
				"app.kubernetes.io/instance": s.InstanceID(),
			},
		},
		Spec: corev1.ServiceSpec{
			Type: corev1.ServiceTypeClusterIP,
			Selector: map[string]string{
				"app.kubernetes.io/instance": s.InstanceID(),
				"app.kubernetes.io/name":     "anvil",
			},
			Ports: []corev1.ServicePort{
				{
					Name:     "port",
					Protocol: corev1.ProtocolTCP,
					Port:     Port,
				},
			},
		},
	}
}
