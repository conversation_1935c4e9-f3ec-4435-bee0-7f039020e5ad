syntax = "proto3";

package RPCNode_service;

option go_package = "sentioxyz/sentio/service/rpcnode/protos";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "google/protobuf/struct.proto";
import "google/api/httpbody.proto";
import "service/common/protos/common.proto";
import "service/analytic/protos/analytic_service.proto";

import "google/protobuf/timestamp.proto";



service RPCNodeService {
  rpc SaveRPCNode(RPCNode) returns (RPCNodeResponse) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/RPCNode"
      body: "*"
    };
  };

  rpc GetRPCNodes(GetRPCNodesRequest) returns (RPCNodeResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/RPCNode/{project_id}/list"
    };
  };

  rpc GetRPCNode(GetRPCNodeRequest) returns (RPCNode) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/RPCNode/{id}"
    };
  };

  rpc GetRPCNodeByForkId(GetRPCNodeByForkRequest) returns (RPCNode) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/RPCNode/fork/{fork_id}"
    };
  };

  rpc DeleteRPCNode(DeleteRPCNodeRequest) returns (google.protobuf.Empty) {
    option (common.auth) = {
      permission: "project:write"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      delete: "/api/v1/RPCNode/{id}"
    };
  };

  rpc RPCNodeRequestLog(RPCNodeLogRequest) returns (RPCNodeLogResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      post: "/api/v1/RPCNode/{project_owner}/{project_slug}/logs"
      body: "*"
    };
  };

  rpc RPCNodeStats(RPCNodeStatsRequest) returns (RPCNodeStatsResponse) {
    option (common.auth) = {
      permission: "project:read"
    };
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/RPCNode/{id}/stats"
    };
  };

  rpc GetNetworks(google.protobuf.Empty) returns (RPCNetworkResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";

    option (google.api.http) = {
      get: "/api/v1/RPCNode/networks"
    };
  };

  rpc GetInternalRPCNode(GetInternalRPCNodeRequest) returns (GetInternalRPCNodeResponse) {
    option (google.api.method_visibility).restriction = "INTERNAL";
  };
}

message ProxyResponse {
  oneof response {
    analytic_service.QuerySQLResultResponse sql_query_result = 1;
    AsyncResponse async_response = 2;
    analytic_service.SyncExecuteSQLResponse sync_sql_response = 3;
  }
  message AsyncResponse {
    string query_id = 1;
    analytic_service.ExecutionInfo execution_info = 2;
    string result_url = 3;
    string result_id = 4;
  }
}

message GetRPCNodesRequest {
  string project_id = 1;
}

message GetRPCNodeRequest {
  string id = 3;
}

message GetRPCNodeByForkRequest {
  string fork_id = 3;
}

message DeleteRPCNodeRequest {
  string id = 1;
}

message GetInternalRPCNodeRequest {
  string processor_id = 1;
}

message GetInternalRPCNodeResponse {
  string url = 1;
  string rpc_node_id = 2;
}



message RPCNode {
  string id = 1;
  string code = 2;
  string network = 4; // Deprecated: use networks instead
  string project_id = 5;
  common.User creator = 6;
  bool enabled = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp last_called = 15;
  uint64 total_calls = 16;
  string fork_id = 17;
  string fork_node_url = 18;
  string type = 19; // "public", "internal"
  string processor_id = 20; // For internal nodes, links to a specific processor version
  repeated string networks = 21; // Whitelist of networks this node can proxy to. "*" means all networks
}

message RPCNodeResponse {
  repeated RPCNode rpc_nodes = 1;
}

message RPCNodeLogRequest {
  string rpc_node_id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  string project_owner = 4;
  string project_slug = 5;
  repeated Filter filters = 6;

  message Filter{
    oneof filter {
      string request_id = 1;
      int32 http_status_code = 2;
      string request_header_kv = 3; // key:value
      string response_header_kv = 4; // key:value
      string method = 5;
      int64 duration_gte = 6;
      int64 duration_lte = 7;
    }
  }
  string search = 7;
  Pagination pagination = 8;
  message Pagination {
    int64 offset = 1;
    int64 limit = 2;
  }
}

message RPCNodeLogResponse {
  repeated common.RequestLog logs = 1;
}

message RPCNodeStatsRequest {
  string id = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  uint64 step_seconds = 4;
}
message RPCNodeStatsResponse {
  uint64 total_requests = 1;
  uint64 total_errors = 2;
  Durations durations = 3;
  Durations query_durations = 4;
  message Durations {
    uint64 min = 1;
    uint64 max = 2;
    uint64 avg = 3;
    uint64 median = 4;
  }
  uint64 total_cost = 5;
  repeated TimeSeries time_series = 6;
  message TimeSeries {
    google.protobuf.Timestamp time = 1;
    uint64 avg_durations = 2;
    uint64 max_durations = 3;
    uint64 avg_query_durations = 4;
    uint64 max_query_durations = 5;
    uint64 success_count = 6;
    uint64 error_count = 7;
  }

}

message RPCNetworkResponse {
  repeated RPCNodeNetwork networks = 1;
}

message RPCNodeNetwork {
  string id = 1;
  string name = 2;
  repeated RPCEndpoint endpoints = 3;
}
message RPCEndpoint {
  string path = 1;
  string name = 2;
}

