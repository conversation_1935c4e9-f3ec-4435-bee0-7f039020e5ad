package rpcnode

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http/httputil"
	"net/url"
	"sentioxyz/sentio/common/gonanoid"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/utils"
	commonModels "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/common/preloader"
	commonProtos "sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/common/redis"
	"sentioxyz/sentio/service/rpcnode/model"
	"sentioxyz/sentio/service/rpcnode/protos"
	"sentioxyz/sentio/service/rpcnode/repository"
	usageUtils "sentioxyz/sentio/service/usage/utils"

	"github.com/ClickHouse/clickhouse-go/v2"
	redisrate "github.com/go-redis/redis_rate/v10"
	goRedis "github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"time"
)

type Service struct {
	protos.UnimplementedRPCNodeServiceServer
	repo        *repository.RPCNodeRepository
	settings    model.RPCSettings
	chConn      clickhouse.Conn
	proxies     map[string]*httputil.ReverseProxy
	releaseName string
	usageClient *usageUtils.Client
	limiter     *redisrate.Limiter
	redisCli    *goRedis.Client
}

func NewService(conn *gorm.DB, chConn clickhouse.Conn, settings model.RPCSettings, releaseName string, usageClient *usageUtils.Client) *Service {
	repo := repository.NewRepository(conn)
	redisCli := redis.NewClientWithDefaultOptions()
	limiter := redisrate.NewLimiter(redisCli)

	s := &Service{
		repo:        repo,
		settings:    settings,
		chConn:      chConn,
		proxies:     make(map[string]*httputil.ReverseProxy),
		releaseName: releaseName,
		usageClient: usageClient,
		limiter:     limiter,
		redisCli:    redisCli,
	}
	for _, n := range settings.Networks {
		for _, ep := range n.Endpoints {
			proxy, err := s.createProxy(ep.Upstream)
			if err != nil {
				log.Fatalfe(err, "failed to parse upstream url", "url", ep.Upstream)
			}
			s.proxies[fmt.Sprintf("%s/%s", n.ID, ep.Path)] = proxy
		}
	}

	return s
}

func (e *Service) createProxy(targetUrl string) (*httputil.ReverseProxy, error) {
	u, err := url.Parse(targetUrl)
	if err != nil {
		return nil, err
	}
	proxy := httputil.NewSingleHostReverseProxy(u)
	proxy.ModifyResponse = e.logResponse
	proxy.Director = e.Director
	return proxy, nil
}

func (e *Service) SaveRPCNode(ctx context.Context, req *protos.RPCNode) (*protos.RPCNodeResponse, error) {
	identity := preloader.PreLoadedIdentity(ctx)
	project, err := preloader.PreLoadedProjectByID(ctx, e.repo.DB, req.ProjectId)
	if err != nil || project == nil {
		return nil, status.Errorf(codes.NotFound, "Project not found: %v", err)
	}
	var node *model.RPCNode
	if len(req.Id) > 0 {
		// update
		node, err = e.repo.FindRPCNodeByID(ctx, req.Id)
		if err != nil {
			return nil, err
		}
		node.FromProto(req)
	} else {
		// create new
		node = &model.RPCNode{}
		node.FromProto(req)
		node.ID = gonanoid.Must(8)
		node.Code = gonanoid.GenerateWithAlphabet(gonanoid.IDAlphabetWithoutUnderline, 32)
	}

	node.ProjectID = project.ID
	node.CreatorID = identity.UserID

	err = e.repo.DB.Save(node).Error
	if err != nil {
		return nil, err
	}
	return &protos.RPCNodeResponse{RpcNodes: []*protos.RPCNode{node.ToProto()}}, nil
}

func (e *Service) GetRPCNode(ctx context.Context, req *protos.GetRPCNodeRequest) (*protos.RPCNode, error) {
	node, err := e.repo.FindRPCNodeByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if node == nil {
		return nil, status.Errorf(codes.NotFound, "RPCNode not found")
	}

	return node.ToProto(), nil
}

func (e *Service) GetRPCNodeByForkId(ctx context.Context, req *protos.GetRPCNodeByForkRequest) (*protos.RPCNode, error) {
	node, err := e.repo.FindRPCNodeByForkID(ctx, req.ForkId)
	if err != nil {
		return nil, err
	}

	return node.ToProto(), nil
}

func (e *Service) GetRPCNodes(ctx context.Context, req *protos.GetRPCNodesRequest) (*protos.RPCNodeResponse, error) {
	project, err := preloader.PreLoadedProjectByID(ctx, e.repo.DB, req.ProjectId)
	if err != nil || project == nil {
		return nil, status.Errorf(codes.NotFound, "Project not found: %v", err)
	}

	nodes, err := e.repo.FindRPCNodes(ctx, project.ID)
	if err != nil {
		return nil, err
	}
	var res []*protos.RPCNode
	simpleStats, err := e.repo.SimpleRPCNodeStats(ctx, e.chConn, utils.MapSliceNoError(nodes, func(ep *model.RPCNode) string { return ep.ID }))
	if err != nil {
		return nil, err
	}
	for _, ep := range nodes {
		proto := ep.ToProto()
		// Hide code for internal RPC nodes
		if ep.Type == "internal" {
			proto.Code = ""
		}
		res = append(res, proto)
		if stats, ok := simpleStats[ep.ID]; ok {
			proto.LastCalled = timestamppb.New(stats.LastCall)
			proto.TotalCalls = stats.Count
		}
	}
	return &protos.RPCNodeResponse{RpcNodes: res}, nil
}

func (e *Service) DeleteRPCNode(ctx context.Context, req *protos.DeleteRPCNodeRequest) (*emptypb.Empty, error) {
	node, err := e.repo.FindRPCNodeByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	if node == nil {
		return nil, status.Errorf(codes.NotFound, "RPCNode not found")
	}

	err = e.repo.DB.Delete(node).Error
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (e *Service) RPCNodeRequestLog(ctx context.Context, req *protos.RPCNodeLogRequest) (*protos.RPCNodeLogResponse, error) {
	project, err := preloader.PreLoadedProjectBySlug(ctx, e.repo.DB, req.ProjectOwner, req.ProjectSlug)
	if err != nil || project == nil {
		return nil, status.Errorf(codes.NotFound, "Project not found: %v", err)
	}
	if len(req.RpcNodeId) > 0 {
		ep, err := e.repo.FindRPCNodeByID(ctx, req.RpcNodeId)
		if err != nil {
			return nil, err
		}
		if ep == nil {
			return nil, status.Errorf(codes.NotFound, "RPCNode not found")
		}
		if ep.ProjectID != project.ID {
			return nil, status.Errorf(codes.PermissionDenied, "Permission denied")
		}
	}
	if req.StartTime == nil {
		// default to 30 days ago
		req.StartTime = timestamppb.New(time.Now().AddDate(0, 0, -30))
	}
	if req.EndTime == nil {
		req.EndTime = timestamppb.New(time.Now())
	}

	logs, err := e.repo.FindRPCNodeLogs(ctx, e.chConn, e.releaseName, project.ID, req)
	if err != nil {
		return nil, err
	}
	response := &protos.RPCNodeLogResponse{
		Logs: utils.MapSliceNoError(logs, func(l *commonModels.RequestLog) *commonProtos.RequestLog {
			return l.ToPB()
		}),
	}
	return response, nil
}

func (e *Service) RPCNodeStats(ctx context.Context, req *protos.RPCNodeStatsRequest) (*protos.RPCNodeStatsResponse, error) {
	ep, err := e.repo.FindRPCNodeByID(ctx, req.Id)
	if err != nil {
		return nil, err
	}

	if ep == nil {
		// possible it is an action RPCNode
	}

	if req.StartTime == nil {
		// default to 30 days ago
		req.StartTime = timestamppb.New(time.Now().AddDate(0, 0, -30))
	}
	if req.EndTime == nil {
		req.EndTime = timestamppb.New(time.Now())
	}
	stats, err := e.repo.GetRPCNodeStats(ctx, e.chConn, e.releaseName, req)
	if err != nil {
		return nil, err
	}

	if req.StepSeconds > 0 {
		timeseries, err := e.repo.GetRPCNodeTimeseries(ctx, e.chConn, e.releaseName, req)
		if err != nil {
			return nil, err
		}
		stats.TimeSeries = timeseries
	}

	return stats, nil
}

func (e *Service) GetNetworks(context.Context, *emptypb.Empty) (*protos.RPCNetworkResponse, error) {
	return e.settings.ToProto(), nil
}

// computeProcessorCode generates a deterministic code based on the internal key and processor ID
func (e *Service) computeProcessorCode(processorID string) string {
	if e.settings.InternalKey == "" {
		return "default"
	}
	hash := sha256.Sum256([]byte(e.settings.InternalKey + processorID))
	return hex.EncodeToString(hash[:])[:16] // Use first 16 chars of the hash
}

func (e *Service) GetInternalRPCNode(ctx context.Context, req *protos.GetInternalRPCNodeRequest) (*protos.GetInternalRPCNodeResponse, error) {
	if req.ProcessorId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "processor_id is required")
	}

	processor, err := e.repo.GetProcessor(ctx, req.ProcessorId)
	if err != nil || processor == nil {
		return nil, status.Errorf(codes.NotFound, "Processor not found: %v", err)
	}

	projectID := processor.ProjectID

	existingNode, err := e.repo.FindInternalRPCNodeByProjectID(ctx, projectID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to query internal RPC node: %v", err)
	}

	var rpcNode *model.RPCNode
	if existingNode != nil {
		rpcNode = existingNode
	} else {
		rpcNode = &model.RPCNode{
			ID:        gonanoid.Must(8),
			Code:      "",
			ProjectID: projectID,
			Type:      "internal",
			Networks:  []string{"*"}, // Internal nodes can access all networks
			Enabled:   true,
		}

		// Save the new internal RPC node
		err = e.repo.DB.Save(rpcNode).Error
		if err != nil {
			return nil, status.Errorf(codes.Internal, "Failed to create internal RPC node: %v", err)
		}
	}

	if e.settings.InternalURL == "" {
		return nil, status.Errorf(codes.Internal, "Internal domain not configured")
	}

	computedCode := e.computeProcessorCode(req.ProcessorId)
	rpcURL := fmt.Sprintf("https://%s/%s/%s/universal",
		e.settings.InternalURL,
		req.ProcessorId,
		computedCode)

	return &protos.GetInternalRPCNodeResponse{
		Url:       rpcURL,
		RpcNodeId: rpcNode.ID,
	}, nil
}
