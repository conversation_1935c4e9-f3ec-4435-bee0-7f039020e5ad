package rpcnode

import (
	"net/http/httputil"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gorm.io/datatypes"

	"sentioxyz/sentio/chain/evm/chaininfo"
	"sentioxyz/sentio/service/rpcnode/model"
)

// NewTestService creates a test service instance
func NewTestService(settings model.RPCSettings) *Service {
	return &Service{
		settings: settings,
		proxies:  make(map[string]*httputil.ReverseProxy),
	}
}

func TestService_getRPCProxy(t *testing.T) {
	tests := []struct {
		name            string
		settings        model.RPCSettings
		node            *model.RPCNode
		slug            string
		expectedNetwork string
		expectedPath    string
		expectedError   codes.Code
		setupChainInfo  func()
	}{
		{
			name: "settings network lookup - exact path match",
			settings: model.RPCSettings{
				DefaultUpstream: "https://rpc.example.com",
				Networks: []model.RPCNetwork{
					{
						ID:   "1",
						Name: "Ethereum",
						Endpoints: []model.RPCEndpoint{
							{
								Path:        "/mainnet",
								Name:        "Ethereum Mainnet",
								Upstream:    "https://eth.example.com/mainnet",
								RateLimitID: "default",
							},
						},
					},
				},
			},
			node: &model.RPCNode{
				ID:       "test-node",
				Networks: datatypes.JSONSlice[string]{"1"},
			},
			slug:            "mainnet",
			expectedNetwork: "1",
			expectedPath:    "/mainnet",
		},
		{
			name: "chaininfo lookup - ethereum mainnet",
			settings: model.RPCSettings{
				DefaultUpstream: "https://rpc.example.com",
				Networks: []model.RPCNetwork{
					{
						ID:   "1",
						Name: "Ethereum",
						Endpoints: []model.RPCEndpoint{
							{
								Path:        "/mainnet",
								Name:        "Ethereum Mainnet",
								Upstream:    "https://eth.example.com/mainnet",
								RateLimitID: "default",
							},
						},
					},
				},
			},
			node: &model.RPCNode{
				ID:       "test-node",
				Networks: datatypes.JSONSlice[string]{"1"},
			},
			slug:            "mainnet",
			expectedNetwork: "1",
			expectedPath:    "/mainnet",
			setupChainInfo: func() {
				chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
					"mainnet": {
						ChainID: "1",
						Name:    "Ethereum",
						Slug:    "mainnet",
					},
				}
			},
		},
		{
			name: "chaininfo lookup - optimism",
			settings: model.RPCSettings{
				DefaultUpstream: "https://rpc.example.com",
				Networks:        []model.RPCNetwork{},
			},
			node: &model.RPCNode{
				ID:       "test-node",
				Networks: datatypes.JSONSlice[string]{"10"},
			},
			slug:            "optimism",
			expectedNetwork: "10",
			expectedPath:    "/optimism",
			setupChainInfo: func() {
				chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
					"optimism": {
						ChainID: "10",
						Name:    "Optimism",
						Slug:    "optimism",
					},
				}
			},
		},
		{
			name: "internal node - allows all networks",
			settings: model.RPCSettings{
				DefaultUpstream: "https://rpc.example.com",
				Networks:        []model.RPCNetwork{},
			},
			node: &model.RPCNode{
				ID:       "internal-node",
				Type:     "internal",
				Networks: datatypes.JSONSlice[string]{"*"},
			},
			slug:            "mainnet",
			expectedNetwork: "1",
			expectedPath:    "/mainnet",
			setupChainInfo: func() {
				chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
					"mainnet": {
						ChainID: "1",
						Name:    "Ethereum",
						Slug:    "mainnet",
					},
				}
			},
		},
		{
			name: "network not allowed",
			settings: model.RPCSettings{
				DefaultUpstream: "https://rpc.example.com",
				Networks:        []model.RPCNetwork{},
			},
			node: &model.RPCNode{
				ID:       "restricted-node",
				Networks: datatypes.JSONSlice[string]{"10"}, // Only Optimism allowed
			},
			slug:            "mainnet", // Trying to access Ethereum
			expectedError:   codes.PermissionDenied,
			setupChainInfo: func() {
				chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
					"mainnet": {
						ChainID: "1",
						Name:    "Ethereum",
						Slug:    "mainnet",
					},
				}
			},
		},
		{
			name: "unknown network slug",
			settings: model.RPCSettings{
				DefaultUpstream: "https://rpc.example.com",
				Networks:        []model.RPCNetwork{},
			},
			node: &model.RPCNode{
				ID:       "test-node",
				Networks: datatypes.JSONSlice[string]{"*"},
			},
			slug:          "unknown-chain",
			expectedError: codes.NotFound,
			setupChainInfo: func() {
				chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{}
			},
		},
		{
			name: "empty slug",
			settings: model.RPCSettings{
				DefaultUpstream: "https://rpc.example.com",
			},
			node: &model.RPCNode{
				ID:       "test-node",
				Networks: datatypes.JSONSlice[string]{"*"},
			},
			slug:          "",
			expectedError: codes.NotFound,
			setupChainInfo: func() {
				chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{}
			},
		},
		{
			name: "default upstream fallback",
			settings: model.RPCSettings{
				DefaultUpstream: "https://default.example.com",
				Networks:        []model.RPCNetwork{},
			},
			node: &model.RPCNode{
				ID:       "test-node",
				Networks: datatypes.JSONSlice[string]{"1"},
			},
			slug:            "mainnet",
			expectedNetwork: "1",
			expectedPath:    "/mainnet",
			setupChainInfo: func() {
				chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
					"mainnet": {
						ChainID: "1",
						Name:    "Ethereum",
						Slug:    "mainnet",
					},
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup chaininfo if needed
			if tt.setupChainInfo != nil {
				tt.setupChainInfo()
			}

			// Create service with test settings
			service := NewTestService(tt.settings)

			// Call GetRPCProxy
			network, rpcEndpoint, proxy, err := service.GetRPCProxy(tt.node, tt.slug)

			if tt.expectedError != codes.OK {
				require.Error(t, err)
				st, ok := status.FromError(err)
				require.True(t, ok)
				assert.Equal(t, tt.expectedError, st.Code())
				return
			}

			// Verify success case
			require.NoError(t, err)
			require.NotNil(t, network)
			require.NotNil(t, rpcEndpoint)
			require.NotNil(t, proxy)

			assert.Equal(t, tt.expectedNetwork, network.ID)
			assert.Equal(t, tt.expectedPath, rpcEndpoint.Path)
		})
	}
}

func TestRPCNode_IsNetworkAllowed(t *testing.T) {
	tests := []struct {
		name      string
		node      *model.RPCNode
		networkID string
		expected  bool
	}{
		{
			name: "internal node allows all",
			node: &model.RPCNode{
				Type:     "internal",
				Networks: datatypes.JSONSlice[string]{"*"},
			},
			networkID: "1",
			expected:  true,
		},
		{
			name: "wildcard allows all",
			node: &model.RPCNode{
				Type:     "public",
				Networks: datatypes.JSONSlice[string]{"*"},
			},
			networkID: "1",
			expected:  true,
		},
		{
			name: "specific network allowed",
			node: &model.RPCNode{
				Networks: datatypes.JSONSlice[string]{"1", "10"},
			},
			networkID: "1",
			expected:  true,
		},
		{
			name: "specific network not allowed",
			node: &model.RPCNode{
				Networks: datatypes.JSONSlice[string]{"10"},
			},
			networkID: "1",
			expected:  false,
		},
		{
			name: "empty networks array - backward compatibility with Network field",
			node: &model.RPCNode{
				Network:  "1",
				Networks: datatypes.JSONSlice[string]{},
			},
			networkID: "1",
			expected:  true,
		},
		{
			name: "empty networks array - Network field mismatch",
			node: &model.RPCNode{
				Network:  "10",
				Networks: datatypes.JSONSlice[string]{},
			},
			networkID: "1",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.node.IsNetworkAllowed(tt.networkID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestService_getRPCProxy_PathMatching(t *testing.T) {
	service := NewTestService(model.RPCSettings{
		DefaultUpstream: "https://default.example.com",
		Networks: []model.RPCNetwork{
			{
				ID:   "1",
				Name: "Ethereum",
				Endpoints: []model.RPCEndpoint{
					{
						Path:        "/mainnet",
						Name:        "Ethereum Mainnet",
						Upstream:    "https://eth.example.com/mainnet",
						RateLimitID: "default",
					},
					{
						Path:        "/",
						Name:        "Universal",
						Upstream:    "https://eth.example.com/",
						RateLimitID: "default",
					},
				},
			},
		},
	})

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
	}

	tests := []struct {
		name         string
		slug         string
		expectedPath string
	}{
		{
			name:         "exact path match",
			slug:         "mainnet",
			expectedPath: "/mainnet",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			network, rpcEndpoint, proxy, err := service.GetRPCProxy(node, tt.slug)

			require.NoError(t, err)
			require.NotNil(t, network)
			require.NotNil(t, rpcEndpoint)
			require.NotNil(t, proxy)

			assert.Equal(t, tt.expectedPath, rpcEndpoint.Path)
		})
	}
}

func TestService_createProxyKey(t *testing.T) {
	service := NewTestService(model.RPCSettings{
		DefaultUpstream: "https://default.example.com",
	})

	// Setup chaininfo
	chaininfo.SlugToInfo = map[string]*chaininfo.EthChainInfo{
		"mainnet": {
			ChainID: "1",
			Name:    "Ethereum",
			Slug:    "mainnet",
		},
	}

	node := &model.RPCNode{
		ID:       "test-node",
		Networks: datatypes.JSONSlice[string]{"1"},
	}

	// First call should create proxy
	_, _, proxy1, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)
	require.NotNil(t, proxy1)

	// Second call should reuse the same proxy
	_, _, proxy2, err := service.GetRPCProxy(node, "mainnet")
	require.NoError(t, err)
	require.NotNil(t, proxy2)

	// Should be the same proxy instance (cached)
	assert.Equal(t, proxy1, proxy2)
}