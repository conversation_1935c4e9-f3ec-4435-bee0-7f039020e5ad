package model

import (
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/rpcnode/protos"

	"gopkg.in/yaml.v3"
)

type RPCSettings struct {
	Networks        []RPCNetwork         `yaml:"networks"`
	RateLimits      map[string]RateLimit `yaml:"rate_limits,omitempty"`
	InternalURL     string               `yaml:"internal_url,omitempty"`
	InternalKey     string               `yaml:"internal_key,omitempty"`
	DefaultUpstream string               `yaml:"default_upstream,omitempty"`
}

type RPCNetwork struct {
	ID        string        `yaml:"id"`
	Name      string        `yaml:"name"`
	Endpoints []RPCEndpoint `yaml:"endpoints,omitempty"`
}

type RPCEndpoint struct {
	Path        string `yaml:"path"`
	Name        string `yaml:"name"`
	Upstream    string `yaml:"upstream,omitempty"`
	RateLimitID string `yaml:"rate_limit,omitempty"`
}

type RateLimit struct {
	Free       Limit `yaml:"free"`
	Dev        Limit `yaml:"dev"`
	Pro        Limit `yaml:"pro"`
	Enterprise Limit `yaml:"enterprise"`
}

type Limit struct {
	Rate   int    `yaml:"rate"`
	Burst  int    `yaml:"burst"`
	Period string `yaml:"period"`
}

func LoadSettings(data []byte) (RPCSettings, error) {
	var settings RPCSettings
	err := yaml.Unmarshal(data, &settings)
	if err != nil {
		return RPCSettings{}, err
	}
	return settings, nil
}

func (s *RPCSettings) ToProto() *protos.RPCNetworkResponse {
	ret := protos.RPCNetworkResponse{
		Networks: make([]*protos.RPCNodeNetwork, 0),
	}
	for _, n := range s.Networks {
		ret.Networks = append(ret.Networks, &protos.RPCNodeNetwork{
			Id:   n.ID,
			Name: n.Name,
			Endpoints: utils.MapSliceNoError(n.Endpoints, func(e RPCEndpoint) *protos.RPCEndpoint {
				return &protos.RPCEndpoint{
					Path: e.Path,
					Name: e.Name,
				}
			}),
		})
	}
	return &ret
}
