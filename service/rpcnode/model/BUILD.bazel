load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "model",
    srcs = [
        "rpc_node.go",
        "rpc_settings.go",
    ],
    importpath = "sentioxyz/sentio/service/rpcnode/model",
    visibility = ["//visibility:public"],
    deps = [
        "//common/utils",
        "//service/common/models",
        "//service/rpcnode/protos",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@io_gorm_datatypes//:datatypes",
        "@org_golang_google_protobuf//types/known/timestamppb",
    ],
)
