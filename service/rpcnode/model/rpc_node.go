package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"

	"sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/rpcnode/protos"
)

type RPCNode struct {
	ID          string `gorm:"primarykey"`
	Code        string `gorm:"unique"`
	Network     string                   // Deprecated: use Networks instead
	Networks    datatypes.JSONSlice[string] // Whitelist of networks this node can proxy to
	CreatedAt   time.Time
	UpdatedAt   time.Time
	ProjectID   string
	Project     *models.Project `gorm:"constraint:OnDelete:CASCADE;"`
	CreatorID   string
	Creator     *models.User `gorm:"constraint:OnDelete:CASCADE;"`
	Enabled     bool
	ForkID      string `gorm:"index"`
	ForkNodeUrl string
	Type        string `gorm:"default:'public'"` // "public", "internal"
}

func (n *RPCNode) FromProto(req *protos.RPCNode) {
	// Backward compatibility: if networks is provided, use it; otherwise use network
	if len(req.Networks) > 0 {
		n.Networks = req.Networks
	} else if req.Network != "" {
		n.Network = req.Network
		n.Networks = []string{req.Network} // Migrate single network to networks array
	}
	n.Enabled = req.Enabled
	n.ForkID = req.ForkId
	if req.ForkNodeUrl != "" {
		n.ForkNodeUrl = req.ForkNodeUrl
	}
	if req.Type != "" {
		n.Type = req.Type
	}
}

func (n *RPCNode) ToProto() *protos.RPCNode {
	// Backward compatibility: if Networks is empty but Network is set, use Network
	networks := n.Networks
	if len(networks) == 0 && n.Network != "" {
		networks = []string{n.Network}
	}
	
	c := &protos.RPCNode{
		Id:        n.ID,
		Code:      n.Code,
		Network:   n.Network, // Keep for backward compatibility
		Networks:  networks,
		ProjectId: n.ProjectID,
		Enabled:   n.Enabled,
		CreatedAt: timestamppb.New(n.CreatedAt),
		UpdatedAt: timestamppb.New(n.UpdatedAt),
		ForkId:    n.ForkID,
		Type:      n.Type,
	}
	if n.Creator != nil {
		c.Creator = n.Creator.ToPB()
	}

	return c
}

// IsNetworkAllowed checks if the given network is allowed for this RPC node
func (n *RPCNode) IsNetworkAllowed(networkID string) bool {
	// Internal nodes allow all networks
	if n.Type == "internal" {
		return true
	}
	
	// Get effective networks list
	networks := n.Networks
	if len(networks) == 0 && n.Network != "" {
		networks = []string{n.Network}
	}
	
	// Check if "*" is in the list (allows all networks)
	for _, net := range networks {
		if net == "*" {
			return true
		}
		if net == networkID {
			return true
		}
	}
	
	return false
}

// GetEffectiveNetworks returns the effective networks list for this RPC node
func (n *RPCNode) GetEffectiveNetworks() []string {
	// Internal nodes allow all networks
	if n.Type == "internal" {
		return []string{"*"}
	}
	
	// Use Networks if available, otherwise fallback to Network
	if len(n.Networks) > 0 {
		return n.Networks
	}
	if n.Network != "" {
		return []string{n.Network}
	}
	
	return []string{}
}
