package repository

import (
	"errors"
	"fmt"
	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/huandu/go-sqlbuilder"
	"golang.org/x/net/context"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"sentioxyz/sentio/common/log"
	commonModel "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/common/repository"
	"sentioxyz/sentio/service/rpcnode/model"
	"sentioxyz/sentio/service/rpcnode/protos"
	"strconv"
	"strings"
	"time"
)

type RPCNodeRepository struct {
	repository.Repository
}

func NewRepository(db *gorm.DB) *RPCNodeRepository {
	repo := repository.NewRepository(db)
	return &RPCNodeRepository{Repository: repo}
}

func (r *RPCNodeRepository) FindRPCNodeByID(ctx context.Context, id string) (*model.RPCNode, error) {
	db := r.DB.WithContext(ctx)
	RPCNode := model.RPCNode{}
	err := db.Preload("Creator").Preload("Project").Where("id = ?", id).First(&RPCNode).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	RPCNode.Project.GetOwner(db)
	return &RPCNode, err
}

func (r *RPCNodeRepository) FindRPCNodeByForkID(ctx context.Context, forkID string) (*model.RPCNode, error) {
	db := r.DB.WithContext(ctx)
	RPCNode := model.RPCNode{}
	err := db.Preload("Creator").Preload("Project").Where("fork_id = ?", forkID).First(&RPCNode).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	RPCNode.Project.GetOwner(db)
	return &RPCNode, err
}

func (r *RPCNodeRepository) FindRPCNodeByCode(ctx context.Context, code string) (*model.RPCNode, error) {
	db := r.DB.WithContext(ctx)
	node := model.RPCNode{}
	query := db.
		Preload("Creator").Preload("Project").Where("code = ?", code).First(&node)

	if errors.Is(query.Error, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	node.Project.GetOwner(db)
	return &node, nil
}

func (r *RPCNodeRepository) FindRPCNodes(ctx context.Context, projectID string) ([]*model.RPCNode, error) {
	db := r.DB.WithContext(ctx)
	var RPCNodes []*model.RPCNode
	err := db.Preload("Creator").Preload("Project").Where("project_id = ?", projectID).Find(&RPCNodes).Error
	return RPCNodes, err
}

func (r *RPCNodeRepository) FindInternalRPCNodeByProjectID(ctx context.Context, projectID string) (*model.RPCNode, error) {
	db := r.DB.WithContext(ctx)
	node := model.RPCNode{}
	err := db.Preload("Creator").Preload("Project").
		Where("project_id = ? AND type = ?", projectID, "internal").
		First(&node).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	if node.Project != nil {
		node.Project.GetOwner(db)
	}
	return &node, nil
}

func (r *RPCNodeRepository) FindRPCNodeLogs(ctx context.Context, conn clickhouse.Conn, env, projectID string, req *protos.RPCNodeLogRequest) ([]*commonModel.RequestLog, error) {
	q := sqlbuilder.Select("Timestamp as CreatedAt",
		"LogAttributes['request_id'] as ID",
		"LogAttributes['rpc_node_id'] as RPCNodeID",
		"LogAttributes['project_id'] as ProjectID",
		"toUInt32OrZero(LogAttributes['status_code']) as StatusCode",
		"LogAttributes['request_header'] as RequestHeader",
		"LogAttributes['response_header'] as ResponseHeader",
		"LogAttributes['request_body'] as RequestBody",
		"LogAttributes['response_body'] as ResponseBody",
		"toUInt64OrZero(LogAttributes['query_time']) as QueryTime",
		"toUInt64OrZero(LogAttributes['duration']) as Duration",
		"LogAttributes['url'] as URL",
		"LogAttributes['http_method'] as Method").From("otel_logs")
	q.Where(q.Equal("ServiceName", "rpcnode-server"),
		q.Equal("Body", "RPCNode Request Log"),
		q.Equal("ResourceAttributes['deployment.environment']", env),
	)

	q.Where(q.Between("TimestampTime", req.StartTime.AsTime().Format("2006-01-02 15:04:05"), req.EndTime.AsTime().Format("2006-01-02 15:04:05")))

	if projectID != "" {
		q = q.Where(q.Equal("LogAttributes['project_id']", projectID))
	}
	if req.RpcNodeId != "" {
		q = q.Where(q.Equal("LogAttributes['rpc_node_id']", req.RpcNodeId))
	}

	for _, filter := range req.Filters {
		switch filter.Filter.(type) {
		case *protos.RPCNodeLogRequest_Filter_HttpStatusCode:
			q = q.Where(q.Equal("LogAttributes['status_code']", strconv.Itoa(int(filter.GetHttpStatusCode()))))
		case *protos.RPCNodeLogRequest_Filter_RequestId:
			q = q.Where(q.Equal("LogAttributes['request_id']", filter.GetRequestId()))

		case *protos.RPCNodeLogRequest_Filter_Method:
			q = q.Where(q.Equal("LogAttributes['http_method']", filter.GetMethod()))
		case *protos.RPCNodeLogRequest_Filter_DurationLte:
			q = q.Where(q.LE("toUInt64(LogAttributes['duration'])", filter.GetDurationLte()))
		case *protos.RPCNodeLogRequest_Filter_DurationGte:
			q = q.Where(q.GE("toUInt64(LogAttributes['duration'])", filter.GetDurationGte()))
		case *protos.RPCNodeLogRequest_Filter_RequestHeaderKv:
			if k, v, found := strings.Cut(filter.GetRequestHeaderKv(), ":"); found {
				q = q.Where(q.Like(fmt.Sprintf(`simpleJSONExtractRaw(LogAttributes['request_header'], '%s')`, k), fmt.Sprintf("%%%s%%", v)))
			}
		case *protos.RPCNodeLogRequest_Filter_ResponseHeaderKv:
			if k, v, found := strings.Cut(filter.GetResponseHeaderKv(), ":"); found {
				q = q.Where(q.Like(fmt.Sprintf(`simpleJSONExtractRaw(LogAttributes['response_header'], '%s')`, k), fmt.Sprintf("%%%s%%", v)))
			}
		}

	}
	if len(req.Search) > 0 {
		q = q.Where(q.Or(
			q.Like("LogAttributes['request_body']", "%"+req.Search+"%"),
			q.Like("LogAttributes['response_body']", "%"+req.Search+"%"),
			q.Like("LogAttributes['request_header']", "%"+req.Search+"%"),
			q.Like("LogAttributes['response_header']", "%"+req.Search+"%"),
			q.Like("LogAttributes['url']", "%"+req.Search+"%"),
			q.Like("LogAttributes['rpc_node_id']", "%"+req.Search+"%"),
			q.Like("LogAttributes['request_id']", "%"+req.Search+"%"),
		))
	}
	q = q.OrderBy("Timestamp DESC")
	if req.Pagination != nil {
		var limit, offset int64 = 100, 0
		if req.Pagination.Limit > 0 {
			limit = req.Pagination.Limit
		}
		if req.Pagination.Offset > 0 {
			offset = req.Pagination.Offset
		}
		q = q.Limit(int(limit)).Offset(int(offset))
	}
	sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
	if err != nil {
		return nil, err
	}
	rows, err := conn.Query(ctx, sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var logs []*commonModel.RequestLog
	for rows.Next() {
		requestLog := commonModel.RequestLog{}
		err = rows.ScanStruct(&requestLog)
		if err != nil {
			return nil, err
		}
		logs = append(logs, &requestLog)
	}
	return logs, nil
}

type SimpleStats struct {
	Count    uint64
	LastCall time.Time
}

func (r *RPCNodeRepository) SimpleRPCNodeStats(ctx context.Context, conn clickhouse.Conn, ids []string) (map[string]SimpleStats, error) {
	ret := make(map[string]SimpleStats)
	if len(ids) > 0 {
		sq := sqlbuilder.Select(
			"Timestamp",
			"LogAttributes['rpc_node_id'] as RPCNode_id",
			"toUInt64OrZero(LogAttributes['status_code']) as status_code",
		).From("otel_logs")
		var args []interface{}
		for _, id := range ids {
			args = append(args, id)
		}
		sq.Where(
			sq.Equal("ServiceName", "rpcnode-server"),
			sq.Equal("Body", "RPCNode Request Log"),
			"TimestampTime >= toStartOfMonth(now())",
			sq.In("LogAttributes['rpc_node_id']", args...),
		)
		subsql, err := sqlbuilder.ClickHouse.Interpolate(sq.Build())
		if err != nil {
			return nil, err
		}
		q := sqlbuilder.Select(
			"RPCNode_id",
			"sum(if(status_code>=200 and status_code <300, 1, 0)) as success",
			"max(Timestamp) as last_call",
		).From(fmt.Sprintf("(%s) x", subsql))
		q.GroupBy("RPCNode_id")
		sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
		if err != nil {
			return nil, err
		}
		rows, err := conn.Query(ctx, sql)
		if err != nil {
			return nil, err
		}
		for rows.Next() {
			var id string
			var count uint64
			var lastCall time.Time
			err = rows.Scan(&id, &count, &lastCall)
			if err != nil {
				return nil, err
			}
			ret[id] = SimpleStats{Count: count, LastCall: lastCall}
		}
	}
	return ret, nil
}

func (r *RPCNodeRepository) GetRPCNodeStats(ctx context.Context, conn clickhouse.Conn, release string, req *protos.RPCNodeStatsRequest) (*protos.RPCNodeStatsResponse, error) {
	sq := sqlbuilder.Select(
		"toUInt32OrZero(LogAttributes['status_code']) as status_code",
		"toUInt64OrZero(LogAttributes['duration']) as duration",
		"toUInt64OrZero(LogAttributes['query_time']) as query_time",
	).From("otel_logs")
	sq.Where(
		sq.Equal("ServiceName", "rpcnode-server"),
		sq.Equal("ResourceAttributes['deployment.environment']", release),
		sq.Equal("Body", "RPCNode Request Log"),
		sq.Between("TimestampTime", req.StartTime.AsTime().Format("2006-01-02 15:04:05"), req.EndTime.AsTime().Format("2006-01-02 15:04:05")),
		sq.Equal("LogAttributes['rpc_node_id']", req.Id))
	subsql, err := sqlbuilder.ClickHouse.Interpolate(sq.Build())
	if err != nil {
		return nil, err
	}
	q := sqlbuilder.Select(
		"COUNT(*) as total",
		"SUM(if(status_code >= 400, 1, 0)) as errors",
		"toUInt64(ifNotFinite(avg(duration), 0)) as avg_duration",
		"ifNull(max(duration), 0) as max_duration",
		"ifNull(min(duration), 0) as min_duration",
		"toUInt64(ifNotFinite(median(duration), 0)) as median_duration",
		"toUInt64(ifNotFinite(avg(query_time), 0)) as avg_query_duration",
		"ifNull(max(query_time), 0) as max_query_duration",
		"ifNull(min(query_time), 0) as min_query_duration",
		"toUInt64(ifNotFinite(median(query_time), 0)) as median_query_duration",
	).From(fmt.Sprintf("(%s) x", subsql))
	sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
	if err != nil {
		return nil, err
	}
	log.Debug("Query: ", sql)
	rows, err := conn.Query(ctx, sql)
	defer rows.Close()
	if err != nil {
		return nil, err
	}
	if rows.Next() {
		var total, errors, avgDuration, maxDuration, minDuration, medianDuration uint64
		var avgQueryDuration, maxQueryDuration, minQueryDuration, medianQueryDuration uint64
		err = rows.Scan(&total, &errors, &avgDuration, &maxDuration, &minDuration, &medianDuration, &avgQueryDuration, &maxQueryDuration, &minQueryDuration, &medianQueryDuration)
		if err != nil {
			return nil, err
		}

		return &protos.RPCNodeStatsResponse{
			TotalRequests: total,
			TotalErrors:   errors,
			Durations: &protos.RPCNodeStatsResponse_Durations{
				Avg:    avgDuration,
				Max:    maxDuration,
				Min:    minDuration,
				Median: medianDuration,
			},
			QueryDurations: &protos.RPCNodeStatsResponse_Durations{
				Avg:    avgQueryDuration,
				Max:    maxQueryDuration,
				Min:    minQueryDuration,
				Median: medianQueryDuration,
			},
		}, nil
	}
	return nil, nil
}

func (r *RPCNodeRepository) GetRPCNodeTimeseries(ctx context.Context, conn clickhouse.Conn, release string, req *protos.RPCNodeStatsRequest) ([]*protos.RPCNodeStatsResponse_TimeSeries, error) {
	sq := sqlbuilder.Select(
		"TimestampTime as time",
		"toUInt32OrZero(LogAttributes['status_code']) as status_code",
		"if(status_code >= 400, 1, 0) as error",
		"if(status_code >= 400, 0, 1) as success",
		"toUInt64OrZero(LogAttributes['duration']) as duration",
		"toUInt64OrZero(LogAttributes['query_time']) as query_time",
	).From("otel_logs")
	sq.Where(
		sq.Equal("ServiceName", "rpcnode-server"),
		sq.Equal("ResourceAttributes['deployment.environment']", release),
		sq.Equal("Body", "RPCNode Request Log"),
		sq.Between("TimestampTime", req.StartTime.AsTime().Format("2006-01-02 15:04:05"), req.EndTime.AsTime().Format("2006-01-02 15:04:05")),
		sq.Equal("LogAttributes['rpc_node_id']", req.Id))
	subsql, err := sqlbuilder.ClickHouse.Interpolate(sq.Build())
	if err != nil {
		return nil, err
	}
	q := sqlbuilder.Select(
		"avg(duration * success) AS avg_duration",
		"max(duration * success) AS max_duration",
		"avg(query_time * success) as avg_query_time",
		"max(query_time * success) as max_query_time",
		"sum(success) as success_count",
		"sum(error) as error_count",
		fmt.Sprintf("toStartOfInterval(time,INTERVAL %d SECOND) AS h", req.StepSeconds),
	).From(fmt.Sprintf("(%s) x", subsql))
	q.GroupBy("h")
	q.OrderBy(fmt.Sprintf("h WITH FILL STEP INTERVAL %d SECOND", req.StepSeconds))
	sql, err := sqlbuilder.ClickHouse.Interpolate(q.Build())
	if err != nil {
		return nil, err
	}
	log.Debug("Query: ", sql)
	rows, err := conn.Query(ctx, sql)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var ret []*protos.RPCNodeStatsResponse_TimeSeries
	for rows.Next() {
		var avgDuration, avgQueryTime float64
		var successCount, errorCount, maxDuration, maxQueryTime uint64
		var h time.Time
		err = rows.Scan(&avgDuration, &maxDuration, &avgQueryTime, &maxQueryTime, &successCount, &errorCount, &h)
		if err != nil {
			return nil, err
		}
		ret = append(ret, &protos.RPCNodeStatsResponse_TimeSeries{
			Time:              timestamppb.New(h),
			SuccessCount:      successCount,
			ErrorCount:        errorCount,
			AvgDurations:      uint64(avgDuration),
			MaxDurations:      maxDuration,
			AvgQueryDurations: uint64(avgQueryTime),
			MaxQueryDurations: maxQueryTime,
		})
	}

	return ret, nil
}
