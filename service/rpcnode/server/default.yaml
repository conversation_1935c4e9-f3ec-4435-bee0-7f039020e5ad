networks:
  - id: mainnet
    name: Ethereum Mainnet
    endpoints:
      - path: /ethereum
        name: Mainnet RPC Endpoint
        rate_limit: default
  - id: movement-testnet
    name: Movement Testnet
    endpoints:
      - path: /movement-testnet/v1
        name: Movement RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/movement-testnet/v1
        rate_limit: default
      - path: /movement-testnet-indexer/v1/graphql
        name: Movement GraphQL Endpoint
        upstream: http://rpc.internal.sentioxyz.co/movement-testnet-indexer/v1/graphql
        rate_limit: default
  - id: movement-mainnet
    name: Movement Mainnet
    endpoints:
      - path: /movement/v1
        name: Movement RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/movement/v1
        rate_limit: default
      - path: /movement-indexer/v1/graphql
        name: Movement GraphQL Endpoint
        upstream: http://rpc.internal.sentioxyz.co/movement-indexer/v1/graphql
        rate_limit: default
  - id: aptos
    name: Aptos
    endpoints:
      - path: /aptos
        name: Aptos RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/aptos
        rate_limit: default
  - id: aptos-testnet
    name: Aptos Testnet
    endpoints:
      - path: /aptos-testnet
        name: Aptos Testnet RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/aptos-testnet
        rate_limit: default
  - id: sui_mainnet
    name: Sui Mainnet
    endpoints:
      - path: /sui-mainnet
        name: Sui RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/sui-mainnet
        rate_limit: default
  - id: sui_testnet
    name: Sui Testnet
    endpoints:
      - path: /sui-testnet
        name: Sui Testnet RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/sui-testnet
        rate_limit: default
  - id: iota_mainnet
    name: IOTA Mainnet
    endpoints:
      - path: /iota-mainnet
        name: IOTA RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/iota-mainnet
        rate_limit: default
  - id: iota_testnet
    name: IOTA Testnet
    endpoints:
      - path: /iota-testnet
        name: IOTA Testnet RPC Endpoint
        upstream: http://rpc.internal.sentioxyz.co/iota-testnet
        rate_limit: default
  
rate_limits:
  default:
    free:
      rate: 2000
      burst: 2000
      period: 60s
    dev:
      rate: 10000
      burst: 10000
      period: 1s
    pro:
      rate: 10000
      burst: 1000
      period: 1s
    enterprise:
      rate: 10000
      burst: 10000
      period: 1s