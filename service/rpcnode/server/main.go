package main

import (
	"flag"
	"fmt"
	"net/http"
	"os"
	"sentioxyz/sentio/common/clickhouse"
	"sentioxyz/sentio/common/flags"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/monitoring"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/common/preloader"
	"sentioxyz/sentio/service/common/rpc"
	"sentioxyz/sentio/service/endpoint"
	"sentioxyz/sentio/service/rpcnode"
	"sentioxyz/sentio/service/rpcnode/model"
	"sentioxyz/sentio/service/rpcnode/preloaders"
	"sentioxyz/sentio/service/rpcnode/protos"
	"sentioxyz/sentio/service/rpcnode/repository"
	usageUtils "sentioxyz/sentio/service/usage/utils"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/encoding/protojson"
)

func main() {
	var (
		dbURL = flag.String(
			"database",
			"postgres://postgres:postgres@localhost:5432/postgres",
			"The postgres database address",
		)
		port                = flag.Uint("port", 18010, "The server port")
		authIssuerURL       = flag.String("auth-issuer-url", "https://sentio-dev.us.auth0.com/", "The auth0 issue url")
		authAudience        = flag.String("auth-audience", "http://localhost:8080/v1", "The auth0 audience")
		authClientID        = flag.String("auth-client-id", "JREam3EysMTM49eFbAjNK02OCykpmda3", "The auth0 app clientId")
		enableProf          = flag.Bool("enable-pprof", false, "Enable pprof")
		usageServiceAddress = flag.String("usage-service-address", "", "Usage service address")
		releaseName         = flag.String("release-name", "test", "The release name")
		configFile          = flag.String("config", "", "The config file")
		logClickHouseDSN    = flag.String("log-clickhouse-dsn", "clickhouse://sentio:2vwzZBJ6cbZbyoKm4j@localhost:19100/default", "address to clickhouse stores log")
	)
	flags.ParseAndInitLogFlag()

	monitoring.StartMonitoring()
	defer monitoring.StopMonitoring()

	conn, err := repository.SetupDB(*dbURL)
	if err != nil {
		log.Fatale(err)
	}

	config := auth.AuthConfig{
		IssuerURL: *authIssuerURL,
		Audience:  *authAudience,
		ClientID:  *authClientID,
	}

	authManager := auth.NewAuthManager(&config, conn)
	ch, err := clickhouse.NewConn(*logClickHouseDSN)
	if err != nil {
		log.Fatale(err)
	}

	usageClient := usageUtils.MustNewClient(*usageServiceAddress)
	svc := rpcnode.NewService(conn, ch.GetClickhouseConn(), ReadConfig(*configFile), *releaseName, usageClient)

	grpcServer := rpc.NewServer(true, grpc.ChainUnaryInterceptor(
		preloader.Interceptor(conn, authManager.IdentityPreloader, preloader.ProjectLoader, preloaders.RPCNodePreloader),
		authManager.GetAuthInterceptor(nil),
		usageUtils.UsageInterceptor(authManager, usageClient),
	))
	protos.RegisterRPCNodeServiceServer(grpcServer, svc)

	mux := runtime.NewServeMux(
		runtime.WithMetadata(rpc.WithAuthAndTraceMetadata),
		runtime.WithMarshalerOption(runtime.MIMEWildcard, &endpoint.HttpMarshaler{
			Marshaler: &runtime.HTTPBodyMarshaler{
				Marshaler: &runtime.JSONPb{
					UnmarshalOptions: protojson.UnmarshalOptions{
						DiscardUnknown: true,
						AllowPartial:   true,
					},
					MarshalOptions: protojson.MarshalOptions{
						EmitUnpopulated: true,
					}},
			},
		}),
		runtime.WithMetadata(endpoint.WithHttpMetadata),
		runtime.WithForwardResponseOption(endpoint.ForwardHttpCode),
		runtime.WithErrorHandler(endpoint.ForwardHttpHeaderWhenError),
		runtime.WithForwardResponseRewriter(endpoint.ForwardResponseRewriter),
	)
	err = mux.HandlePath("GET", "/healthz", rpc.HealthCheck(conn))
	if err != nil {
		log.Fatale(err)
	}

	err = protos.RegisterRPCNodeServiceHandlerFromEndpoint(context.Background(),
		mux,
		fmt.Sprintf(":%d", *port),
		rpc.GRPCGatewayDialOptions)
	if err != nil {
		log.Fatale(err)
	}

	if *enableProf {
		go func() {
			log.Fatale(http.ListenAndServe(":6060", nil))
		}()
	}

	rpc.BindAndServeWithHTTP(svc.Handle(mux), grpcServer, int(*port), nil)
}

func ReadConfig(configPath string) model.RPCSettings {
	if configPath == "" {
		configPath = "default.yaml"
	}
	if f, err := os.ReadFile(configPath); err != nil {
		log.Fatale(err)
	} else {
		settings, err := model.LoadSettings(f)
		if err != nil {
			log.Fatale(err)
		}
		log.Info("loaded config", "config", settings)
		return settings
	}
	return model.RPCSettings{}
}
