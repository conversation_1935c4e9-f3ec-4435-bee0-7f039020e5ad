load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "preloaders",
    srcs = ["rpcnode_loader.go"],
    importpath = "sentioxyz/sentio/service/rpcnode/preloaders",
    visibility = ["//visibility:public"],
    deps = [
        "//service/common/preloader",
        "//service/common/repository",
        "//service/rpcnode/model",
        "//service/rpcnode/protos",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//reflect/protoreflect",
    ],
)
