syntax = "proto3";

package ai_service;

option go_package = "sentioxyz/sentio/service/ai/protos";

import "google/api/annotations.proto";
import "google/api/visibility.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "service/common/protos/common.proto";
import "service/ai/api/api.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

// AiRole defines the possible roles in a conversation
enum AiRole {
  AI_ROLE_SYSTEM = 0;    // System message
  AI_ROLE_USER = 1;      // User message
  AI_ROLE_ASSISTANT = 2; // Assistant message
}

// ChartType defines the supported visualization types
enum ChartType {
  CHART_TYPE_UNSPECIFIED = 0; // Default unspecified type
  CHART_TYPE_TABLE = 1;       // Tabular data visualization
  CHART_TYPE_LINE = 2;        // Line chart
  CHART_TYPE_BAR = 3;         // Bar chart
  CHART_TYPE_PIE = 4;         // Pie chart
}

// AiMessage represents a single message in an AI conversation
message AiMessage {
  AiRole role = 1;    // The role of the message sender
  string content = 2; // The content of the message
  string run_id = 3;  // Unique identifier for the conversation run
}

// SQLAgentResponse represents the response from the SQL agent
message SQLAgentResponse {
  string type = 1;             // Type of response: "message", "query", or "clarification"
  string content = 2;          // Text content of the response
  string sql = 3;              // Generated SQL query
  ChartType chart_type = 4;    // Recommended visualization type
  string explanation = 5;      // Explanation of the SQL query
  string title = 6;            // Title for the result
  repeated string questions = 7; // Follow-up questions
  string run_id = 8;           // Unique identifier for the run
}

// SQLAgentRequest represents a request to the SQL agent
message SQLAgentRequest {
  repeated AiMessage messages = 1; // Conversation history
  string project_owner = 2;        // Owner of the project
  string project_slug = 3;         // Project identifier
  int32 version = 4;               // API version
}

// AiService provides AI-powered features for data querying, visualization, and chat
service AiService {
  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_tag) = {
    name: "AI"
  };

  // SQLCompletion generates SQL queries based on natural language input
  rpc SQLCompletion(SQLCompletionRequest) returns (SQLCompletionResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/{project_owner}/{project_slug}/sql_completion"
      body: "*"
    };
  }
  
  // SQLAgentChat provides interactive SQL generation through conversation
  rpc SQLAgentChat(SQLAgentRequest) returns (SQLAgentResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/{project_owner}/{project_slug}/sql_agent_chat"
      body: "*"
    };
  }
  
  // GraphQLCompletion generates GraphQL queries based on natural language input
  rpc GraphQLCompletion(GraphQLCompletionRequest) returns (GraphQLCompletionResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/{project_owner}/{project_slug}/graphql_completion"
      body: "*"
    };
  }
  
  // ChartCompletion recommends chart visualizations for data
  rpc ChartCompletion(ChartCompletionRequest) returns (ChartCompletionResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/{project_owner}/{project_slug}/chart_completion"
      body: "*"
    };
  }
  
  // FixSQL repairs and optimizes SQL queries that have errors
  rpc FixSQL(FixSQLRequest) returns (FixSQLResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/{project_owner}/{project_slug}/fix_sql"
      body: "*"
    };
  }
  
  // SubmitFeedback allows users to provide feedback on AI responses
  rpc SubmitFeedback(SubmitFeedbackRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/{project_owner}/{project_slug}/feedback"
      body: "*"
    };
  }
  
  // History chat related RPCs
  
  // SaveHistoryChat persists a chat conversation to history
  rpc SaveHistoryChat(SaveHistoryChatRequest) returns (SaveHistoryChatResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/history_chat"
      body: "*"
    };
  }
  
  // DeleteHistoryChat removes a saved chat conversation
  rpc DeleteHistoryChat(DeleteHistoryChatRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      delete: "/api/v1/ai/history_chat/{id}"
    };
  }
  
  // UpdateHistoryChat modifies an existing chat conversation
  rpc UpdateHistoryChat(UpdateHistoryChatRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      put: "/api/v1/ai/history_chat"
      body: "*"
    };
  }
  
  // GetHistoryChats retrieves all saved chat conversations
  rpc GetHistoryChats(GetHistoryChatsRequest) returns (GetHistoryChatsResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      get: "/api/v1/ai/history_chats"
    };
  }
  
  // GetHistoryChatById retrieves a specific chat conversation by ID
  rpc GetHistoryChatById(GetHistoryChatByIdRequest) returns (GetHistoryChatByIdResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      get: "/api/v1/ai/history_chat/{id}"
    };
  }

  // Sharing chat related RPCs
  
  // CreateSharingChat creates a shareable version of a chat conversation
  rpc CreateSharingChat(CreateSharingChatRequest) returns (CreateSharingChatResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/sharing_chat"
      body: "*"
    };
  }
  
  // UpdateSharingChat modifies a shared chat conversation
  rpc UpdateSharingChat(UpdateSharingChatRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      put: "/api/v1/ai/sharing_chat"
      body: "*"
    };
  }
  
  // DeleteSharingChat removes a shared chat conversation
  rpc DeleteSharingChat(DeleteSharingChatRequest) returns (google.protobuf.Empty) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      delete: "/api/v1/ai/sharing_chat/{id}"
    };
  }
  
  // GetSharingById retrieves a shared chat by its ID
  rpc GetSharingById(GetSharingByIdRequest) returns (GetSharingChatResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      get: "/api/v1/ai/sharing_chat/{id}"
    };
  }
  
  // GetSharingByChat retrieves a shared chat by its original chat ID
  rpc GetSharingByChat(GetSharingByChatRequest) returns (GetSharingChatResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      get: "/api/v1/ai/sharing_chat/chat/{chat_id}"
    };
  }

  // Create Chat Session
  //
  // Initialize a new AI chat session. Returns a session_id that can be used with PostSessionMessage to have a conversation with the AI. Messages are generated as part of runs, with is_final indicating run completion.
  rpc CreateChatSession(ChatSession) returns (CreateChatSessionResponse) {
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    
    option (common.track_usage) = {
      api_sku: "api_ai"
      webui_sku: "webui_ai"
      project_owner_field: "context.project_owner"
      project_slug_field: "context.project_slug"
    };

    option (google.api.http) = {
      post: "/api/v1/ai/chat"
      body: "*"
    };
  }

  // Query Chat Session
  //
  // Retrieve information about an existing chat session, returning only messages after the specified cursor position. Messages include run_id to identify generation runs.
  rpc QueryChatSession(QueryChatSessionRequest) returns (ChatSession) {
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    
    option (google.api.http) = {
      get: "/api/v1/ai/chat/{session_id}"
    };
  }

  // Post Session Message
  //
  // Add a new message to an existing chat session. This will trigger AI message generation as a run. check is_final to know when all messages for the run have been generated.
  rpc PostSessionMessage(PostSessionMessageRequest) returns (PostSessionMessageResponse) {
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
    };
    
    option (common.track_usage) = {
      api_sku: "api_ai"
      webui_sku: "webui_ai"
    };

    option (google.api.http) = {
      post: "/api/v1/ai/chat/{session_id}/message"
      body: "*"
    };
  }

  // GenerateProcessor triggers creation of a Sentio processor project based on inputs
  rpc GenerateProcessor(ai_api.ProcessorGenerateRequest) returns (ai_api.ProcessorGenerateResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    option (google.api.http) = {
      post: "/api/v1/ai/processor/generate"
      body: "*"
    };
  }

  // SuggestReply provides AI-generated reply suggestions for a conversation
  rpc SuggestReply(ChatSession) returns (ai_api.SuggestReplyResponse) {
    option (google.api.method_visibility).restriction = "PREVIEW";
    
    option (google.api.http) = {
      post: "/api/v1/ai/suggest_reply"
      body: "*"
    };
  }
}

// SQLCompletionRequest contains the necessary information for SQL generation
message SQLCompletionRequest {
  repeated AiMessage messages = 1; // Conversation history
  string project_owner = 2;        // Owner of the project
  string project_slug = 3;         // Project identifier
  int32 version = 4;               // API version
}

// GraphQLCompletionRequest contains the necessary information for GraphQL generation
message GraphQLCompletionRequest {
  repeated AiMessage messages = 1; // Conversation history
  string project_owner = 2;        // Owner of the project
  string project_slug = 3;         // Project identifier
  int32 version = 4;               // API version
}

// ChartCompletionRequest contains the necessary information for chart recommendations
message ChartCompletionRequest {
  repeated AiMessage messages = 1; // Conversation history
  string project_owner = 2;        // Owner of the project
  string project_slug = 3;         // Project identifier
  int32 version = 4;               // API version
  string dashboard_id = 5;         // Dashboard where the chart will be placed
}

// FixSQLRequest contains an SQL query and error information for repair
message FixSQLRequest {
  repeated AiMessage messages = 1; // Conversation history
  string error = 2;                // Error message from failed SQL execution
  string project_owner = 3;        // Owner of the project
  string project_slug = 4;         // Project identifier
  int32 version = 5;               // API version
}

// SubmitFeedbackRequest allows users to provide ratings on AI responses
message SubmitFeedbackRequest {
  string run_id = 1;         // Unique identifier for the conversation run
  string project_owner = 2;  // Owner of the project
  string project_slug = 3;   // Project identifier
  int32 score = 4;           // User rating (typically 1-5)
}

// SQLCompletionResponse contains generated SQL query options
message SQLCompletionResponse {
  repeated AiMessage choices = 1; // Generated SQL query options
}

// ChartCompletionResponse contains chart visualization recommendations
message ChartCompletionResponse {
  repeated AiMessage choices = 1; // Chart visualization recommendations
}

// GraphQLCompletionResponse contains generated GraphQL query options
message GraphQLCompletionResponse {
  repeated AiMessage choices = 1; // Generated GraphQL query options
}

// FixSQLResponse contains repair suggestions for SQL queries
message FixSQLResponse {
  repeated AiMessage choices = 1; // Repaired SQL query options
}

// ChatSession represents an interactive conversation session with the AI. Messages in the session are ordered and accessed via cursor positions.
message ChatSession {
  repeated Message messages = 1;  // Message history in the session
  Context context = 2;            // Context information for the session
  bool streaming = 3;             // Whether to use streaming responses
  bool preserve_session = 4;      // Whether to persist the session
}

// QueryChatSessionRequest retrieves information about a chat session
message QueryChatSessionRequest { 
  string session_id = 1;    // Unique identifier for the session
  int32 cursor_position = 2; // Start cursor position - only messages after this position will be returned
}

// CreateChatSessionResponse contains the newly created session information
message CreateChatSessionResponse {
  string session_id = 1;    // Unique identifier for the new session
  int32 current_cursor_position = 2;    // Current cursor position - indicates the latest message position in the conversation
}

// PostSessionMessageRequest adds a message to an existing session
message PostSessionMessageRequest {
  string session_id = 1;    // Unique identifier for the session
  Message message = 2;       // Message to add to the session
}

// PostSessionMessageResponse confirms message addition and updates cursor
message PostSessionMessageResponse {
  int32 current_cursor_position = 1;    // Updated cursor position after adding the message
}

// Message represents a single message in an AI conversation with either text or structured content. Messages are generated as part of a 'run' (identified by run_id), and the is_final flag indicates when all messages for a run have been generated.
message Message {
  Role role = 1;    // Role of the message sender (user, assistant, system, or tool)
  oneof content {
    string text = 2;                 // Plain text content of the message
    StructuredContent structured = 3; // Structured content like SQL queries, errors, etc.
  }
  enum Role { 
    ROLE_UNSPECIFIED = 0; 
    ROLE_USER = 1; 
    ROLE_ASSISTANT = 2; 
    ROLE_SYSTEM = 3; 
    AI_ROLE_TOOL = 4; 
  }
  bool is_final = 4;    // Indicates this is the final message of a run, meaning the run is complete and all generated messages have been returned
  string run_id = 5;    // Unique identifier for the current run. Messages with the same run_id belong to the same generation process, and is_final indicates when the run is complete
  repeated Resource resources = 6;    // Resources attached to this message
}

// StructuredContent represents structured content that can contain SQL queries, errors, or other formatted data
message StructuredContent {
  ContentType type = 1;    // Type of structured content (SQL, error, etc.)
  oneof payload {
    SqlContent sql = 2;    // SQL query and related information
    InsightQueryContent insightQuery = 3; // Insight query and related information
    ErrorContent error = 4;  // Error information
  }
  enum ContentType { 
    CONTENT_TYPE_UNSPECIFIED = 0; 
    CONTENT_TYPE_SQL = 1; 
    CONTENT_TYPE_INSIGHT_QUERY = 2;
    CONTENT_TYPE_ERROR = 3; 
  }
}

// SqlContent contains content including the query text, explanation, visualization options and results
message SqlContent {
  string query = 1;    // SQL query text
  string explanation = 2;    // Explanation of what the query does and how it works
  ChartType chart_type = 3;    // Recommended visualization type for the query results
  string title = 4;    // Title for the query result visualization
  oneof query_result {
    common.TabularData result = 5;    // Successful query result data
    string error = 6;    // Error from query execution
  }
}

message InsightQueryResult {
  string id = 1;
  string alias = 2;
  oneof result_type {
    common.Matrix matrix = 3;
    string error = 4;
  }
}

message InsightQueryContent {
  string explanation = 1;    // Explanation of what the query does and how it works
  ChartType chart_type = 2;    // Recommended visualization type for the query results
  repeated common.Query queries = 3;    // List of queries to execute for generating insights
  repeated common.Formula formulas = 4;    // Mathematical formulas to apply to the query results
  int32 samples_limit = 5;    // Maximum number of data samples to return
  common.TimeRangeLite time_range = 6;    // Time range filter for the query execution
  repeated InsightQueryResult results = 7; // List of query results
  string title = 8;   // Title for the query result visualization
}

// ErrorContent represents error information
message ErrorContent { 
  string code = 1;    // Error code identifying the type of error
  string message = 2;    // Human-readable error message
}

// Context provides environment information for a chat session
message Context {
  enum Scenario { 
    SCENARIO_UNSPECIFIED = 0; 
    SCENARIO_SQL = 1;
    SCENARIO_INSIGHT = 2;
    SCENARIO_AUTO = 3;
  }
  
  string project_owner = 1;    // Owner of the project (username or organization)
  string project_slug = 2;    // Project identifier/slug
  int32 version = 3;    // API version to use
  Scenario scenario = 4;    // Specific scenario type for this chat session
  oneof config {
    SqlConfig sql_config = 5;    // SQL-specific configuration options
    InsightConfig insight_config = 6;    // Insight-specific configuration options
    AutoConfig auto_config = 7;    // Auto-specific configuration options
  }
}

// SqlConfig contains configuration specific to SQL scenarios
message SqlConfig {
  bool execute_query = 1;    // Whether to automatically execute generated SQL queries
}

// InsightConfig contains configuration specific to Insight scenarios
message InsightConfig {
  bool execute_query = 1;    // Whether to automatically execute generated Insight queries
}

// AutoConfig contains configuration specific to Auto scenarios
message AutoConfig {
  bool execute_query = 1;    // Whether to automatically execute generated queries (SQL or Insight)
}

// ChatType defines the possible types of chat interactions
enum ChatType {
  CHAT_TYPE_UNSPECIFIED = 0;  // Default unspecified type
  CHAT_TYPE_CHAT = 1;         // Regular conversation
  CHAT_TYPE_ACTION = 2;       // Action-oriented interaction
}

// HistoryChat represents a saved chat conversation
message HistoryChat {
  string id = 1;                          // Unique identifier
  string title = 2;                       // Chat title
  google.protobuf.ListValue messages = 4; // Message history
  ChatType type = 5;                      // Type of chat interaction
  google.protobuf.Struct meta = 6;        // Extensible metadata
  google.protobuf.Timestamp created_at = 7;  // Creation timestamp
  google.protobuf.Timestamp updated_at = 8;  // Last update timestamp
  optional string project_owner = 9;      // Owner of the project
  optional string project_slug = 10;      // Project identifier
}

// SaveHistoryChatRequest saves a chat conversation to history
message SaveHistoryChatRequest {
  HistoryChat history_chat = 1;        // Chat to save
  optional string project_owner = 2;   // Owner of the project
  optional string project_slug = 3;    // Project identifier
}

// SaveHistoryChatResponse confirms the chat has been saved
message SaveHistoryChatResponse {
  string id = 1; // The ID of the saved history chat
}

// DeleteHistoryChatRequest removes a chat from history
message DeleteHistoryChatRequest {
  string id = 1; // ID of the chat to delete
}

// UpdateHistoryChatRequest modifies an existing history chat
message UpdateHistoryChatRequest {
  HistoryChat history_chat = 1;       // Updated chat data
  optional string project_owner = 2;  // Owner of the project
  optional string project_slug = 3;   // Project identifier
}

// GetHistoryChatsRequest retrieves all history chats for the current user
message GetHistoryChatsRequest {
  // Empty request as we'll return all history chats for the current user
}

// SimplifiedHistoryChat contains basic chat information without messages
message SimplifiedHistoryChat {
  string id = 1;                        // Unique identifier
  string title = 2;                     // Chat title
  ChatType type = 3;                    // Type of chat
  google.protobuf.Struct meta = 4;      // Metadata
  google.protobuf.Timestamp created_at = 5;  // Creation timestamp
  google.protobuf.Timestamp updated_at = 6;  // Last update timestamp
  optional string project_owner = 7;    // Owner of the project
  optional string project_slug = 8;     // Project identifier
}

// GetHistoryChatsResponse contains a list of simplified history chats
message GetHistoryChatsResponse {
  repeated SimplifiedHistoryChat history_chats = 1; // List of history chats
}

// GetHistoryChatByIdRequest retrieves a specific history chat
message GetHistoryChatByIdRequest {
  string id = 1; // ID of the chat to retrieve
}

// GetHistoryChatByIdResponse contains the requested history chat
message GetHistoryChatByIdResponse {
  HistoryChat history_chat = 1; // The requested history chat
}

// SharingChat represents a chat that has been shared with others
message SharingChat {
  string id = 1;                          // Unique identifier
  string ai_chat_id = 2;                  // Foreign key to AiChatHistory
  string project_id = 3;                  // Associated project
  string user_id = 4;                     // User who shared the chat
  string title = 5;                       // Chat title
  ChatType type = 6;                      // Type of chat
  google.protobuf.ListValue messages = 7; // Message history
  google.protobuf.Struct meta = 8;        // Extensible metadata
  google.protobuf.Timestamp created_at = 9;  // Creation timestamp
  google.protobuf.Timestamp updated_at = 10; // Last update timestamp
}

// CreateSharingChatRequest creates a new shared chat
message CreateSharingChatRequest {
  string chat_id = 1; // ID of the chat to share
  string title = 2;   // Title for the shared chat
}

// CreateSharingChatResponse confirms creation of a shared chat
message CreateSharingChatResponse {
  string id = 1; // The ID of the created sharing chat
}

// UpdateSharingChatRequest modifies an existing shared chat
message UpdateSharingChatRequest {
  string chat_id = 1; // ID of the shared chat
  string title = 2;   // New title
}

// DeleteSharingChatRequest removes a shared chat
message DeleteSharingChatRequest {
  string id = 1; // ID of the shared chat to delete
}

// GetSharingByIdRequest retrieves a shared chat by ID
message GetSharingByIdRequest {
  string id = 1; // ID of the shared chat
}

// GetSharingByChatRequest retrieves a shared chat by original chat ID
message GetSharingByChatRequest {
  string chat_id = 2; // ID of the original chat
}

// GetSharingChatResponse contains the requested shared chat
message GetSharingChatResponse {
  SharingChat sharing_chat = 1;     // The shared chat
  optional common.Project project = 2; // Associated project information
}

// Resource represents a resource that can be attached to a message, such as a file, image, or other content
message Resource {
  string uri = 1;    // Unique identifier or location for the resource
  optional string name = 2;    // Human-readable name for the resource
  optional string description = 3;    // Description of the resource
  optional string mime_type = 4;    // MIME type of the resource content
  oneof content {
    string text = 5;    // Text content of the resource
    bytes blob = 6;    // Binary content of the resource
  }
}


