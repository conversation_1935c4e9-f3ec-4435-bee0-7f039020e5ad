package ai

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"sentioxyz/sentio/common/identifier"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/redislock"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/ai/api"
	"sentioxyz/sentio/service/ai/protos"
	analyticProtos "sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/common/auth"
	commonModels "sentioxyz/sentio/service/common/models"
	"sentioxyz/sentio/service/common/redis"
	"sentioxyz/sentio/service/common/rpc"
	o11yProtos "sentioxyz/sentio/service/observability/protos"
	protosObservability "sentioxyz/sentio/service/observability/protos"
	"sentioxyz/sentio/service/web/models"

	sentioProtojson "sentioxyz/sentio/common/protojson"
	commonProtos "sentioxyz/sentio/service/common/protos"
	usageUtils "sentioxyz/sentio/service/usage/utils"

	"gorm.io/gorm"

	"github.com/google/uuid"
	goredis "github.com/redis/go-redis/v9"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"
)

const (
	// Redis key prefix for chat sessions
	sessionKeyPrefix = "chat_session:"
	// TTL for chat sessions in Redis - 24 hours
	sessionTTL = 2 * time.Hour
	// Prefix for chat session processing locks
	sessionLockPrefix = "chat_session_lock:"
	// TTL for session locks - 5 minutes
	sessionLockTTL = 5 * time.Minute

	ApiSku   = "api_ai"
	WebuiSku = "webui_ai"
)

type Service struct {
	protos.UnimplementedAiServiceServer
	aiService       api.AiApiClient
	analyticService analyticProtos.AnalyticServiceClient
	metricService   o11yProtos.ObservabilityServiceClient
	authManager     auth.AuthManager

	// Redis client for distributed session cache
	redisClient *goredis.Client
	usageClient *usageUtils.Client
	db          *gorm.DB
}

func NewService(authManager auth.AuthManager, serverAddress string,
	analyticServiceAddress, o11yServiceAddress string, usageClient *usageUtils.Client, conn *gorm.DB) *Service {
	server, err := rpc.DialInsecure(serverAddress)
	if err != nil {
		log.Fatale(err)
	}
	var metricService o11yProtos.ObservabilityServiceClient
	o11yConn, err := rpc.DialAuto(o11yServiceAddress, rpc.RetryDialOption)
	if err != nil {
		log.Fatale(err)
	}
	metricService = o11yProtos.NewObservabilityServiceClient(o11yConn)

	var analyticService analyticProtos.AnalyticServiceClient
	analyticConn, err := rpc.DialAuto(analyticServiceAddress, rpc.RetryDialOption)
	if err != nil {
		log.Fatale(err)
	}
	analyticService = analyticProtos.NewAnalyticServiceClient(analyticConn)

	return &Service{
		authManager:     authManager,
		db:              conn,
		aiService:       api.NewAiApiClient(server),
		analyticService: analyticService,
		metricService:   metricService,
		usageClient:     usageClient,
		redisClient:     redis.NewClientWithDefaultOptions(),
	}
}

// getSessionKey creates a Redis key for a session
func getSessionKey(sessionID string) string {
	return sessionKeyPrefix + sessionID
}

// getSessionLockKey creates a Redis key for a session lock
func getSessionLockKey(sessionID string) string {
	return sessionLockPrefix + sessionID
}

// saveSessionToRedis saves a ChatSession to Redis with TTL
func (s *Service) saveSessionToRedis(ctx context.Context, sessionID string, request *protos.ChatSession) error {
	// Directly marshal the protobuf message to JSON
	data, err := sentioProtojson.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal chat session: %w", err)
	}

	key := getSessionKey(sessionID)
	return s.redisClient.Set(ctx, key, data, sessionTTL).Err()
}

// getChatSessionFromRedis gets a CreateChatSession from Redis
func (s *Service) getChatSessionFromRedis(ctx context.Context, sessionID string) (*protos.ChatSession, error) {
	key := getSessionKey(sessionID)
	data, err := s.redisClient.Get(ctx, key).Bytes()
	if err != nil {
		if err == goredis.Nil {
			return nil, status.Error(codes.NotFound, "session not found")
		}
		return nil, fmt.Errorf("failed to get session from Redis: %w", err)
	}

	// Unmarshal the request using protojson
	request := &protos.ChatSession{}
	if err := sentioProtojson.Unmarshal(data, request); err != nil {
		return nil, fmt.Errorf("failed to unmarshal chat session: %w", err)
	}

	return request, nil
}

// deleteSessionFromRedis deletes a session from Redis
func (s *Service) deleteSessionFromRedis(ctx context.Context, sessionID string) error {
	key := getSessionKey(sessionID)
	return s.redisClient.Del(ctx, key).Err()
}

// buildSchemaFromTablesResponse converts the response from QueryTables into an SQLSchema object
func buildSchemaFromTablesResponse(resp *analyticProtos.QueryTablesResponse) *api.SQLSchema {
	schema := &api.SQLSchema{
		Tables: make([]*api.SQLSchema_SQLTable, 0),
	}

	// If there are Entity tables, we need to find the raw tables associated with them and exclude Entity tables
	excludedTableNames := make(map[string]bool)
	for _, t := range resp.Tables {
		if t.TableType == analyticProtos.Table_ENTITY {
			rawTableName := t.Name + "_raw"
			for _, tb := range resp.Tables {
				if tb.Name == rawTableName {
					excludedTableNames[t.Name] = true
					break
				}
			}
		}
	}
	// Add table schema information
	for _, t := range resp.Tables {
		if excludedTableNames[t.Name] {
			continue
		}
		table := api.SQLSchema_SQLTable{
			Name:      t.Name,
			Columns:   make([]*api.SQLSchema_SQLTable_SQLColumn, 0),
			TableType: convertToTableType(t.TableType),
		}
		for _, c := range t.Columns {
			table.Columns = append(table.Columns, &api.SQLSchema_SQLTable_SQLColumn{
				Name: c.Name,
				Type: c.ClickhouseDataType,
			})
		}
		schema.Tables = append(schema.Tables, &table)
	}

	return schema
}

// getTableSchema queries tables from the analytic service and builds a schema
func (s *Service) getTableSchema(ctx context.Context, projectOwner string, projectSlug string, version int32) (*api.SQLSchema, error) {
	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	resp, err := s.analyticService.QueryTables(ctx, &analyticProtos.QueryTablesRequest{
		ProjectOwner:     projectOwner,
		ProjectSlug:      projectSlug,
		Version:          version,
		IncludeChains:    true,
		IncludeViews:     true,
		IncludeExternals: true,
		IncludeDash:      projectOwner == "sui" && projectSlug == "main",
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to fetch schema: %v", err)
	}

	return buildSchemaFromTablesResponse(resp), nil
}

func (s *Service) getMetricsInfo(ctx context.Context, projectId string, version int32) ([]*api.MetricInfo, error) {
	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	resp1, err := s.metricService.GetMetrics(ctx, &protosObservability.GetMetricsRequest{
		ProjectId: projectId,
		Version:   version,
	})
	if err != nil {
		return nil, err
	}

	metrics := utils.MapSliceNoError(resp1.Metrics, func(v *protosObservability.MetricInfo) *api.MetricInfo {
		return &api.MetricInfo{
			Name: v.Name,
			Type: v.Metadata.Type,
		}
	})

	return metrics, nil
}

func (s *Service) CreateChatSession(ctx context.Context, session *protos.ChatSession) (*protos.CreateChatSessionResponse, error) {
	if session.Context == nil {
		return nil, status.Error(codes.InvalidArgument, "context is not set")
	}

	if session.Context.Scenario != protos.Context_SCENARIO_SQL &&
		session.Context.Scenario != protos.Context_SCENARIO_INSIGHT &&
		session.Context.Scenario != protos.Context_SCENARIO_AUTO {
		return nil, status.Error(codes.InvalidArgument, "Unsupported scenario")
	}

	if session.Context.ProjectOwner == "" || session.Context.ProjectSlug == "" {
		return nil, status.Error(codes.InvalidArgument, "project owner or project slug is not set")
	}

	// Create a unique session ID
	sessionID := uuid.New().String()
	// Store the original request in Redis
	if err := s.saveSessionToRedis(ctx, sessionID, session); err != nil {
		log.Errorf("Failed to save request to Redis: %v", err)
		return nil, status.Errorf(codes.Internal, "failed to save session: %v", err)
	}

	// Only process if the last message is from the user
	if len(session.Messages) > 0 && session.Messages[len(session.Messages)-1].Role == protos.Message_ROLE_USER {
		// Only fetch schema if project info is provided
		var schema *api.SQLSchema
		var metricInfo []*api.MetricInfo
		var projectID string
		if session.Context.ProjectOwner != "" && session.Context.ProjectSlug != "" {
			// Verify user has access to the project
			_, project, err := s.authManager.RequiredLoginForProjectOwnerAndSlug(
				ctx,
				session.Context.ProjectOwner,
				session.Context.ProjectSlug,
				auth.READ,
			)
			if err != nil {
				// Delete the session we just created since we encountered an auth error
				if deleteErr := s.deleteSessionFromRedis(ctx, sessionID); deleteErr != nil {
					log.Errorf("Failed to delete session from Redis after auth error: %v", deleteErr)
				}
				return nil, status.Errorf(
					codes.PermissionDenied,
					"You don't have permission to access this project: %v",
					err,
				)
			}

			projectID = project.ID

			if session.Context.Scenario == protos.Context_SCENARIO_AUTO || session.Context.Scenario == protos.Context_SCENARIO_SQL {
				// Get schema for the project
				schema, err = s.getTableSchema(ctx, session.Context.ProjectOwner, session.Context.ProjectSlug, session.Context.Version)
				if err != nil {
					return nil, err
				}
			}

			if session.Context.Scenario == protos.Context_SCENARIO_AUTO || session.Context.Scenario == protos.Context_SCENARIO_INSIGHT {
				// Get metrics info for the project
				metricInfo, err = s.getMetricsInfo(ctx, projectID, session.Context.Version)
				if err != nil {
					return nil, err
				}
			}
		} else {
			// Use empty schema when project info is not provided
			schema = &api.SQLSchema{
				Tables: make([]*api.SQLSchema_SQLTable, 0),
			}
			metricInfo = []*api.MetricInfo{}
		}

		mdCopy := auth.CopyAuthHeadersToMetaData(ctx)
		bgCtx := metadata.NewOutgoingContext(context.Background(), mdCopy)

		// Process the request in a background goroutine
		go func(ctx context.Context) {
			// Create a lock for this session
			sessionLock := s.createSessionLock(sessionID)

			// Process the chat request within the locked critical section
			_ = sessionLock.CriticalSection(ctx, redislock.ModeWrite, func(lockCtx context.Context) error {
				// Process the chat session
				return s.processChatSessionInBackground(lockCtx, session, schema, metricInfo, sessionID, projectID)
			})
		}(bgCtx)
	}

	// Return the session ID immediately
	return &protos.CreateChatSessionResponse{
		SessionId:             sessionID,
		CurrentCursorPosition: int32(len(session.Messages)),
	}, nil
}

// processChatSessionInBackground handles the actual processing of the chat session
// It returns the run ID if successful, or an error
func (s *Service) processChatSessionInBackground(ctx context.Context, session *protos.ChatSession, schema *api.SQLSchema, metricInfo []*api.MetricInfo, sessionID string, projectID string) error {
	// Defer saving non-streaming session updates
	if !session.Streaming {
		defer func() {
			// Update request in Redis with the latest messages
			if saveErr := s.saveSessionToRedis(ctx, sessionID, session); saveErr != nil {
				log.Errorf("Failed to update request in Redis: %v", saveErr)
			}
		}()
	}

	// Convert messages to the format expected by the AI service
	messages := make([]*api.Message, 0, len(session.Messages))

	for _, msg := range session.Messages {
		var content string
		if msg.GetText() != "" {
			content = msg.GetText()
		} else if msg.GetStructured() != nil && msg.GetStructured().GetSql() != nil {
			sqlContent := msg.GetStructured().GetSql()
			content = fmt.Sprintf("Title: %s\nSQL Query: %s\nExplanation: %s\nChartType:%s", sqlContent.Title, sqlContent.Query, sqlContent.Explanation, sqlContent.ChartType)
			if sqlContent.GetResult() != nil {
				formatter := NewTabularFormatter()
				formattedResult := formatter.FormatTabularData(sqlContent.GetResult())
				content += fmt.Sprintf("\n%s", formattedResult)
			} else if sqlContent.GetError() != "" {
				content += fmt.Sprintf("\nError: %s", sqlContent.GetError())
			}
		} else if msg.GetStructured() != nil && msg.GetStructured().GetInsightQuery() != nil {
			insightContent := msg.GetStructured().GetInsightQuery()
			content = fmt.Sprintf("Title: %s\nExplanation: %s\nChartType:%s", insightContent.Title, insightContent.Explanation, insightContent.ChartType)
			// Add query information
			for i, query := range insightContent.Queries {
				content += fmt.Sprintf("\nQuery %d: %s (alias: %s)", i+1, query.Query, query.Alias)
			}
			if len(insightContent.Results) > 0 {
				for _, result := range insightContent.Results {
					if result.GetError() != "" {
						content += fmt.Sprintf("\nError: %s", result.GetError())
					} else {
						if result.GetMatrix() != nil {
							formatter := NewCompactMatrixFormatter()
							formattedResult := formatter.FormatMatrix(result.GetMatrix())
							content += fmt.Sprintf("\nid: %s\nalias: %s\n%s", result.GetId(), result.GetAlias(), formattedResult)
						}
					}
				}
			}
		}

		if msg.Role == protos.Message_ROLE_USER {
			var usedTables []string
			var usedMetrics []string
			for _, resource := range msg.Resources {
				if strings.Contains(resource.Uri, "tables") {
					usedTables = append(usedTables, resource.GetName())
				}
				if strings.Contains(resource.Uri, "metrics") {
					usedMetrics = append(usedMetrics, resource.GetName())
				}
			}
			if len(usedTables) > 0 {
				content += fmt.Sprintf("\nPlease ONLY use the following tables: %v", strings.Join(usedTables, ", "))
			}
			if len(usedMetrics) > 0 {
				content += fmt.Sprintf("\nPlease ONLY use the following metrics: %v", strings.Join(usedMetrics, ", "))
			}
		}

		messages = append(messages, &api.Message{
			Role:    api.Message_Role(msg.Role),
			Content: content,
		})
	}

	// Handle different scenarios
	switch session.Context.Scenario {
	case protos.Context_SCENARIO_SQL:
		sqlProcessor := NewSQLScenarioProcessor(s)
		return sqlProcessor.ProcessSQL(ctx, session, messages, schema, sessionID)
	case protos.Context_SCENARIO_INSIGHT:
		insightProcessor := NewInsightScenarioProcessor(s)
		return insightProcessor.ProcessInsight(ctx, session, messages, metricInfo, sessionID)
	case protos.Context_SCENARIO_AUTO:
		autoProcessor := NewAutoScenarioProcessor(s)
		return autoProcessor.ProcessAuto(ctx, session, messages, schema, metricInfo, sessionID)
	default:
		return fmt.Errorf("unsupported scenario: %v", session.Context.Scenario)
	}
}

// addErrorMessageToSession adds an error message to a session
func (s *Service) addErrorMessageToSession(session *protos.ChatSession, errorMessage string) {
	errorMsg := &protos.Message{
		Role: protos.Message_ROLE_ASSISTANT,
		Content: &protos.Message_Structured{
			Structured: &protos.StructuredContent{
				Type: protos.StructuredContent_CONTENT_TYPE_ERROR,
				Payload: &protos.StructuredContent_Error{
					Error: &protos.ErrorContent{
						Code:    "processing_error",
						Message: errorMessage,
					},
				},
			},
		},
		IsFinal: true,
	}
	session.Messages = append(session.Messages, errorMsg)
}

// QueryChatSession retrieves information about an existing chat session
func (s *Service) QueryChatSession(ctx context.Context, req *protos.QueryChatSessionRequest) (*protos.ChatSession, error) {
	// Pass auth headers
	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	// Check if session ID is valid
	if req.SessionId == "" {
		return nil, status.Error(codes.InvalidArgument, "session_id cannot be empty")
	}

	// Retrieve the session from Redis
	chatSession, err := s.getChatSessionFromRedis(ctx, req.SessionId)
	if err != nil {
		return nil, err
	}

	// If the session has project info, verify user has access to that project
	if chatSession.Context != nil &&
		chatSession.Context.ProjectOwner != "" &&
		chatSession.Context.ProjectSlug != "" {
		_, _, err := s.authManager.RequiredLoginForProjectOwnerAndSlug(
			ctx,
			chatSession.Context.ProjectOwner,
			chatSession.Context.ProjectSlug,
			auth.READ,
		)
		if err != nil {
			return nil, status.Errorf(
				codes.PermissionDenied,
				"You don't have permission to access this chat session: %v",
				err,
			)
		}
	}

	messagesToReturn := make([]*protos.Message, 0)
	if len(chatSession.Messages) > int(req.CursorPosition) {
		messagesToReturn = chatSession.Messages[req.CursorPosition:]

		// If client has seen all messages and the last message is final, clean up the session
		// Only delete the session if preserve_session is false
		if len(messagesToReturn) > 0 && messagesToReturn[len(messagesToReturn)-1].IsFinal && !chatSession.PreserveSession {
			// Delete session from Redis in the background
			go func() {
				// Create a new background context since the original one may be canceled after function returns
				bgCtx := context.Background()
				if err := s.deleteSessionFromRedis(bgCtx, req.SessionId); err != nil {
					log.Errorf("Failed to delete session from Redis: %v", err)
				}
			}()
		}
	}

	return &protos.ChatSession{
		Messages:        messagesToReturn,
		Context:         chatSession.Context,
		Streaming:       chatSession.Streaming,
		PreserveSession: chatSession.PreserveSession,
	}, nil
}

// createSessionLock creates a distributed lock for a chat session
func (s *Service) createSessionLock(sessionID string) *redislock.RWLock {
	lockKey := getSessionLockKey(sessionID)
	return &redislock.RWLock{
		Key:              lockKey,
		Token:            identifier.TemplateID(),
		Expiration:       sessionLockTTL,
		SubUnlockTimeout: sessionLockTTL * 2,
		Client:           s.redisClient,
	}
}

// PostSessionMessage adds a new message to an existing chat session
func (s *Service) PostSessionMessage(ctx context.Context, req *protos.PostSessionMessageRequest) (*protos.PostSessionMessageResponse, error) {
	// Pass auth headers
	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	// Check if session ID is valid
	if req.SessionId == "" {
		return nil, status.Error(codes.InvalidArgument, "session_id cannot be empty")
	}

	// Validate request
	if req.Message == nil {
		return nil, status.Error(codes.InvalidArgument, "message cannot be empty")
	}

	// Throw error if not a USER message
	if req.Message.Role != protos.Message_ROLE_USER {
		return nil, status.Error(codes.InvalidArgument, "message role must be USER")
	}

	var chatSession *protos.ChatSession
	var schema *api.SQLSchema
	var metricInfo []*api.MetricInfo

	// Create a mutex for this session
	sessionLock := s.createSessionLock(req.SessionId)

	var projectID string
	// Try to acquire the lock with a timeout - use write mode for exclusive access
	// This first lock is just to update the session with the user's message
	lockErr := sessionLock.CriticalSection(ctx, redislock.ModeWrite, func(lockCtx context.Context) error {
		// Retrieve the session from Redis
		var err error
		chatSession, err = s.getChatSessionFromRedis(lockCtx, req.SessionId)
		if err != nil {
			return err
		}

		// Add the new message to the session
		chatSession.Messages = append(chatSession.Messages, req.Message)

		// Only fetch schema if project info is provided
		if chatSession.Context != nil && chatSession.Context.ProjectOwner != "" && chatSession.Context.ProjectSlug != "" {
			// Verify user has access to the project
			identity, project, err := s.authManager.RequiredLoginForProjectOwnerAndSlug(
				lockCtx,
				chatSession.Context.ProjectOwner,
				chatSession.Context.ProjectSlug,
				auth.READ,
			)
			projectID = project.ID
			if err != nil {
				return status.Errorf(
					codes.PermissionDenied,
					"You don't have permission to access this chat session: %v",
					err,
				)
			}

			defer s.usageClient.Go(lockCtx, identity, project, ApiSku, WebuiSku, &err, "version", chatSession.Context.Version)()

			if chatSession.Context.Scenario == protos.Context_SCENARIO_AUTO || chatSession.Context.Scenario == protos.Context_SCENARIO_SQL {
				// Get schema for the project
				schema, err = s.getTableSchema(lockCtx, chatSession.Context.ProjectOwner, chatSession.Context.ProjectSlug, chatSession.Context.Version)
				if err != nil {
					return err
				}
			}

			if chatSession.Context.Scenario == protos.Context_SCENARIO_AUTO || chatSession.Context.Scenario == protos.Context_SCENARIO_INSIGHT {
				// Get metrics info for the project
				metricInfo, err = s.getMetricsInfo(lockCtx, projectID, chatSession.Context.Version)
				if err != nil {
					return err
				}
			}
		} else {
			// Use empty schema when project info is not provided
			schema = &api.SQLSchema{
				Tables: make([]*api.SQLSchema_SQLTable, 0),
			}
			metricInfo = []*api.MetricInfo{}
		}

		// Save the updated session to Redis
		if err := s.saveSessionToRedis(lockCtx, req.SessionId, chatSession); err != nil {
			log.Errorf("Failed to update session in Redis: %v", err)
			return status.Errorf(codes.Internal, "failed to update session: %v", err)
		}

		return nil
	})

	if lockErr != nil {
		if errors.Is(lockErr, context.DeadlineExceeded) || errors.Is(lockErr, redislock.ErrLost) {
			return nil, status.Error(codes.ResourceExhausted, "session is currently being processed, please try again later")
		}
		return nil, status.Errorf(codes.Internal, "failed to process message: %v", lockErr)
	}

	// Create a background context with auth headers for the goroutine
	bgCtx := metadata.NewOutgoingContext(context.Background(), outgoingMD)

	// Now start processing in a separate goroutine
	// This will acquire its own lock, not nested with the previous one
	go func(session *protos.ChatSession, sqlSchema *api.SQLSchema, metricInfo []*api.MetricInfo) {
		processLock := s.createSessionLock(req.SessionId)

		err := processLock.CriticalSection(bgCtx, redislock.ModeWrite, func(processCtx context.Context) error {
			return s.processChatSessionInBackground(processCtx, session, sqlSchema, metricInfo, req.SessionId, projectID)
		})

		if err != nil {
			log.Errorf("Failed to process chat session in background: %v", err)
		}
	}(chatSession, schema, metricInfo)

	// Return the current cursor position
	return &protos.PostSessionMessageResponse{
		CurrentCursorPosition: int32(len(chatSession.Messages)),
	}, nil
}

// executeQuery executes a SQL query against the analytic service
func (s *Service) executeQuery(ctx context.Context, query string, projectOwner string, projectSlug string, version int32) (*commonProtos.TabularData, error) {
	// Create a query request with SQLQuery
	sqlQuery := &analyticProtos.SQLQuery{
		Sql: query,
	}

	request := &analyticProtos.SQLRequest{
		ProjectOwner: projectOwner,
		ProjectSlug:  projectSlug,
		Version:      version,
		QueryType: &analyticProtos.SQLRequest_SqlQuery{
			SqlQuery: sqlQuery,
		},
		Source: analyticProtos.Source_SQL_EDITOR,
	}

	// Execute the query
	response, err := s.analyticService.ExecuteSQL(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("query execution failed: %v", err)
	}

	// Check if there's an error in the response
	if response.GetError() != "" {
		return nil, fmt.Errorf("query execution error: %s", response.GetError())
	}

	return response.GetResult(), nil
}

// executeInsightQuery executes insight queries and returns the Matrix result
func (s *Service) executeInsightQuery(ctx context.Context, queries []*commonProtos.Query, formulas []*commonProtos.Formula, timeRange *commonProtos.TimeRangeLite, projectOwner string, projectSlug string, version int32, samplesLimit int32) ([]*protos.InsightQueryResult, error) {
	// Call the observability service for metrics queries
	resp, err := s.metricService.QueryRange(ctx, &o11yProtos.QueryRangeRequest{
		ProjectOwner: projectOwner,
		ProjectSlug:  projectSlug,
		Version:      version,
		Queries:      queries,
		Formulas:     formulas,
		TimeRange:    timeRange,
		SamplesLimit: samplesLimit,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to execute insight query: %w", err)
	}

	results := make([]*protos.InsightQueryResult, 0, len(resp.Results))
	for _, result := range resp.Results {
		if result.GetError() != "" {
			results = append(results, &protos.InsightQueryResult{
				ResultType: &protos.InsightQueryResult_Error{Error: result.GetError()},
			})
			continue
		}

		// Convert the metrics matrix to common Matrix
		metricsMatrix := result.GetMatrix()
		if metricsMatrix == nil {
			continue
		}

		queryResult := &protos.InsightQueryResult{
			Id:    result.GetId(),
			Alias: result.GetAlias(),
			ResultType: &protos.InsightQueryResult_Matrix{
				Matrix: &commonProtos.Matrix{
					Samples: make([]*commonProtos.Matrix_Sample, 0, len(metricsMatrix.Samples)),
				},
			},
		}

		for _, sample := range metricsMatrix.Samples {
			matrixSample := &commonProtos.Matrix_Sample{
				Metric: &commonProtos.Matrix_Metric{
					Name:        sample.Metric.Name,
					Labels:      sample.Metric.Labels,
					DisplayName: sample.Metric.DisplayName,
				},
				Values: make([]*commonProtos.Matrix_Value, 0, len(sample.Values)),
			}

			for _, value := range sample.Values {
				matrixSample.Values = append(matrixSample.Values, &commonProtos.Matrix_Value{
					Timestamp: value.Timestamp,
					Value:     value.Value,
				})
			}

			queryResult.ResultType.(*protos.InsightQueryResult_Matrix).Matrix.Samples = append(queryResult.ResultType.(*protos.InsightQueryResult_Matrix).Matrix.Samples, matrixSample)
		}

		results = append(results, queryResult)
	}

	return results, nil
}

func (s *Service) SQLCompletion(ctx context.Context, req *protos.SQLCompletionRequest) (*protos.SQLCompletionResponse, error) {
	// Get schema from analytic service
	schema, err := s.getTableSchema(ctx, req.ProjectOwner, req.ProjectSlug, req.Version)
	if err != nil {
		return nil, err
	}

	if len(req.Messages) == 0 {
		return nil, status.Error(codes.InvalidArgument, "messages cannot be empty")
	}

	request := &api.SQLRequest{
		Project: fmt.Sprintf("%s/%s", req.ProjectOwner, req.ProjectSlug),
		Version: req.Version,
		Messages: utils.MapSliceNoError(req.Messages, func(t *protos.AiMessage) *api.Message {
			return &api.Message{
				Role:    api.Message_Role(t.Role),
				Content: t.Content,
			}
		}),
		Schema: schema,
	}

	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	response, err := s.aiService.SQL(ctx, request)
	if err != nil {
		return nil, err
	}

	ret := &protos.SQLCompletionResponse{
		Choices: utils.MapSliceNoError(response.Choices, func(t *api.Message) *protos.AiMessage {
			return &protos.AiMessage{
				Content: t.Content,
				Role:    protos.AiRole(t.Role),
				RunId:   t.RunId,
			}
		}),
	}

	return ret, nil
}

func (s *Service) FixSQL(ctx context.Context, req *protos.FixSQLRequest) (*protos.FixSQLResponse, error) {
	// Get schema from analytic service
	schema, err := s.getTableSchema(ctx, req.ProjectOwner, req.ProjectSlug, req.Version)
	if err != nil {
		return nil, err
	}

	if len(req.Messages) == 0 {
		return nil, status.Error(codes.InvalidArgument, "messages cannot be empty")
	}

	request := &api.FixSQLRequest{
		Project: fmt.Sprintf("%s/%s", req.ProjectOwner, req.ProjectSlug),
		Version: req.Version,
		Messages: utils.MapSliceNoError(req.Messages, func(t *protos.AiMessage) *api.Message {
			return &api.Message{
				Role:    api.Message_Role(t.Role),
				Content: t.Content,
			}
		}),
		Error:  req.Error,
		Schema: schema,
	}

	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	response, err := s.aiService.FixSQL(ctx, request)
	if err != nil {
		return nil, err
	}

	ret := &protos.FixSQLResponse{
		Choices: utils.MapSliceNoError(response.Choices, func(t *api.Message) *protos.AiMessage {
			return &protos.AiMessage{
				Content: t.Content,
				Role:    protos.AiRole(t.Role),
				RunId:   t.RunId,
			}
		}),
	}

	return ret, nil
}

// convertToChartType converts a string chart type to the corresponding ChartType enum value
// The comparison is case-insensitive
func convertToChartType(chartType string) protos.ChartType {
	// Convert to uppercase for case-insensitive comparison
	upperChartType := strings.ToUpper(chartType)

	switch upperChartType {
	case "LINE":
		return protos.ChartType_CHART_TYPE_LINE
	case "BAR":
		return protos.ChartType_CHART_TYPE_BAR
	case "PIE":
		return protos.ChartType_CHART_TYPE_PIE
	case "TABLE":
		return protos.ChartType_CHART_TYPE_TABLE
	default:
		// Default to UNSPECIFIED if unrecognized
		return protos.ChartType_CHART_TYPE_UNSPECIFIED
	}
}

func convertToTableType(tableType analyticProtos.Table_TableType) api.SQLSchema_SQLTable_TableType {
	switch tableType {
	case analyticProtos.Table_SYSTEM:
		return api.SQLSchema_SQLTable_SYSTEM
	case analyticProtos.Table_MATERIALIZED_VIEW:
		return api.SQLSchema_SQLTable_MATERIALIZED_VIEW
	case analyticProtos.Table_RESERVED:
		return api.SQLSchema_SQLTable_RESERVED
	case analyticProtos.Table_ENTITY:
		return api.SQLSchema_SQLTable_ENTITY
	case analyticProtos.Table_EVENT:
		return api.SQLSchema_SQLTable_EVENT
	case analyticProtos.Table_IMPORTED_SUBGRAPH:
		return api.SQLSchema_SQLTable_IMPORTED_SUBGRAPH
	case analyticProtos.Table_SUBGRAPH:
		return api.SQLSchema_SQLTable_SUBGRAPH
	case analyticProtos.Table_IMPORTED_ENTITY:
		return api.SQLSchema_SQLTable_IMPORTED_ENTITY
	case analyticProtos.Table_METRICS:
		return api.SQLSchema_SQLTable_METRICS
	case analyticProtos.Table_USER_REFRESHABLE_VIEW:
		return api.SQLSchema_SQLTable_USER_REFRESHABLE_VIEW
	case analyticProtos.Table_DASH_CURATED_ENTITY:
		return api.SQLSchema_SQLTable_ENTITY
	case analyticProtos.Table_DASH_CURATED_EVENT:
		return api.SQLSchema_SQLTable_EVENT
	case analyticProtos.Table_DASH_CURATED_SUBGRAPH:
		return api.SQLSchema_SQLTable_SUBGRAPH
	default:
		return api.SQLSchema_SQLTable_SYSTEM
	}
}

func (s *Service) SQLAgentChat(ctx context.Context, req *protos.SQLAgentRequest) (*protos.SQLAgentResponse, error) {
	// Get schema from analytic service
	schema, err := s.getTableSchema(ctx, req.ProjectOwner, req.ProjectSlug, req.Version)
	if err != nil {
		return nil, err
	}

	if len(req.Messages) == 0 {
		return nil, status.Error(codes.InvalidArgument, "messages cannot be empty")
	}

	request := &api.SQLAgentRequest{
		Messages: utils.MapSliceNoError(req.Messages, func(t *protos.AiMessage) *api.Message {
			return &api.Message{
				Role:    api.Message_Role(t.Role),
				Content: t.Content,
			}
		}),
		Schema:  schema,
		Project: fmt.Sprintf("%s/%s", req.ProjectOwner, req.ProjectSlug),
		Version: req.Version,
	}

	// pass auth headers
	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	response, err := s.aiService.SQLAgentChat(ctx, request)
	if err != nil {
		return nil, err
	}

	ret := &protos.SQLAgentResponse{
		Type:        response.Type,
		Content:     response.Content,
		Sql:         response.Sql,
		ChartType:   convertToChartType(response.ChartType),
		Explanation: response.Explanation,
		Title:       response.Title,
		Questions:   response.Questions,
		RunId:       response.RunId,
	}

	return ret, nil
}

func (s *Service) SubmitFeedback(ctx context.Context, req *protos.SubmitFeedbackRequest) (*emptypb.Empty, error) {
	request := &api.SubmitFeedbackRequest{
		RunId:        req.RunId,
		ProjectOwner: req.ProjectOwner,
		ProjectSlug:  req.ProjectSlug,
		Score:        req.Score,
	}

	_, err := s.aiService.SubmitFeedback(ctx, request)
	return &emptypb.Empty{}, err
}

func (s *Service) SaveHistoryChat(ctx context.Context, req *protos.SaveHistoryChatRequest) (*protos.SaveHistoryChatResponse, error) {
	// Authenticate user
	identity, err := s.authManager.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	// Validate request
	if req.HistoryChat == nil {
		return nil, status.Error(codes.InvalidArgument, "history_chat cannot be empty")
	}

	// Create a new AiChatHistory model and populate it from the request
	chatHistory := &models.AiChatHistory{}

	// Get project ID if project owner and slug are provided
	projectID := ""
	if req.GetProjectOwner() != "" && req.GetProjectSlug() != "" {
		_, project, err := s.authManager.RequiredLoginForProjectOwnerAndSlug(ctx, req.GetProjectOwner(), req.GetProjectSlug(), auth.READ)
		if err == nil && project != nil {
			projectID = project.ID
		}
	}

	chatHistory.FromPB(req.HistoryChat, projectID, identity.UserID)

	// Save to database
	if err := s.db.Create(chatHistory).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save chat history: %v", err)
	}

	return &protos.SaveHistoryChatResponse{
		Id: chatHistory.ID,
	}, nil
}

func (s *Service) DeleteHistoryChat(ctx context.Context, req *protos.DeleteHistoryChatRequest) (*emptypb.Empty, error) {
	// Authenticate user
	identity, err := s.authManager.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	// Find the chat history by ID and user ID
	var chatHistory models.AiChatHistory
	result := s.db.Where("id = ? AND user_id = ?", req.Id, identity.UserID).First(&chatHistory)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "chat history not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find chat history: %v", result.Error)
	}

	// Delete the chat history
	if err := s.db.Delete(&chatHistory).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to delete chat history: %v", err)
	}

	return &emptypb.Empty{}, nil
}

func (s *Service) UpdateHistoryChat(ctx context.Context, req *protos.UpdateHistoryChatRequest) (*emptypb.Empty, error) {
	// Validate request
	if req.HistoryChat == nil {
		return nil, status.Error(codes.InvalidArgument, "history_chat cannot be empty")
	}

	// Authenticate user
	identity, err := s.authManager.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	// Find the chat history by ID and user ID to ensure it belongs to the user
	var chatHistory models.AiChatHistory
	result := s.db.Where("id = ? AND user_id = ?", req.HistoryChat.Id, identity.UserID).First(&chatHistory)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "chat history not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find chat history: %v", result.Error)
	}

	// Determine project ID to use
	projectID := chatHistory.ProjectID // Default to keeping the existing project ID

	// If project information is provided in the request, update the project ID
	if req.GetProjectOwner() != "" && req.GetProjectSlug() != "" {
		_, project, err := s.authManager.RequiredLoginForProjectOwnerAndSlug(ctx, req.GetProjectOwner(), req.GetProjectSlug(), auth.READ)
		if err == nil && project != nil {
			projectID = project.ID // Update to the new project ID
		}
	}

	// Update the chat history
	chatHistory.FromPB(req.HistoryChat, projectID, identity.UserID)

	if err := s.db.Save(&chatHistory).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to update chat history: %v", err)
	}

	return &emptypb.Empty{}, nil
}

func (s *Service) GetHistoryChats(ctx context.Context, req *protos.GetHistoryChatsRequest) (*protos.GetHistoryChatsResponse, error) {
	// Authenticate user
	identity, err := s.authManager.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	// Get all chat histories for the current user
	// Select only the columns needed for SimplifiedHistoryChat, excluding the Messages column which can be large
	var chatHistories []models.AiChatHistory
	if err := s.db.Select("id, project_id, user_id, title, type, meta, created_at, updated_at").Where("user_id = ?", identity.UserID).Order("updated_at DESC").Find(&chatHistories).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get chat histories: %v", err)
	}

	// Convert to simplified protobuf response without messages data
	response := &protos.GetHistoryChatsResponse{
		HistoryChats: make([]*protos.SimplifiedHistoryChat, 0, len(chatHistories)),
	}

	for _, history := range chatHistories {
		response.HistoryChats = append(response.HistoryChats, history.ToSimplifiedPB())
	}

	return response, nil
}

func (s *Service) GetHistoryChatById(ctx context.Context, req *protos.GetHistoryChatByIdRequest) (*protos.GetHistoryChatByIdResponse, error) {
	// Authenticate user
	identity, err := s.authManager.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}

	// Get the specific chat history by ID for the current user
	var chatHistory models.AiChatHistory
	if err := s.db.Where("id = ? AND user_id = ?", req.Id, identity.UserID).First(&chatHistory).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, status.Errorf(codes.NotFound, "chat history not found: %v", err)
		}
		return nil, status.Errorf(codes.Internal, "failed to get chat history: %v", err)
	}

	// Convert to protobuf response with full details including messages
	historyChat := chatHistory.ToPB()

	// If projectID exists, fetch project owner and slug
	if chatHistory.ProjectID != "" {
		// Get project information
		var project commonModels.Project
		err = s.db.Where(commonModels.Project{ID: chatHistory.ProjectID}).First(&project).Error
		if err == nil {
			// Add project owner and slug to the response
			ownerName := project.GetOwnerName()
			historyChat.ProjectOwner = &ownerName
			historyChat.ProjectSlug = &project.Slug
		}
	}

	response := &protos.GetHistoryChatByIdResponse{
		HistoryChat: historyChat,
	}

	return response, nil
}

func (s *Service) CreateSharingChat(ctx context.Context, req *protos.CreateSharingChatRequest) (*protos.CreateSharingChatResponse, error) {
	// Get chat history by ID
	var chatHistory models.AiChatHistory
	result := s.db.Where("id = ?", req.ChatId).First(&chatHistory)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "chat history not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find chat history: %v", result.Error)
	}

	identity, project, err := s.authManager.RequiredLoginForProject(ctx, chatHistory.ProjectID, auth.READ)
	if err != nil {
		return nil, err
	}

	// Create a new SharingChat model and populate it from the request
	sharingChat := &models.SharingChat{}
	history := chatHistory.ToPB()
	sharingChat.FromHistoryChat(history, project.ID, identity.UserID)

	// Set custom title if provided
	if req.Title != "" {
		sharingChat.Title = req.Title
	}

	// Check if a sharing chat already exists for this history chat
	var existingSharingChat models.SharingChat
	var istExist bool = false
	result = s.db.Where("ai_chat_id = ?", req.ChatId).First(&existingSharingChat)
	if result.Error == nil {
		// A sharing chat already exists, return its ID
		sharingChat.ID = existingSharingChat.ID
		istExist = true
	} else if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
		// An error occurred other than record not found
		return nil, status.Errorf(codes.Internal, "failed to check existing sharing chat: %v", result.Error)
	}

	// Save to database
	if istExist {
		if err := s.db.Save(sharingChat).Error; err != nil {
			return nil, status.Errorf(codes.Internal, "failed to update sharing chat: %v", err)
		}
	} else {
		if err := s.db.Create(sharingChat).Error; err != nil {
			return nil, status.Errorf(codes.Internal, "failed to save sharing chat: %v", err)
		}
	}

	return &protos.CreateSharingChatResponse{
		Id: sharingChat.ID,
	}, nil
}

func (s *Service) UpdateSharingChat(ctx context.Context, req *protos.UpdateSharingChatRequest) (*emptypb.Empty, error) {
	// Get chat history by ID
	var chatHistory models.AiChatHistory
	result := s.db.Where("id = ?", req.ChatId).First(&chatHistory)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "chat history not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find chat history: %v", result.Error)
	}

	identity, project, err := s.authManager.RequiredLoginForProject(ctx, chatHistory.ProjectID, auth.READ)
	if err != nil {
		return nil, err
	}

	// query sharing chat by chat history id
	var prev models.SharingChat
	result = s.db.Where("ai_chat_id = ? AND project_id = ? AND user_id = ?", req.ChatId, project.ID, identity.UserID).First(&prev)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "sharing chat not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find sharing chat: %v", result.Error)
	}

	// Update the sharing chat
	var newModel models.SharingChat
	newModel.FromHistoryChat(chatHistory.ToPB(), project.ID, identity.UserID)
	newModel.ID = prev.ID
	newModel.Public = true
	if req.Title != "" {
		newModel.Title = req.Title
	}

	// Save the updated sharing chat
	if err := s.db.Save(&newModel).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to update sharing chat: %v", err)
	}

	return &emptypb.Empty{}, nil
}

func (s *Service) DeleteSharingChat(ctx context.Context, req *protos.DeleteSharingChatRequest) (*emptypb.Empty, error) {
	// Find the sharing chat by ID and project ID to ensure it belongs to the project
	var sharingChat models.SharingChat
	result := s.db.Where("id = ?", req.GetId()).First(&sharingChat)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "sharing chat not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find sharing chat: %v", result.Error)
	}

	// check for auth
	identity, err := s.authManager.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	if identity.UserID != sharingChat.UserID {
		return nil, status.Error(codes.PermissionDenied, "user not authorized to delete this sharing chat")
	}

	// Instead of deleting, set public to false, hide the sharing chat
	sharingChat.Public = false
	if err := s.db.Save(&sharingChat).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "failed to update sharing chat: %v", err)
	}

	return &emptypb.Empty{}, nil
}

func (s *Service) GetSharingById(ctx context.Context, req *protos.GetSharingByIdRequest) (*protos.GetSharingChatResponse, error) {
	// Get the sharing chat by ID
	var sharingChat models.SharingChat
	result := s.db.Where("id = ?", req.Id).First(&sharingChat)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "sharing chat not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find sharing chat: %v", result.Error)
	}
	if sharingChat.Public == false {
		return nil, status.Error(codes.PermissionDenied, "sharing chat not found")
	}

	var project commonModels.Project
	result = s.db.Where("id = ?", sharingChat.ProjectID).First(&project)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "project not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find project: %v", result.Error)
	}

	// Convert to protobuf response
	response := &protos.GetSharingChatResponse{
		SharingChat: sharingChat.ToPB(),
		Project:     project.ToPB(),
	}

	return response, nil
}

func (s *Service) GetSharingByChat(ctx context.Context, req *protos.GetSharingByChatRequest) (*protos.GetSharingChatResponse, error) {
	// Get the sharing chat by ID
	var sharingChat models.SharingChat
	result := s.db.Where("ai_chat_id = ?", req.ChatId).First(&sharingChat)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, status.Error(codes.NotFound, "sharing chat not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to find sharing chat: %v", result.Error)
	}

	if sharingChat.Public == false {
		return nil, status.Error(codes.PermissionDenied, "sharing chat not found")
	}

	identity, err := s.authManager.RequiredLogin(ctx)
	if err != nil {
		return nil, err
	}
	if identity.UserID != sharingChat.UserID {
		return nil, status.Error(codes.PermissionDenied, "user not authorized to get this sharing chat")
	}

	// Convert to protobuf response
	response := &protos.GetSharingChatResponse{
		SharingChat: sharingChat.ToPB(),
	}

	return response, nil
}

func (s *Service) SuggestReply(ctx context.Context, req *protos.ChatSession) (*api.SuggestReplyResponse, error) {
	// Convert messages to the format expected by the AI service
	messages := make([]*api.Message, 0, len(req.Messages))

	for _, msg := range req.Messages {
		var content string
		if msg.GetText() != "" {
			content = msg.GetText()
		} else if msg.GetStructured() != nil && msg.GetStructured().GetSql() != nil {
			sqlContent := msg.GetStructured().GetSql()
			content = fmt.Sprintf("Title: %s\nSQL Query: %s\nExplanation: %s\nChartType:%s", sqlContent.Title, sqlContent.Query, sqlContent.Explanation, sqlContent.ChartType)
			if sqlContent.GetResult() != nil {
				formatter := NewTabularFormatter()
				formattedResult := formatter.FormatTabularData(sqlContent.GetResult())
				content += fmt.Sprintf("\n%s", formattedResult)
			} else if sqlContent.GetError() != "" {
				content += fmt.Sprintf("\nError: %s", sqlContent.GetError())
			}
		} else if msg.GetStructured() != nil && msg.GetStructured().GetInsightQuery() != nil {
			insightContent := msg.GetStructured().GetInsightQuery()
			content = fmt.Sprintf("Title: %s\nExplanation: %s\nChartType:%s", insightContent.Title, insightContent.Explanation, insightContent.ChartType)
			// Add query information
			for i, query := range insightContent.Queries {
				content += fmt.Sprintf("\nQuery %d: %s (alias: %s)", i+1, query.Query, query.Alias)
			}
			if len(insightContent.Results) > 0 {
				for _, result := range insightContent.Results {
					if result.GetError() != "" {
						content += fmt.Sprintf("\nError: %s", result.GetError())
					} else {
						if result.GetMatrix() != nil {
							formatter := NewCompactMatrixFormatter()
							formattedResult := formatter.FormatMatrix(result.GetMatrix())
							content += fmt.Sprintf("\nid: %s\nalias: %s\n%s", result.GetId(), result.GetAlias(), formattedResult)
						}
					}
				}
			}
		}

		messages = append(messages, &api.Message{
			Role:    api.Message_Role(msg.Role),
			Content: content,
		})
	}

	// Create SuggestReply request
	request := &api.SuggestReplyRequest{
		Messages: messages,
	}

	// Call the AI service
	response, err := s.aiService.SuggestReply(ctx, request)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// GenerateProcessor proxies to the Python AI server to generate a Sentio processor
func (s *Service) GenerateProcessor(ctx context.Context, req *api.ProcessorGenerateRequest) (*api.ProcessorGenerateResponse, error) {
	// Pass auth headers
	outgoingMD := auth.CopyAuthHeadersToMetaData(ctx)
	ctx = metadata.NewOutgoingContext(ctx, outgoingMD)

	resp, err := s.aiService.GenerateProcessor(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

/*
func (s *Service) ChartCompletion(ctx context.Context, req *protos.ChartCompletionRequest) (*protos.ChartCompletionResponse, error) {
	_, project, err := s.authManager.RequiredLoginForProjectOwnerAndSlug(ctx, req.ProjectOwner, req.ProjectSlug, auth.READ)
	if err != nil {
		return nil, err
	}
	resp, err := s.analyticService.GetEvents(ctx, &analyticProtos.GetEventsRequest{
		ProjectSlug: "",
		ProjectId:   project.ID,
		Version:     req.Version,
	})
	if err != nil {
		return nil, err
	}
	events := utils.MapSliceNoError(resp.Events, func(v *analyticProtos.Event) string {
		return v.Name
	})

	resp1, err := s.metricService.GetMetrics(ctx, &protosObservability.GetMetricsRequest{
		ProjectId: project.ID,
		Version:   req.Version,
	})
	if err != nil {
		return nil, err
	}

	metrics := utils.MapSliceNoError(resp1.Metrics, func(v *protosObservability.MetricInfo) string {
		return v.Name
	})

	if len(req.Messages) == 0 {
		return nil, status.Error(codes.InvalidArgument, "messages cannot be empty")
	}

	choices, err := s.gpt.ChartCompletion(ctx, req.DashboardId, metrics, events, req.Messages[0].Content)
	if err != nil {
		return nil, err
	}
	ret := &protos.ChartCompletionResponse{
		Choices: utils.MapSliceNoError(choices, func(v string) *protos.AiMessage {
			return &protos.AiMessage{
				Content: v,
				Role:    protos.AiRole_AI_ROLE_ASSISTANT,
			}
		}),
	}

	return ret, nil
}*/
