import re
from typing import List, Dict, Any
from uuid import UUID
import time

from langchain_openai import ChatOpenAI
from langchain.prompts import HumanMessagePromptTemplate, AIMessagePromptTemplate, \
	SystemMessagePromptTemplate, ChatPromptTemplate
from langchain.callbacks.base import <PERSON>CallbackHand<PERSON>
from pydantic import BaseModel, Field
from langchain.schema.runnable import RunnableConfig, RunnableLambda
from service.ai.server.models.model_registry import ModelRegistry
import tiktoken

from sql_api import SqlApi

systemPromptTemplate = """You are an AI database engineer tasked with generating and correcting SQL queries for a ClickHouse database. Follow these guidelines:

1. **Schema Awareness**: Use only the column names provided in the schema. Avoid querying non-existent columns.
2. **Table and Column Context**: Ensure you know which column belongs to which table. Qualify column names with their table names when necessary.
3. **Schema and Field Information**: The database schema is provided below, enclosed in triple backticks:
   ```
   {schema}
   ```
4. **Error Handling**: Handle errors based on their type:
   
   For syntax errors:
   - Analyze the error message and correct the query
   - For long queries, provide the correction in a unified diff format like:
   ```
   @@ <chunk name A> @@
   - <original problematic lines>
   + <fixed lines>
   @@ <chunk name B> @@
   - <original problematic lines>
   + <fixed lines>
   ```
   - For shorter queries, just provide the corrected SQL directly.

   For performance errors (e.g., MEMORY_LIMIT_EXCEEDED, TIMEOUT_EXCEEDED):
   - Focus on optimizing the query rather than syntax fixes
   - Provide optimization suggestions in a unified diff format, such as:
     * Adding more specific WHERE clauses to reduce data volume
     * Using appropriate indexes
     * Breaking complex JOINs into simpler parts
     * Adding LIMIT clauses for testing
     * Using aggregations to reduce data volume
     * Optimizing JOIN conditions and order
     * Consider using materialized views or pre-aggregations

5. **Output Requirements**: Your SQL output must be syntactically correct for ClickHouse and end with a semicolon.

Generate or correct the SQL query based on the above instructions.
"""

unified_diff_reminder = """#File editing rules:
Return edits similar to unified diffs that `diff -U0` would produce.

Start each hunk of changes with a `@@ ... @@` line.
Don't include line numbers like `diff -U0` does.

The user's patch tool needs CORRECT patches that apply cleanly against the current contents of the file!
Think carefully and make sure you include and mark all lines that need to be removed or changed as `-` lines.
Make sure you mark all new or modified lines with `+`.
Don't leave out any lines or the diff patch won't apply correctly.

Indentation matters in the diffs!

Start a new hunk for each section of the file that needs changes.

Only output hunks that specify changes with `+` or `-` lines.
Skip any hunks that are entirely unchanging ` ` lines.

Output hunks in whatever order makes the most sense.
Hunks don't need to be in any particular order.

When editing a function, method, loop, etc use a hunk to replace the *entire* code block.
Delete the entire existing version with `-` lines and then add a new, updated version with `+` lines.
This will help you generate correct code and correct diffs.
"""

class Answer(BaseModel):
	sql: str = Field(description="the SQL query that answers the user query or the diff format for long queries")

def count_tokens(text: str) -> int:
	"""Count the number of tokens in a text using cl100k_base encoding"""
	encoding = tiktoken.get_encoding("cl100k_base")
	return len(encoding.encode(text))

class SQLChain:

	def __init__(self, model_registry: ModelRegistry, host, sql_token_threshold=600, retry_timeout=80):
		self.model_registry = model_registry
		self.host = host
		self.sql_token_threshold = sql_token_threshold
		self.retry_timeout = retry_timeout  # Timeout in seconds for retries
		
		# Create two LLM instances
		self.llm_large = self.model_registry.get_gpt41(temperature=0.1).with_structured_output(Answer)
		
		self.llm_small = self.model_registry.get_gpt41_mini(temperature=0.1).with_structured_output(Answer)
		

	def run(self, schema: str, history: List[str] = None, auth_headers=None, project=None, version=None):
		if history is None:
			history = []
		if auth_headers is None:
			auth_headers = {}

		sql_api = SqlApi(self.host, project, version, auth_headers)

		# Create message history
		histories = [
			SystemMessagePromptTemplate.from_template(systemPromptTemplate),
		]
		for message in history:
			if message.startswith("Human:"):
				histories.append(HumanMessagePromptTemplate.from_template(
					escape_curly_braces(message.replace("Human:", ""))))
			else:
				histories.append(AIMessagePromptTemplate.from_template(
					escape_curly_braces(message.replace("AI:", ""))))

		handler = SQLChainHandler()
		
		# Start with large model by default
		current_llm = self.llm_large
		start_time = time.time()

		def execute_sql(inputs: Dict[str, Any]) -> Dict[str, Any]:
			nonlocal current_llm
			sql = inputs["sql"]
			sql = normalize(sql)
			
			# Check if we've exceeded the timeout
			if time.time() - start_time > self.retry_timeout:
				raise TimeoutError("SQL execution exceeded retry timeout")
				
			try:
				_, err = sql_api.test(sql)
			except Exception as e:
				err = str(e)

			if not err:
				return {"result": sql}
			else:
				err = escape_curly_braces(err)
				histories.extend([
					AIMessagePromptTemplate.from_template(escape_curly_braces(sql)),
					SystemMessagePromptTemplate.from_template(
						f"Previous SQL failed with error: {err}"),
					HumanMessagePromptTemplate.from_template(
						"Please correct the SQL and try again.")
				])

				# If the failed SQL is longer than threshold, use small model for retry
				current_llm = self.llm_small if count_tokens(sql) > self.sql_token_threshold else self.llm_large

				raise ValueError(f"Failed to execute SQL: {err}")

		# Create main chain with retries
		chain = (
			RunnableLambda(lambda x: ChatPromptTemplate.from_messages(histories))
			| current_llm
			| (lambda x: {"sql": x.sql})
			| RunnableLambda(execute_sql)
		).with_retry(stop_after_attempt=8, retry_if_exception_type=(ValueError,))

		try:
			# Execute chain
			result = chain.invoke(
				{"schema": schema},
				config=RunnableConfig(
					callbacks=[handler],
					tags=["sql-gen"]
				)
			)
		except TimeoutError:
			raise ValueError("SQL execution exceeded retry timeout")

		return {
			"result": result["result"],
			"run_id": str(handler.run_id)
		}

def normalize(sql: str):
	match = re.search(
		r"select(.+)[;$]", sql.strip(), re.MULTILINE | re.IGNORECASE | re.DOTALL
	)
	if match:
		return match.group()
	return sql

def escape_curly_braces(text: str) -> str:
	"""Escape curly braces in text by doubling them."""
	return text.replace("{{", "").replace("}}", "").replace("{", "{{").replace("}", "}}")

class SQLChainHandler(BaseCallbackHandler):
    """Callback handler for capturing SQL chain execution details"""
    
    def __init__(self):
        self.run_id = None
        
    def on_chain_start(
        self, serialized: Dict[str, Any], inputs: Dict[str, Any], run_id: UUID, **kwargs: Any
    ) -> None:
        """Capture run ID when chain starts"""
        self.run_id = run_id

    def on_chain_end(
        self, outputs: Dict[str, Any], run_id: UUID, **kwargs: Any
    ) -> None:
        """Store final outputs with run ID"""
        if run_id == self.run_id:
            self.final_outputs = outputs
