import logging
import os

logging.basicConfig(
	format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
	level=logging.INFO
)
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

if __name__ == "__main__":
	import argparse
	parser = argparse.ArgumentParser()
	parser.add_argument('program', default='sql', help='which program to run, sql or doc or index')
	parser.add_argument("--port", type=int, default=10100)
	parser.add_argument("--openai-api-key", type=str, default="***************************************************")
	parser.add_argument("--openai-org-id", type=str, default='')
	parser.add_argument("--telegram-bot-token", type=str, default="**********************************************")
	parser.add_argument("--data-path", type=str, default=os.path.join(os.path.dirname(__file__), '../docs'))
	parser.add_argument("--debug", type=bool, default=True)
	parser.add_argument("--pinecone-api-key", type=str, default="cee7eb09-0cae-4ef6-99dd-2e8162cda654")
	parser.add_argument("--pinecone-index-name", type=str, default="sentio-doc")
	parser.add_argument("--web-service-host", type=str, default="https://test.sentio.xyz")
	parser.add_argument("--es-url", type=str, default="http://localhost:9200")
	parser.add_argument("--es-user", type=str, default="ai-bot")
	parser.add_argument("--es-password", type=str, default="78zftqQDHnm2.Js")
	parser.add_argument("--top-tables-to-select", type=int, default=3)
	parser.add_argument("--redis", type=str, default="")
	parser.add_argument("--sql-token-threshold", type=int, default=600,
					   help="Token count threshold for switching to smaller model")
	parser.add_argument("--anthropic-api-key", type=str, default="************************************************************************************************************")
	parser.add_argument("--github-token", type=str, default="", help="GitHub personal access token for API authentication")

	args = parser.parse_args()

	if args.program == 'sql':
		import ai_server
		import servicer

		logging.info('starting sql bot service with config: %s', args)

		port = args.port
		service = servicer.Servicer(args.openai_api_key, args.openai_org_id, args.anthropic_api_key, args.top_tables_to_select, args.redis, args.web_service_host, sql_token_threshold=args.sql_token_threshold, github_token=args.github_token)
		ai_server.serve(1, ["[::]:" + str(port)], service)
	elif args.program == 'doc':
		import doc_bot
		logging.info('starting doc bot')
		doc_bot.run(args)
	elif args.program == 'index_pinecone':
		import indexer_pinecone
		logging.info('starting index docs')
		indexer_pinecone.run(args)
	elif args.program == 'index_es':
		import indexer_es
		logging.info('starting index docs')
		indexer_es.run(args)
	else:
		parser.print_usage()