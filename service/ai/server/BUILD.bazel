load("@aspect_rules_py//py:defs.bzl", "py_binary", "py_image_layer")
load("@rules_python//python:defs.bzl", "py_test")
load("//bazel:images.bzl", "create_image_with_tar")

#load("@rules_uv//uv:pip.bzl", "pip_compile")

py_binary(
    name = "server",
    srcs = [
        "agent.py",
        "ai_server.py",
        # "doc_bot.py",
        # "download_docs.py",
        # "indexer_es.py",
        # "indexer_pinecone.py",
        # "qa_chain.py",
        # "sentio_price.py",
        "server.py",
        "servicer.py",
        "agents/sql_agent.py",
        "sql_api.py",
        "sql_chain.py",
        "sql_lang.py",
        "agents/table_selection_agent.py",
        "table_selector.py",
        "table_selector_test.py",
        "utils.py",
        "agents/document_agent.py",
        "agents/table_summarizer_agent.py",
        "table_summarizer.py",
        "models/model_registry.py",
        "agents/suggest_reply_agent.py",
        "agents/sql_chat_agent.py",
        "agents/sql_gen_agent.py",
        "agents/sentio_react_agent.py",
        "agents/web_search_agent.py",
        "agents/insight_agent.py",
        "agents/agent_chat_handler.py",
        "agents/query_routing_agent.py",
        "agents/qa_agent.py",
        "agents/sql_fix_react_agent.py",
        "agents/processor_generator_agent.py",
    ],
    data = [
        "@bazel_tools//tools/bash/runfiles",
    ],
    imports = ["."],
    deps = [
        "//service/ai/api:api_python_grpc",
        # "@pip//elasticsearch",
        # "@pip//gitpython",
        "@pip//langchain",
        "@pip//langchain_community",
        "@pip//langchain_core",
        "@pip//langchain_openai",
        "@pip//langchain_text_splitters",
        "@pip//mmh3",
        "@pip//openai",
        # "@pip//pinecone_client",
        "@pip//pydantic",
        # "@pip//python_telegram_bot",
        "@pip//redis",
        "@pip//requests",
        "@pip//tiktoken",
        "@pip//langchain_anthropic",
        "@pip//langgraph",
        "@pip//langchain_mcp_adapters",
        "@pip//claude_code_sdk",
    ],
)

create_image_with_tar(
    base = "@python_base_image",
    entrypoint = ["/app/service/ai/server/server"],
    tars = py_image_layer(
        name = "image_layer",
        binary = ":server",
        root = "/app/",
        tags = ["manual"],
    ),
)

py_test(
    name = "table_selector_test",
    size = "small",
    srcs = ["table_selector_test.py"],
    deps = [
        ":server",
        "@pip//langchain",
        "@pip//numpy",
    ],
)
