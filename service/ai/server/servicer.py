import logging
from typing import List, Dict, Iterable
from service.ai.server.table_selector import TableSelector
from service.ai.server.table_summarizer import TableSummarizer
from sql_lang import sql_escape_field

from service.ai.api import api_pb2, api_pb2_grpc
from sql_chain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langsmith import Client
import redis
import tiktoken
from service.ai.server.agents.sql_agent import SQLAgent
from service.ai.server.agents.sentio_react_agent import run_sentio_agent
from sql_api import SqlApi
import uuid
from service.ai.server.agents.table_selection_agent import TableSelectionAgent
from service.ai.server.models.model_registry import ModelRegistry
from service.ai.server.agents.suggest_reply_agent import SuggestReplyAgent
from service.ai.server.agents.insight_agent import InsightAgent
from service.ai.server.agents.agent_chat_handler import <PERSON><PERSON>hat<PERSON>andler
from service.ai.server.agents.sql_fix_react_agent import create_sql_fix_agent
from service.ai.server.agents.processor_generator_agent import ProcessorGeneratorAgent
import asyncio

class Servicer(api_pb2_grpc.AiApiServicer):
	openai_api_key: str
	openai_organization: str
	anthropic_api_key: str
	table_selector: TableSelector
	table_summarizer: TableSummarizer
	top_tables_to_select: int
	host: str
	max_allowed_tokens: int = 8192
	encoding_name: str = "cl100k_base"
	redis_client: redis.Redis = None

	def __init__(self, openai_api_key, openai_organization, anthropic_api_key, top_tables_to_select, redis_url, web_service_host=None,
				 sql_token_threshold=600, github_token=None):
		self.openai_api_key = openai_api_key
		self.openai_organization = openai_organization
		self.anthropic_api_key = anthropic_api_key
		self.github_token = github_token
		self.model_registry = ModelRegistry(openai_api_key, openai_organization, anthropic_api_key)
		self.host = web_service_host
		self.sql_chain = SQLChain(
			self.model_registry,
			web_service_host,
			sql_token_threshold=sql_token_threshold
		)
		self.sql_fix_agent = create_sql_fix_agent(
			self.model_registry,
			web_service_host
		)
		self.client = Client()
		if redis_url:
			# adding redis:// prefix if not provided
			if not redis_url.startswith("redis://"):
				redis_url = "redis://" + redis_url
			self.redis_client = redis.from_url(redis_url)
		else:
			self.redis_client = None
		self.table_selector = TableSelector(openai_api_key, self.redis_client)
		self.table_summarizer = TableSummarizer(self.model_registry, self.redis_client)
		self.top_tables_to_select = top_tables_to_select
		self.processor_generator_agent = ProcessorGeneratorAgent()

	def _extract_auth_headers(self, context):
		auth_headers = {}
		auth_headers["Cookie"] = "AUID=ai-bot"
		for key, value in context.invocation_metadata():
			if key == "auth":
				auth_headers["authorization"] = f'Bearer {value}'
			if key == "api-key":
				auth_headers["api-key"] = value
			if key == "admin-mode":
				auth_headers["x-admin-mode"] = value
		return auth_headers

	def _build_schema(self, tables) -> str:
		schema = ""
		for table in tables:
			schema += "CREATE TABLE " + table.name + " ("
			for column in table.columns:
				schema += sql_escape_field(column.name) + " " + column.type + "\n"
			schema += ");\n"
		return schema

	#     RESERVED = 0;
	#     EVENT = 1;
	#     METRICS = 2;
	#     SUBGRAPH = 3;
	#     MATERIALIZED_VIEW = 4;
	#     IMPORTED_EVENT = 5;
	#     SYSTEM = 6;
	#     ENTITY = 7;
	#     IMPORTED_ENTITY = 8;
	#     IMPORTED_SUBGRAPH = 9;
	#     USER_REFRESHABLE_VIEW = 10;
	def _build_schema_md(self, tables, table_summaries: Dict[str, str] = None, column_summaries: Dict[str, Dict[str, str]] = None) -> str:
		table_type_mapping = {
			0: "Reserved",
			1: "Project table",
			2: "Project table",
			3: "Project table",
			4: "View",
			5: "Project table",
			6: "System",
			7: "Project table",
			8: "Project table",
			9: "Project table",
			10: "View"
		}
		schema = ""
		for table in tables:
			# Add table header with name
			schema += f"# Table: {table.name}\n"
			schema += f"Type: {'External project table' if '.' in table.name and (table.table_type != 0 and table.table_type != 6) else table_type_mapping[table.table_type]}\n"
			if table_summaries and table.name in table_summaries:
				schema += f"Description: {table_summaries[table.name]}\n"
			schema += "Columns:\n"
			
			# Add each column with its type
			for column in table.columns:
				column_name = sql_escape_field(column.name)
				schema += f"- {column_name} ({column.type})\n"
				if column_summaries and table.name in column_summaries:
					if column.name in column_summaries[table.name]:
						schema += f"Description: {column_summaries[table.name][column.name]}\n"
			
			# Add a blank line between tables for readability
			schema += "\n"
	
		return schema

	def _build_history(self, messages) -> List[str]:
		history = []
		for message in messages:
			prefix = "Human" if message.role == api_pb2.Message.Role.USER else "AI"
			history.append(prefix + ": " + message.content + "\n")
		return history

	def _calculate_num_tokens(self, schema: str, history: List[str]) -> int:
		num_tokens = self._num_tokens_from_string(schema)
		for message in history:
			num_tokens += self._num_tokens_from_string(message)
		return num_tokens

	def _num_tokens_from_string(self, string: str) -> int:
		encoding = tiktoken.get_encoding(self.encoding_name)
		num_tokens = len(encoding.encode(string))
		return num_tokens

	def SQL(self, request, context):
		run_id = uuid.uuid4()
		auth_headers = self._extract_auth_headers(context)
		schema = self._build_schema_md(request.schema.tables)
		history = self._build_history(request.messages)
		num_tokens = self._calculate_num_tokens(schema, history)

		logging.info(f"num_tokens: {num_tokens}, max_allowed_tokens: {self.max_allowed_tokens}")
		
		# Use TableSelectionAgent instead of table_selector
		table_selection_agent = TableSelectionAgent(self.model_registry)
		selected_table_names = table_selection_agent.select_tables(
			request.messages, 
			schema, 
			[table.name for table in request.schema.tables], 
			run_id
		)
		
		logging.info(f"selected_table_names: {selected_table_names}")
		filtered_tables = [t for t in request.schema.tables
						   if t.name in selected_table_names]
		sql_api = SqlApi(host=self.host,
						 project=request.project,
						 version=request.version,
						 auth_headers=auth_headers)
		table_summaries = None
		column_summaries = None
		if len(selected_table_names) < 8:
			table_summaries, column_summaries = self.table_summarizer.get_table_summaries(
				request.project,
				filtered_tables,
				sql_api,
				run_id
			)
		
		schema = self._build_schema_md(filtered_tables, table_summaries, column_summaries)

		result = self.sql_chain.run(schema, history,
									auth_headers=auth_headers,
									project=request.project,
									version=request.version)
		msg = api_pb2.Message()
		msg.role = api_pb2.Message.Role.ASSISTANT
		msg.content = result["result"]
		msg.run_id = result["run_id"]
		response = api_pb2.Response()
		response.choices.extend([msg])
		return response

	def FixSQL(self, request, context):
		run_id = uuid.uuid4()
		auth_headers = self._extract_auth_headers(context)
		schema = self._build_schema_md(request.schema.tables)
		
		# Use TableSelectionAgent instead of table_selector
		table_selection_agent = TableSelectionAgent(self.model_registry)
		selected_table_names = table_selection_agent.select_tables(
			request.messages, 
			schema, 
			[table.name for table in request.schema.tables], 
			run_id
		)
		
		logging.info(f"selected_table_names: {selected_table_names}")
		filtered_tables = [t for t in request.schema.tables
						   if t.name in selected_table_names]
		sql_api = SqlApi(host=self.host,
						 project=request.project,
						 version=request.version,
						 auth_headers=auth_headers)
		table_summaries = None
		column_summaries = None
		if len(selected_table_names) < 8:
			table_summaries, column_summaries = self.table_summarizer.get_table_summaries(
				request.project,
				filtered_tables,
				sql_api,
				run_id
			)
		
		schema = self._build_schema_md(filtered_tables, table_summaries, column_summaries)

		original_sql = request.messages[-1].content

		# Use the new LangGraph React agent for SQL fixing
		result = self.sql_fix_agent.fix_sql(
			original_sql,
			schema,
			request.error,
			auth_headers=auth_headers,
			project=request.project,
			version=request.version
		)

		msg = api_pb2.Message()
		msg.role = api_pb2.Message.Role.ASSISTANT
		msg.content = result["result"]	
		msg.run_id = result["run_id"]
		response = api_pb2.Response()
		response.choices.extend([msg])
		return response

	def SQLAgentChat(self, request, context):
		run_id = uuid.uuid4()
		schema = self._build_schema_md(request.schema.tables)
		table_selection_agent = TableSelectionAgent(self.model_registry)
		selected_table_names = table_selection_agent.select_tables(request.messages, schema, [table.name for table in request.schema.tables], run_id)
		print(f"selected_table_names: {selected_table_names}")

		selected_tables = [t for t in request.schema.tables if t.name in selected_table_names]
		auth_headers = self._extract_auth_headers(context)
		sql_api = SqlApi(host=self.host,
						 project=request.project,
						 version=request.version,
						 auth_headers=auth_headers)
		
		table_summaries = None
		column_summaries = None
		if len(selected_table_names) < 8:
			table_summaries, column_summaries = self.table_summarizer.get_table_summaries(
				request.project,
				selected_tables,
				sql_api,
				run_id
			)
		
		if request.project == "sentio/pancakeswap":
			# Use the SentioReactAgent for the test project
			query = request.messages[-1].content if request.messages else ""

			# Run the Sentio React agent synchronously
			result = asyncio.run(run_sentio_agent(query, self.model_registry, auth_headers))
			
			# Format the response to match expected output
			response_content = result["messages"][-1].content
			return api_pb2.SQLAgentResponse(
				type="message",
				content=response_content,
				run_id=str(run_id)
			)
		else:
			sql_agent = SQLAgent(self.model_registry, sql_api)
		
		filtered_schema = self._build_schema_md(
			selected_tables,
			table_summaries,
			column_summaries
		)
		
		result = sql_agent.chat(request.messages, filtered_schema, run_id)
		return api_pb2.SQLAgentResponse(
			type=result["type"],
			content=result["content"],
			sql=result["sql"],
			chart_type=result["chart_type"],
			explanation=result["explanation"],
			title=result["title"],
			questions=result["questions"],
			used_tables=result["used_tables"],
			run_id=result["run_id"]
		)

	def SQLAgentChatStream(self, request, context) -> Iterable[api_pb2.SQLAgentStreamResponse]:
		"""
		Stream SQL agent chat responses to the client
		"""
		run_id = uuid.uuid4()
		schema = self._build_schema_md(request.schema.tables)
		table_selection_agent = TableSelectionAgent(self.model_registry)
		selected_table_names = table_selection_agent.select_tables(request.messages, schema, [table.name for table in request.schema.tables], run_id)
		print(f"selected_table_names: {selected_table_names}")
		if len(selected_table_names) != 0:
			yield api_pb2.SQLAgentStreamResponse(
				chunk_type="tool",
				chunk=f"Found {len(selected_table_names)} relevant tables: {selected_table_names}",
				done=False,
				run_id=str(run_id)
			)

		selected_tables = [t for t in request.schema.tables if t.name in selected_table_names]
		auth_headers = self._extract_auth_headers(context)
		sql_api = SqlApi(host=self.host,
						 project=request.project,
						 version=request.version,
						 auth_headers=auth_headers)
		
		table_summaries = None
		column_summaries = None
		if len(selected_table_names) < 8:
			table_summaries, column_summaries = self.table_summarizer.get_table_summaries(
				request.project,
				selected_tables,
				sql_api,
				run_id
			)
		
		filtered_schema = self._build_schema_md(
			selected_tables,
			table_summaries,
			column_summaries
		)
		
		if request.project == "sentio/pancakeswap":
			# For the MCP-based agent streaming case
			query = request.messages[-1].content if request.messages else ""
			
			# Yield an initial message
			yield api_pb2.SQLAgentStreamResponse(
				chunk_type="tool",
				chunk="Connecting to Sentio MCP server...",
				done=False,
				run_id=str(run_id)
			)
			
			# Run the Sentio React agent (non-streaming for now)
			try:
				result = asyncio.run(run_sentio_agent(query, self.model_registry, auth_headers))
				
				# Send the complete response
				response_content = result["messages"][-1].content
				yield api_pb2.SQLAgentStreamResponse(
					chunk_type="message",
					chunk=response_content,
					done=True,
					run_id=str(run_id)
				)
			except Exception as e:
				# Handle errors
				yield api_pb2.SQLAgentStreamResponse(
					chunk_type="error",
					chunk=f"Error connecting to MCP server: {str(e)}",
					done=True,
					run_id=str(run_id)
				)
		else:
			# For SQLAgent, use the existing streaming implementation
			sql_agent = SQLAgent(self.model_registry, sql_api)
			
			# Use the synchronous streaming implementation directly
			for chunk in sql_agent.chat_stream(request.messages, filtered_schema, run_id):
				yield api_pb2.SQLAgentStreamResponse(
					chunk_type=chunk["chunk_type"],
					chunk=chunk["chunk"],
					done=chunk["done"],
					sql=chunk.get("sql", ""),
					chart_type=chunk.get("chart_type", ""),
					explanation=chunk.get("explanation", ""),
					title=chunk.get("title", ""),
					questions=chunk.get("questions", []),
					used_tables=chunk.get("used_tables", []),
					run_id=chunk.get("run_id", str(run_id))
				)

	def SubmitFeedback(self, request, context):
		key = f"{request.project_owner}/{request.project_slug}"
		self.client.create_feedback(
			run_id=request.run_id,
			key=key,
			score=request.score
		)

		logging.info(f"Feedback submitted for run_id: {request.run_id}")
		return api_pb2.Response()
	
	def SuggestReply(self, request, context):
		suggest_reply_agent = SuggestReplyAgent(self.model_registry)
		replies = suggest_reply_agent.suggest_reply(request.messages)
		return api_pb2.SuggestReplyResponse(
			suggested_replies=replies
		)

	def InsightAgentChat(self, request, context):
		run_id = uuid.uuid4()
		
		insight_agent = InsightAgent(self.model_registry, request.project)
		
		metrics_string = "\n".join([f"- name: {metric.name}, type: {metric.type}" for metric in request.metrics])
		result = insight_agent.chat(request.messages, metrics_string, run_id)
		
		return api_pb2.InsightAgentResponse(
			type=result["type"],
			content=result["content"],
			queries=self._convert_queries_to_proto(result.get("queries", [])),
			samples_limit=result.get("samples_limit", 1000),
			time_range=self._convert_time_range_to_proto(result.get("time_range")),
			chart_type=result.get("chart_type", ""),
			explanation=result.get("explanation", ""),
			title=result.get("title", ""),
			used_metrics=result.get("used_metrics", []),
			run_id=result["run_id"]
		)

	def AgentChat(self, request, context):
		"""
		Unified agent chat that automatically routes to SQL or Insight agent
		"""
		try:
			auth_headers = self._extract_auth_headers(context)
			sql_api = SqlApi(host=self.host,
							 project=request.project,
							 version=request.version,
							 auth_headers=auth_headers)
			
			# Initialize the agent chat handler
			agent_chat_handler = AgentChatHandler(self.model_registry, sql_api, auth_headers, self.redis_client, github_token=self.github_token)
			
			# Process the request
			response = agent_chat_handler.agent_chat(request)
			
			logging.info(f"AgentChat completed - Agent: {response.agent_type}, Type: {response.type}")
			return response
			
		except Exception as e:
			logging.error(f"Error in AgentChat: {str(e)}")
			# Return error response
			error_response = api_pb2.AgentChatResponse()
			error_response.type = "error"
			error_response.content = "I encountered an error while processing your request. Please try again."
			error_response.run_id = str(uuid.uuid4())
			error_response.agent_type = "error"
			error_response.routing_reasoning = f"Error occurred: {str(e)}"
			return error_response

	def AgentChatStream(self, request, context) -> Iterable[api_pb2.AgentChatStreamResponse]:
		"""
		Unified streaming agent chat that automatically routes to SQL or Insight agent
		"""
		try:
			auth_headers = self._extract_auth_headers(context)
			sql_api = SqlApi(host=self.host,
							 project=request.project,
							 version=request.version,
							 auth_headers=auth_headers)
			
			# Initialize the agent chat handler
			agent_chat_handler = AgentChatHandler(self.model_registry, sql_api, auth_headers, self.redis_client, github_token=self.github_token)
			
			# Stream responses
			for chunk in agent_chat_handler.agent_chat_stream(request):
				if context.is_active():
					yield chunk
				else:
					logging.info("Client disconnected, stopping AgentChatStream")
					break
					
		except Exception as e:
			logging.error(f"Error in AgentChatStream: {str(e)}")
			# Yield error response
			error_response = api_pb2.AgentChatStreamResponse()
			error_response.chunk_type = "error"
			error_response.chunk = "I encountered an error while processing your request. Please try again."
			error_response.done = True
			error_response.run_id = str(uuid.uuid4())
			error_response.agent_type = "error"
			error_response.routing_reasoning = f"Error occurred: {str(e)}"
			yield error_response

	def GenerateProcessor(self, request, context):
		"""Generate a Sentio processor project using the ProcessorGeneratorAgent"""
		try:
			# Run the async agent method synchronously within the GRPC handler
			async def run():
				return await self.processor_generator_agent.generate_processor(
					chain_id=request.chain_id,
					contract_address=request.contract_address,
					user_prompt=request.user_prompt,
					project_name=request.project_name if request.project_name else None,
				)

			result = asyncio.run(run())

			resp = api_pb2.ProcessorGenerateResponse(
				success=bool(result.get("success", False)),
				processor_code=result.get("processor_code", ""),
				summary=result.get("summary", ""),
				error=result.get("error", ""),
				project_path=result.get("project_path", ""),
				run_id=str(uuid.uuid4()),
			)
			return resp
		except Exception as e:
			logging.error(f"Error in GenerateProcessor: {str(e)}", exc_info=True)
			return api_pb2.ProcessorGenerateResponse(
				success=False,
				error=f"Failed to generate processor: {str(e)}",
				run_id=str(uuid.uuid4()),
			)

	def _convert_queries_to_proto(self, queries: List[Dict]) -> List[api_pb2.Query]:
		"""Convert query dictionaries to protobuf Query objects"""
		proto_queries = []
		for query_dict in queries:
			proto_query = api_pb2.Query()
			proto_query.query = query_dict.get("query", "")
			proto_query.alias = query_dict.get("alias", "")
			proto_query.id = query_dict.get("id", "")
			
			# Convert label selector
			if "labelSelector" in query_dict:
				for key, value in query_dict["labelSelector"].items():
					proto_query.label_selector[key] = value
			
			# Convert aggregate
			if "aggregate" in query_dict and query_dict["aggregate"]:
				agg = query_dict["aggregate"]
				proto_query.aggregate.op = self._convert_aggregate_op(agg.get("op", "SUM"))
				if "grouping" in agg:
					proto_query.aggregate.grouping.extend(agg["grouping"])
			
			# Convert functions
			if "functions" in query_dict:
				for func_dict in query_dict["functions"]:
					proto_func = api_pb2.Function()
					proto_func.name = func_dict.get("name", "")
					
					# Convert arguments
					if "arguments" in func_dict:
						for arg_dict in func_dict["arguments"]:
							proto_arg = api_pb2.Argument()
							if "durationValue" in arg_dict:
								duration = arg_dict["durationValue"]
								proto_arg.duration_value.value = duration.get("value", 1.0)
								proto_arg.duration_value.unit = duration.get("unit", "m")
							elif "intValue" in arg_dict:
								proto_arg.int_value = arg_dict["intValue"]
							elif "stringValue" in arg_dict:
								proto_arg.string_value = arg_dict["stringValue"]
							elif "doubleValue" in arg_dict:
								proto_arg.double_value = arg_dict["doubleValue"]
							elif "boolValue" in arg_dict:
								proto_arg.bool_value = arg_dict["boolValue"]
							proto_func.arguments.append(proto_arg)
					
					proto_query.functions.append(proto_func)
			
			proto_queries.append(proto_query)
		
		return proto_queries

	def _convert_aggregate_op(self, op_str: str) -> api_pb2.Aggregate.AggregateOps:
		"""Convert string aggregate operation to protobuf enum"""
		op_map = {
			"AVG": api_pb2.Aggregate.AggregateOps.AVG,
			"SUM": api_pb2.Aggregate.AggregateOps.SUM,
			"MIN": api_pb2.Aggregate.AggregateOps.MIN,
			"MAX": api_pb2.Aggregate.AggregateOps.MAX,
			"COUNT": api_pb2.Aggregate.AggregateOps.COUNT,
		}
		return op_map.get(op_str, api_pb2.Aggregate.AggregateOps.SUM)

	def _convert_time_range_to_proto(self, time_range: Dict) -> api_pb2.TimeRangeLite:
		"""Convert time range dictionary to protobuf TimeRangeLite"""
		if not time_range:
			return None
		
		proto_time_range = api_pb2.TimeRangeLite()
		proto_time_range.start = time_range.get("start", "")
		proto_time_range.end = time_range.get("end", "")
		proto_time_range.step = time_range.get("step", 3600)
		proto_time_range.timezone = time_range.get("timezone", "UTC")
		
		return proto_time_range

	def InsightAgentChatStream(self, request, context) -> Iterable[api_pb2.InsightAgentStreamResponse]:
		"""
		Stream insight agent chat responses to the client
		"""
		run_id = uuid.uuid4()
		
		metrics_string = "\n".join([f"- name: {metric.name}, type: {metric.type}" for metric in request.metrics])
		
		auth_headers = self._extract_auth_headers(context)
		insight_agent = InsightAgent(self.model_registry, request.project)
		
		# Use the streaming implementation
		for chunk in insight_agent.chat_stream(request.messages, metrics_string, run_id):
			if chunk["chunk_type"] == "query":
				# Handle queries
				queries = chunk.get("queries", [])
				proto_queries = self._convert_queries_to_proto(queries)
				yield api_pb2.InsightAgentStreamResponse(
					chunk_type="query",
					chunk="",
					done=True,
					queries=proto_queries,
					samples_limit=chunk.get("samples_limit", 1000),
					time_range=self._convert_time_range_to_proto(chunk.get("time_range")),
					chart_type=chunk.get("chart_type", ""),
					explanation=chunk.get("explanation", ""),
					title=chunk.get("title", ""),
					used_metrics=chunk.get("used_metrics", []),
					run_id=chunk.get("run_id", str(run_id))
				)
			else:
				yield api_pb2.InsightAgentStreamResponse(
					chunk_type=chunk["chunk_type"],
					chunk=chunk["chunk"],
					done=chunk["done"],
					chart_type=chunk.get("chart_type", ""),
					explanation=chunk.get("explanation", ""),
					title=chunk.get("title", ""),
					run_id=chunk.get("run_id", str(run_id))
				)
