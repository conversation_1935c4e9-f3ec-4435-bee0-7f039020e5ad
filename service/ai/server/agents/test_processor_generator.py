#!/usr/bin/env python3
"""
Test script for the ProcessorGeneratorAgent.

This script demonstrates how to use the agent and can be used for testing.
"""
import asyncio
import sys
from processor_generator_agent import ProcessorGeneratorAgent, create_processor_with_claude


async def test_basic_functionality():
    """Test basic functionality without actually running Claude Code SDK."""
    print("🧪 Testing ProcessorGeneratorAgent basic functionality...")
    
    def status_callback(status: str):
        print(f"[TEST STATUS] {status}")
    
    agent = ProcessorGeneratorAgent(status_callback=status_callback)
    
    # Test status callback
    agent._update_status("Test status update")
    
    # Test command preparation (without execution)
    print("✅ Basic functionality test passed")
    
    agent.cleanup()


async def test_full_generation():
    """
    Test full processor generation (requires Claude Code SDK).
    This is commented out by default since it requires setup.
    """
    print("🚀 Testing full processor generation...")
    print("Note: This requires Claude Code SDK and Sentio CLI to be installed")
    
    def status_callback(status: str):
        print(f"[GENERATION] {status}")
    
    # Uncomment the lines below to test with actual generation
    result = await create_processor_with_claude(
        chain_id="1",
        contract_address="0x31724cA0C982A31fbb5C57f4217AB585271fc9a5",  # ERC-4337
        user_prompt="Create a simple processor that records Mint and Burn events",
        status_callback=status_callback
    )
    
    if result["success"]:
        print("✅ Full generation test passed!")
        print(f"Processor code: {result['processor_code']}")
        print(f"Summary: {result['summary']}")
        print(f"Project path: {result['project_path']}")
    else:
        print(f"❌ Full generation test failed: {result['error']}")
    
    print("⚠️  Full generation test skipped (requires Claude Code SDK setup)")


async def main():
    """Run all tests."""
    print("🔧 ProcessorGeneratorAgent Test Suite")
    print("=" * 50)
    
    try:
        await test_basic_functionality()
        print()
        await test_full_generation()
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())