import logging
from typing import List, Dict, Any, Optional

from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.tools import load_mcp_tools
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.messages import HumanMessage
from langchain_openai import ChatOpenAI
from service.ai.server.models.model_registry import ModelRegistry

async def create_sentio_agent(
    model_registry: ModelRegistry, 
    auth_headers: Dict[str, str], 
    system_prompt: str = "You are a helpful assistant that leverages Sentio tools.", 
    enable_processor_tools_only: bool = False,
    additional_tools: Optional[List] = None
):
    """
    Create a React agent that connects to the Sentio API MCP server.
    
    Args:
        model_registry: The model registry to get the model from.
        auth_headers: Authentication headers for the MCP server.
        system_prompt: The system prompt to use for the agent.
        enable_processor_tools_only: If True, only enable processor-related tools.
        additional_tools: Optional list of additional tools to include (e.g., web search).
        
    Returns:
        A React agent that can use tools from the Sentio API.
    """
    try:
        logging.info(auth_headers)

        client = MultiServerMCPClient(
            {
                "sentio": {
                    "url": "http://localhost:3000/sse",
                    "transport": "sse",
                    "headers": auth_headers
                }
            }
        )

        # Load tools from the MCP server
        tools = await client.get_tools()

        # Filter tools if only processor tools are requested
        if enable_processor_tools_only:
            processor_tool_names = [
                'getProcessorStatus',
                'getProcessorSourceFiles'
            ]
            tools = [tool for tool in tools if tool.name in processor_tool_names]
            logging.info(f"Filtered to processor tools only: {[tool.name for tool in tools]}")

        # Add additional tools if provided
        if additional_tools:
            tools.extend(additional_tools)
            logging.info(f"Added additional tools: {[tool.name for tool in additional_tools]}")

        # use the model registry to get the model
        model = model_registry.get_gpt41()
        
        # Create the React agent
        agent = create_react_agent(
            model=model,
            tools=tools,
            prompt=system_prompt
        )

        return agent
    except Exception as e:
        logging.error(f"Error creating Sentio agent: {e}")
        raise e


async def run_sentio_agent(
    query: str, 
    model_registry: ModelRegistry, 
    auth_headers: Dict[str, str], 
    system_prompt: str = "You are a helpful assistant that leverages Sentio tools.", 
    enable_processor_tools_only: bool = False,
    additional_tools: Optional[List] = None
):
    """
    Run the Sentio React agent with the given query.
    
    Args:
        query: The user query to process.
        model_registry: The model registry to get the model from.
        auth_headers: Authentication headers for the MCP server.
        system_prompt: The system prompt to use for the agent.
        enable_processor_tools_only: If True, only enable processor-related tools.
        additional_tools: Optional list of additional tools to include (e.g., web search).
        
    Returns:
        The agent's response.
    """
    agent = await create_sentio_agent(
        model_registry, 
        auth_headers, 
        system_prompt, 
        enable_processor_tools_only,
        additional_tools
    )
    
    # Run the agent
    response = await agent.ainvoke(
        {"messages": [HumanMessage(content=query)]}
    )
    
    return response
