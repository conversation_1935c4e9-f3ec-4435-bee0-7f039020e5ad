from service.ai.api import api_pb2
from typing import List, Dict, Optional
from langchain.schema.runnable import <PERSON><PERSON><PERSON>Lambda
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from pydantic import BaseModel, <PERSON>
from typing import List as TypedList
from uuid import UUID
from service.ai.server.models.model_registry import ModelRegistry

def get_system_prompt() -> str:
    base_prompt = """You are a highly intelligent query routing assistant. Your primary function is to analyze a user's request and route it to the correct specialized agent.

<instructions>
  1. First, analyze the user's query against the agent responsibilities described in the `<responsibilities>` section.
  2. If the query matches rules for multiple agents, use the `<hierarchy>` to resolve the conflict.
  3. Use the examples in the `<examples>` section to understand nuanced cases.
</instructions>

<hierarchy>
  1. SQL Agent (Highest Priority)
  2. Insight Agent
  3. QA Agent (Lowest Priority)
</hierarchy>

<responsibilities>
  <agent name="SQL Agent">
    - The user asks for database schema information (e.g., list tables, describe columns, show schema).
    - The user mentions specific table or column names.
    - The user asks for complex data analysis, trends, aggregations, or changes over time.
    - The user wants to create dashboards, visualizations, charts, or graphs.
    - The query is an ambiguous or general analytical question about the data. **This is the default agent for any data exploration.**
  </agent>

  <agent name="Insight Agent">
    - The query does NOT meet the criteria for the SQL Agent.
    - The user explicitly requests a predefined metric by its exact name from the available metrics list.
    - The user uses phrases like "generate insight query for..." or "use insight query to..." followed by a known metric name.
  </agent>

  <agent name="QA Agent">
    - The query does NOT meet the criteria for the SQL or Insight agents.
    - The user asks general "how to" questions about using the Sentio platform.
    - The user needs help with platform-level issues like debugging, errors, performance, or best practices.
    - The user asks for guidance on documentation.
  </agent>
</responsibilities>

<examples>
  <example>
    <query>List all tables in my project.</query>
    <agent_type>SQL Agent</agent_type>
  </example>
  <example>
    <query>What was the daily average transaction fee on Polygon last week?</query>
    <agent_type>SQL Agent</agent_type>
  </example>
  <example>
    <query>Use insight query to get last week's daily active wallets.</query>
    <agent_type>Insight Agent</agent_type>
  </example>
  <example>
    <query>How do I fix a `TypeError` in my processor script?</query>
    <agent_type>QA Agent</agent_type>
  </example>
  <example>
    <query>Show me some insights on user retention.</query>
    <agent_type>SQL Agent</agent_type>
  </example>
</examples>
    """
    
    return base_prompt

class QueryRoutingOutput(BaseModel):
    agent_type: str = Field(
        description=f"The agent to route to: 'sql', 'insight', or 'qa'"
    )
    reasoning: str = Field(
        description="Clear explanation of why this agent was chosen"
    )

class QueryRoutingAgent:
    def __init__(self, model_registry: ModelRegistry):
        self.model_registry = model_registry

    def route_query(
        self, 
        messages: List[Dict[str, str]], 
        all_tables_metadata: Optional[str] = None, 
        default_tables: Optional[TypedList[str]] = None, 
        available_metrics: Optional[str] = None,
        run_id: UUID = None,
        hostname: Optional[str] = None
    ) -> Dict:
        """
        Analyze a user query and decide which agent should handle it
        
        Args:
            messages: The conversation messages
            all_tables_metadata: Metadata about available database tables
            default_tables: Default tables to fall back to
            available_metrics: Information about available metrics
            run_id: Unique identifier for this conversation
            hostname: Optional hostname for environment detection
            
        Returns:
            A dict with routing decision, reasoning, and selected tables (if applicable)
        """
        # Prepare system prompt with additional context
        system_content = get_system_prompt()
        
        if all_tables_metadata:
            system_content += "\n\n<available_database_tables>\n" + all_tables_metadata + "\n</available_database_tables>\n"
            
        if available_metrics:
            system_content += "\n\n<available_metrics>\n" + available_metrics + "\n</available_metrics>\n"

        # Convert messages to LangChain format
        langchain_messages = [SystemMessage(content=system_content)]
    
        for msg in messages:
            if msg.role == api_pb2.Message.Role.USER:
                langchain_messages.append(HumanMessage(content=msg.content))
            elif msg.role == api_pb2.Message.Role.ASSISTANT:
                langchain_messages.append(AIMessage(content=msg.content))

        return self.route_inner(langchain_messages, default_tables or [], run_id)

    def route_inner(self, messages: List[BaseMessage], default_tables: TypedList[str], run_id: UUID) -> Dict:
        try:
            # Create the LangChain ChatOpenAI instance with structured output
            llm = self.model_registry.get_gpt41_mini(temperature=0.1, max_tokens=1500)

            # Create a structured output chain using the Pydantic model
            structured_llm = llm.with_structured_output(QueryRoutingOutput)
            
            # Invoke the model with structured output
            output: QueryRoutingOutput = RunnableLambda(lambda x: structured_llm.invoke(x)).invoke(messages, {"run_id": run_id})
            
            # Process the output and validate agent type
            agent_type = output.agent_type.lower()
            
            reasoning = output.reasoning
            
            result = {
                "agent_type": agent_type,
                "reasoning": reasoning
            }
            
            return result

        except Exception as e:
            # Return a safe default response in case of error
            error_message = f"Error in query routing: {str(e)}"
            print(error_message)  # Log the error
            
            # Default to SQL agent with default tables as fallback
            return {
                "agent_type": "sql",
                "reasoning": "Error occurred during routing, defaulting to SQL agent",
                "selected_tables": default_tables
            } 