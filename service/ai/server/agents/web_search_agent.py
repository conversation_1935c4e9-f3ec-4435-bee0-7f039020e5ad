from typing import List, Optional
from pydantic import BaseModel, Field
from langchain_core.messages import SystemMessage, HumanMessage
from service.ai.server.models.model_registry import ModelRegistry

class WebSearchInput(BaseModel):
    query: str = Field(description="The search query to look up on the web")
    domains: Optional[List[str]] = Field(default=None, description="Optional list of domains to restrict search to")
    site_restriction: Optional[str] = Field(default=None, description="Optional site restriction (e.g., 'site:docs.sentio.xyz')")

# Define web search tool configurations
web_search_tool = {
    "type": "web_search_preview"
}

SYSTEM_PROMPT = """You are a web search agent that retrieves up-to-date information about blockchain, cryptocurrency, and DeFi topics.

# Steps
1. **Understand the Query**: Identify what specific information the user is looking for.
2. **Search the Web**: Use the web search tool to find relevant, recent information.
3. **Extract Key Information**: Gather the most relevant facts, data, and insights from search results.
4. **Include Sources ONLY if Available**: If the search results provide actual URLs, include them. If no URLs are found in results, do NOT create or invent URLs.
5. **Present Results Clearly**: Format the information in a readable way with proper attribution.

# CRITICAL GUIDELINES
- **NEVER invent URLs**: Only include URLs that are explicitly found in the search results
- **Be explicit about source availability**: If no specific URLs are found, state "No specific documentation links were found in the search results"
- **Prioritize accuracy over completeness**: It's better to provide information without links than to provide fake links

Focus on finding factual, accurate information from reputable sources, especially for blockchain and cryptocurrency topics.

When domain restrictions are provided, focus your search within those domains for more targeted results.
"""

class WebSearchAgent:
    """Agent responsible for searching the web for up-to-date information."""
    
    def __init__(self, model_registry: ModelRegistry):
        """
        Initialize the WebSearchAgent.
        
        Args:
            model_registry: The model registry for accessing LLM clients
        """
        self.model_registry = model_registry
    
    def search(self, query: str, domains: Optional[List[str]] = None, site_restriction: Optional[str] = None) -> str:
        """
        Search the web for information based on the provided query.
        
        Args:
            query: The search query
            domains: Optional list of domains to restrict search to (e.g., ['docs.sentio.xyz', 'sentio.xyz'])
            site_restriction: Optional site restriction string (e.g., 'site:docs.sentio.xyz')
            
        Returns:
            Formatted search results with references
        """
        # Enhance the query with domain restrictions
        enhanced_query = self._enhance_query_with_restrictions(query, domains, site_restriction)
        
        # Convert messages to LangChain format
        langchain_messages = [SystemMessage(content=SYSTEM_PROMPT)]
        
        search_instruction = f"Search the web for the following query and return factual information with sources: {enhanced_query}"
        if domains or site_restriction:
            search_instruction += f"\n\nNote: This search should focus on information from the specified domains/sites for more targeted results."
        
        langchain_messages.append(HumanMessage(content=search_instruction))
        
        return self.process_query(langchain_messages)
    
    def search_sentio_docs(self, query: str) -> str:
        """
        Search specifically within Sentio documentation.
        
        Args:
            query: The search query related to Sentio
            
        Returns:
            Formatted search results from Sentio documentation
        """
        return self.search(
            query=query,
            domains=['docs.sentio.xyz', 'sentio.xyz'],
            site_restriction='site:docs.sentio.xyz OR site:sentio.xyz'
        )
    
    def _enhance_query_with_restrictions(self, query: str, domains: Optional[List[str]] = None, site_restriction: Optional[str] = None) -> str:
        """
        Enhance the search query with domain restrictions.
        
        Args:
            query: The original search query
            domains: Optional list of domains to restrict to
            site_restriction: Optional site restriction string
            
        Returns:
            Enhanced query with restrictions
        """
        enhanced_query = query
        
        # Add site restriction if provided
        if site_restriction:
            enhanced_query = f"{enhanced_query} {site_restriction}"
        elif domains:
            # Convert domains list to site restrictions
            site_restrictions = " OR ".join([f"site:{domain}" for domain in domains])
            enhanced_query = f"{enhanced_query} ({site_restrictions})"
        
        return enhanced_query
    
    def process_query(self, messages: List) -> str:
        """
        Process the search query using LangChain's web search tool
        
        Args:
            messages: The messages to send to the model
            
        Returns:
            Formatted search results
        """
        try:
            # Get the model from the registry
            llm = self.model_registry.get_gpt41()
            
            # Bind the web search tool to the LLM
            llm_with_tools = llm.bind_tools([web_search_tool])
            
            # Invoke the model
            result = llm_with_tools.invoke(messages)
            
            # Format the result if needed
            if not result or not result.content:
                return "No web search results found."
            
            # Extract content from response
            content = result.content
            return content
            
        except Exception as e:
            print(f"Web search error: {str(e)}")
            return f"Web search failed: {str(e)}"
