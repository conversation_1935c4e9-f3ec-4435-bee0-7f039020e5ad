from service.ai.api import api_pb2
from typing import Dict, List, Generator
from service.ai.server.agents.query_routing_agent import QueryRoutingAgent
from service.ai.server.agents.table_selection_agent import TableSelectionAgent
from service.ai.server.agents.sql_agent import SQLAgent
from service.ai.server.agents.insight_agent import InsightAgent
from service.ai.server.agents.qa_agent import QAAgent
from service.ai.server.table_summarizer import TableSummarizer
from service.ai.server.models.model_registry import ModelRegistry
from sql_api import SqlApi
from uuid import UUID, uuid4
import logging

logger = logging.getLogger(__name__)

class AgentChatHandler:
    def __init__(self, model_registry: ModelRegistry, sql_api: SqlApi, auth_headers: Dict[str, str] = None, redis_client=None, github_token: str = None):
        self.model_registry = model_registry
        self.sql_api = sql_api
        self.auth_headers = auth_headers or {}
        self.github_token = github_token
        self.routing_agent = QueryRoutingAgent(model_registry)
        self.table_selection_agent = TableSelectionAgent(model_registry)
        self.table_summarizer = TableSummarizer(model_registry, redis_client)
    #     RESERVED = 0;
    #     EVENT = 1;
    #     METRICS = 2;
    #     SUBGRAPH = 3;
    #     MATERIALIZED_VIEW = 4;
    #     IMPORTED_EVENT = 5;
    #     SYSTEM = 6;
    #     ENTITY = 7;
    #     IMPORTED_ENTITY = 8;
    #     IMPORTED_SUBGRAPH = 9;
    #     USER_REFRESHABLE_VIEW = 10;
    def build_schema_md(self, tables, table_summaries: Dict[str, str] = None, column_summaries: Dict[str, Dict[str, str]] = None) -> str:
        table_type_mapping = {
            0: "Reserved",
            1: "Project table",
            2: "Project table",
            3: "Project table",
            4: "View",
            5: "Project table",
            6: "System",
            7: "Project table",
            8: "Project table",
            9: "Project table",
            10: "View"
        }
        schema = ""
        for table in tables:
            # Add table header with name
            schema += f"# Table: {table.name}\n"
            schema += f"Type: {'External project table' if '.' in table.name and (table.table_type != 0 and table.table_type != 6) else table_type_mapping[table.table_type]}\n"
            if table_summaries and table.name in table_summaries:
                schema += f"Description: {table_summaries[table.name]}\n"
            schema += "Columns:\n"
            
            # Add each column with its type
            for column in table.columns:
                column_name = column.name
                schema += f"- {column_name} ({column.type})\n"
                if column_summaries and table.name in column_summaries:
                    if column.name in column_summaries[table.name]:
                        schema += f"Description: {column_summaries[table.name][column.name]}\n"
            
            # Add a blank line between tables for readability
            schema += "\n"
    
        return schema

    def format_metrics_info(self, metrics: List[api_pb2.MetricInfo]) -> str:
        """Convert metrics protobuf to string format for the agents"""
        if not metrics:
            return ""
        
        metrics_lines = []
        for metric in metrics:
            metrics_lines.append(f"- {metric.name} ({metric.type})")
        
        return "\n".join(metrics_lines)

    def get_default_tables(self, schema: api_pb2.SQLSchema) -> List[str]:
        """Extract default tables with priority for project tables"""
        project_tables = []
        other_tables = []
        
        for table in schema.tables:
            table_name = table.name
            table_type = table.table_type
            
            # Prioritize project tables (EVENT, METRICS, etc.)
            if table_type in [
                api_pb2.SQLSchema.SQLTable.TableType.EVENT,
                api_pb2.SQLSchema.SQLTable.TableType.METRICS,
                api_pb2.SQLSchema.SQLTable.TableType.MATERIALIZED_VIEW,
                api_pb2.SQLSchema.SQLTable.TableType.ENTITY,
                api_pb2.SQLSchema.SQLTable.TableType.USER_REFRESHABLE_VIEW
            ]:
                project_tables.append(table_name)
            else:
                other_tables.append(table_name)
        
        # Return project tables first, then others
        return project_tables + other_tables

    def agent_chat(self, request: api_pb2.AgentChatRequest) -> api_pb2.AgentChatResponse:
        """Handle non-streaming agent chat requests"""
        try:
            run_id = uuid4()
            
            # Convert protobuf data to formats expected by agents
            schema_info = self.build_schema_md(request.schema.tables)
            metrics_info = self.format_metrics_info(request.metrics)
            default_tables = self.get_default_tables(request.schema)
            
            # Step 1: Get routing decision
            routing_result = self.routing_agent.route_query(
                messages=request.messages,
                all_tables_metadata=schema_info,
                default_tables=default_tables,
                available_metrics=metrics_info,
                run_id=run_id,
                hostname=self.sql_api.host
            )
            
            logger.info(f"Routing decision: {routing_result['agent_type']} - {routing_result['reasoning']}")
            
            # Step 2: Route to appropriate agent
            response = api_pb2.AgentChatResponse()
            response.run_id = str(run_id)
            response.agent_type = routing_result['agent_type']
            response.routing_reasoning = routing_result['reasoning']
            
            if routing_result['agent_type'] == 'sql':
                # Use SQL Agent
                sql_agent = SQLAgent(self.model_registry, self.sql_api)
                
                # Use table selection agent to select relevant tables
                selected_table_names = self.table_selection_agent.select_tables(
                    messages=request.messages,
                    all_tables_metadata=schema_info,
                    default_tables=default_tables,
                    run_id=run_id
                )
                selected_tables = [t for t in request.schema.tables if t.name in selected_table_names]

                table_summaries = None
                column_summaries = None
                if len(selected_tables) < 8:
                    table_summaries, column_summaries = self.table_summarizer.get_table_summaries(
                        request.project,
                        selected_tables,
                        self.sql_api,
                        run_id
                    )
                filtered_schema_info = self.build_schema_md(selected_tables, table_summaries, column_summaries)
                # Get SQL agent response
                sql_response = sql_agent.chat(request.messages, filtered_schema_info, run_id)
                
                # Map SQL response to AgentChat response
                response.type = sql_response.get("type", "")
                response.content = sql_response.get("content", "")
                response.sql = sql_response.get("sql", "")
                response.chart_type = sql_response.get("chart_type", "")
                response.explanation = sql_response.get("explanation", "")
                response.title = sql_response.get("title", "")
                response.questions.extend(sql_response.get("questions", []))
                response.used_tables.extend(sql_response.get("used_tables", []))
            elif routing_result['agent_type'] == 'insight':
                # Use Insight Agent
                insight_agent = InsightAgent(self.model_registry, request.project)
                
                # Get insight agent response
                insight_response = insight_agent.chat(request.messages, metrics_info, run_id)
                
                # Map Insight response to AgentChat response
                response.type = insight_response.get("type", "")
                response.content = insight_response.get("content", "")
                response.chart_type = insight_response.get("chart_type", "")
                response.explanation = insight_response.get("explanation", "")
                response.title = insight_response.get("title", "")
                response.samples_limit = insight_response.get("samples_limit", 1000)
                response.used_metrics.extend(insight_response.get("used_metrics", []))
                # Handle queries
                queries = insight_response.get("queries", [])
                for query_dict in queries:
                    query = self.convert_query_dict_to_protobuf(query_dict)
                    response.queries.append(query)
                
                # Handle time range
                if "time_range" in insight_response and insight_response["time_range"]:
                    time_range = api_pb2.TimeRangeLite()
                    tr_dict = insight_response["time_range"]
                    time_range.start = tr_dict.get("start", "")
                    time_range.end = tr_dict.get("end", "")
                    time_range.step = tr_dict.get("step", 0)
                    time_range.timezone = tr_dict.get("timezone", "")
                    response.time_range.CopyFrom(time_range)
            elif routing_result['agent_type'] == 'qa':
                # Use QA Agent
                qa_agent = QAAgent(self.model_registry, request.project, github_token=self.github_token)
                
                # Get QA agent response with full conversation history
                qa_response = qa_agent.chat_sync(request.messages, self.auth_headers)
                
                # Map QA response to AgentChat response
                response.type = qa_response.get("type", "")
                response.content = qa_response.get("content", "")
                response.explanation = "Generated using Sentio documentation and web search."
                response.title = "Sentio Q&A Response"
            
            return response
            
        except Exception as e:
            logger.error(f"Error in agent_chat: {str(e)}")
            
            # Return error response
            error_response = api_pb2.AgentChatResponse()
            error_response.type = "error"
            error_response.content = "I encountered an error while processing your request. Please try again."
            error_response.run_id = str(uuid4())
            error_response.agent_type = "error"
            error_response.routing_reasoning = f"Error occurred: {str(e)}"
            
            return error_response

    def agent_chat_stream(self, request: api_pb2.AgentChatRequest) -> Generator[api_pb2.AgentChatStreamResponse, None, None]:
        """Handle streaming agent chat requests"""
        try:
            run_id = uuid4()
            
            # Convert protobuf data to formats expected by agents
            schema_info = self.build_schema_md(request.schema.tables)
            metrics_info = self.format_metrics_info(request.metrics)
            default_tables = self.get_default_tables(request.schema)
            
            # Step 1: Get routing decision
            routing_result = self.routing_agent.route_query(
                messages=request.messages,
                all_tables_metadata=schema_info,
                default_tables=default_tables,
                available_metrics=metrics_info,
                run_id=run_id,
                hostname=self.sql_api.host
            )
            
            logger.info(f"Streaming routing decision: {routing_result['agent_type']} - {routing_result['reasoning']}")
            
            # Step 2: Yield routing information
            routing_chunk = api_pb2.AgentChatStreamResponse()
            routing_chunk.chunk_type = "routing"
            routing_chunk.chunk = f"Routing to {routing_result['agent_type']} agent: {routing_result['reasoning']}"
            routing_chunk.done = False
            routing_chunk.agent_type = routing_result['agent_type']
            routing_chunk.routing_reasoning = routing_result['reasoning']
            yield routing_chunk
            
            # Step 3: Stream from appropriate agent
            if routing_result['agent_type'] == 'sql':
                # Use SQL Agent
                sql_agent = SQLAgent(self.model_registry, self.sql_api)
                
                # Use table selection agent to select relevant tables
                selected_table_names = self.table_selection_agent.select_tables(
                    messages=request.messages,
                    all_tables_metadata=schema_info,
                    default_tables=default_tables,
                    run_id=run_id
                )
                selected_tables = [t for t in request.schema.tables if t.name in selected_table_names]
                table_summaries = None
                column_summaries = None
                if len(selected_tables) < 8:
                    table_summaries, column_summaries = self.table_summarizer.get_table_summaries(
                        request.project,
                        selected_tables,
                        self.sql_api,
                        run_id
                    )
                filtered_schema_info = self.build_schema_md(selected_tables, table_summaries, column_summaries)
                # Stream from SQL Agent
                for chunk_dict in sql_agent.chat_stream(request.messages, filtered_schema_info, run_id):
                    stream_response = api_pb2.AgentChatStreamResponse()
                    
                    # Map common fields
                    stream_response.chunk_type = chunk_dict.get("chunk_type", "")
                    stream_response.chunk = chunk_dict.get("chunk", "")
                    stream_response.done = chunk_dict.get("done", False)
                    stream_response.run_id = chunk_dict.get("run_id", str(run_id))
                    stream_response.agent_type = routing_result['agent_type']
                    stream_response.routing_reasoning = routing_result['reasoning']
                    stream_response.used_tables.extend(chunk_dict.get("used_tables", []))
                    
                    # Map SQL-specific fields
                    if "sql" in chunk_dict:
                        stream_response.sql = chunk_dict["sql"]
                    if "chart_type" in chunk_dict:
                        stream_response.chart_type = chunk_dict["chart_type"]
                    if "explanation" in chunk_dict:
                        stream_response.explanation = chunk_dict["explanation"]
                    if "title" in chunk_dict:
                        stream_response.title = chunk_dict["title"]
                    if "questions" in chunk_dict:
                        stream_response.questions.extend(chunk_dict["questions"])
                    yield stream_response
                    
            elif routing_result['agent_type'] == 'insight':
                # Use Insight Agent
                insight_agent = InsightAgent(self.model_registry, request.project)
                
                # Stream from Insight Agent
                for chunk_dict in insight_agent.chat_stream(request.messages, metrics_info, run_id):
                    stream_response = api_pb2.AgentChatStreamResponse()
                    
                    # Map common fields
                    stream_response.chunk_type = chunk_dict.get("chunk_type", "")
                    stream_response.chunk = chunk_dict.get("chunk", "")
                    stream_response.done = chunk_dict.get("done", False)
                    stream_response.run_id = chunk_dict.get("run_id", str(run_id))
                    stream_response.agent_type = routing_result['agent_type']
                    stream_response.routing_reasoning = routing_result['reasoning']
                    stream_response.used_metrics.extend(chunk_dict.get('used_metrics', []))
                    if "chart_type" in chunk_dict:
                        stream_response.chart_type = chunk_dict["chart_type"]
                    if "explanation" in chunk_dict:
                        stream_response.explanation = chunk_dict["explanation"]
                    if "title" in chunk_dict:
                        stream_response.title = chunk_dict["title"]
                    if "samples_limit" in chunk_dict:
                        stream_response.samples_limit = chunk_dict["samples_limit"]

                    # Handle queries
                    queries = chunk_dict.get("queries", [])
                    for query_dict in queries:
                        query = self.convert_query_dict_to_protobuf(query_dict)
                        stream_response.queries.append(query)
                    
                    # Handle time range
                    if "time_range" in chunk_dict and chunk_dict["time_range"]:
                        time_range = api_pb2.TimeRangeLite()
                        tr_dict = chunk_dict["time_range"]
                        time_range.start = tr_dict.get("start", "")
                        time_range.end = tr_dict.get("end", "")
                        time_range.step = tr_dict.get("step", 0)
                        time_range.timezone = tr_dict.get("timezone", "")
                        stream_response.time_range.CopyFrom(time_range)
                    yield stream_response
            elif routing_result['agent_type'] == 'qa':
                # Use QA Agent
                qa_agent = QAAgent(self.model_registry, request.project, github_token=self.github_token)
                
                # Stream from QA Agent
                for chunk_dict in qa_agent.chat_stream(request.messages, run_id, self.auth_headers):
                    stream_response = api_pb2.AgentChatStreamResponse()
                    
                    # Map common fields
                    stream_response.chunk_type = chunk_dict.get("chunk_type", "")
                    stream_response.chunk = chunk_dict.get("chunk", "")
                    stream_response.done = chunk_dict.get("done", False)
                    stream_response.run_id = chunk_dict.get("run_id", str(run_id))
                    stream_response.agent_type = routing_result['agent_type']
                    yield stream_response
            
        except Exception as e:
            logger.error(f"Error in agent_chat_stream: {str(e)}")
            
            # Yield error response
            error_response = api_pb2.AgentChatStreamResponse()
            error_response.chunk_type = "error"
            error_response.chunk = "I encountered an error while processing your request. Please try again."
            error_response.done = True
            error_response.run_id = str(uuid4())
            error_response.agent_type = "error"
            error_response.routing_reasoning = f"Error occurred: {str(e)}"
            
            yield error_response

    def convert_query_dict_to_protobuf(self, query_dict: Dict) -> api_pb2.Query:
        """Convert dictionary to protobuf message"""
        query = api_pb2.Query()
        query.query = query_dict.get("query", "")
        query.alias = query_dict.get("alias", "")
        query.id = query_dict.get("id", "")
        
        # Handle label selector
        if "labelSelector" in query_dict:
            for key, value in query_dict["labelSelector"].items():
                query.label_selector[key] = value
        
        # Handle aggregate
        if "aggregate" in query_dict:
            agg_dict = query_dict["aggregate"]
            aggregate = api_pb2.Aggregate()
            
            # Map operation
            op_str = agg_dict.get("op", "SUM").upper()
            if op_str == "AVG":
                aggregate.op = api_pb2.Aggregate.AggregateOps.AVG
            elif op_str == "SUM":
                aggregate.op = api_pb2.Aggregate.AggregateOps.SUM
            elif op_str == "MIN":
                aggregate.op = api_pb2.Aggregate.AggregateOps.MIN
            elif op_str == "MAX":
                aggregate.op = api_pb2.Aggregate.AggregateOps.MAX
            elif op_str == "COUNT":
                aggregate.op = api_pb2.Aggregate.AggregateOps.COUNT
            
            aggregate.grouping.extend(agg_dict.get("grouping", []))
            query.aggregate.CopyFrom(aggregate)
        
        # Handle functions
        if "functions" in query_dict:
            for func_dict in query_dict["functions"]:
                function = api_pb2.Function()
                function.name = func_dict.get("name", "")
                
                # Handle arguments
                for arg_dict in func_dict.get("arguments", []):
                    argument = api_pb2.Argument()
                    
                    if "durationValue" in arg_dict:
                        duration = api_pb2.Duration()
                        duration.value = arg_dict["durationValue"].get("value", 0)
                        duration.unit = arg_dict["durationValue"].get("unit", "")
                        argument.duration_value.CopyFrom(duration)
                    elif "intValue" in arg_dict:
                        argument.int_value = arg_dict["intValue"]
                    elif "stringValue" in arg_dict:
                        argument.string_value = arg_dict["stringValue"]
                    
                    function.arguments.append(argument)
                
                query.functions.append(function)

        return query