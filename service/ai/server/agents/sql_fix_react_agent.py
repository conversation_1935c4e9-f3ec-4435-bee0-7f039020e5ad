"""
LangGraph React Agent for SQL Fixing

This module implements a React agent using LangGraph to fix SQL queries.
It replaces the existing SQLChain.fix_sql() method with enhanced reasoning capabilities.
"""

import re
from typing import Dict, Any, Optional
from uuid import uuid4

from langgraph.prebuilt import create_react_agent
from langchain_core.messages import HumanMessage
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from service.ai.server.models.model_registry import ModelRegistry
from service.ai.server.agents.document_agent import DocumentAgent, file_search_tool_clickhouse
from sql_api import SqlApi


class SQLFixInput(BaseModel):
    """Input for SQL fix operations."""
    old_string: str = Field(description="Exact text to replace from the original SQL")
    new_string: str = Field(description="Replacement text")
    replace_all: bool = Field(default=False, description="Replace all occurrences of old_string")


class SQLTestInput(BaseModel):
    """Input for SQL testing operations."""
    pass  # No parameters needed - tests the current internal query


class SQLViewInput(BaseModel):
    """Input for viewing current query operations."""
    pass  # No parameters needed - shows the current internal query


class SQLDocumentSearchInput(BaseModel):
    """Input for searching ClickHouse documentation."""
    query: str = Field(description="Search query for ClickHouse documentation (e.g., 'array functions', 'JOIN syntax', 'date functions')")


class SQLRejectInput(BaseModel):
    """Input for rejecting a fix request."""
    reason: str = Field(description="Reason why the error cannot be fixed through SQL modifications")


class SQLFixReactAgent:
    """
    A LangGraph React agent for fixing SQL queries.
    
    This agent can:
    - Test SQL queries for syntax errors
    - Fix SQL queries based on error messages
    - Handle both short queries (direct fix) and long queries (unified diff format)
    - Retry failed fixes with different strategies
    - Optimize queries for performance issues
    """
    
    def __init__(self, model_registry: ModelRegistry, host: str):
        """
        Initialize the SQL Fix React Agent.
        
        Args:
            model_registry: Registry for accessing different models
            host: Host URL for SQL API
        """
        self.model_registry = model_registry
        self.host = host
        
        # Internal state for maintaining current query
        self.current_query = ""
        self.sql_api = None
        
        # Track if the fix was rejected
        self.fix_rejected = False
        self.rejection_reason = ""
        
        
        # Initialize document agent for ClickHouse documentation search
        self.document_agent = DocumentAgent(model_registry)
        
        # Initialize the agent with tools
        self._setup_tools()
        
    def _setup_tools(self):
        """Set up the tools for the React agent."""
        
        @tool("test_sql", args_schema=SQLTestInput)
        def test_sql() -> str:
            """
            Test the current SQL query for syntax errors.
            
            Tests the current internal query state against the database.
                
            Returns:
                A message indicating if the query is valid or has errors
            """
            try:
                if not self.current_query:
                    return "Error: No current query to test"
                    
                _, error = self.sql_api.test(self.current_query)
                if error:
                    return f"Current SQL query failed with error: {error}"
                return "Current SQL query is valid and executable"
            except Exception as e:
                return f"Error testing current SQL query: {str(e)}"
        
        @tool("edit_sql", args_schema=SQLFixInput)
        def edit_sql(old_string: str, new_string: str, replace_all: bool = False) -> str:
            """
            Edit the current SQL query by replacing a substring.
            
            Key Requirements:
            - The old_string must match the current query contents exactly (including whitespace/indentation)
            - The old_string must be unique in the query, or use replace_all: True for multiple instances
            - This modifies the internal query state
            - IMPORTANT: Always preserve existing SQL comments (-- and /* */) unless they are directly causing syntax errors
            
            Parameters:
            - old_string: Exact text to replace from the current SQL query
            - new_string: Replacement text
            - replace_all: Optional boolean to replace all occurrences (default: False)
            
            Example:
            edit_sql(
                old_string="COUNT(*) > 100",
                new_string="COUNT(*) >= 100"
            )
            
            Returns:
                Success message or error message
            """
            try:
                if not self.current_query:
                    return "Error: No current query to edit"
                
                # Check if old_string exists in the current query
                if old_string not in self.current_query:
                    return f"Error: Could not find substring '{old_string}' in current query"
                
                # Count occurrences
                occurrences = self.current_query.count(old_string)
                
                if occurrences > 1 and not replace_all:
                    return f"Error: Found {occurrences} occurrences of '{old_string}'. Use replace_all=True to replace all instances."
                
                # Perform the replacement and update internal state
                if replace_all:
                    self.current_query = self.current_query.replace(old_string, new_string)
                    return f"Applied replacement to all {occurrences} occurrences successfully. Query updated."
                else:
                    self.current_query = self.current_query.replace(old_string, new_string, 1)
                    return f"Applied substring replacement successfully. Query updated."
                    
            except Exception as e:
                return f"Failed to apply replacement: {str(e)}"
        
        @tool("view_current_query", args_schema=SQLViewInput)
        def view_current_query() -> str:
            """
            View the current SQL query state.
            
            Shows the current internal query after any edits have been applied.
            Useful for checking what the query looks like before testing or making further edits.
                
            Returns:
                The current SQL query or an error message if no query is loaded
            """
            try:
                if not self.current_query:
                    return "Error: No current query loaded"
                
                return f"Current query:\n```sql\n{self.current_query}\n```"
                    
            except Exception as e:
                return f"Error retrieving current query: {str(e)}"
        
        @tool("search_clickhouse_docs", args_schema=SQLDocumentSearchInput)
        def search_clickhouse_docs(query: str) -> str:
            """
            Search ClickHouse documentation for SQL syntax and functions.
            
            Use this tool when you encounter unfamiliar ClickHouse syntax, functions, or need
            to understand specific ClickHouse SQL features while fixing queries.
            
            Parameters:
            - query: Search query for ClickHouse documentation (e.g., 'array functions', 'JOIN syntax', 'date functions')
            
            Returns:
                Relevant ClickHouse documentation content
            """
            try:
                context = self.document_agent.retrieve_context(query, file_search_tool_clickhouse)
                return f"ClickHouse Documentation:\n{context}"
                    
            except Exception as e:
                return f"Error searching ClickHouse documentation: {str(e)}"
        
        @tool("reject_fix", args_schema=SQLRejectInput)
        def reject_fix(reason: str) -> str:
            """
            Reject the fix request when the error cannot be resolved through SQL modifications.
            
            Use this tool when the error requires system-level changes such as:
            - Permission/privilege issues
            - Connection problems  
            - Resource/infrastructure issues
            - Authentication failures
            
            Parameters:
            - reason: Clear explanation of why the error cannot be fixed through SQL changes
            
            Returns:
                Confirmation message that the fix was rejected
            """
            try:
                self.fix_rejected = True
                self.rejection_reason = reason
                return f"Fix request rejected: {reason}"
                    
            except Exception as e:
                return f"Error rejecting fix: {str(e)}"
        
        self.tools = [test_sql, edit_sql, view_current_query, search_clickhouse_docs, reject_fix]
    
    def normalize_sql(self, sql: str) -> str:
        """Normalize SQL query by extracting the main SELECT statement."""
        match = re.search(
            r"select(.+)[;$]", sql.strip(), re.MULTILINE | re.IGNORECASE | re.DOTALL
        )
        if match:
            return match.group()
        return sql
    
    def fix_sql(
        self,
        original_sql: str,
        schema: str,
        error: str,
        auth_headers: Optional[Dict[str, str]] = None,
        project: Optional[str] = None,
        version: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Fix a SQL query using the React agent.
        
        Args:
            original_sql: The original SQL query that failed
            schema: Database schema information
            error: Error message from the failed SQL query
            auth_headers: Authentication headers for SQL API
            project: Project identifier
            version: Version identifier
            
        Returns:
            Dict containing the fixed SQL result and run_id
        """
        if not original_sql:
            raise ValueError("Original SQL is required for SQL fixing")
        
        if auth_headers is None:
            auth_headers = {}
        
        # Initialize SQL API and current query state
        self.sql_api = SqlApi(self.host, project, version, auth_headers)
        self.current_query = original_sql
        
        # Reset rejection state
        self.fix_rejected = False
        self.rejection_reason = ""
        
        # Build system prompt
        system_prompt = self._build_system_prompt(schema)
        
        # Create React agent
        agent = create_react_agent(
            model=self.model_registry.get_gpt41(),
            tools=self.tools,
            prompt=system_prompt
        )
        
        # Build conversation messages
        messages = []
        
        # Add the error and fix request
        error_cleaned = error
        if error_cleaned.find("SETTINGS log_comment") != -1:
            error_cleaned = error_cleaned[:error_cleaned.find("SETTINGS log_comment")]
        
        fix_request = f"""
The following ClickHouse SQL query:

```sql
{original_sql}
```
failed with the following error: {error_cleaned}

Please analyze the error and fix the SQL query.
"""
        messages.append(HumanMessage(content=fix_request))
        
        # Execute the agent
        try:
            # Run the agent with configuration
            agent.invoke(
                {"messages": messages},
                config={"configurable": {"thread_id": str(uuid4())}}
            )
            
            # Check if the agent rejected the fix request
            if self.fix_rejected:
                raise ValueError(f"Fix rejected: {self.rejection_reason}")
            
            # Check if the agent completed successfully with a fixed query
            if self.current_query and self.current_query != original_sql:
                return {
                    "result": self.current_query,
                    "run_id": str(uuid4())
                }
            else:
                raise ValueError("Agent did not produce a valid query fix")
                
        except Exception as e:
            raise ValueError(f"Failed to fix SQL: {str(e)}")
    
    
    def _build_system_prompt(self, schema: str) -> str:
        """Build the system prompt for the React agent."""
        prompt = f"""You are an expert **ClickHouse Database Engineer AI Agent**. Your sole purpose is to receive a broken SQL query, an error message, and a database schema, and then use the provided tools to generate a corrected, executable SQL query.

## **Workflow**

You **MUST** follow this iterative process:

1.  **Assess Error**: First, determine if the error can be fixed by modifying the SQL query:
      - **FIXABLE**: Syntax errors, wrong column names, type mismatches, function usage, query logic issues, performance optimization (slow queries, inefficient joins, missing indexes)
      - **REJECT**: Permission/privilege errors, connection issues, resource limits, infrastructure problems
      - If the error requires system-level changes (like "ACCESS_DENIED", "Not enough privileges", "Connection refused"), use the `reject_fix` tool with a clear reason
2.  **Analyze**: If fixable, carefully examine the original SQL query, error message and table schemas. Form a precise hypothesis about the root cause of the error.
3.  **Plan**: Based on your analysis, formulate a specific plan to fix the query. Decide what exact change needs to be made.
4.  **Execute & Test**:
      - Use the `edit_sql` tool to apply your single, targeted fix.
      - Immediately after using `edit_sql`, you **MUST** use the `test_sql` tool on the modified query to verify if the fix was successful.
5.  **Iterate**:
      - **If `test_sql` succeeds**, your job is done. Provide the final, working SQL as your answer.
      - **If `test_sql` fails**, analyze the new error message, refine your hypothesis, and repeat the workflow from Step 1.

##  **Tool Usage**

- **`edit_sql(old_string, new_string, replace_all=False)`**: Use this to modify the current query state. Make precise, minimal changes.
      - *Example*: To fix a wrong function name, use `edit_sql(old_string='COUNTD(user_id)', new_string='uniq(user_id)')`.
      - This updates the internal query state automatically.
- **`test_sql()`**: Use this to validate the current query state *after every modification*. No parameters needed.
      - **IMPORTANT**: Before testing, if the query contains Sentio variables like `${{limit}}`, `${{start_date}}`, etc., you must temporarily replace them with appropriate constants for testing. After testing succeeds, restore the original variables in your final result.
- **`view_current_query()`**: Use this to see the current query state. Helpful for double checking what the query looks like after edits.
- **`search_clickhouse_docs(query)`**: Search ClickHouse documentation for syntax, functions, or features you're unfamiliar with.
- **`reject_fix(reason)`**: Use this to reject the fix request when the error requires system-level changes (permissions, connections, etc.).


## **Rules & Constraints**

- **Termination**: If you fail to produce a valid query after **5** attempts, you **MUST** terminate the process. To terminate, provide the final error message you received and state that you are unable to find a fix.
- **Schema Adherence**: Only use tables and columns defined in the provided schema. Qualify column names with table names (e.g., `users.id`) if the query involves `JOIN`s.
- **Comment Preservation**: **DO NOT** remove or modify comments (both single-line `--` and multi-line `/* */` comments) unless they are directly causing syntax errors.
- **ClickHouse Syntax**:
      - Use single quotes for string literals (e.g., `'value'`).
      - Use standard ClickHouse functions (`toDate()`, `sumIf()`, `uniq()`, etc.).
      - Explicitly specify the `JOIN` type (e.g., `INNER JOIN`).
      - All SQL statements must end with a semicolon `;`.
      - **PRESERVE Sentio variables**: Keep `${{}}` variable syntax intact (e.g., `${{limit}}`, `${{start_date}}`) - these are Sentio platform variables that get substituted at runtime.
      - **Variable Testing Strategy**: When testing queries with Sentio variables:
        1. Temporarily replace `${{limit}}` with `100`, `${{offset}}` with `0`, `${{start_date}}`/`${{end_date}}` with `'2024-01-01'`, etc.
        2. Test the query with these constants
        3. After successful testing, restore the original `${{}}` variables in your final result
- **Final Output**: Once you have a working SQL query (validated with test_sql), your work is complete. The corrected query is maintained in the internal state.

## **Available Tables & Columns**

```
{schema}
```
"""
        return prompt
    


def create_sql_fix_agent(
    model_registry: ModelRegistry, 
    host: str
) -> SQLFixReactAgent:
    """
    Factory function to create a SQL Fix React Agent.
    
    Args:
        model_registry: Registry for accessing different models
        host: Host URL for SQL API
        
    Returns:
        Configured SQLFixReactAgent instance
    """
    return SQLFixReactAgent(
        model_registry=model_registry,
        host=host
    )