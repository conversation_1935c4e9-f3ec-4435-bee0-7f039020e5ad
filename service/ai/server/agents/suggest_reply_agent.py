from service.ai.api import api_pb2
from typing import List, Dict
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from pydantic import BaseModel, <PERSON>
from typing import List as TypedList
from service.ai.server.models.model_registry import ModelRegistry
SYSTEM_PROMPT = """Generate three suggested replies for the user based on the conversation context of an AI assistant's response. The AI assistant can handle blockchain data analysis, SQL queries, metrics analysis, Sentio platform questions, processor debugging, and documentation guidance.

# Steps
1. Review the AI assistant's response and preceding context.
2. Identify the response type: blockchain data, SQL/insight queries, Sentio platform features, processor debugging, or documentation guidance.
3. Craft three distinct, contextually relevant suggestions with varied perspectives.
4. Ensure suggestions encourage conversation continuation, seek clarification, or request more specific information.

# Output Format
- List format with complete sentences or short paragraphs.
- Keep brief and concise - ideally 10 words or less.
- Direct, actionable follow-ups requiring minimal editing.
- Maintain clarity and relevance.

# Context Guidelines

## For Query Results (SQL/Insight Agent):
- Suggest refinements, related data exploration, or alternative visualizations.
- Propose chart type changes, time range adjustments, or data aggregation modifications.
- Include requests for specific blockchain metrics or data points.

## For Sentio Platform Questions (QA Agent):
- Ask for implementation details or step-by-step guides.
- Request specific code examples or configuration samples.
- Seek clarification on best practices or advanced features.

## For Processor Debugging (QA Agent):
- Ask for more specific error details or logs.
- Request troubleshooting steps for related issues.
- Inquire about performance optimization or monitoring.

## For Documentation Guidance (QA Agent):
- Request links to specific documentation sections.
- Ask for practical examples or tutorials.
- Seek information about related features or concepts.

# Examples

## Blockchain Data Analysis:
input: **AI:** "I've analyzed Ethereum transaction volumes showing a spike on Wednesday with 1.2M transactions."
output: ["Compare with Bitcoin volumes?", "What caused Wednesday's spike?", "Show as bar chart instead?"]

input: **AI:** "Here's the TVL trend for your DeFi protocol over the past month."
output: ["Break down by token type?", "Compare with competitor protocols?", "Show daily percentage changes?"]

## Sentio Platform Questions:
input: **AI:** "Sentio processors can index data 100x faster than traditional indexers using TypeScript with ABI typings."
output: ["How do I create my first processor?", "Show me a code example?", "What chains are supported?"]

input: **AI:** "You can monitor your processors in real-time using Sentio's built-in dashboards with zero setup required."
output: ["How do I access the dashboard?", "Can I create custom visualizations?", "What metrics are available?"]

## Processor Debugging:
input: **AI:** "This error typically occurs when the contract ABI doesn't match the deployed contract. Check your ABI configuration."
output: ["How do I verify the ABI?", "Where do I update the configuration?", "What are common ABI issues?"]

input: **AI:** "To optimize processor performance, consider reducing the number of event handlers and using batch processing."
output: ["Show me batch processing examples?", "How do I monitor performance?", "What's the ideal batch size?"]

## Documentation Guidance:
input: **AI:** "You can find detailed processor setup instructions in the Sentio documentation at docs.sentio.xyz/docs/collect-data/processors."
output: ["Show me a quick start tutorial?", "What about advanced configurations?", "Are there video guides available?"]
"""

class SuggestReplyOutput(BaseModel):
    replies: TypedList[str] = Field(
        description="List of three suggested replies for the user"
    )

class SuggestReplyAgent:
    def __init__(self, model_registry: ModelRegistry):
        self.model_registry = model_registry

    def suggest_reply(self, messages: List[Dict[str, str]]):
        """
        Suggest three replies for the user based on the conversation context of an AI assistant's response.
        
        Args:
            messages: The conversation context of an AI assistant's response
            
        Returns:
            A dict with three suggested replies for the user
        """
        try:
            # Prepare system prompt with additional context if metadata is provided
            system_content = SYSTEM_PROMPT
            # Convert messages to LangChain format
            langchain_messages = [SystemMessage(content=system_content)]
        
            for msg in messages:
                if msg.role == api_pb2.Message.Role.USER:
                    langchain_messages.append(HumanMessage(content=msg.content))
                elif msg.role == api_pb2.Message.Role.ASSISTANT:
                    langchain_messages.append(AIMessage(content=msg.content))

            llm = self.model_registry.get_gpt4o_mini()
            structured_llm = llm.with_structured_output(SuggestReplyOutput)
            output: SuggestReplyOutput = structured_llm.invoke(langchain_messages)

            return output.replies
        except Exception as e:
            print(f"Error in suggest_reply: {str(e)}")
            return []
