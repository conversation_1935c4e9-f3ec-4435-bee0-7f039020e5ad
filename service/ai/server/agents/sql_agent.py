from service.ai.api import api_pb2
from typing import Dict, List, Generator
from langchain.schema.runnable import <PERSON><PERSON><PERSON><PERSON><PERSON>b<PERSON>
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage, BaseMessage
from langchain_core.tools import tool
from pydantic import BaseModel, Field
from typing import Optional, List as TypedList
from sql_api import SqlApi
from uuid import UUID
import requests
import datetime
import logging
from service.ai.server.agents.document_agent import DocumentAgent, file_search_tool_sentio_doc, file_search_tool_clickhouse
from service.ai.server.agents.web_search_agent import WebSearchAgent, WebSearchInput
from service.ai.server.models.model_registry import ModelRegistry
SYSTEM_PROMPT = """You are a helpful blockchain data analyst assistant. Your primary responsibilities include:
1. Answering questions about blockchain data.
2. Generating ClickHouse SQL queries and visualizations to analyze data when needed.
3. Working in a Sentio's project.
4. Providing up-to-date information about blockchain and cryptocurrency topics by searching the web when necessary.

<principles>
- Generate SQL queries only when the user explicitly asks for data analysis or when it's clearly needed to answer their question.
- When a user's request requires data from multiple tables, always generate a single SQL query that joins or combines these tables appropriately. Never generate multiple separate queries when one comprehensive query can address the user's needs.
- When displaying addresses in dashboard results, prefer readable names over addresses using CASE statements when mapping context is available.
</principles>

<tool_calling>
- Check the SQL query syntax using the `test_sql` tool.
- After generating a SQL query, always use the `return_sql_result` tool to return the result to the user.
- Use `retrieve_sentio_document` tool to retrieve Sentio documentation to answer questions about Sentio concepts.
- Use `retrieve_sql_document` tool to retrieve ClickHouse SQL reference documentation to augment the query generation.
- Use `web_search` tool to search for up-to-date information about blockchain, cryptocurrency, and related topics when needed, especially for:
  * Current market conditions or trends
  * Recent blockchain protocol updates
  * New tokens or projects
  * Regulatory developments
  * Technical specifications not available in the database
- When you need to get the price of a cryptocurrency, use the `get_crypto_price` tool.
</tool_calling>

<clickhouse_sql_syntax_requirements>
1. ONLY use columns that are explicitly listed in the provided table schemas.
2. Before writing any query, carefully review the available schemas to confirm exact column names, data types, and meanings.
3. Use single quotes for string literals (e.g., 'value' not "value").
4. Date functions: use `toDate()`, `toDateTime()`, `now()`, `today()`.
5. Aggregation: use functions like `sumIf()`, `countIf()` for conditional aggregation.
6. Subqueries: use `WITH` clauses for CTEs when appropriate.
7. JOIN syntax: specify join type explicitly (e.g., `LEFT JOIN`, `INNER JOIN`).
8. Array functions: use `arrayJoin()`, `arrayElement()`, `has()` when working with arrays.
9. String functions: use `position()`, `substring()`, `lower()`, `upper()`, `replaceRegexpAll()`.
10. Window functions: use the `OVER` clause for window functions.
11. `LIMIT` and `OFFSET` must come at the end of the query.
12. For optimal performance, specify `ORDER BY` before `GROUP BY`.
13. All SQL statements must end with a semicolon.
14. Better to restrict the time range of the query to a specific time period to improve the query performance.
</clickhouse_sql_syntax_requirements>

<working_with_multiple_tables>
1. Use JOIN operations (`INNER JOIN`, `LEFT JOIN`, `RIGHT JOIN`, `FULL JOIN`) to combine data from multiple tables.
2. Use appropriate join conditions based on common fields between tables.
3. Create subqueries or CTEs (`WITH` clause) for complex multi-table operations.
4. Always use table aliases when joining multiple tables for clarity.
5. Select only the necessary columns to improve query performance.
</working_with_multiple_tables>

<suggesting_visualizations>
1. Use LINE charts for:
   - Time series data.
   - Trends over time.
   - Historical analysis.
2. Use BAR charts for:
   - Comparisons between categories.
   - Ranking data.
   - Distribution analysis.
3. Use PIE charts for:
   - Percentage distributions.
   - Part-to-whole relationships.
   - Market share analysis.
4. Use TABLE view for:
   - Raw data inspection.
   - Complex data with many columns.
   - When no clear visualization fits.
</suggesting_visualizations>

<examples>
<example 1 - Compare TPS trends last month for Sui and Aptos>
```sql
WITH sui_tps AS ( 
    SELECT 
        toDate(timestamp) as date,
        count(*) / 86400 as tps
    FROM sui.transactions
    WHERE timestamp >= today() - 30
    GROUP BY date
),
aptos_tps AS (
    SELECT 
        toDate(block_timestamp) as date,
        count(*) / 86400 as tps
    FROM aptos.transactions
    WHERE block_timestamp >= today() - 30
    GROUP BY date
)
SELECT 
    COALESCE(sui_tps.date, aptos_tps.date) as date,
    sui_tps.tps as sui_tps,
    aptos_tps.tps as aptos_tps
FROM sui_tps
FULL OUTER JOIN aptos_tps ON sui_tps.date = aptos_tps.date
ORDER BY date;
```
</example 1>

<example 2 - Pool analytics with readable names>
```sql
SELECT 
    toDate(timestamp) as date,
    CASE 
        WHEN pool_address = '0x9fc6b2cadaa287d2c4d635231b51c96b8cc92859' THEN 'wS-METRO'
        WHEN pool_address = '0x56b404073c990e828691af936bcfff710f6c97a1' THEN 'wS-USDC'
        ELSE pool_address 
    END AS pool_name,
    SUM(volume_usd) as daily_volume
FROM pool_data 
WHERE timestamp >= today() - 7
GROUP BY date, pool_address
ORDER BY date;
```
</example 2>
</examples>
"""


# Define the Pydantic models for our tools
class SQLQueryInput(BaseModel):
	sql: str = Field(description="The SQL query to execute")
	chart_type: str = Field(description="The type of visualization to use (TABLE, LINE, BAR, or PIE)")
	explanation: str = Field(description="Explanation of what the query does and why this visualization was chosen")
	title: str = Field(description="Suggested title for the chart")
	used_tables: List[str] = Field(description="List of table names used in the SQL query")

class TestSQLInput(BaseModel):
	sql: str = Field(description="The SQL query to test")

# class SampleDataInput(BaseModel):
# 	table_name: str = Field(description="The name of the table to sample data from")
# 	limit: int = Field(default=10, description="The number of rows to sample")
# 	where_clause: Optional[str] = Field(default=None, description="Optional WHERE clause to filter the sample data")

class CryptoPriceInput(BaseModel):
	symbol: str = Field(description="The cryptocurrency symbol (e.g., BTC, ETH)")
	timestamp: Optional[str] = Field(default=None, description="Optional timestamp in ISO format (e.g., '2023-01-01T00:00:00Z'). If not provided, the current time will be used.")

class RetrieveDocumentInput(BaseModel):
	question: str = Field(description="The question to retrieve ClickHouse SQL reference documentation. Examples: 'How to use the `toDate` function?', 'What is the syntax for the `SELECT` statement?'")

class RetrieveSentioDocumentInput(BaseModel):
	question: str = Field(description="The question to retrieve Sentio documentation. Examples: 'How to write a sentio processor?'")

class WebSearchInput(BaseModel):
	query: str = Field(description="The search query to look up on the web")

# Maximum number of test_sql iterations
MAX_TEST_SQL_ITERATIONS = 3

class SQLAgent:
	def __init__(self, model_registry: ModelRegistry, sql_api: SqlApi):
		self.model_registry = model_registry
		self.sql_api = sql_api

		# Define the tool for testing SQL queries
		@tool("test_sql", args_schema=TestSQLInput, return_direct=True)
		def test_sql(
			sql: str,
		) -> str:
			"""
			Test a SQL query for syntax errors.
			
			Args:
				sql: The SQL query to test
				
			Returns:
				A message indicating if the query is valid or has errors
			"""
			_, error = self.sql_api.test(sql)
			
			if error:
				return f"The SQL query has an error: {str(error)}. Please fix the query."
			
			return "SQL query is valid"

		# Define the tool for SQL query generation with structured output
		@tool("return_sql_result", args_schema=SQLQueryInput, return_direct=True)
		def return_sql_result(
			sql: str,
			chart_type: str,
			explanation: str,
			title: str,
			used_tables: List[str],
		) -> str:
			"""
            return the SQL query result to the user
            
            Args:
                sql: The SQL query to return
                chart_type: The type of visualization to use (TABLE, LINE, BAR, or PIE)
                explanation: Explanation of what the query does and why this visualization was chosen
                title: Suggested title for the chart
                used_tables: List of table names used in the SQL query

            Returns:
                A message confirming the SQL query result has been returned
            """
			return "SQL query result returned"

		# Define the tool for sampling data from a table
		# @tool("sample_data", args_schema=SampleDataInput, return_direct=True)
		# def sample_data(
		# 	table_name: str,
		# 	limit: int = 10,
		# 	where_clause: Optional[str] = None,
		# ) -> str:
		# 	"""
    #         Sample data from a specified table to provide context for decision making
            
    #         Args:
    #             table_name: The name of the table to sample data from
    #             limit: The number of rows to sample (default: 10)
    #             where_clause: Optional WHERE clause to filter the sample data
                
    #         Returns:
    #             The sample data from the table
    #         """
		# 	sample_query = f"SELECT * FROM {table_name}"
		# 	if where_clause:
		# 		sample_query += f" WHERE {where_clause}"
		# 	sample_query += f" LIMIT {limit};"
		# 	return self.sql_api.sample_data(sample_query, max_value_size=100, should_continue_message="\n\nPlease continue with generating a SQL query based on the available information and schema.")
			
		# Define the tool for getting cryptocurrency prices
		@tool("get_crypto_price", args_schema=CryptoPriceInput, return_direct=True)
		def get_crypto_price(
			symbol: str,
			timestamp: Optional[str] = None,
		) -> str:
			"""
			Get the price of a cryptocurrency at a specified timestamp
			
			Args:
				symbol: The cryptocurrency symbol (e.g., BTC, ETH)
				timestamp: Optional timestamp in ISO format (e.g., '2023-01-01T00:00:00Z'). If not provided, the current time will be used.
				
			Returns:
				The price of the cryptocurrency at the given timestamp
			"""
			url = f"{self.sql_api.host}/api/v1/prices"
			
			params = {
				"coin_id.symbol": symbol.upper(),
			}

			if timestamp:
				params["timestamp"] = timestamp
			
			headers = self.sql_api.auth_headers
			
			try:
				response = requests.get(url, params=params, headers=headers)
				if response.ok:
					data = response.json()
					if "price" in data:
						return f"The price of {symbol.upper()} at {data.get('timestamp', 'the requested time')} is ${data['price']}"
					return f"Price data for {symbol.upper()} was found, but the price value is missing in the response."
				else:
					return f"Failed to get price for {symbol.upper()}: {response.text} (HTTP {response.status_code})"
			except Exception as e:
				return f"Error retrieving price for {symbol.upper()}: {str(e)}"
			
		@tool("retrieve_sql_document", args_schema=RetrieveDocumentInput, return_direct=True)
		def retrieve_sql_document(
			question: str,
		) -> str:
			"""
			Retrieve ClickHouse SQL reference documentation to augment query generation

			Args:
				question: The question to retrieve ClickHouse SQL reference documentation. Examples: 'How to use the `toDate` function?', 'What is the syntax for the `SELECT` statement?'

			Returns:
				A string containing the retrieved ClickHouse SQL reference documentation
			"""

			document_agent = DocumentAgent(self.model_registry)
			context = document_agent.retrieve_context(question, file_search_tool_clickhouse)
			return context

		@tool("retrieve_sentio_document", args_schema=RetrieveSentioDocumentInput, return_direct=True)
		def retrieve_sentio_document(
			question: str,
		) -> str:
			"""
			Retrieve Sentio documentation to augment query generation
			"""
			document_agent = DocumentAgent(self.model_registry)
			context = document_agent.retrieve_context(question, file_search_tool_sentio_doc)
			return context
		
		@tool("web_search", args_schema=WebSearchInput, return_direct=True)
		def web_search(
			query: str,
		) -> str:
			"""
			Search the web for real-time information about blockchain, cryptocurrency, or other topics
			
			Args:
				query: The search query to look up on the web
				
			Returns:
				A string containing the search results with source URLs
			"""
			# Create a web search agent instance and use it to perform the search
			web_search_agent = WebSearchAgent(self.model_registry)
			return web_search_agent.search(query)

		self.tool_name_to_tool = {
			return_sql_result.name: return_sql_result,
			test_sql.name: test_sql,
			# sample_data.name: sample_data,
			get_crypto_price.name: get_crypto_price,
			retrieve_sql_document.name: retrieve_sql_document,
			retrieve_sentio_document.name: retrieve_sentio_document,
			web_search.name: web_search
		}

	def chat(self, messages: List[Dict[str, str]], schema: str, run_id: UUID):
		# Get current UTC time
		current_utc_time = datetime.datetime.now(datetime.timezone.utc).isoformat()
		
		# Prepare system prompt with additional context including current time, current project and available tables
		system_content = SYSTEM_PROMPT + f"\n\n<project_info>\n\n The current UTC time is {current_utc_time}. The current project is {self.sql_api.project}.\n\n </project_info>\n\n<available_schema>\n\n {schema} \n\n</available_schema>\n\n"

		# Convert messages to LangChain format
		langchain_messages = [SystemMessage(content=system_content)]

		for msg in messages:
			if msg.role == api_pb2.Message.Role.USER:
				langchain_messages.append(HumanMessage(content=msg.content))
			elif msg.role == api_pb2.Message.Role.ASSISTANT:
				langchain_messages.append(AIMessage(content=msg.content))

		return self.chat_inner(langchain_messages, run_id, 0)  # Initialize test_sql_count as 0

	def chat_stream(self, messages: List[Dict[str, str]], schema: str, run_id: UUID) -> Generator[Dict, None, None]:
		"""
		Stream the agent's response using the LLM's streaming capabilities.
		
		Args:
			messages: The messages in the conversation
			schema: The database schema information
			run_id: A unique identifier for this conversation
			
		Returns:
			A generator that yields chunks of the response
		"""
		# Get current UTC time
		current_utc_time = datetime.datetime.now(datetime.timezone.utc).isoformat()
		
		# Prepare system prompt with additional context including current time, current project and available tables
		system_content = SYSTEM_PROMPT + f"\n\n**Current UTC Time:** {current_utc_time}\n\n**Current Project:**{self.sql_api.project}\n\n**Available Schema:**\n" + schema

		# Convert messages to LangChain format
		langchain_messages = [SystemMessage(content=system_content)]

		for msg in messages:
			if msg.role == api_pb2.Message.Role.USER:
				langchain_messages.append(HumanMessage(content=msg.content))
			elif msg.role == api_pb2.Message.Role.ASSISTANT:
				langchain_messages.append(AIMessage(content=msg.content))
		
		for chunk in self.chat_stream_inner(langchain_messages, run_id):
			yield chunk
	
	def chat_stream_inner(self, messages: List[BaseMessage], run_id: UUID) -> Generator[Dict, None, None]:
		"""
		Helper method for continuing a streaming conversation after a tool call
		
		Args:
			messages: The messages in the conversation
			run_id: A unique identifier for this conversation
			
		Returns:
			A generator that yields chunks of the response
		"""
		# Create the LangChain ChatOpenAI instance with the tools
		llm = self.model_registry.get_gpt41(temperature=0.1, streaming=True)

		# Prepare the LLM with tools
		llm_with_tools = llm.bind_tools(list(self.tool_name_to_tool.values()))
		
		# Track tool call chunks by index
		tool_chunks_by_index = {}
		
		try:
			for chunk in llm_with_tools.stream(messages, {"run_id": run_id}):
				# Handle regular text content
				if hasattr(chunk, "content") and chunk.content:
					yield {
						"chunk_type": "text",
						"chunk": chunk.content,
						"done": False
					}
				
				# Process tool call chunks
				if hasattr(chunk, 'tool_call_chunks') and chunk.tool_call_chunks:
					for tool_chunk in chunk.tool_call_chunks:
						index = tool_chunk.get('index', 0)
						
						# Initialize this tool call if it's new
						if index not in tool_chunks_by_index:
							tool_chunks_by_index[index] = {
								"name": None,
								"args_string": "",
								"id": None,
								"args_dict": {},
								"complete": False
							}
						
						# Update with new information from this chunk
						if tool_chunk.get('name'):
							tool_chunks_by_index[index]["name"] = tool_chunk.get('name')
						if tool_chunk.get('id'):
							tool_chunks_by_index[index]["id"] = tool_chunk.get('id')
						if tool_chunk.get('args') is not None:
							tool_chunks_by_index[index]["args_string"] += tool_chunk.get('args', '')
						
						# Try to parse args as JSON if they're not already parsed
						if tool_chunks_by_index[index]["args_string"] and not tool_chunks_by_index[index]["complete"]:
							try:
								args_dict = {}
								# Only attempt to parse if it looks like valid JSON
								if tool_chunks_by_index[index]["args_string"].startswith('{') and tool_chunks_by_index[index]["args_string"].endswith('}'):
									import json
									args_dict = json.loads(tool_chunks_by_index[index]["args_string"])
									tool_chunks_by_index[index]["args_dict"] = args_dict
									tool_chunks_by_index[index]["complete"] = True
							except:
								# Not complete JSON yet, keep accumulating
								pass
						
				# Check for any complete tool calls that haven't been processed
				for index, tool_data in list(tool_chunks_by_index.items()):
					if tool_data["complete"] and not tool_data.get("processed"):
						tool_name = tool_data["name"]
						tool_args = tool_data["args_dict"]
						tool_id = tool_data["id"]
						
						# Mark as processed
						tool_data["processed"] = True
						
						if tool_name == "return_sql_result":
							# This means we're returning SQL results
							sql = tool_args["sql"]
							
							# For the final response, include all metadata
							yield {
								"chunk_type": "query",
								"chunk": "",
								"done": True,
								"sql": sql,
								"chart_type": tool_args["chart_type"],
								"explanation": tool_args["explanation"],
								"title": tool_args["title"],
								"questions": [],
								"used_tables": tool_args.get("used_tables", []),
								"run_id": str(run_id)
							}
							return
						
						# Handle other tool calls
						# Create a synthetic message with the complete tool call
						from langchain_core.messages import AIMessage
						tool_calls_message = AIMessage(
							content="",
							tool_calls=[{
								"name": tool_name,
								"args": tool_args,
								"id": tool_id
							}]
						)
						
						messages_copy = messages.copy()
						messages_copy.append(tool_calls_message)
						
						if tool_name == "test_sql":
							tool_result = self.tool_name_to_tool[tool_name].invoke(tool_args)
							if "is valid" in tool_result:
								yield {
									"chunk_type": "tool",
									"chunk": "Verified the generated SQL query is valid",
									"done": False
								}
							else:
								yield {
									"chunk_type": "tool",
									"chunk": "The generated SQL query is invalid, retrying...",
									"done": False
								}
							
							messages_copy.append(ToolMessage(content=tool_result, tool_call_id=tool_id))
							
							# Continue the conversation with the tool result
							for result_chunk in self.chat_stream_inner(messages_copy, run_id):
								yield result_chunk
							return
							
						elif tool_name in ["get_crypto_price", "retrieve_sql_document", "retrieve_sentio_document", "web_search"]:
							tool_result = self.tool_name_to_tool[tool_name].invoke(tool_args)
							
							if tool_name == "web_search":
								yield {
									"chunk_type": "tool",
									"chunk": "Searching the web for information...",
									"done": False
								}
								
							messages_copy.append(ToolMessage(content=tool_result, tool_call_id=tool_id))
							
							# Continue the conversation with the tool result
							for result_chunk in self.chat_stream_inner(messages_copy, run_id):
								yield result_chunk
							return
			
			# If we made it here, this is a regular response with no tools
			yield {
				"chunk_type": "text",
				"chunk": "",
				"done": True,
				"content": "",
				"sql": "",
				"chart_type": "",
				"explanation": "",
				"title": "",
				"questions": [],
				"used_tables": [],
				"run_id": str(run_id)
			}
			
		except Exception as e:
			# Yield an error message in case of exceptions
			error_message = f"Error in streaming chat: {str(e)}"
			logging.error(error_message)  # Log the error
			
			yield {
				"chunk_type": "error",
				"chunk": "I encountered an error while processing your request. Please try again.",
				"done": True,
				"sql": "",
				"chart_type": "",
				"explanation": "",
				"title": "",
				"questions": [],
				"used_tables": [],
				"run_id": str(run_id)
			}

	def chat_inner(self, messages: List[BaseMessage], run_id: UUID, test_sql_count: int = 0):
		try:
			
			# Create the LangChain ChatOpenAI instance with the tools
			llm = self.model_registry.get_gpt41(temperature=0.1)

			# Prepare the LLM with tools
			llm_with_tools = llm.bind_tools(list(self.tool_name_to_tool.values()))

			# Invoke the model
			response = RunnableLambda(lambda x: llm_with_tools.invoke(x)).invoke(messages, {"run_id": run_id})

			# Check if the response contains tool calls
			if hasattr(response, 'tool_calls') and len(response.tool_calls) > 0:
				# If it's a return_sql_result tool call, handle it immediately (final result)
				for tool_call in response.tool_calls:
					if tool_call['name'] == "return_sql_result":
						tool_args = tool_call['args']
						sql = tool_args["sql"]
						return {
							"type": "query",
							"content": "",
							"sql": sql,
							"chart_type": tool_args["chart_type"],
							"explanation": tool_args["explanation"],
							"title": tool_args["title"],
							"questions": [],
							"used_tables": tool_args.get("used_tables", []),
							"run_id": str(run_id)
						}
				
				# For other tool calls, process them all before continuing
				messages.append(response)
				
				# Check for test_sql iterations limit
				has_test_sql = any(tool_call['name'] == "test_sql" for tool_call in response.tool_calls)
				if has_test_sql and test_sql_count >= MAX_TEST_SQL_ITERATIONS:
					tool_result = "Maximum number of SQL test iterations reached. Please submit your best query."
					# Add a generic tool message response
					messages.append(ToolMessage(content=tool_result, tool_call_id=response.tool_calls[0]["id"]))
					return self.chat_inner(messages, run_id, test_sql_count)
				
				# Process all tool calls
				
				for tool_call in response.tool_calls:
					tool_name = tool_call['name']
					tool_args = tool_call['args']
					tool_id = tool_call['id']
					
					# Skip test_sql if we've reached the max iterations
					if tool_name == "test_sql" and test_sql_count >= MAX_TEST_SQL_ITERATIONS:
						continue
					
					# Process each tool call and append the results
					tool_result = self.tool_name_to_tool[tool_name].invoke(tool_args)
					messages.append(ToolMessage(content=tool_result, tool_call_id=tool_id))
					
				# Increment test_sql_count if we processed a test_sql tool call
				new_test_sql_count = test_sql_count
				if has_test_sql:
					new_test_sql_count += 1
				
				# Continue the conversation with all the tool results
				result = self.chat_inner(messages, run_id, new_test_sql_count)
				
				return result

			# Regular conversation response (no tool call)
			return {
				"type": "message",
				"content": response.content,
				"sql": "",
				"chart_type": "",
				"explanation": "",
				"title": "",
				"questions": [],
				"used_tables": [],
				"run_id": str(run_id)
			}

		except Exception as e:
			# Return a safe default response in case of error
			error_message = f"Error in chat: {str(e)}"
			logging.error(error_message)  # Log the error

			return {
				"type": "message",
				"content": "I encountered an error while processing your request. Please try again.",
				"sql": "",
				"chart_type": "",
				"explanation": "",
				"title": "",
				"questions": [],
				"used_tables": [],
				"run_id": str(run_id)
			}
