import logging
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, Callable
import asyncio
import subprocess
import uuid
from claude_code_sdk import query, ClaudeCodeOptions


class ProcessorGeneratorAgent:
    """
    Agent that generates Sentio processors using Claude Code SDK.
    
    This agent:
    1. Creates a temporary working directory
    2. Uses Sentio CLI to bootstrap a processor project
    3. Downloads contract ABIs and generates type bindings
    4. Uses Claude Code to generate processor code based on user requirements
    5. Returns the generated code and a summary
    """
    
    def __init__(self, status_callback: Optional[Callable[[str], None]] = None):
        """
        Initialize the processor generator agent.
        
        Args:
            status_callback: Optional callback function to report status updates
        """
        self.status_callback = status_callback or self._default_status_callback
        self.temp_dir: Optional[Path] = None
        
    def _default_status_callback(self, status: str):
        """Default status callback that logs to info level."""
        logging.info(f"ProcessorGenerator: {status}")
    
    def _update_status(self, status: str):
        """Update status through callback."""
        self.status_callback(status)
    
    async def generate_processor(
        self, 
        chain_id: str, 
        contract_address: str, 
        user_prompt: str,
        project_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate a Sentio processor for the specified contract.
        
        Args:
            chain_id: The blockchain chain ID (e.g., '1' for Ethereum mainnet)
            contract_address: The contract address to create processor for
            user_prompt: User requirements for the processor
            project_name: Optional project name (will generate one if not provided)
            
        Returns:
            Dict containing:
            - success: bool
            - processor_code: str (generated processor code)
            - summary: str (summary of what was generated)
            - error: str (error message if failed)
            - project_path: str (path to generated project)
        """
        if not query:
            return {
                "success": False,
                "error": "Claude Code SDK not available. Please install: pip install claude-code-sdk",
                "processor_code": "",
                "summary": "",
                "project_path": ""
            }
        
        # Generate project name if not provided
        if not project_name:
            project_name = f"processor_{contract_address[:8]}_{uuid.uuid4().hex[:6]}"
        
        try:
            self._update_status("Creating temporary working directory...")
            self.temp_dir = Path(tempfile.mkdtemp(prefix=f"sentio_processor_{project_name}_"))
            
            self._update_status(f"Setting up Sentio processor project: {project_name}")
            await self._setup_processor_project(project_name, chain_id, contract_address)
            
            self._update_status("Generating processor code with Claude Code...")
            processor_code, summary = await self._generate_processor_code(
                contract_address, user_prompt, chain_id
            )
            
            self._update_status("Processor generation completed successfully")
            
            return {
                "success": True,
                "processor_code": processor_code,
                "summary": summary,
                "error": "",
                "project_path": str(self.temp_dir)
            }
            
        except Exception as e:
            error_msg = f"Failed to generate processor: {str(e)}"
            self._update_status(f"Error: {error_msg}")
            logging.error(error_msg, exc_info=True)
            
            return {
                "success": False,
                "processor_code": "",
                "summary": "",
                "error": error_msg,
                "project_path": str(self.temp_dir) if self.temp_dir else ""
            }
    
    async def _setup_processor_project(self, project_name: str, chain_id: str, contract_address: str):
        """Set up the Sentio processor project using CLI commands."""
        
        # Create processor project
        self._update_status("Creating processor project with Sentio CLI...")
        create_cmd = [
            "sentio", "create", 
            "-c", "eth",  # Default to Ethereum for now
            "-n", project_name
        ]
        
        try:
            result = await self._run_command_with_timeout(create_cmd, cwd=self.temp_dir, timeout=180)  # 3 minute timeout
        except asyncio.TimeoutError:
            # Check if project directory was created despite timeout
            project_path = self.temp_dir / project_name
            if project_path.exists():
                self._update_status("Project creation timed out but project structure was created, continuing...")
            else:
                raise Exception("Project creation timed out and no directory was created")
            # Skip the rest of the error checking since we're handling timeout
            result = None
        
        if result and result.returncode != 0:
            # Check if it's an npm install failure specifically
            stderr_lower = result.stderr.lower()
            if 'install failed' in stderr_lower or 'npm install' in stderr_lower:
                # Log the npm install failure but continue - the project structure might still be created
                logging.warning(f"npm install failed during project creation: {result.stderr}")
                self._update_status("Project creation encountered npm install issues, checking if project structure was created...")
                
                # Check if project directory was created despite npm failure
                project_path = self.temp_dir / project_name
                if project_path.exists():
                    self._update_status("Project structure created successfully despite npm install failure, continuing...")
                else:
                    raise Exception(f"Project creation failed and no directory was created: {result.stderr}")
            else:
                # Filter out npm warnings from actual errors
                stderr_lines = result.stderr.strip().split('\n')
                error_lines = [line for line in stderr_lines if not line.startswith('npm warn')]
                actual_errors = '\n'.join(error_lines).strip()
                
                if actual_errors:
                    raise Exception(f"Failed to create processor project: {actual_errors}")
                else:
                    # If only npm warnings, log them but continue
                    self._update_status(f"Project created with npm warnings: {result.stderr[:200]}...")
        
        # Change to project directory
        project_path = self.temp_dir / project_name
        if not project_path.exists():
            raise Exception(f"Project directory not created: {project_path}")
        
        # Install project dependencies before further generation steps
        self._update_status("Installing npm dependencies...")
        npm_cmd = ["npm", "ci", "--silent"] if (project_path / "package-lock.json").exists() else ["npm", "install", "--silent"]
        try:
            result = await self._run_command_with_timeout(npm_cmd, cwd=project_path, timeout=600)
            if result.returncode != 0:
                logging.warning(f"npm install encountered issues: {result.stderr}")
        except asyncio.TimeoutError:
            logging.warning("npm install timed out; continuing with generation steps")

        # Add contract ABI
        self._update_status(f"Adding contract ABI for {contract_address} on chain {chain_id}...")
        add_cmd = [
            "sentio", "add", 
            "--chain", chain_id,
            contract_address
        ]
        
        result = await self._run_command(add_cmd, cwd=project_path)
        if result.returncode != 0:
            # Non-fatal error - continue with generation even if ABI download fails
            logging.warning(f"Failed to add contract ABI: {result.stderr}")
        
        # Generate type bindings
        self._update_status("Generating type bindings...")
        gen_cmd = ["sentio", "gen"]
        
        result = await self._run_command(gen_cmd, cwd=project_path)
        if result.returncode != 0:
            # Non-fatal error
            logging.warning(f"Failed to generate type bindings: {result.stderr}")
    
    async def _generate_processor_code(self, contract_address: str, user_prompt: str, chain_id: str) -> tuple[str, str]:
        """Use Claude Code SDK to generate the processor code."""
        
        project_path = None
        for item in self.temp_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                project_path = item
                break
        
        if not project_path:
            raise Exception("Could not find generated project directory")
        
        # Construct the task prompt for Claude Code
        claude_prompt = f"""
You are generating a production-ready Sentio processor with minimal iterations.

Project Context:
- Chain ID: {chain_id}
- Contract Address: {contract_address}
- User Requirements: {user_prompt}

Strict objectives (optimize for single-pass correctness):
1) Open and review /src, /abis, sentio.yaml, and generated types only if needed. Avoid exploratory tool calls.
2) Write a complete, final version of src/processor.ts in one edit. Do not draft; produce directly runnable code.
3) Prefer proven-stable SDK APIs from @sentio/sdk.
   - For ERC-20 style tokens, default to using ERC20Processor and infer mints/burns via zero-address on Transfer events.
   - Only use contract-specific generated types if they exist AND their casing/paths match the generated index (e.g. types/eth/stakedtokenv1.js). Never mix incorrect casing.
4) Keep imports ESM-compatible and case-accurate. Avoid unused imports.
5) Emit metrics and event logs using Sentio patterns. Keep code concise and readable.
6) Do not change package.json or install new deps. Do not create new files unless strictly necessary.
7) If types are inconsistent or generation fails, fall back to generic processor approaches (ERC20Processor or EthContractProcessor with topic filters) and do not rely on typegen.
8) Avoid computing keccak/event signatures manually unless you can use a local, reliable utility; otherwise prefer zero-address heuristics.
9) Ensure code compiles under TypeScript strict mode assumptions; avoid type casts that depend on external typings.
10) At the end, output a short "Summary:" paragraph describing what the processor does.

Deliverables:
- One atomic rewrite of src/processor.ts containing the complete solution.
"""
        
        # Configure Claude Code options
        options = ClaudeCodeOptions(
            max_turns=20,
            cwd=project_path,
            system_prompt=(
                "You are an expert Sentio processor engineer. Your goal is to finish in as few turns as "
                "possible by planning mentally and then performing one precise edit.\n\n"
                "Guidelines:\n"
                "- Prefer ERC20Processor for token flows.\n"
                "- Use generated types only if they exist with matching lowercase paths (e.g., types/eth/stakedtokenv1.js).\n"
                "- Avoid fragile imports and mixed ethers versions. Keep imports to @sentio/sdk and stable entrypoints.\n"
                "- Don't modify package.json, tsconfig, or install steps.\n"
                "- If typegen is inconsistent, avoid it and implement with generic processors.\n"
                "- Produce a single, compilable src/processor.ts that adheres to Sentio best practices.\n"
                "- Conclude with a short 'Summary:' message."
            ),
            allowed_tools=["Read", "Write", "Glob", "Bash"],
            permission_mode="acceptEdits"
        )
        
        # Query Claude Code
        messages = []
        async for message in query(prompt=claude_prompt, options=options):
            print(f"Message: {message}")
            messages.append(message)
        
        # Extract the generated processor code
        processor_code = ""
        summary = ""
        
        # Try to read the generated processor file
        processor_file = project_path / "src" / "processor.ts"
        if processor_file.exists():
            processor_code = processor_file.read_text()
        
        # Extract summary from Claude's responses
        for message in messages:
            if hasattr(message, 'content') and isinstance(message.content, str):
                if "summary" in message.content.lower() or len(message.content) > 100:
                    summary = message.content
                    break
        
        if not summary:
            summary = f"Generated Sentio processor for contract {contract_address} on chain {chain_id} based on user requirements."
        
        return processor_code, summary
    
    async def _run_command(self, cmd: list, cwd: Optional[Path] = None) -> subprocess.CompletedProcess:
        """Run a command asynchronously."""
        process = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=cwd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        return subprocess.CompletedProcess(
            args=cmd,
            returncode=process.returncode,
            stdout=stdout.decode(),
            stderr=stderr.decode()
        )
    
    async def _run_command_with_timeout(self, cmd: list, cwd: Optional[Path] = None, timeout: int = 60) -> subprocess.CompletedProcess:
        """Run a command asynchronously with a timeout."""
        process = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=cwd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)
            return subprocess.CompletedProcess(
                args=cmd,
                returncode=process.returncode,
                stdout=stdout.decode(),
                stderr=stderr.decode()
            )
        except asyncio.TimeoutError:
            # Kill the process if it times out
            try:
                process.kill()
                await process.wait()
            except:
                pass
            raise asyncio.TimeoutError(f"Command {' '.join(cmd)} timed out after {timeout} seconds")
    
    def cleanup(self):
        """Clean up temporary directory."""
        if self.temp_dir and self.temp_dir.exists():
            self._update_status(f"Cleaning up temporary directory: {self.temp_dir}")
            shutil.rmtree(self.temp_dir)
            self.temp_dir = None
    
    # def __del__(self):
    #     """Ensure cleanup on object destruction."""
    #     self.cleanup()


async def create_processor_with_claude(
    chain_id: str,
    contract_address: str, 
    user_prompt: str,
    project_name: Optional[str] = None,
    status_callback: Optional[Callable[[str], None]] = None
) -> Dict[str, Any]:
    """
    Convenience function to create a processor using the ProcessorGeneratorAgent.
    
    Args:
        chain_id: The blockchain chain ID
        contract_address: The contract address
        user_prompt: User requirements for the processor
        project_name: Optional project name
        status_callback: Optional status update callback
        
    Returns:
        Dictionary with generation results
    """
    agent = ProcessorGeneratorAgent(status_callback=status_callback)
    
    try:
        result = await agent.generate_processor(
            chain_id=chain_id,
            contract_address=contract_address,
            user_prompt=user_prompt,
            project_name=project_name
        )
        return result
    finally:
        pass
        # agent.cleanup()


# Example usage
if __name__ == "__main__":
    async def main():
        def status_update(status: str):
            print(f"[STATUS] {status}")
        
        result = await create_processor_with_claude(
            chain_id="1",
            contract_address="0x31724cA0C982A31fbb5C57f4217AB585271fc9a5",  # StakedTokenv1
            user_prompt="Create a processor that tracks all Transfer events and calculates daily transfer volumes. Also track unique users per day.",
            status_callback=status_update
        )
        
        if result["success"]:
            print("✅ Processor generated successfully!")
            print(f"Summary: {result['summary']}")
            print(f"Code length: {len(result['processor_code'])} characters")
            print(f"Project path: {result['project_path']}")
        else:
            print(f"❌ Failed to generate processor: {result['error']}")
    
    asyncio.run(main())