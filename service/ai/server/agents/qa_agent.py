import logging
from typing import List, Dict, Any, Union
from datetime import datetime, timezone
import requests

from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, AIMessageChunk, ToolMessage
from langchain_core.tools import tool
from service.ai.server.models.model_registry import ModelRegistry
from service.ai.server.agents.sentio_react_agent import create_sentio_agent
from service.ai.server.agents.web_search_agent import WebSearchAgent
from service.ai.api import api_pb2

QA_SYSTEM_PROMPT = """You are a Sentio Expert Assistant. Your primary goal is to provide accurate, helpful, and timely support to users of the Sentio platform.

<core_mission>
Your mission is to help users with general Sentio questions and processor debugging by leveraging your knowledge, documentation, and specialized tools. You must guide users to effective solutions, citing your sources and explaining your reasoning.
</core_mission>

<persona>
- **Expert & Precise**: You have deep knowledge of Sentio. Your answers are accurate and detailed.
- **Collaborative & Proactive**: You work with the user. If information is missing, you proactively ask for it.
- **Patient & Clear**: You explain complex concepts simply, using code examples and documentation links. You avoid jargon where possible or explain it clearly.
</persona>

<key_sentio_concepts>
- **Sentio SDK**: Next-gen indexing framework with TypeScript typings from ABIs
- **Processors**: Custom data indexing logic that can be 100x faster than traditional indexers  
- **Multi-chain Support**: Index across chains in the same project
- **Data Types**: Metrics, Event Logs, Entities, Webhooks
</key_sentio_concepts>

<tooling_strategy_and_workflow>
You will follow a logical workflow to resolve user issues, leveraging tools methodically.

<step_1>
First, understand the user's need. Is it a general question or a debugging request?

- **For debugging requests**, you MUST ask for the following information if not provided:
    1. The **Project ID** (`owner` and `slug`).
    2. The specific ERROR MESSAGE or stack trace.
</step_1>

<step_2>
Based on the user's query, use the available tools if NEEDED. Think step-by-step.

- **Tool: `search_sentio_documentation(query: str)`**: To find conceptual explanations, guides, and best practices.
- **Tool: `search_processor_examples(query: str)`**: To find real processor implementation examples from the official Sentio processors repository.
- **Tool: `getProcessorStatus(owner: str, slug: str)`**: To retrieve the real-time operational status of a specific processor.
- **Tool: `getProcessorSourceFiles(owner: str, slug: str)`**: To fetch the source code (`.ts` and `sentio.yaml` files) for a processor.
</step_2>

<step_3>
Combine the information from the tools and your knowledge base to construct a complete answer.

- **Provide Actionable Advice**: Don't just state the error. Explain *why* it's happening and suggest a specific code change to fix it.
- **Use Real Code Examples**: When possible, reference actual working examples from the processor examples repository to show users exactly how to implement solutions.
- **Reference Documentation Carefully**: When referencing documentation:
  * Only include specific links if they are explicitly provided by the search tools
  * Use general references like "See the Sentio documentation" if no specific links are available
  * If you found information via search tools, mention that the search results provided the information
- **Format for Readability**:
    - Display error messages and stack traces in JavaScript code blocks.
    - Use TypeScript code blocks for code examples and suggestions.
</step_3>

<response_guidelines_and_constraints>
- **NEVER Create Links**: Do not create or invent specific documentation URLs. Only reference documentation in general terms unless exact links are provided by search tools.
- **NEVER Guess**: If a tool fails or you don't have enough information, state what you're missing and ask the user for it. It is better to say "I cannot determine the cause without the processor's error log" than to invent a solution.
- **Stay in Scope**: You are a Sentio assistant. Do not answer questions about general programming unrelated to Sentio, and do not provide financial advice.
- **Do Not Write Full Processors**: Your role is to debug and provide guidance. Offer snippets and corrections, but do not write a complete, complex processor from scratch.
- **Be Transparent**: If search tools don't return results or links, say so explicitly rather than making up information.
</response_guidelines_and_constraints>
"""

class QAAgent:
    """Agent responsible for answering general Sentio questions and helping with processor debugging."""
    
    def __init__(self, model_registry: ModelRegistry, project: str = None, github_token: str = None):
        """
        Initialize the QAAgent.
        
        Args:
            model_registry: The model registry for accessing LLM clients
            project: The current project context
            github_token: GitHub personal access token for API authentication
        """
        self.model_registry = model_registry
        self.project = project
        self.github_token = github_token
        self.web_search_agent = WebSearchAgent(model_registry)
        self.processors_repo = "sentioxyz/sentio-processors"

    def search_processor_examples(self, query: str) -> str:
        """
        Search the Sentio processors GitHub repository for code examples.
        
        Args:
            query: Search query for processor examples
            
        Returns:
            Formatted search results with file paths and code snippets
        """
        try:
            # GitHub search API
            search_url = "https://api.github.com/search/code"
            params = {
                'q': f'{query} repo:{self.processors_repo}',
                'sort': 'indexed',
                'per_page': 10
            }
            
            # Add authentication headers if token is provided
            headers = {
                'Accept': 'application/vnd.github+json',
                'X-GitHub-Api-Version': '2022-11-28'
            }
            if self.github_token:
                headers['Authorization'] = f'Bearer {self.github_token}'
            
            response = requests.get(search_url, params=params, headers=headers, timeout=30)
            
            if response.status_code == 200:
                # remove auth header
                headers.pop('Authorization', None)
                data = response.json()
                results = []
                
                for item in data.get('items', [])[:5]:  # Limit to top 5 results
                    file_path = item.get('path', '')
                    file_url = item.get('html_url', '')
                    
                    # Get file content
                    content_url = item.get('url', '')
                    if content_url:
                        try:
                            content_response = requests.get(content_url, headers=headers, timeout=30)
                            if content_response.status_code == 200:
                                content_data = content_response.json()
                                content = content_data.get('content', '')
                                
                                # Decode base64 content
                                import base64
                                try:
                                    decoded_content = base64.b64decode(content).decode('utf-8')
                                    # Truncate very long files
                                    if len(decoded_content) > 2000:
                                        decoded_content = decoded_content[:2000] + "\n... (truncated)"
                                    
                                    results.append({
                                        'file_path': file_path,
                                        'file_url': file_url,
                                        'content': decoded_content
                                    })
                                except Exception as e:
                                    logging.warning(f"Failed to decode content for {file_path}: {e}")
                            else:
                                logging.warning(f"Failed to fetch content for {file_path} with url {content_url}: HTTP {content_response.status_code}")
                        except Exception as e:
                            logging.warning(f"Network error fetching {file_path}: {e}")
                            # Add file info without content if fetch fails
                            results.append({
                                'file_path': file_path,
                                'file_url': file_url,
                                'content': f"// Content unavailable due to network error: {str(e)}"
                            })
                
                if results:
                    formatted_results = f"Found {len(results)} processor examples:\n\n"
                    for i, result in enumerate(results, 1):
                        formatted_results += f"**Example {i}: {result['file_path']}**\n"
                        formatted_results += f"Repository: https://github.com/{self.processors_repo}\n"
                        formatted_results += f"File URL: {result['file_url']}\n\n"
                        formatted_results += "```typescript\n"
                        formatted_results += result['content']
                        formatted_results += "\n```\n\n"
                    
                    return formatted_results
                else:
                    return f"No processor examples found for query: {query}"
            
            elif response.status_code == 403:
                return "GitHub API rate limit exceeded. Please try again later."
            else:
                return f"GitHub search failed with status {response.status_code}"
                
        except Exception as e:
            logging.error(f"Error searching processor examples: {e}")
            return f"Error searching processor examples: {str(e)}"

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt with current context information.
        
        Returns:
            The complete system prompt with context
        """
        current_utc_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        
        context_prompt = QA_SYSTEM_PROMPT + f"\n\n<current_utc_time>{current_utc_time}</current_utc_time>"
        
        if self.project:
            context_prompt += f"\n\n<current_project>{self.project}</current_project>"
            
        return context_prompt

    async def chat_stream_async(self, messages: List[Dict[str, str]], run_id, auth_headers: Dict[str, str] = None):
        """
        Async version of streaming the QA agent's response using LangGraph's streaming capabilities.
        
        Args:
            messages: The conversation messages (full history)
            run_id: A unique identifier for this conversation
            auth_headers: Optional authentication headers for Sentio API access
            
        Returns:
            An async generator that yields chunks of the response
        """
        try:
            # Create the LangGraph agent
            agent = await self._create_qa_agent(auth_headers)
            
            # Convert messages to LangChain format
            langchain_messages = self._convert_messages_to_langchain(messages)
            
            # Use "messages" stream mode 
            async for token, _ in agent.astream(
                {"messages": langchain_messages},
                stream_mode="messages"
            ):
                # Extract all response chunks from the raw chunk
                response_chunks = self._extract_response_chunks_from_raw_chunk(token, run_id)
                for response_chunk in response_chunks:
                    yield response_chunk
            
            # Final chunk to indicate completion
            yield {
                "chunk_type": "text",
                "chunk": "",
                "done": True,
                "run_id": str(run_id)
            }
            
        except Exception as e:
            logging.error(f"Error in QA agent async streaming: {e}")
            # Yield error chunk
            yield {
                "chunk_type": "error",
                "chunk": f"I encountered an error while processing your question: {str(e)}. Please try rephrasing your question or contact support if the issue persists.",
                "done": True,
                "run_id": str(run_id)
            }

    def chat_stream(self, messages: List[Dict[str, str]], run_id, auth_headers: Dict[str, str] = None):
        """
        Stream the QA agent's response using LangGraph's streaming capabilities.
        
        This is a synchronous wrapper around the async streaming method for compatibility.
        
        Args:
            messages: The conversation messages (full history)
            run_id: A unique identifier for this conversation
            auth_headers: Optional authentication headers for Sentio API access
            
        Returns:
            A generator that yields chunks of the response
        """
        try:
            import asyncio
            
            # Get the current event loop or create a new one
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If we're already in an async context, fall back to sync approach
                    result = self.chat_sync(messages, auth_headers)
                    yield {
                        "chunk_type": "text",
                        "chunk": result["content"],
                        "done": True,
                        "run_id": str(run_id)
                    }
                    return
            except RuntimeError:
                # No event loop running, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Run the async streaming method
            async_gen = self.chat_stream_async(messages, run_id, auth_headers)
            
            # Convert async generator to sync generator
            while True:
                try:
                    chunk = loop.run_until_complete(async_gen.__anext__())
                    yield chunk
                except StopAsyncIteration:
                    break
            
        except Exception as e:
            logging.error(f"Error in QA agent streaming: {e}")
            # Yield error chunk
            yield {
                "chunk_type": "error",
                "chunk": f"I encountered an error while processing your question: {str(e)}. Please try rephrasing your question or contact support if the issue persists.",
                "done": True,
                "run_id": str(run_id)
            }

    def chat_sync(self, messages: List[Dict[str, str]], auth_headers: Dict[str, str] = None) -> Dict[str, Any]:
        """
        Synchronous version of chat for integration with non-async handlers.
        
        Args:
            messages: The conversation messages (full history)
            auth_headers: Optional authentication headers for Sentio API access
            
        Returns:
            Dict containing the response and metadata
        """
        try:
            import asyncio
            
            # Get or create event loop
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Create agent and run synchronously
            agent = loop.run_until_complete(self._create_qa_agent(auth_headers))
            
            # Convert messages to LangChain format
            langchain_messages = self._convert_messages_to_langchain(messages)
            
            # Invoke the LangGraph agent
            result = loop.run_until_complete(agent.ainvoke({"messages": langchain_messages}))
            
            # Extract content from result
            content = ""
            if result and result.get("messages"):
                last_message = result["messages"][-1]
                if hasattr(last_message, 'content'):
                    content = last_message.content
                else:
                    content = str(last_message)
            
            if not content.strip():
                content = "No results found in Sentio documentation."
            
            return {
                "content": content,
                "type": "qa_response",
                "run_id": None,
                "agent_type": "qa_agent"
            }
            
        except Exception as e:
            logging.error(f"Error in sync QA agent: {e}")
            return {
                "content": f"I encountered an error while processing your question: {str(e)}. Please try rephrasing your question or contact support if the issue persists.",
                "type": "error",
                "run_id": None,
                "agent_type": "qa_agent"
            }

    async def _create_qa_agent(self, auth_headers: Dict[str, str] = None):
        """
        Create a LangGraph React agent configured for QA tasks.
        
        Args:
            auth_headers: Optional authentication headers for Sentio API access
            
        Returns:
            A configured LangGraph React agent
        """
        try:
            # Create a tool instance that uses the WebSearchAgent
            @tool
            def search_sentio_documentation(query: str) -> str:
                """
                Search Sentio documentation for information about processors, debugging, and platform features.
                
                Args:
                    query: The search query string related to Sentio
                    
                Returns:
                    Search results from Sentio documentation as a string
                """
                try:
                    return self.web_search_agent.search_sentio_docs(query)
                except Exception as e:
                    logging.error(f"Error in Sentio docs search: {e}")
                    return f"Unable to search Sentio documentation at this time: {str(e)}"

            # Create a tool instance that searches the processor examples repository
            @tool
            def search_processor_examples(query: str) -> str:
                """
                Search the official Sentio processors GitHub repository for real implementation examples.
                This provides working code examples for various processor patterns and use cases.
                
                Args:
                    query: The search query for processor examples (e.g., "ERC20 transfer", "Uniswap swap", "event handler")
                    
                Returns:
                    Search results with file paths, URLs, and code snippets from the processors repository
                """
                try:
                    return self.search_processor_examples(query)
                except Exception as e:
                    logging.error(f"Error in processor examples search: {e}")
                    return f"Unable to search processor examples at this time: {str(e)}"
            
            # Prepare additional tools for documentation lookup
            additional_tools = []
            
            # Try to include web search tool
            try:
                additional_tools.append(search_sentio_documentation)
                logging.info("Added Sentio documentation search tool to QA agent")
            except Exception as tool_error:
                logging.warning(f"Could not add web search tool: {tool_error}")

            # Try to include processor examples search tool
            try:
                additional_tools.append(search_processor_examples)
                logging.info("Added processor examples search tool to QA agent")
            except Exception as tool_error:
                logging.warning(f"Could not add processor examples search tool: {tool_error}")
            
            # Create the Sentio React agent with QA configuration
            agent = await create_sentio_agent(
                model_registry=self.model_registry,
                auth_headers=auth_headers or {},
                system_prompt=self._get_system_prompt(),
                enable_processor_tools_only=True,
                additional_tools=additional_tools
            )
            
            logging.info("Successfully created QA agent with LangGraph")
            return agent
            
        except Exception as e:
            logging.error(f"Error creating QA agent: {e}")
            # Fallback: try without additional tools
            try:
                agent = await create_sentio_agent(
                    model_registry=self.model_registry,
                    auth_headers=auth_headers or {},
                    system_prompt=self._get_system_prompt(),
                    enable_processor_tools_only=True,
                    additional_tools=[]
                )
                logging.warning("Created QA agent without additional tools as fallback")
                return agent
            except Exception as fallback_error:
                logging.error(f"Fallback agent creation also failed: {fallback_error}")
                raise e
    
    def _convert_messages_to_langchain(self, messages: List[Dict[str, str]]) -> List:
        """
        Convert API messages to LangChain message format.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            List of LangChain message objects
        """
        langchain_messages = [SystemMessage(content=self._get_system_prompt())]
        
        # Add conversation history
        for msg in messages:
            if hasattr(msg, 'role') and hasattr(msg, 'content'):
                # Handle protobuf message objects
                if msg.role == api_pb2.Message.Role.USER:
                    langchain_messages.append(HumanMessage(content=msg.content))
                elif msg.role == api_pb2.Message.Role.ASSISTANT:
                    langchain_messages.append(AIMessage(content=msg.content))
            elif isinstance(msg, dict):
                # Handle dictionary messages
                role = msg.get('role', '')
                content = msg.get('content', '')
                if role.lower() in ['user', 'human']:
                    langchain_messages.append(HumanMessage(content=content))
                elif role.lower() in ['assistant', 'ai']:
                    langchain_messages.append(AIMessage(content=content))
        
        # If no user messages found, add a default message
        if len(langchain_messages) == 1:  # Only system message
            langchain_messages.append(HumanMessage(content="Please provide information about Sentio."))
        
        return langchain_messages
    
    def _extract_response_chunks_from_raw_chunk(self, chunk: Union[AIMessageChunk, ToolMessage], run_id) -> List[Dict[str, Any]]:
        """
        Extract response chunks from a raw LangGraph chunk, handling tool calls and completion states.
        
        Args:
            chunk: The streaming chunk from LangGraph
            run_id: The run ID for this conversation
            
        Returns:
            List of response chunk dictionaries (can be empty, single, or multiple chunks)
        """
        try:
            # Handle AIMessageChunk objects
            if hasattr(chunk, '__class__') and chunk.__class__.__name__ == 'AIMessageChunk':
                # Check if this is the final chunk with finish_reason: 'stop'
                if (hasattr(chunk, 'response_metadata') and 
                    chunk.response_metadata and 
                    chunk.response_metadata.get('finish_reason') == 'stop'):
                    # Final chunk - indicates completion
                    return [{
                        "chunk_type": "text",
                        "chunk": "",
                        "done": True,
                        "run_id": str(run_id)
                    }]
                
                # Check if this chunk has tool calls (start of tool execution)
                if hasattr(chunk, 'tool_calls') and chunk.tool_calls:
                    tool_calls = chunk.tool_calls
                    chunks = []

                    # Create a chunk for each tool call to indicate tool execution started
                    for tool_call in tool_calls:
                        tool_name = tool_call.get('name', 'unknown')
                        
                        # Generate appropriate message for each tool
                        if tool_name == 'search_sentio_documentation':
                            content = "🔍 Searching Sentio documentation..."
                        elif tool_name == 'getProcessorStatus':
                            content = "📊 Checking processor status..."
                        elif tool_name == 'getProcessorSourceFiles':
                            content = "📁 Retrieving processor source files..."
                        elif tool_name == 'search_processor_examples':
                            content = "🔍 Searching processor examples..."
                        else:
                            continue
                        
                        chunks.append({
                            "chunk_type": "tool",
                            "chunk": content,
                            "done": False,
                            "run_id": str(run_id)
                        })
                    
                    return chunks

                # Handle regular streaming text content
                if hasattr(chunk, 'content') and chunk.content:
                    if isinstance(chunk.content, str):
                        return [{
                            "chunk_type": "text",
                            "chunk": chunk.content,
                            "done": False,
                            "run_id": str(run_id)
                        }]
            
            # Handle ToolMessage objects (marks end of tool call)
            elif hasattr(chunk, '__class__') and chunk.__class__.__name__ == 'ToolMessage':
                # ToolMessage indicates the tool call has completed
                tool_name = chunk.name
                # Generate appropriate message for each tool
                if tool_name == 'search_sentio_documentation':
                    content = "🔍 Searching Sentio documentation completed"
                elif tool_name == 'getProcessorStatus':
                    content = "📊 Checking processor status completed"
                elif tool_name == 'getProcessorSourceFiles':
                    content = "📁 Retrieving processor source files completed"
                elif tool_name == 'search_processor_examples':
                    content = "🔍 Searching processor examples completed"
                return [{
                    "chunk_type": "tool",
                    "chunk": content,
                    "done": False,
                    "run_id": str(run_id)
                }]
            return []
            
        except Exception as e:
            logging.warning(f"Error extracting response chunks from raw chunk: {e}")
            return []
