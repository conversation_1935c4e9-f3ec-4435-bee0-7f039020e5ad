import threading
from typing import Dict, Optional, Tuple, List, Any
from service.ai.api import api_pb2
from collections import OrderedDict
import logging
import time
import json
from redis import Redis
from uuid import UUID
from service.ai.server.agents.table_summarizer_agent import TableSummarizerAgent
from service.ai.server.models.model_registry import ModelRegistry
from service.ai.server.sql_api import SqlApi
from mmh3 import hash128

def calculate_table_hash(table: api_pb2.SQLSchema.SQLTable, project: str) -> str:
    """
    Create a deterministic hash of a table schema that changes when the schema changes
    """
    # Create a deterministic string representation of the table schema
    schema_parts = [f"table:{table.name}"]

    # If the table is not system or reserved, add the project name to the hash
    if table.table_type not in [api_pb2.SQLSchema.SQLTable.TableType.SYSTEM, api_pb2.SQLSchema.SQLTable.TableType.RESERVED]:
        schema_parts.append(f"project:{project}")
    
    # Sort columns by name to ensure consistent ordering
    sorted_columns = sorted(table.columns, key=lambda x: x.name)
    
    # Add each column's definition
    for col in sorted_columns:
        schema_parts.append(f"column:{col.name}:{col.type}")
    
    # Join all parts with a delimiter
    schema_str = "|".join(schema_parts)
    
    # Calculate 128-bit murmur hash and return as hex string
    hash_value = hash128(schema_str.encode())
    return f"{hash_value:032x}"  # Convert to 32-character hex string

class MemoryCache:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, max_size: int = 1000):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(MemoryCache, cls).__new__(cls)
                    cls._instance.cache = OrderedDict()
                    cls._instance.max_size = max_size
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, max_size: int = 1000):
        if not self._initialized:
            self.cache = OrderedDict()
            self.max_size = max_size
            self._initialized = True
        
    def get(self, key: str) -> Optional[Any]:
        if key not in self.cache:
            return None
        value, expire_time = self.cache[key]
        if expire_time and time.time() > expire_time:
            del self.cache[key]
            return None
        return value
        
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        if len(self.cache) >= self.max_size:
            # Remove oldest item when cache is full
            self.cache.popitem(last=False)
            
        expire_time = time.time() + ttl if ttl else None
        self.cache[key] = (value, expire_time)

class TableSummaryCache:
    def __init__(self, redis_client: Optional[Redis] = None, ttl: Optional[int] = None):  # Default no TTL
        self.redis_client = redis_client
        self.memory_cache = MemoryCache()
        self.ttl = ttl
        self.table_key_prefix = "table_summary:"
        self.column_key_prefix = "column_summaries:"
        
        # Log cache configuration
        if redis_client:
            logging.info("TableSummaryCache initialized with Redis backend")
        else:
            logging.info("TableSummaryCache initialized with singleton memory cache backend")
    
    def get_table_summary(self, schema_hash: str, version: str) -> Optional[str]:
        """
        Get table summary using schema hash in the key
        """
        table_key = f"{self.table_key_prefix}{schema_hash}:{version}"
        if self.redis_client:
            table_summary = self.redis_client.get(table_key)
            if table_summary:
                return table_summary.decode('utf-8')
            return None
        
        # For memory cache
        return self.memory_cache.get(table_key)
            
    def get_column_summaries(self, schema_hash: str, version: str) -> Optional[Dict[str, str]]:
        """
        Get column summaries using schema hash in the key
        """
        columns_key = f"{self.column_key_prefix}{schema_hash}:{version}"
        if self.redis_client:
            val = self.redis_client.get(columns_key)
            if val:
                try:
                    return json.loads(val)
                except json.JSONDecodeError:
                    return None
        
        # For memory cache
        return self.memory_cache.get(columns_key)
            
    def set_summaries(self, project: str, table: api_pb2.SQLSchema.SQLTable, table_summary: str, column_summaries: Dict[str, str], version: str):
        """
        Store table and column summaries with schema hash in the key
        """
        table_name = table.name
        # Calculate current hash
        schema_hash = calculate_table_hash(table, project)
        table_key = f"{self.table_key_prefix}{schema_hash}:{version}"
        columns_key = f"{self.column_key_prefix}{schema_hash}:{version}"
        # Store in Redis if available
        if self.redis_client:
            try:
                # Table summary
                if self.ttl is None:
                    # Use set when TTL is None
                    self.redis_client.set(table_key, table_summary)
                    self.redis_client.set(columns_key, json.dumps(column_summaries))
                    logging.info(f"Stored summaries for table {table_name} in Redis with no TTL using schema hash in key")
                else:
                    # Use setex when TTL is provided
                    self.redis_client.setex(table_key, self.ttl, table_summary)
                    self.redis_client.setex(columns_key, self.ttl, json.dumps(column_summaries))
                    logging.info(f"Stored summaries for table {table_name} in Redis with {self.ttl} seconds TTL using schema hash in key")
            except Exception as e:
                logging.error(f"Error storing in Redis: {str(e)}")
                # Fallback to memory cache
                self._store_in_memory(table_key, columns_key, table_summary, column_summaries)
        else:
            logging.info(f"Redis not available, storing summaries for table {table_name} in memory cache")
            # Store in memory cache
            self._store_in_memory(table_key, columns_key, table_summary, column_summaries)
    
    def _store_in_memory(self, table_key: str, columns_key: str, table_summary: str, column_summaries: Dict[str, str]):
        self.memory_cache.set(table_key, table_summary, self.ttl)
        self.memory_cache.set(columns_key, column_summaries, self.ttl)

class TableSummarizer:
    def __init__(self, model_registry: ModelRegistry, redis_client: Optional[Redis] = None):
        self.model_registry = model_registry
        self.cache = TableSummaryCache(redis_client)
    
    def get_table_summaries(self, 
                           project: str,
                           tables: List[api_pb2.SQLSchema.SQLTable], 
                           sql_api,
                           run_id: UUID) -> Tuple[Dict[str, str], Dict[str, Dict[str, str]]]:
        """
        Get summaries for selected tables, using cache when available
        
        Args:
            project: Project identifier
            tables: List of all available tables
            sql_api: API for executing SQL queries
            run_id: Run identifier
            
        Returns:
            A tuple of (table_summaries, column_summaries) dictionaries
        """
        table_summaries = {}
        column_summaries = {}
        tables_to_summarize = []

        summarizer_agent = TableSummarizerAgent(
            self.model_registry,
            sql_api
        )
        
        # First check cache for each table
        for table in tables:
            # Calculate current hash
            schema_hash = calculate_table_hash(table, project)
            # Try to get from cache first using schema hash in key
            table_summary = self.cache.get_table_summary(schema_hash, summarizer_agent.version)
            cols_summary = self.cache.get_column_summaries(schema_hash, summarizer_agent.version)
            
            if table_summary:
                table_summaries[table.name] = table_summary
                
                if cols_summary:
                    column_summaries[table.name] = cols_summary
            else:
                # Need to generate summaries for this table
                tables_to_summarize.append(table)
                
        # Generate summaries for tables not in cache
        if tables_to_summarize:
            def generate_summaries():
                for table in tables_to_summarize:
                    try:
                        logging.info(f"Generating summary for table {table.name}")
                        table_summary, cols_summary = summarizer_agent.summarize_table(table, run_id)
                        # Cache the results
                        self.cache.set_summaries(project, table, table_summary, cols_summary, summarizer_agent.version)
                        
                    except Exception as e:
                        logging.error(f"Error summarizing table {table.name}: {str(e)}")
            
            # start a thread in background to generate summaries for all tables
            thread = threading.Thread(target=generate_summaries)
            thread.start()
        
        return table_summaries, column_summaries