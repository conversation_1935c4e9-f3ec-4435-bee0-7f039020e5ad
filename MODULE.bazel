# https://github.com/aalyria/api/blob/main/MODULE.bazel

module(
    name = "sentio",
    version = "1.0",
)

bazel_dep(name = "platforms", version = "0.0.11")
bazel_dep(name = "rules_license", version = "1.0.0")
bazel_dep(name = "rules_pkg", version = "1.1.0")
bazel_dep(name = "bazel_skylib", version = "1.8.1")
bazel_dep(name = "aspect_bazel_lib", version = "2.20.0")
bazel_dep(name = "protobuf", version = "29.2")
bazel_dep(name = "rules_nodejs", version = "6.5.0")
bazel_dep(name = "rules_go", version = "0.52.0")
bazel_dep(name = "gazelle", version = "0.44.0")
bazel_dep(name = "googleapis", version = "0.0.0-20241220-5e258e33")

#bazel_dep(name = "rules_cc", version = "0.0.13")
bazel_dep(name = "rules_python", version = "1.5.2")
bazel_dep(name = "rules_uv", version = "0.86.0")

#bazel_dep(name = "rules_jvm_external", version = "6.5")
bazel_dep(name = "aspect_rules_js", version = "2.4.2")
bazel_dep(name = "aspect_rules_ts", version = "3.6.3")
bazel_dep(name = "aspect_rules_py", version = "1.6.1")
bazel_dep(name = "rules_proto", version = "7.1.0")
bazel_dep(name = "rules_proto_grpc", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_cpp", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_go", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_python", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_grpc_gateway", version = "5.0.1")
bazel_dep(name = "toolchains_protoc", version = "0.4.3")
bazel_dep(name = "rules_oci", version = "2.2.6")
bazel_dep(name = "rules_multirun", version = "0.12.0")
bazel_dep(name = "hermetic_cc_toolchain", version = "4.0.1")

PTYHON_VERSION = "3.11"

#protoc = use_extension("@toolchains_protoc//protoc:extensions.bzl", "protoc")
#use_repo(protoc, "toolchains_protoc_hub")
#
#register_toolchains("@toolchains_protoc_hub//:all")

### Python
# override protobuf version
archive_override(
    module_name = "rules_proto_grpc_python",
    integrity = "sha256-D/8yirPs+qJxonkqQLvaDfH7TQJkViK30eQs/Q/P5a4=",
    patch_strip = 1,
    patches = [
        "//third_party:rules_proto_grpc_python.patch",
    ],
    strip_prefix = "rules_proto_grpc_python-5.0.1",
    urls = ["https://github.com/rules-proto-grpc/rules_proto_grpc/releases/download/5.0.1/rules_proto_grpc_python-5.0.1.tar.gz"],
)

#dev_pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")
#dev_pip.parse(
#    hub_name = "pypi",
#    python_version = PTYHON_VERSION,
#    requirements_lock = "//tools:requirements.txt",
#)
#use_repo(dev_pip, "pypi")

python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    is_default = True,
    python_version = PTYHON_VERSION,
)

pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")
pip.parse(
    hub_name = "pip",
    python_version = PTYHON_VERSION,
    requirements_lock = "//:requirements.txt",
)
use_repo(pip, "pip")

go_sdk = use_extension("@rules_go//go:extensions.bzl", "go_sdk")
go_sdk.download(version = "1.23.12")
go_sdk.nogo(nogo = "//:my_nogo")

go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")
use_repo(go_deps, "com_github_alicebob_miniredis_v2", "com_github_aptos_labs_aptos_go_sdk", "com_github_auth0_go_jwt_middleware_v2", "com_github_bits_and_blooms_bloom_v3", "com_github_blevesearch_bleve", "com_github_block_vision_sui_go_sdk", "com_github_btcsuite_btcd", "com_github_btcsuite_btcd_chaincfg_chainhash", "com_github_burntsushi_toml", "com_github_bytedance_sonic", "com_github_cenkalti_backoff_v4", "com_github_cespare_xxhash_v2", "com_github_clickhouse_clickhouse_go_v2", "com_github_cockroachdb_errors", "com_github_cockroachdb_pebble", "com_github_code_hex_go_generics_cache", "com_github_dominikbraun_graph", "com_github_dustin_go_humanize", "com_github_ethereum_go_ethereum", "com_github_fardream_go_bcs", "com_github_feiin_sqlstring", "com_github_felixge_httpsnoop", "com_github_fergusstrange_embedded_postgres", "com_github_gagliardetto_solana_go", "com_github_gammazero_deque", "com_github_gammazero_workerpool", "com_github_gertd_go_pluralize", "com_github_go_co_op_gocron", "com_github_go_faster_city", "com_github_go_logr_logr", "com_github_go_logr_zapr", "com_github_go_redis_redis_rate_v10", "com_github_go_redsync_redsync_v4", "com_github_go_telegram_bot_api_telegram_bot_api_v5", "com_github_goccy_go_json", "com_github_gocolly_colly_v2", "com_github_golang_mock", "com_github_golang_snappy", "com_github_google_go_cmp", "com_github_google_uuid", "com_github_gorilla_handlers", "com_github_gorilla_websocket", "com_github_graph_gophers_graphql_go", "com_github_graphql_go_graphql", "com_github_grpc_ecosystem_go_grpc_middleware_v2", "com_github_grpc_ecosystem_grpc_gateway_v2", "com_github_hashicorp_go_retryablehttp", "com_github_huandu_go_sqlbuilder", "com_github_invisionapp_go_health_v2", "com_github_ipfs_go_ipfs_api", "com_github_jinzhu_copier", "com_github_kinbiko_jsonassert", "com_github_klauspost_compress", "com_github_lmittmann_w3", "com_github_matoous_go_nanoid_v2", "com_github_miguelmota_go_solidity_sha3", "com_github_minio_minio_go_v7", "com_github_mitchellh_hashstructure_v2", "com_github_mr_tron_base58", "com_github_nethermindeth_juno", "com_github_nethermindeth_starknet_go", "com_github_nutsdb_nutsdb", "com_github_patrickmn_go_cache", "com_github_peterbourgon_diskv_v3", "com_github_pkg_errors", "com_github_prometheus_client_golang", "com_github_prometheus_client_model", "com_github_prometheus_common", "com_github_prometheus_prometheus", "com_github_redis_go_redis_extra_redisotel_v9", "com_github_redis_go_redis_v9", "com_github_roaringbitmap_roaring", "com_github_robfig_cron_v3", "com_github_samber_lo", "com_github_sashabaranov_go_openai", "com_github_segmentio_go_hll", "com_github_sendgrid_sendgrid_go", "com_github_sentioxyz_fuel_go", "com_github_sentioxyz_go_gecko", "com_github_sentioxyz_golang_lru", "com_github_sentioxyz_qs", "com_github_shirou_gopsutil_v3", "com_github_shopspring_decimal", "com_github_slack_go_slack", "com_github_soheilhy_cmux", "com_github_sourcegraph_scip", "com_github_spaolacci_murmur3", "com_github_stretchr_testify", "com_github_stripe_stripe_go_v75", "com_github_syndtr_goleveldb", "com_github_test_go_testify", "com_github_tidwall_gjson", "com_github_tidwall_sjson", "com_github_unpackdev_solgo", "com_github_uptrace_opentelemetry_go_extra_otelgorm", "com_github_vmihailenco_msgpack_v5", "com_github_wasmerio_wasmer_go", "com_google_cloud_go_pubsub", "com_google_cloud_go_storage", "in_gopkg_natefinch_lumberjack_v2", "in_gopkg_yaml_v2", "in_gopkg_yaml_v3", "io_gorm_datatypes", "io_gorm_driver_postgres", "io_gorm_gorm", "io_k8s_api", "io_k8s_apimachinery", "io_k8s_client_go", "io_k8s_sigs_controller_runtime", "io_k8s_sigs_yaml", "io_k8s_utils", "io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc", "io_opentelemetry_go_contrib_instrumentation_net_http_otelhttp", "io_opentelemetry_go_otel", "io_opentelemetry_go_otel_exporters_otlp_otlpmetric_otlpmetricgrpc", "io_opentelemetry_go_otel_exporters_otlp_otlptrace", "io_opentelemetry_go_otel_exporters_otlp_otlptrace_otlptracegrpc", "io_opentelemetry_go_otel_exporters_stdout_stdoutmetric", "io_opentelemetry_go_otel_exporters_stdout_stdouttrace", "io_opentelemetry_go_otel_metric", "io_opentelemetry_go_otel_sdk", "io_opentelemetry_go_otel_sdk_metric", "io_opentelemetry_go_otel_trace", "org_golang_google_api", "org_golang_google_genproto", "org_golang_google_genproto_googleapis_api", "org_golang_google_genproto_googleapis_rpc", "org_golang_google_grpc", "org_golang_google_protobuf", "org_golang_x_exp", "org_golang_x_net", "org_golang_x_sync", "org_golang_x_time", "org_gonum_v1_gonum", "org_modernc_mathutil", "org_uber_go_ratelimit", "org_uber_go_zap")
go_deps.gazelle_default_attributes(
    directives = ["gazelle:proto disable"],
)
go_deps.module_override(
    patch_strip = 1,
    patches = ["//third_party:com_github_ethereum_go_ethereum_secp256k1.patch"],
    path = "github.com/ethereum/go-ethereum",
)
go_deps.module_override(
    patch_strip = 1,
    patches = ["//third_party:com_github_sourcegraph_scip.patch"],
    path = "github.com/sourcegraph/scip",
)
go_deps.gazelle_override(
    build_file_generation = "off",
    path = "github.com/wasmerio/wasmer-go",
)
#go_deps.module_override(
#    patch_strip = 1,
#    patches = ["//third_party:com_github_grpc_ecosystem_grpc_gateway_v2.patch"],
#    path = "github.com/grpc-ecosystem/grpc-gateway/v2",
#)

go_tool_deps = use_extension("@gazelle//:extensions.bzl", "go_deps", isolate = True)
go_tool_deps.from_file(go_mod = "//tools:go.mod")
use_repo(go_tool_deps, "com_github_grpc_ecosystem_protoc_gen_grpc_gateway_ts")

## Nodejs
node = use_extension("@rules_nodejs//nodejs:extensions.bzl", "node", dev_dependency = True)
node.toolchain(node_version = "22.18.0")

npm = use_extension("@aspect_rules_js//npm:extensions.bzl", "npm", dev_dependency = True)
npm.npm_translate_lock(
    name = "npm",
    bins = {
        # derived from "bin" attribute in node_modules/next/package.json
        "next": ["next=./dist/bin/next"],
    },
    # This is not really need but to suppress warning
    data = [
        "@//:app/package.json",
        "@//:browser/package.json",
        "@//:package.json",
        "@//:packages/chain/package.json",
        "@//:packages/debugger-common/package.json",
        "@//:packages/debugger-server/package.json",
        "@//:packages/debugger/package.json",
        "@//:packages/monitoring/package.json",
        "@//:packages/scip/package.json",
        "@//:pnpm-workspace.yaml",
        "@//:website/package.json",
    ],
    #    no_optional = True,
    npmrc = "//:.npmrc",
    pnpm_lock = "//:pnpm-lock.yaml",
    update_pnpm_lock = True,
    #    verify_node_modules_ignored = "//:.bazelignore",
)
use_repo(npm, "npm")

pnpm = use_extension("@aspect_rules_js//npm:extensions.bzl", "pnpm")
pnpm.pnpm(
    name = "pnpm",
    pnpm_version = "10.13.1",
)
use_repo(pnpm, "pnpm")

rules_ts_ext = use_extension("@aspect_rules_ts//ts:extensions.bzl", "ext", dev_dependency = True)
rules_ts_ext.deps(
    ts_version_from = "//:package.json",
)
use_repo(rules_ts_ext, "npm_typescript")

### Additional CC Toolchains for cross compile

toolchains = use_extension("@hermetic_cc_toolchain//toolchain:ext.bzl", "toolchains")
use_repo(toolchains, "zig_sdk")

register_toolchains(
    "@zig_sdk//toolchain:linux_amd64_gnu.2.31",
    #    "@zig_sdk//toolchain:linux_arm64_gnu.2.31",
    #    "@zig_sdk//toolchain:windows_amd64",
    #    "@zig_sdk//toolchain:windows_arm64",
)

### OCI Image plugin
PLATFORMS = [
    "linux/amd64",
    #        "linux/arm64",
]

oci = use_extension("@rules_oci//oci:extensions.bzl", "oci")
oci.pull(
    name = "go_base_image",
    image = "gcr.io/distroless/base",
    platforms = PLATFORMS,
    tag = "debug",
)
use_repo(oci, "go_base_image")
oci.pull(
    name = "python_base_image",
    image = "ghcr.io/sentioxyz/python",
    platforms = PLATFORMS,
    tag = "3.10-1.1.0",
)
use_repo(oci, "python_base_image")
oci.pull(
    name = "node_base_image",
    image = "ghcr.io/sentioxyz/node",
    platforms = PLATFORMS,
    tag = "22-1.3.0",
)
use_repo(oci, "node_base_image")
oci.pull(
    name = "clickhouse_client_image",
    image = "docker.io/clickhouse/clickhouse-server",
    platforms = PLATFORMS,
    tag = "head-alpine",
)
use_repo(oci, "clickhouse_client_image")
oci.pull(
    name = "move_base_image",
    image = "docker.io/google/cloud-sdk",
    platforms = PLATFORMS,
    tag = "slim",
)
use_repo(oci, "move_base_image")
use_repo(oci, "clickhouse_client_image_linux_amd64", "go_base_image_linux_amd64", "move_base_image_linux_amd64", "node_base_image_linux_amd64", "python_base_image_linux_amd64")

# TODO upstream
download_plugins = use_extension("@rules_proto_grpc_grpc_gateway//:module_extensions.bzl", "download_plugins")
use_repo(
    download_plugins,
    "grpc_gateway_plugin_darwin_arm64",
    "grpc_gateway_plugin_darwin_x86_64",
    "grpc_gateway_plugin_linux_arm64",
    "grpc_gateway_plugin_linux_x86_64",
    "grpc_gateway_plugin_windows_arm64",
    "grpc_gateway_plugin_windows_x86_64",
    "openapiv2_plugin_darwin_arm64",
    "openapiv2_plugin_darwin_x86_64",
    "openapiv2_plugin_linux_arm64",
    "openapiv2_plugin_linux_x86_64",
    "openapiv2_plugin_windows_arm64",
    "openapiv2_plugin_windows_x86_64",
)

bazel_dep(name = "openapi_tools_generator_bazel", version = "0.2.0")
git_override(
    module_name = "openapi_tools_generator_bazel",
    commit = "d34ce01b6325164cab271c74329900444afb2067",
    remote = "https://github.com/sentioxyz/openapi-generator-bazel",
)

openapi_gen = use_extension("@openapi_tools_generator_bazel//:extension.bzl", "openapi_gen")
openapi_gen.client(
    sha256 = "33e7dfa7a1f04d58405ee12ae19e2c6fc2a91497cf2e56fa68f1875a95cbf220",
    version = "7.12.0",
)
