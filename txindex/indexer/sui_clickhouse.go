package indexer

import (
	"context"
	"fmt"
	"time"

	"sentioxyz/sentio/common/log"

	"github.com/ClickHouse/clickhouse-go/v2"
)

type SuiIndexer interface {
	TableName() string
	ClusterAndEngine(ctx context.Context) (string, string)
	EnableIDIndex() bool
	ClickhouseConn() clickhouse.Conn
	LoadIndexingState() error
	GetIndexingState() IndexerState
}

const IDIndexTableNameSuffix = "_idindex"

func clickhouseExec(ctx context.Context, conn clickhouse.Conn, f string, v ...any) error {
	ctx = clickhouse.Context(ctx, clickhouse.WithSettings(clickhouse.Settings{
		"allow_experimental_lightweight_delete": "true",
	}))
	sql := fmt.Sprintf(f, v...)
	startTime := time.Now()
	err := conn.Exec(ctx, sql)
	_, logger := log.FromContext(ctx)
	if err == nil {
		logger.Debugf("ClickHouse exec: %s (%v)", sql, time.Since(startTime))
	} else {
		logger.Warnfe(err, "ClickHouse exec failed (%v): %s", err, sql)
	}
	return err
}

func clearClickHouseTable(ctx context.Context, conn clickhouse.Conn, tableName string) error {
	_, logger := log.FromContext(ctx)
	logger.Infof("Clearing ClickHouse table %s ...", tableName)
	if err := clickhouseExec(ctx, conn, "TRUNCATE TABLE `%s`", tableName); err != nil {
		logger.Errorfe(err, "clear table %s failed", tableName)
		return err
	}
	return nil
}

func clearClickHouse(ctx context.Context, i SuiIndexer) error {
	conn := i.ClickhouseConn()
	tableName := i.TableName()
	if err := clearClickHouseTable(ctx, conn, tableName); err != nil {
		return err
	}
	if i.EnableIDIndex() {
		if err := clearClickHouseTable(ctx, conn, tableName+IDIndexTableNameSuffix); err != nil {
			return err
		}
	}
	return nil
}

func clickhouseCreateTableIfNotExists(ctx context.Context, i SuiIndexer, createStmt string) bool {
	conn := i.ClickhouseConn()
	tableName := i.TableName()
	clusterArgv, engine := i.ClusterAndEngine(ctx)
	rows, err := conn.Query(ctx, "SHOW TABLES WHERE name = ?", tableName)
	if err != nil {
		panic(fmt.Errorf("clickhouse show tables name is %q failed: %w", tableName, err))
	}
	defer rows.Close()
	has := rows.Next()
	if rows.Err() != nil {
		panic(fmt.Errorf("clickhouse scan rows failed: %w", rows.Err()))
	}
	if !has {
		_, logger := log.FromContext(ctx)
		logger.Infof("table %q not found, will try to create it", tableName)
		err = clickhouseExec(ctx, conn, createStmt, tableName, clusterArgv, engine)
		if err != nil {
			panic(err)
		}
	}
	return !has
}

func clickhouseCreateIfNotExists(ctx context.Context, i SuiIndexer, createStmts ...string) bool {
	// i.tableName is used to support object query request mainly based on checkpoint
	mainCreated := clickhouseCreateTableIfNotExists(ctx, i, createStmts[0])
	// i.tableName+"_idindex" is used for query last version of the target object
	if i.EnableIDIndex() && len(createStmts) > 1 {
		idIndexCreated := clickhouseCreateTableIfNotExists(ctx, i, createStmts[1])
		return mainCreated || idIndexCreated
	}
	return mainCreated
}

func clickhouseResetTableToHead(ctx context.Context, conn clickhouse.Conn, tableName, blockColumn string, indexedHeight int64) error {
	_, logger := log.FromContext(ctx)
	logger.Infof("Resetting ClickHouse table %s to height %d..", tableName, indexedHeight)

	// query records will be deleted
	sql := fmt.Sprintf("SELECT COUNT(*) FROM %q WHERE %s > %d", tableName, blockColumn, indexedHeight)
	rows, err := conn.Query(ctx, sql)
	if err != nil {
		logger.Errorw("query failed", "error", err.Error(), "sql", sql)
		return fmt.Errorf("query %q failed: %w", sql, err)
	}
	defer rows.Close()
	var count uint64
	if rows.Next() {
		if err = rows.Scan(&count); err != nil {
			return fmt.Errorf("scan for %q failed: %w", sql, err)
		}
	}
	if count == 0 {
		// no data need to be deleted, do not execute delete sql
		logger.Infof("no data need to be deleted")
		return nil
	}

	logger.Infof("will delete %d records", count)
	return clickhouseExec(ctx, conn, "DELETE FROM `%s` WHERE %s > %d", tableName, blockColumn, indexedHeight)
}

func clickhouseResetToHead(ctx context.Context, i SuiIndexer, blockColumn string) error {
	err := i.LoadIndexingState()
	if err != nil {
		return err
	}
	indexingState := i.GetIndexingState()
	if indexingState.IndexedHeight == -1 {
		return clearClickHouse(ctx, i)
	}
	indexed := indexingState.IndexedHeight
	conn := i.ClickhouseConn()
	tableName := i.TableName()
	if err = clickhouseResetTableToHead(ctx, conn, tableName, blockColumn, indexed); err != nil {
		return err
	}
	if i.EnableIDIndex() {
		if err = clickhouseResetTableToHead(ctx, conn, tableName+IDIndexTableNameSuffix, blockColumn, indexed); err != nil {
			return err
		}
	}
	return nil
}

func clickhouseGetClusterAndEngine(ctx context.Context, conn clickhouse.Conn) (clusterArgv, engine string) {
	clusterArgv = ""
	engine = "MergeTree()"
	row := conn.QueryRow(context.Background(), "SELECT cluster FROM ("+
		"SELECT cluster, count(*) AS rs, SUM(host_address = '127.0.0.1') AS cl "+
		"FROM system.clusters "+
		"WHERE cluster not like 'all-%' "+
		"GROUP BY cluster"+
		") WHERE cl > 0 AND rs > 1")
	if row.Err() != nil {
		log.Errorf("get cluster failed: %v", row.Err())
		return
	}
	var cluster string
	err := row.Scan(&cluster)
	if err != nil {
		log.Errorf("scan cluster failed: %v", err)
		return
	}
	if cluster != "" {
		clusterArgv = " ON CLUSTER '" + cluster + "'"
		engine = "ReplicatedMergeTree('/clickhouse/tables/{cluster}/{database}/{shard}/{table}/{uuid}', '{replica}')"
	}
	return
}
