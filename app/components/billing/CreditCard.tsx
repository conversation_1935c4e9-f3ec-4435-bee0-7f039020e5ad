import React from 'react'
import { upperCase } from 'lodash'
import { LuCreditCard, LuPencil } from 'react-icons/lu'

interface Props {
  last4Digits?: string
  expireDate?: string
  cardholderName?: string
  brand?: string
  onClickEdit?: () => void
  small?: boolean
}

function CreditCard({ last4Digits, expireDate, cardholderName, brand, onClickEdit, small }: Props) {
  const cardNumber = `**** **** **** ${last4Digits}`
  const formattedCardNumber = cardNumber.replaceAll('*', '•')

  if (small) {
    return (
      <div className="text-text-foreground group flex w-full flex-wrap items-center gap-4">
        <div className="flex items-center justify-between gap-2">
          <LuCreditCard className="h-6 w-6 shrink-0" />
          <div className="text-sm font-medium">{brand ? upperCase(brand) : 'Credit Card'}</div>
          <div className="w-full text-sm font-medium">{formattedCardNumber}</div>
        </div>
        <div className="flex-0 font-bold">{upperCase(cardholderName)}</div>
        <div className="flex-0 flex items-center gap-2">
          <p className="text-sm">VALID THRU</p>
          <div className="text-sm font-bold">{expireDate}</div>
        </div>
        <LuPencil
          className="hover:text-primary-600 hidden h-4 w-4 cursor-pointer group-hover:block"
          onClick={onClickEdit}
        />
      </div>
    )
  }

  return (
    <div
      className="relative flex h-40 w-72 flex-col justify-between rounded-lg bg-gray-200 p-4 shadow-lg"
      style={{
        backgroundImage: "url('/credit-card.png')",
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <p className="mb-4 flex items-center justify-between text-white">
        <div className="w-full text-2xl font-bold">{formattedCardNumber}</div>
        <LuPencil
          className="hover:text-primary-500 dark:hover:text-primary-700 h-5 w-5 cursor-pointer"
          onClick={onClickEdit}
        />
      </p>
      <div className="font-bold text-white">{upperCase(cardholderName)}</div>
      <div className="flex w-full items-center justify-between text-white">
        <div className="">
          <p className="text-sm">VALID THRU</p>
          <div className="text-sm font-bold">{expireDate}</div>
        </div>
        <div className="flex-0">
          <div className="text-sm font-medium italic opacity-80">{brand ? upperCase(brand) : 'Credit Card'}</div>
        </div>
      </div>
    </div>
  )
}

export default CreditCard
