import { useInvoices } from '../../lib/data/use-billing'
import { money } from '../../lib/money'
import dayjs from 'dayjs'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import classNames from '../../lib/classnames'
import { InvoiceStatus } from '../../gen/service/billing/protos/service.pb'

dayjs.extend(localizedFormat)

interface Props {
  accountId?: string
}

export default function InvoiceList({ accountId }: Props) {
  const { invoices } = useInvoices(accountId)

  return (
    <div className="w-full rounded-md border" id="invoice-list">
      <table className="min-w-full divide-y divide-gray-300 ">
        <thead className="">
          <tr>
            <th scope="col" className="text-text-foreground py-2 pl-4 pr-3 text-left text-sm font-semibold">
              ID
            </th>

            <th scope="col" className="text-text-foreground px-3 py-2 text-left text-sm font-semibold">
              Billing Period
            </th>
            <th scope="col" className="text-text-foreground px-3 py-2 text-left text-sm font-semibold">
              Amount
            </th>
            <th scope="col" className="text-text-foreground px-3 py-2 text-left text-sm font-semibold">
              Due Date
            </th>
            <th scope="col" className="text-text-foreground px-3 py-2 text-left text-sm font-semibold">
              Status
            </th>
            <th />
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 p-2">
          {invoices
            .sort((a, b) => b.billingStart?.localeCompare(a.billingStart ?? '') ?? 0)
            .map((invoice) => {
              const total = invoice.items?.reduce((total, item) => total.add(money(item.amount)), money(0))
              let status
              switch (invoice.status) {
                case InvoiceStatus.PAID:
                  status = <span className="font-medium text-gray-600">Paid</span>
                  break
                case InvoiceStatus.SENT:
                  status = <span className="font-medium text-orange-700">Unpaid</span>
                  break
                case InvoiceStatus.FAILED:
                  status = <span className="font-medium text-red-500">Failed</span>
              }

              return (
                <tr key={invoice.id} className="p-2">
                  <td className="text-text-foreground whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium">
                    {invoice.id}
                  </td>
                  <td className="text-text-foreground whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium">
                    {dayjs(invoice.billingStart).format('LL')}-{dayjs(invoice.billingEnd).format('LL')}
                  </td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm text-gray-500">{total?.toString()}</td>
                  <td className="whitespace-nowrap px-3 py-2 text-sm text-gray-500">
                    {dayjs(invoice.dueDate).format('LL')}
                  </td>
                  <td className={classNames('whitespace-nowrap px-3 py-2 text-sm text-gray-500')}>{status}</td>
                  <td className="text-primary flex gap-2 whitespace-nowrap px-3 py-2 text-sm">
                    <a target="_blank" rel="noreferrer" href={`/billing/invoice/${invoice.id}`}>
                      View
                    </a>
                    <a href={`/api/invoices/pdf/${invoice.id}?hash=${invoice.hash}`}>Download</a>
                    {/*<a href={`/api/invoices/pdf/${invoice.id}?hash=${invoice.hash}`}>Checkout</a>*/}
                  </td>
                </tr>
              )
            })}
        </tbody>
      </table>
    </div>
  )
}
