import { usePlans, useSubscriptions } from '../../lib/data/use-billing'
import { useEffect, useMemo, useRef, useState } from 'react'
import { money } from '../../lib/money'
import { NumberFormat } from '../../lib/number'
import dayJs from 'dayjs'
import BigDecimal from '@sentio/bigdecimal'
import Slider from 'components/common/input/Slider'
import { formatQuantity } from './plan/Utils'
import PlanBar from './plan/PlanBar'
import PlanCard from './plan/PlanCard'
import Switch from 'components/common/input/Switch'
import { TrialEndDays } from './plan/PlanTrial'
import { OverQuotaLimit } from './plan/OverQuotaLimit'
import { PaymentPeriod } from '@sentio/service/billing/protos/service.pb'
import { NewBillingFlight, useFlightWith } from 'lib/data/use-flight'

interface Props {
  accountId?: string
}

const NF = NumberFormat({
  notation: 'compact'
})

const ConcurrentBackFill = {
  free: 1,
  dev: 5,
  pro: 10,
  enterprise: 'custom'
}

const defaultPlans = ['free', 'dev', 'pro', 'enterprise']

function InactiveUsageLimit() {
  return (
    <div className="space-y-4">
      <div className="text-base font-bold">
        <label className="text-gray-900">Auto-scaling usage: </label>
        <span className="text-gray-600">Inactive</span>
      </div>
      <div className="text-icontent flex items-center gap-1">
        <label className="font-medium text-gray-800">Upgrade to lock,</label>
        <span className="font-medium text-gray-600">
          Learn more about auto-scaling
          <a href="https://docs.sentio.xyz" target="_blank" rel="noreferrer" className="ml-1 hover:underline">
            here
          </a>
          .
        </span>
      </div>
    </div>
  )
}

function getCapByCount(count: number) {
  if (count < 10) {
    return BigDecimal(*********).multipliedBy(count)
  }
  return BigDecimal(*********)
    .multipliedBy(10)
    .plus(BigDecimal(*********).multipliedBy(count - 10))
}

const tierOrder = ['FREE', 'DEV', 'PRO', 'ENTERPRISE']

export function NewPlans({ accountId }: Props) {
  const { plans, loading } = usePlans(accountId)
  const { currentSubscription: subscription } = useSubscriptions(accountId)
  const renewDate = useMemo(() => {
    if (!subscription) {
      return ''
    }
    const startDate = dayJs(subscription.startAt)
    if (subscription?.paymentPeriod === PaymentPeriod.Yearly) {
      return startDate.add(1, 'year').startOf('month').format('ll')
    }
    return startDate.add(1, 'month').startOf('month').format('ll')
  }, [subscription])
  const [count, setCount] = useState(0)
  const [selected, setSelected] = useState<string | undefined>('free')
  const sortedPlans = useMemo(() => {
    const allPlans = [...plans]
    // Ensure current plan are included if it exists by checking the plan ID
    if (subscription?.plan && !allPlans.some((p) => p.id === subscription.plan?.id)) {
      allPlans.push(subscription.plan)
    }
    return allPlans
      .filter((p) => !p.isTrial)
      .sort((a, b) => {
        const tierA = (a.tierDefaultPlan || '').toUpperCase()
        const tierB = (b.tierDefaultPlan || '').toUpperCase()
        const idxA = tierOrder.indexOf(tierA)
        const idxB = tierOrder.indexOf(tierB)
        if (idxA !== idxB) {
          return idxA - idxB
        }
        return parseFloat(a.unitCap || '0') - parseFloat(b.unitCap || '0')
      })
  }, [plans, subscription?.plan])
  const trialPlans = useMemo(() => plans.filter((p) => p.isTrial), [plans])
  const initRef = useRef(false)
  useEffect(() => {
    if (initRef.current) {
      return
    }
    let targetPlan = subscription?.plan
    if (targetPlan?.id && targetPlan?.isTrial === false) {
      if (sortedPlans.find((p) => p.id === targetPlan?.id)) {
        setSelected(targetPlan.id)
        initRef.current = true
      }
      return
    }
    if (targetPlan) {
      targetPlan = sortedPlans.find((p) => p.tierDefaultPlan === targetPlan!.tierDefaultPlan)
    }
    if (targetPlan?.id) {
      setSelected(targetPlan.id)
      initRef.current = true
      return
    }
  }, [subscription?.plan?.id, sortedPlans])
  const [payByYear, setPayByYear] = useState(false)
  const selectedPlan = sortedPlans.find((p) => p.id === selected)
  const selectedTrialPlan = trialPlans.find((p) => p.tierDefaultPlan === selectedPlan?.tierDefaultPlan)
  const currentPlan = subscription?.plan || sortedPlans[0]
  const [maxPrice, allPrices] = useMemo(() => {
    let maxPrice = BigDecimal(0)
    const allPrices: Record<string, BigDecimal> = {}
    const totalUnits = getCapByCount(count)
    sortedPlans.forEach((plan) => {
      if (!plan.id) return
      let currentPrice = BigDecimal(money(plan.flatFee).toNumber())
      if (plan.unitCap && totalUnits.gt(BigDecimal(plan.unitCap))) {
        currentPrice = currentPrice.plus(
          BigDecimal(money(plan.unitPrice).toNumber()).multipliedBy(totalUnits.minus(BigDecimal(plan.unitCap)))
        )
      }
      allPrices[plan.id] = currentPrice
      if (currentPrice.gt(maxPrice)) {
        maxPrice = currentPrice
      }
    })
    return [maxPrice, allPrices]
  }, [count, sortedPlans])
  const isCurrentPlanTrial = currentPlan?.isTrial
  const currentSelectedPrice = selected ? allPrices[selected] || BigDecimal(0) : BigDecimal(0)
  const isNewBilling = useFlightWith(NewBillingFlight)
  return (
    <div className="dark:bg-sentio-gray-100 h-full w-full bg-white">
      <div className="w-full p-4 sm:px-14 sm:py-12">
        <div className="grid items-stretch justify-items-stretch gap-5 xl:grid-cols-2">
          <div className="border-border-color space-y-4 rounded-md border p-3 sm:p-5">
            <div className="text-base font-bold">
              <label className="text-gray-900">Active Plan: </label>
              <span className="text-primary-600 dark:text-primary-800">{currentPlan?.name}</span>
              {isCurrentPlanTrial ? (
                <TrialEndDays subscription={subscription} className="ml-2 text-sm font-medium text-gray-700" />
              ) : null}
            </div>
            <div className="text-icontent">
              {subscription && (
                <div className="flex items-center gap-4">
                  <div className="inline-flex items-center gap-1">
                    <label className="font-medium text-gray-600">Plan Renews on:</label>
                    <span className="font-bold text-gray-900">{renewDate}</span>
                  </div>
                  <div className="inline-flex items-center gap-1">
                    <label className="font-medium text-gray-600">Plan type:</label>
                    <span className="font-bold text-gray-900">
                      {subscription?.paymentPeriod === PaymentPeriod.Yearly ? 'Annual' : 'Monthly'}
                    </span>
                  </div>
                </div>
              )}
              <div className="flex items-center gap-1">
                <label className="font-medium text-gray-600">Plan quota per month:</label>
                <span className="font-bold text-gray-900">
                  {currentPlan?.payAsYouGo
                    ? 'Pay-as-you-go (Unlimited usage)'
                    : currentPlan?.unitCap
                      ? `${formatQuantity(currentPlan.unitCap, 0)} Sentio Units, SU`
                      : ''}
                </span>
              </div>
            </div>
          </div>
          <div className="border-border-color grid items-center justify-items-stretch space-y-4 rounded-md border p-3 sm:p-5">
            {currentPlan?.name === 'Free' ? (
              <InactiveUsageLimit />
            ) : (
              <OverQuotaLimit
                accountId={accountId}
                limit={subscription?.account?.usageOverCapLimit}
                isPayAsYouGo={currentPlan?.payAsYouGo}
              />
            )}
          </div>
        </div>
        <div className="mt-6">
          <div className="grid w-full grid-cols-10 items-stretch justify-items-stretch gap-5">
            <div className="col-span-3 grid items-center">
              <label className="text-lg font-bold">Find the best plan for you</label>
            </div>
            <div className="col-span-7 h-20">
              <div className="mt-1 flex w-full justify-between">
                <span className="text-icontent font-medium text-gray-800">Estimated Monthly Usage</span>
                <span className="text-icontent font-medium text-gray-800">
                  {formatQuantity(getCapByCount(count), 2, false)} Sentio Units
                </span>
              </div>
              <div className="mt-6">
                <Slider
                  initialValue={0}
                  maxValue={100}
                  onChange={(v: number) => {
                    setCount(Math.round(v))
                  }}
                />
              </div>
            </div>
          </div>
          <div className="grid min-h-[500px] w-full grid-cols-1 items-stretch justify-items-stretch gap-5 pt-2 sm:grid-cols-10">
            <div className="grid items-start sm:col-span-3">
              <PlanCard
                plan={selectedPlan}
                currentPlan={currentPlan}
                accountId={accountId}
                trialPlan={selectedTrialPlan}
                payByYear={payByYear}
              />
            </div>
            <div className="flex min-h-[300px] flex-col gap-3 sm:col-span-7">
              <div className="flex-0 grid items-center justify-items-center">
                {isNewBilling ? null : (
                  <div className="flex items-center gap-2 py-1">
                    <span className="text-icontent font-medium text-gray-800">MONTHLY</span>
                    <Switch
                      size="default"
                      checked={payByYear}
                      onChange={(v) => {
                        setPayByYear(v)
                      }}
                    />
                    <span className="text-icontent font-medium text-gray-800">ANNUAL</span>
                    <span className="bg-lake-blue-600 text-lake-blue-50 text-icontent whitespace-nowrap rounded-full px-2 font-medium">
                      up to 30% off
                    </span>
                  </div>
                )}
              </div>
              <div className="col-span-7 flex flex-1 gap-3 overflow-x-auto">
                {sortedPlans.map((plan) => {
                  const planId = plan.id!
                  let overQuota = false
                  if (plan?.id === 'free' && BigDecimal(plan.unitCap ?? 0).lt(getCapByCount(count))) {
                    overQuota = true
                  }
                  const price = allPrices[planId]
                  const value = price.dividedBy(maxPrice).multipliedBy(100).toNumber()
                  let currentSave = 0
                  if (currentSelectedPrice.gt(price)) {
                    currentSave = Math.floor(
                      currentSelectedPrice.minus(price).dividedBy(currentSelectedPrice).multipliedBy(100).toNumber()
                    )
                  }
                  return (
                    <div className="min-w-[180px] flex-1" key={planId}>
                      <PlanBar
                        value={value}
                        selected={selected === planId}
                        current={subscription?.plan?.id === planId}
                        onSelect={(v) => {
                          setSelected(v)
                        }}
                        plan={plan}
                        period={payByYear ? 'year' : 'month'}
                        price={allPrices[planId]}
                        overQuota={overQuota}
                        currentSave={currentSave}
                      />
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
