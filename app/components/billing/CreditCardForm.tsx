import { PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js'
import { useCallback, useRef, useState } from 'react'
import { useNotification } from '../../lib/data/use-notification'
import { usePaymentMethods } from '../../lib/data/use-billing'
import { BaseDialog } from '../common/dialogs/BaseDialog'

interface Props {
  accountId?: string
  open: boolean
  onClose: () => void
}

export function CreditCardForm({ accountId, open, onClose }: Props) {
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const { showNotification } = useNotification()
  const { setupPayment } = usePaymentMethods(accountId)
  const formRef = useRef<HTMLFormElement>(null)
  const [errorMessage, setError] = useState<string>()
  const handleError = (error) => {
    setLoading(false)
    setError(error.message)
  }
  const handleSubmit = useCallback(
    async (event) => {
      setError(undefined)
      event.preventDefault()
      if (!stripe || !elements || !accountId || loading) {
        return
      }
      setLoading(true)
      const { error: submitError } = await elements.submit()
      if (submitError) {
        handleError(submitError)
        return
      }

      const resp = await setupPayment(accountId)

      const { error } = await stripe.confirmSetup({
        elements,
        clientSecret: resp.clientSecret!,
        confirmParams: {
          return_url: window.location.href
        }
      })
      if (error) {
        handleError(error)
      }
    },
    [stripe, elements, accountId, loading]
  )

  return (
    <form onSubmit={handleSubmit} ref={formRef}>
      <BaseDialog
        title="Your credit card"
        open={open}
        onCancel={onClose}
        onClose={onClose}
        okText={'Submit'}
        okProps={{ type: 'submit', processing: loading }}
        onOk={() => formRef.current?.requestSubmit()}
        errorMessages={errorMessage}
        footerBorder={false}
      >
        <div className="p-4">
          <PaymentElement />
        </div>
      </BaseDialog>
    </form>
  )
}
