import NewButton from '../common/buttons/NewButton'
import { Plan } from '../../gen/service/billing/protos/service.pb'
import { useMemo } from 'react'
import { money, Money, Zero } from '../../lib/money'
import { useStatistic } from '../../lib/data/use-statistic'
import dayjs from 'dayjs'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import { useAccount, useBillingWarning, usePlans, useSubscriptions } from '../../lib/data/use-billing'
import BarLoading from '../common/util/BarLoading'
import { useUserUsage } from '../../lib/data/use-user-usage'
import { UsageProgress } from './UsageProgress'
import { skuToTitleMap } from '../usage/UsageComponent'
import BigDecimal from '@sentio/bigdecimal'
import { TrialBanner } from './plan/PlanTrial'
import { ArrowUpOnSquareIcon, InformationCircleIcon, PlusIcon } from '@heroicons/react/24/outline'
import { PopoverTooltip } from 'components/common/tooltip/DivTooltip'
import { PlanSuspend } from './plan/PlanSuspend'
import { getUsageBreakdown, PayAsYouGoProcess } from './PayAsYouGoProcess'
import { NewBillingFlight, useFlightWith } from 'lib/data/use-flight'

dayjs.extend(localizedFormat)

interface Props {
  accountId?: string
  isSentioAdmin?: boolean
}

const ZeroPlan = {
  name: 'Free',
  unitCap: '0',
  flatFee: Zero,
  unitPrice: Zero
} as Plan

export function BillingSummary({ accountId, isSentioAdmin = false }: Props) {
  const isNewBilling = useFlightWith(NewBillingFlight)
  const { account } = useAccount(accountId)
  const { isSuspended } = useBillingWarning(accountId)
  const [start, end] = useMemo(() => {
    return [dayjs().utc().utcOffset(0).startOf('month'), dayjs().utc().utcOffset(0).endOf('month')]
  }, [])

  const { plans, loading } = usePlans(accountId)
  const { currentSubscription: subscription } = useSubscriptions(accountId)
  const { data: usages } = useStatistic(
    accountId,
    account == null ? null : account.owner?.organization != null ? 'organizations' : 'users',
    start?.toISOString(),
    end?.toISOString(),
    '-',
    isSentioAdmin
  )
  const plan = useMemo(() => {
    if (subscription?.plan) {
      return subscription.plan
    } else {
      // no subscription, find the free plan
      return plans?.find((p) => p.tierDefaultPlan === 'FREE') || ZeroPlan
    }
  }, [subscription, plans])

  const { projectCount, alertCount } = useUserUsage(account?.ownerId, end?.toISOString())

  const breakdownUsages = useMemo(() => {
    const breakdownUsages: Record<string, number> = {}
    for (const usage of usages) {
      const key = usage?.tags?.sku || 'Other'
      let value = breakdownUsages[key] || 0
      value += parseFloat(usage.cost || '0')
      breakdownUsages[key] = value
    }
    return breakdownUsages
  }, [usages])

  const { totalUsage, additionFee, totalFee, tiers } = useMemo(() => {
    let totalUsage = 0
    let additionUsage = 0
    let additionFee = BigDecimal(0)
    let totalFee = BigDecimal(0)
    let tiers: Array<{
      label: string
      usage: number
      price: string
      cost: BigDecimal
      color: string
      isActive: boolean
    }> = []
    if (usages) {
      totalUsage = usages.reduce((acc, cur) => acc + parseFloat(cur.cost || '0'), 0)
      additionUsage = totalUsage - parseFloat(plan.unitCap || '0')
      if (additionUsage < 0) {
        additionUsage = 0
      }
      additionFee = BigDecimal(new Money(plan.unitPrice).toNumber()).multipliedBy(additionUsage)
      totalFee = BigDecimal(new Money(plan.flatFee).toNumber()).plus(additionFee)
    }

    if (plan.payAsYouGo) {
      const res = getUsageBreakdown(totalUsage, plan)
      totalFee = BigDecimal(new Money(plan.flatFee).toNumber()).plus(res.totalCost)
      tiers = res.tiers.filter((tier) => tier.isActive)
    }

    return {
      totalUsage,
      additionUsage,
      additionFee,
      totalFee,
      tiers
    }
  }, [usages, plan])
  const isPlanTrial = plan?.isTrial

  if (loading) {
    return <BarLoading />
  }

  const breakdown = Object.entries(breakdownUsages).filter(([key, value]) => value > 0)

  return (
    <div className="dark:bg-sentio-gray-100 h-full w-full bg-white">
      <div className="w-full p-4 sm:px-14 sm:py-12">
        {isSuspended ? (
          <PlanSuspend account={account} className="mb-8" />
        ) : isPlanTrial ? (
          <div className="mb-8">
            <TrialBanner subscription={subscription} plans={plans} />
          </div>
        ) : null}
        <div className="flex-1 items-stretch gap-4 space-y-4 sm:flex sm:space-y-0">
          <div className="flex-1">
            <div className="flex justify-between">
              <h2 className="text-sm font-semibold">Plan Details</h2>
              <a href="#tab=payment" className="text-icontent font-medium text-blue-600">
                Manage Billing & Payment
              </a>
            </div>
            <div className="mt-5 divide-y rounded-md border px-5 text-sm">
              <div className="py-5">
                <div className="flex justify-between">
                  <span className="font-semibold text-gray-800">{plan.name}</span>
                  <span className="font-semibold text-gray-800">{money(plan.flatFee).toString()}</span>
                </div>
                <div>
                  <span className="text-icontent text-gray-600">
                    {start.format('LL')} - {end.format('LL')}
                  </span>
                </div>
              </div>

              {plan.payAsYouGo ? (
                <div className="space-y-2 py-5">
                  <div className="flex justify-between">
                    <span className="font-semibold text-gray-800">Usage Breakdown</span>
                  </div>
                  {tiers
                    .filter((tier) => tier.isActive)
                    .map((tier, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-700">{tier.label}</span>
                          <span className="text-gray-500">({tier.price})</span>
                        </div>
                        <div className="text-right">
                          <span className="font-medium text-gray-900">${tier.cost.toFixed(2)}</span>
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="py-5">
                  <div className="flex justify-between">
                    <span className="font-semibold text-gray-800">Addition Usage</span>
                    <span className="font-semibold text-gray-800">{additionFee.toFixed(2)}</span>
                  </div>
                  <div className="text-icontent">
                    {plan.id === 'free' ? (
                      <span className="text-gray-500">
                        No addition SU rate for the free plan. Upgrade to a paid plan to get more SUs.
                      </span>
                    ) : (
                      <span className="text-gray-600">
                        Addition SU rate: {new Money(plan.unitPrice).multiply(1000).toString()} per 4,000,000 SU
                      </span>
                    )}
                  </div>
                </div>
              )}
              <div className="bg-sentio-gray-100 -mx-5 px-5 py-5">
                <div className="flex justify-between">
                  <span className="font-semibold text-gray-800">Total Per Month</span>
                  <span className="font-semibold text-gray-800">${totalFee.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-icontent text-gray-600">Renews: {end.add(1, 'day').format('LL')}</span>
                </div>
              </div>
            </div>
            <div className="border-border-color mt-6 flex w-full items-stretch justify-between rounded-md border p-5">
              <div>
                <h2 className="text-sm font-semibold text-gray-800">Update or Modify Plan</h2>
                <div className="mt-1 inline-flex items-center gap-2">
                  <span className="text-ilabel">Current plan:</span>
                  <span className="text-primary-600 text-icontent font-semibold">{plan.name}</span>
                </div>
              </div>
              <div className="grid items-center justify-items-center">
                <NewButton
                  size="sm"
                  role="primary"
                  className="rounded-md bg-blue-600 px-4 py-2 text-white"
                  icon={<ArrowUpOnSquareIcon />}
                >
                  <a href={`#tab=plan`}>Modify</a>
                </NewButton>
              </div>
            </div>
            {isNewBilling && (
              <div className="border-border-color mt-6 w-full overflow-hidden rounded-md border">
                <div className="flex items-center gap-2 bg-cyan-100 px-5 py-1">
                  <img src="/bonus.svg" className="h-6 w-6" alt="bonus logo" />
                  <span className="text-xs font-semibold text-cyan-700">
                    Buy credits and get extra bonus (up to 15%)
                  </span>
                </div>
                <div className="flex items-stretch justify-between p-5">
                  <div>
                    <h2 className="text-sm font-semibold text-gray-800">Credits</h2>
                    <div className="mt-1 inline-flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {account?.prepaidBalance ? money(account?.prepaidBalance).toString() : '$0'}
                      </span>
                    </div>
                  </div>
                  <div className="grid items-center justify-items-center">
                    <NewButton
                      size="sm"
                      role="primary"
                      className="rounded-md bg-blue-600 px-4 py-2 text-white"
                      icon={<PlusIcon />}
                    >
                      <a href={`#tab=payment`}>Buy Credits</a>
                    </NewButton>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="flex flex-1 flex-col">
            <div className="flex justify-between">
              <h2 className="text-sm font-semibold">Data Usage</h2>
              <a href="#tab=usage" className="text-icontent font-medium text-blue-600">
                View Usage Details & History
              </a>
            </div>
            <div className="mt-5 flex-1 divide-y rounded-md border px-5 text-sm">
              <div className="pb-2 pt-5">
                <div className="div">
                  <div className="font-semibold text-gray-800">
                    This billing period ({start.format('LL')} - {end.format('LL')})
                  </div>
                </div>
              </div>
              <div className="py-5">
                {isNewBilling ? null : (
                  <>
                    <div className="mt-2 py-2 font-semibold text-gray-800">Plan quota usage</div>
                    <div>
                      <span className="text-ilabel font-medium text-gray-800">
                        Sentio Units: {totalUsage.toLocaleString()} /{' '}
                        {plan?.unitCap && !plan.payAsYouGo ? BigDecimal(plan.unitCap).toFormat(0) : 'Unlimited'}
                      </span>
                    </div>
                  </>
                )}
                <div>
                  {plan.payAsYouGo ? (
                    <PayAsYouGoProcess usage={totalUsage} currentPlan={plan} />
                  ) : (
                    <UsageProgress plans={plans} currentPlan={plan} usage={totalUsage} />
                  )}
                </div>
                {breakdown.length > 0 && (
                  <>
                    <div className="text-ilabel font-semibold text-gray-800">Breakdown</div>
                    <ul className="text-icontent space-y-1 p-2 text-gray-600">
                      {breakdown.map(([key, value], index) => {
                        const name = skuToTitleMap[key] || key
                        return <li key={index}>{`- ${name}: ${value?.toLocaleString()}`}</li>
                      })}
                    </ul>
                  </>
                )}
                <div className="text-ilabel mt-2 font-medium text-gray-800">
                  Number of Projects: {projectCount || 0} / {plan.projectCap || 'Unlimited'}
                </div>
                <div className="text-ilabel mt-2 font-medium text-gray-800">
                  Number of Alerts: {alertCount || 0} / {plan.alertCap || 'Unlimited'}
                </div>
              </div>
              <div className="py-4">
                {plan.id === 'free' ? (
                  <div className="mt-2 font-semibold text-gray-800">
                    Additional Sentio Units:{' '}
                    <span className="font-normal text-gray-600">upgrade to a paid plan to get more SUs.</span>
                  </div>
                ) : (
                  <div className="mt-2 flex items-center gap-1">
                    <div className="font-semibold text-gray-800">
                      Additional Sentio Units: {money(plan.unitPrice).multiply(1000000).format(3)} Per 1M Sentio Units
                    </div>
                    <PopoverTooltip
                      hideArrow
                      text={
                        <p className="text-xs font-normal text-gray-600">
                          Sentio Unit is a billing metric used to measure resource consumption in Sentio, with costs
                          varying by usage type (e.g., metrics, logs, webhooks, API calls) and stage (e.g., Watching or
                          Backfill).
                          <br />
                          <a
                            href="https://docs.sentio.xyz/docs/quotas-and-limits#sentio-units"
                            target="_blank"
                            rel="noreferrer"
                            className="text-primary-500 cursor-pointer hover:underline"
                          >
                            Learn More
                          </a>
                        </p>
                      }
                    >
                      <InformationCircleIcon className="h-4 w-4" />
                    </PopoverTooltip>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
