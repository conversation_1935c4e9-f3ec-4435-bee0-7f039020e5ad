import { CreditCardIcon, PlusIcon } from '@heroicons/react/24/outline'
import NewButton from '../common/buttons/NewButton'
import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { useAccount, useBillingWarning, usePaymentMethods, useSubscriptions } from '../../lib/data/use-billing'
import { NotificationContext } from '../../lib/data/use-notification'
import { CreditCardDialog } from './CreditCardDialog'
import CreditCard from './CreditCard'
import InvoiceList from './InvoiceList'
import { PlanSuspend } from './plan/PlanSuspend'
import { SwitchButton } from 'components/common/input/SwitchButton'
import { useResizeDetector } from 'react-resize-detector'
import { CryptoIcon } from './Icons'
import { money } from 'lib/money'
import classNames from 'lib/classnames'
import { withJsonApiAutoToken } from 'lib/data/with-json-api'
import {
  BillingService,
  CheckoutRequest,
  GetInvoiceRequest,
  InvoiceStatus,
  NewPrepaidInvoiceRequest,
  PaymentGateway
} from '@sentio/service/billing/protos/service.pb'
import { NewBillingFlight, useFlightWith } from 'lib/data/use-flight'
import { LuCoins, LuLoaderCircle } from 'react-icons/lu'
import PrepaidInvoiceList from './PrepaidInvoiceList'
import { Group, List, Panels, Panel } from 'components/common/tabs/StyledTabs'
import { useDarkMode } from 'lib/util/use-dark-mode'

interface Props {
  accountId?: string
}

export function PaymentPage({ accountId }: Props) {
  const isNewBilling = useFlightWith(NewBillingFlight)
  const { account, updateAccount } = useAccount(accountId)
  const { cardInfo } = usePaymentMethods(accountId)
  const [name, setName] = useState(account?.name)
  const [email, setEmail] = useState(account?.contact)
  const [address, setAddress] = useState(account?.address)
  const [showCreditCardDialog, setShowCreditCardDialog] = useState(false)
  const { showNotification } = useContext(NotificationContext)
  const isDirty = name !== account?.name || email !== account?.contact || address !== account?.address
  const { currentSubscription } = useSubscriptions(accountId)
  const { isSuspended } = useBillingWarning(accountId)
  const { ref, width } = useResizeDetector({
    refreshMode: 'debounce',
    refreshRate: 100
  })
  const isDarkMode = useDarkMode()

  useEffect(() => {
    if (account) {
      setName(account.name)
      setEmail(account.contact)
      setAddress(account.address)
    }
  }, [account])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    updateAccount({
      id: accountId,
      name,
      contact: email,
      address: address
    }).catch((e) => {
      showNotification(
        {
          type: 'error',
          title: 'Failed to update account',
          message: e?.body?.message
        },
        10
      )
    })
  }

  const [isPaying, setPaying] = useState(false)
  const [isEditing, setEditing] = useState(false)
  const [isCryptoPay, setCryptoPay] = useState(false)
  const [amount, setAmount] = useState(50)
  const [waitingPayment, setWaitingPayment] = useState(false)
  // const [currentInvoiceId, setCurrentInvoiceId] = useState<string | null>(null)
  const [bonusAmount, bonusText] = useMemo(() => {
    const { prepaidBonus } = currentSubscription?.plan || {}
    let bonus = 0
    if (prepaidBonus && prepaidBonus.length > 0) {
      prepaidBonus.forEach((d) => {
        const n = money(d.amount)
        if (amount >= n.toNumber()) {
          bonus = Math.max(bonus, d.bonus || 0)
        }
      })
    }
    const bonusAmount = money(amount * bonus).add(money(amount))
    return [
      bonus > 0 ? ` + ${bonus * 100}% bonus` : '',
      bonus > 0 ? `Get ${bonusAmount.toString()} (include ${bonus * 100}% bonus)` : ''
    ]
  }, [amount, currentSubscription])
  const checkBonus = useCallback(
    (amount: number) => {
      const { prepaidBonus } = currentSubscription?.plan || {}
      let bonus = 0
      if (prepaidBonus && prepaidBonus.length > 0) {
        prepaidBonus.forEach((d) => {
          const n = money(d.amount)
          if (amount >= n.toNumber()) {
            bonus = Math.max(bonus, d.bonus || 0)
          }
        })
      }
      return bonus ? `+ ${bonus * 100}%` : ''
    },
    [currentSubscription]
  )

  const handleAmountChange = (newAmount: number) => {
    setAmount(newAmount)
  }

  const refreshInvoiceRef = useRef<any>(null)
  const refreshInvoiceInterval = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    return () => {
      if (refreshInvoiceInterval.current) {
        clearInterval(refreshInvoiceInterval.current)
        refreshInvoiceInterval.current = null
      }
      if (refreshInvoiceRef.current) {
        refreshInvoiceRef.current = null
      }
    }
  }, [])

  const handlePayment = async () => {
    try {
      setPaying(true)
      // step 1 - create invoice
      const req: NewPrepaidInvoiceRequest = {
        subscriptionId: currentSubscription?.id,
        amount: money(amount)
      }
      const res = await withJsonApiAutoToken(BillingService.NewPrepaidInvoice)(req)
      // step 2 - redirect to payment page
      const invoiceId = res?.invoice?.id
      const hash = res?.invoice?.hash
      if (invoiceId) {
        const req2: CheckoutRequest = {
          invoiceId: invoiceId,
          paymentMethod: isCryptoPay ? PaymentGateway.REQUEST_FINANCE : PaymentGateway.STRIPE
        }
        const checkoutRes = await withJsonApiAutoToken(BillingService.Checkout)(req2)
        if (checkoutRes?.redirectUrl) {
          window.open(checkoutRes.redirectUrl, '_blank')
          // Show waiting state with invoice ID
          // setCurrentInvoiceId(invoiceId)
          setWaitingPayment(true)
          // Start polling for invoice status
          refreshInvoiceRef.current = (alwaysShow?: boolean) => {
            const req: GetInvoiceRequest = {
              invoiceId: invoiceId
            }
            withJsonApiAutoToken(BillingService.RefreshInvoice)(req)
              .then((res) => {
                if (res.status === InvoiceStatus.PAID) {
                  setWaitingPayment(false)
                  // setCurrentInvoiceId(null)
                  showNotification(
                    {
                      type: 'success',
                      title: 'Payment Successful',
                      message: 'Your payment has been successfully processed.'
                    },
                    3
                  )
                } else if (alwaysShow && res.status === InvoiceStatus.FAILED) {
                  setWaitingPayment(false)
                  // setCurrentInvoiceId(null)
                  showNotification(
                    {
                      type: 'error',
                      title: 'Payment Failed',
                      message: 'Your payment has failed. Please try again.'
                    },
                    10
                  )
                } else if (alwaysShow) {
                  showNotification(
                    {
                      type: 'info',
                      title: 'Payment Status',
                      message: 'Your payment is still processing. Please wait.'
                    },
                    3
                  )
                }

                if (res.status && [InvoiceStatus.PAID, InvoiceStatus.SENT].includes(res.status)) {
                  // If payment is successful or invoice is sent, clear the interval
                  if (refreshInvoiceInterval.current) {
                    clearInterval(refreshInvoiceInterval.current)
                    refreshInvoiceInterval.current = null
                  }
                }
              })
              .catch((e) => {
                if (alwaysShow) {
                  showNotification(
                    {
                      type: 'error',
                      title: 'Payment Error',
                      message: e?.body?.message || 'Failed to refresh payment status'
                    },
                    10
                  )
                }
              })
          }
          // Start polling every 10 seconds
          refreshInvoiceInterval.current = setInterval(() => {
            if (refreshInvoiceRef.current) {
              refreshInvoiceRef.current()
            }
          }, 10000)
        }
      }
      setPaying(false)
    } catch (e: any) {
      setPaying(false)
      showNotification(
        {
          type: 'error',
          title: 'Payment Error',
          message: e?.body?.message || 'Failed to process payment. Please try again.'
        },
        10
      )
    }
  }

  const handleRefresh = () => {
    if (refreshInvoiceRef.current) {
      refreshInvoiceRef.current(true)
    }
  }

  const handleCancelWaiting = () => {
    setWaitingPayment(false)
    // setCurrentInvoiceId(null)
    if (refreshInvoiceInterval.current) {
      clearInterval(refreshInvoiceInterval.current)
      refreshInvoiceInterval.current = null
    }
  }

  return (
    <div className="dark:bg-sentio-gray-100 h-full w-full overflow-auto bg-white">
      {/* Loading Mask for Waiting Payment */}
      {waitingPayment && (
        <div className="fixed inset-0 z-10 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
          <div className="mx-4 w-full max-w-md rounded-lg bg-white p-8 shadow-xl">
            <div className="text-center">
              <div className="mb-4">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                  <LuLoaderCircle className="h-8 w-8 animate-spin text-blue-600" />
                </div>
              </div>

              <h3 className="mb-2 text-lg font-semibold text-gray-900">Waiting for Payment</h3>

              <p className="mb-4 text-gray-600">Please complete your payment in the new tab/window that opened.</p>

              {/* {currentInvoiceId && (
                <div className="mb-6 rounded-lg bg-gray-50 p-3">
                  <p className="mb-1 text-sm text-gray-500">Invoice ID:</p>
                  <p className="break-all font-mono text-sm text-gray-900">{currentInvoiceId}</p>
                </div>
              )} */}

              <div className="flex gap-3">
                <NewButton role="secondary" size="md" onClick={handleCancelWaiting} className="flex-1 justify-center">
                  Cancel
                </NewButton>
                <NewButton role="primary" size="md" onClick={handleRefresh} className="flex-1 justify-center">
                  Refresh
                </NewButton>
              </div>

              <p className="mt-4 text-xs text-gray-400">
                This dialog will automatically close when payment is completed.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-2 p-4 sm:px-14 sm:py-12">
        {isSuspended && <PlanSuspend account={account} className="mb-8" shouldFocus />}
        <div
          className={classNames(
            'mb-4 grid w-full flex-1 justify-items-stretch gap-4',
            isNewBilling ? 'sm:grid-cols-2' : ''
          )}
        >
          <div
            className={classNames(' gap-6', isNewBilling ? 'flex h-full flex-col' : 'grid grid-cols-1 sm:grid-cols-2')}
          >
            <form className="border-border-color rounded-md border p-5" onSubmit={handleSubmit}>
              <div className="flex w-full items-center justify-between">
                <div className="text-base font-medium">Receipt Information</div>
                {isEditing ? (
                  <div className="flex items-center gap-2">
                    <NewButton role="secondary" type="button" onClick={() => setEditing(false)}>
                      Cancel
                    </NewButton>
                    <NewButton disabled={!isDirty} role="primary" type="submit">
                      Save
                    </NewButton>
                  </div>
                ) : (
                  <NewButton type="button" onClick={() => setEditing(true)}>
                    Edit
                  </NewButton>
                )}
              </div>
              <div className="text-text-foreground mx-auto mt-4 grid grid-cols-3 items-center gap-2">
                <label className="text-ilabel font-medium leading-6">Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="text-icontent border-border-color col-span-2 rounded-md border py-0.5"
                    value={name}
                    onChange={(e) => {
                      setName(e.target.value)
                    }}
                    placeholder={'Enter your company name.'}
                  />
                ) : (
                  <span className="text-icontent col-span-2">{account?.name}</span>
                )}
                <label className="text-ilabel font-medium leading-6">Email</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="text-icontent border-border-color col-span-2 rounded-md border py-0.5"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value)
                    }}
                    placeholder={'Enter a comma separated list of emails to receive copies of your receipts.'}
                  />
                ) : (
                  <span className="text-icontent col-span-2">{account?.contact}</span>
                )}
                <label className="text-ilabel font-medium leading-6">Address</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="text-icontent border-border-color col-span-2 rounded-md border py-0.5"
                    value={address}
                    onChange={(e) => {
                      setAddress(e.target.value)
                    }}
                    placeholder={'Enter your company address.'}
                  />
                ) : (
                  <span className="text-icontent col-span-2">{account?.address}</span>
                )}
              </div>
            </form>
            <div className="border-border-color w-full flex-1 rounded-md border p-5">
              <div className="flex w-full items-center justify-between">
                <h3 className="mb-4 text-base font-medium">Payment method</h3>
                {cardInfo && (
                  <NewButton type="button" onClick={() => setShowCreditCardDialog(true)}>
                    Edit
                  </NewButton>
                )}
              </div>
              {cardInfo ? (
                <CreditCard
                  cardholderName={cardInfo.cardHolderName}
                  expireDate={`${cardInfo.cardExpMonth}/${cardInfo.cardExpYear}`}
                  last4Digits={cardInfo.cardLast4}
                  brand={cardInfo.cardBrand}
                  onClickEdit={() => setShowCreditCardDialog(true)}
                  small={true}
                />
              ) : (
                <p className="text-icontent text-gray-500">Enter a new card to use for your Sentio payments</p>
              )}
              {!cardInfo && (
                <NewButton role="secondary" size="md" className="mt-2" onClick={() => setShowCreditCardDialog(true)}>
                  <PlusIcon className="h-4 w-4" />
                  Add Credit Card
                </NewButton>
              )}
              <div className="text-icontent mt-2 text-gray-500">
                <p>
                  You can choose to pay through
                  <a
                    href={`/billing/subscription?plan=${currentSubscription?.plan?.id}&account=${accountId}`}
                    className={'text-primary-500 mx-1'}
                  >
                    crypto
                  </a>
                  as well.
                </p>
              </div>
            </div>
          </div>
          {isNewBilling && (
            <div className="w-full overflow-hidden">
              <div className="border-border-color h-full w-full rounded-md border p-5">
                <div className="flex w-full items-center justify-between">
                  <h3 className="mb-4 text-base font-medium">Buy Credits</h3>
                  <div
                    className="text-primary-600 dark:text-primary-700 inline-flex items-center gap-2"
                    title="Current Credit Balance"
                  >
                    <LuCoins className="h-4.5 w-4.5" />
                    <span className="text-base font-medium">
                      {account?.prepaidBalance ? money(account?.prepaidBalance).toString() : '$0'}
                    </span>
                  </div>
                </div>
                <div className="mt-[22px]" ref={ref}>
                  <h3 className="mb-2 text-sm font-medium text-gray-600">Payment Method</h3>
                  <SwitchButton
                    isChecked={isCryptoPay}
                    onChange={setCryptoPay}
                    onText={
                      <span className="inline-flex items-center gap-2 text-sm">
                        <CryptoIcon className="h-5 w-5" />
                        Crypto
                      </span>
                    }
                    offText={
                      <span className="inline-flex items-center gap-2 text-sm">
                        <CreditCardIcon className="h-5 w-5" />
                        Fiat
                      </span>
                    }
                    width={width ? `${width}px` : '256px'}
                  />
                </div>
                <div className="mt-6">
                  <h3 className="mb-2 text-sm font-medium text-gray-600">Amount</h3>

                  {/* Amount input with credits display */}
                  <div className="relative mb-4">
                    <input
                      type="number"
                      value={amount}
                      onChange={(e) => handleAmountChange(Number(e.target.value) || 0)}
                      className="border-sentio-gray-300 dark:border-sentio-gray-600 focus:ring-primary-500 focus:border-primary-500 w-full rounded-lg border px-3 py-2 text-sm outline-none focus:ring-2"
                      placeholder="50"
                      min="1"
                    />
                    <div className="absolute right-8 top-1/2 -translate-y-1/2">
                      {bonusText ? (
                        <span className="text-icontent rounded-full font-medium text-cyan-700">{bonusText}</span>
                      ) : null}
                    </div>
                  </div>

                  {/* Preset amount buttons */}
                  <div className="mb-2 flex flex-wrap items-center justify-stretch gap-2">
                    {[50, 100, 300, 500].map((presetAmount) => (
                      <div key={presetAmount} className="relative">
                        <button
                          onClick={() => handleAmountChange(presetAmount)}
                          className={classNames(
                            'h-8 rounded-full px-4 font-medium transition-colors',
                            'relative flex items-center gap-2',
                            amount === presetAmount
                              ? 'bg-primary-600 text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          )}
                        >
                          <span className="text-icontent">${presetAmount}</span>
                          {checkBonus(presetAmount) && (
                            <div
                              className={classNames(
                                'text-xs',
                                amount === presetAmount
                                  ? 'text-primary-100 dark:text-white'
                                  : 'text-primary-600 dark:text-primary-800'
                              )}
                            >
                              {checkBonus(presetAmount)} more
                            </div>
                          )}
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
                <NewButton
                  onClick={handlePayment}
                  disabled={amount <= 0}
                  size="lg"
                  className="relative mt-6 h-8 w-full justify-center"
                  role="primary"
                  processing={isPaying}
                >
                  Pay
                  {/* {bonusAmount && (
                    <span className="ml-2 rounded-full border border-white/30 bg-white/20 px-2 py-0.5 text-xs font-semibold text-white shadow-sm backdrop-blur-sm">
                      {bonusAmount}
                    </span>
                  )} */}
                </NewButton>
              </div>
            </div>
          )}
        </div>
        <CreditCardDialog
          open={showCreditCardDialog}
          onClose={() => setShowCreditCardDialog(false)}
          accountId={accountId}
          isDarkMode={isDarkMode}
        />
        <Group>
          <List tabs={['Invoices', 'Prepaid']} className="mb-4" noBorder />
          <Panels className="w-full">
            <Panel>
              <InvoiceList accountId={accountId} />
            </Panel>
            <Panel>
              <PrepaidInvoiceList accountId={accountId} />
            </Panel>
          </Panels>
        </Group>
      </div>
    </div>
  )
}

export default PaymentPage
