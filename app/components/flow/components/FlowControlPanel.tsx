import dayjs, { Dayjs } from 'dayjs'
import { Checkbox } from 'components/common/select/Checkbox'
import { NewMultipleSelect } from 'components/common/select/NewMultipleSelect'
import { PopoverTooltip } from 'components/common/tooltip/DivTooltip'
import { DatePicker } from 'components/timerange/DatePicker'
import { QuickDateRangeButtons } from './QuickDateRangeButtons'
import { formatValue } from '../utils/FlowUtils'
import { LuChevronLeft, LuChevronRight } from 'react-icons/lu'
import { useMemo } from 'react'
import classNames from 'lib/classnames'

const PoolSelect = NewMultipleSelect<string>

interface FlowControlPanelProps {
  startDate: Dayjs | undefined
  endDate: Dayjs | undefined
  size: number
  showUnknown: boolean
  selectedPools: string[]
  poolOptions: string[]
  showOutflow: boolean
  totalValuesBySource: { tag: string; value: number; percentage: number }[]
  totalValuesByTarget: { tag: string; value: number; percentage: number }[]
  cycleDetectionLogs: string[]
  onStartDateChange: (date: Dayjs | undefined) => void
  onEndDateChange: (date: Dayjs | undefined) => void
  onSizeChange: (size: number) => void
  onShowUnknownChange: (show: boolean) => void
  onSelectedPoolsChange: (pools: string[]) => void
  onShowOutflowChange: (show: boolean) => void
  onSetDataRange: (startDate: Dayjs | undefined, endDate: Dayjs | undefined) => void
}

export const FlowControlPanel = ({
  startDate,
  endDate,
  size,
  showUnknown,
  selectedPools,
  poolOptions,
  showOutflow,
  totalValuesBySource,
  totalValuesByTarget,
  cycleDetectionLogs,
  onStartDateChange,
  onEndDateChange,
  onSizeChange,
  onShowUnknownChange,
  onSelectedPoolsChange,
  onShowOutflowChange,
  onSetDataRange
}: FlowControlPanelProps) => {
  const disableNext = useMemo(() => {
    if (!startDate || !endDate) return true
    const preDay = dayjs().utc().startOf('day').subtract(1, 'days')
    return endDate.utc().isBefore(preDay) === false
  }, [startDate, endDate])
  const maxDate = dayjs().utc().startOf('day').subtract(1, 'seconds')
  return (
    <div className="flex h-full flex-col gap-3 overflow-auto p-3 pl-2">
      <div className="space-y-2">
        <div className="text-sm font-medium">Date Range</div>
        <QuickDateRangeButtons setDataRange={onSetDataRange} current={maxDate} />
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div className="flex w-full items-center gap-2">
            <button
              className="flex-0 hidden h-full rounded-md px-1 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600 sm:block"
              onClick={() => {
                if (startDate && endDate) {
                  onSetDataRange(startDate.subtract(1, 'days'), endDate.subtract(1, 'days'))
                }
              }}
              title="Previous"
            >
              <LuChevronLeft className="h-5 w-5 " />
            </button>
            <div className="flex-1">
              <div className="mb-1 text-xs text-gray-500">Start</div>
              <DatePicker value={startDate} onChange={onStartDateChange} maxDate={endDate || maxDate} format="MMM DD" />
            </div>
          </div>
          <div className="flex w-full items-center gap-2">
            <div className="flex-1">
              <div className="mb-1 text-xs text-gray-500">End</div>
              <DatePicker
                value={endDate}
                onChange={onEndDateChange}
                minDate={startDate}
                maxDate={maxDate}
                disabled={!startDate}
                placeholder={startDate ? 'End date' : 'Start first'}
                format="MMM DD"
              />
            </div>
            <button
              className={classNames(
                'flex-0 hidden h-full rounded-md px-1 text-gray-400 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600 sm:block',
                disableNext ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
              )}
              onClick={() => {
                if (startDate && endDate) {
                  onSetDataRange(startDate.add(1, 'days'), endDate.add(1, 'days'))
                }
              }}
              disabled={disableNext}
              title={disableNext ? 'Reached the latest date' : 'Next'}
            >
              <LuChevronRight className="h-5 w-5" />
            </button>
          </div>
        </div>
        <div className="flex w-full items-center justify-between gap-4 pt-2 sm:hidden">
          <button
            className="flex h-full flex-1 items-center gap-2 rounded-md border border-gray-200 p-2 text-gray-600 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600"
            onClick={() => {
              if (startDate && endDate) {
                onSetDataRange(startDate.subtract(1, 'days'), endDate.subtract(1, 'days'))
              }
            }}
          >
            <LuChevronLeft className="h-5 w-5 " /> Previous Range
          </button>
          <button
            className="flex h-full flex-1 items-center justify-end gap-2 rounded-md border border-gray-200 p-2 text-gray-600 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-600"
            onClick={() => {
              if (startDate && endDate) {
                onSetDataRange(startDate.add(1, 'days'), endDate.add(1, 'days'))
              }
            }}
          >
            Next Range <LuChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>

      <div className="space-y-1">
        <div className="text-sm font-medium">Data Size</div>
        <input
          type="number"
          value={size}
          step={100}
          min={100}
          max={10000}
          onChange={(e) => onSizeChange(Number(e.target.value))}
          className="text-icontent w-full rounded-md"
        />
      </div>

      <div className="mt-4 text-sm font-medium">Other Options</div>

      <div>
        <Checkbox label="Show Unknown" checked={showUnknown} onChange={onShowUnknownChange} />
      </div>

      <div className="space-y-1">
        <div className="flex w-full flex-wrap items-center justify-between gap-1">
          <label className="text-sm font-medium">Filter by Protocols</label>
          <div className="text-xs text-gray-500">
            {selectedPools.length > 0
              ? `(${selectedPools.length} Pool${selectedPools.length > 1 ? 's' : ''})`
              : 'All Protocols'}
          </div>
        </div>
        <div>
          <PoolSelect
            options={poolOptions}
            value={selectedPools}
            onChange={onSelectedPoolsChange}
            className="max-w-full rounded-md border p-1 sm:max-w-sm"
            unSelectedText="All Protocols"
            labelClassName="py-1 !pr-1 gap-1 rounded"
            autoOptionsWidth
          />
          {cycleDetectionLogs.length > 0 && (
            <PopoverTooltip
              hideArrow
              text={
                <p className="max-h-[400px] overflow-y-auto p-1 text-xs text-gray-600">
                  {cycleDetectionLogs.map((log, index) => (
                    <span key={index}>
                      {log}
                      <br />
                    </span>
                  ))}
                </p>
              }
            >
              <div className="mt-2 text-xs text-gray-500">
                Detected {cycleDetectionLogs.length} cycle(s) and auto fixed them
              </div>
            </PopoverTooltip>
          )}
        </div>
      </div>

      <div className="flex-1"></div>

      <div className="flex-0 space-y-2 pt-3">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium">Total Value by {showOutflow ? 'Outflow' : 'Inflow'}</div>
            <button
              onClick={() => onShowOutflowChange(!showOutflow)}
              className="text-primary-500 hover:text-primary-700 dark:text-primary-700 dark:hover:text-primary-900 text-xs"
            >
              Switch to {showOutflow ? 'Inflow' : 'Outflow'}
            </button>
          </div>
          <div className="scrollbar-thin max-h-[200px] space-y-1 overflow-y-auto">
            {(showOutflow ? totalValuesBySource : totalValuesByTarget).map(({ tag, value, percentage }) => (
              <div key={tag} className="group relative flex items-center justify-between px-2 py-1 text-xs">
                <div
                  className="dark:bg-sentio-gray-600/50 bg-sentio-gray-300/50 absolute inset-0 z-0 transition-all duration-200"
                  style={{ width: `${percentage}%` }}
                />
                <div className="relative flex w-full items-center justify-between">
                  <span className="relative text-gray-600">{tag}</span>
                  <span
                    className="hover:text-primary-500 hidden cursor-pointer px-2 text-[10px] text-gray-500 group-hover:block dark:hover:text-white"
                    onClick={() => {
                      if (!selectedPools.includes(tag)) {
                        onSelectedPoolsChange([...selectedPools, tag])
                      }
                    }}
                  >
                    + Add Filter
                  </span>
                  <span className="flex-1"></span>
                  <span className="text-primary-500 dark:text-primary-800 relative">{formatValue(value)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="text-xs text-gray-500">
          Data Range: {`${startDate?.format('YYYY-MM-DD')} ~ ${endDate?.format('YYYY-MM-DD')}`} (UTC)
        </div>
      </div>
    </div>
  )
}
