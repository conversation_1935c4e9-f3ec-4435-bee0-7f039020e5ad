import { Dayjs } from 'dayjs'

interface QuickDateRangeButtonsProps {
  current?: Dayjs
  setDataRange: (startDate: Dayjs | undefined, endDate: Dayjs | undefined) => void
}

export const QuickDateRangeButtons = ({ current, setDataRange }: QuickDateRangeButtonsProps) => {
  const setRange = (days: number) => {
    const end = current?.endOf('day')
    const start = current?.startOf('day').subtract(days - 1, 'days')
    setDataRange(start, end)
  }

  const buttons = [
    { label: 'Last Day', days: 1 },
    { label: 'Last 3 Days', days: 3 },
    { label: 'Last 5 Days', days: 5 },
    { label: 'Last 7 Days', days: 7 }
  ]

  return (
    <div className="flex flex-wrap gap-x-2 gap-y-2">
      {buttons.map(({ label, days }) => (
        <button
          key={days}
          onClick={() => setRange(days)}
          className="rounded bg-gray-100 px-2 py-1 text-xs text-gray-600 hover:bg-gray-200"
        >
          {label}
        </button>
      ))}
    </div>
  )
}
