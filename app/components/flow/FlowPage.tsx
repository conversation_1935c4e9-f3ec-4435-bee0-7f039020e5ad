import { useRouter } from 'next/router'
import Head from 'next/head'
import { useEffect, useMemo, useState } from 'react'
import { isEqual, sortBy } from 'lodash'
import dayjs, { Dayjs } from 'dayjs'
import { Source } from 'gen/service/analytic/protos/service.pb'
import { OpenGraphTags } from 'components/common/OpenGraphTags'
import Tabs from 'components/common/tabs/Tabs'
import Tab from 'components/common/tabs/Tab'
import template from 'lodash/template'
import { useQueryParams, NumberParam, StringParam, DelimitedArrayParam, BooleanParam } from 'use-query-params'
import { HiOutlineAdjustmentsHorizontal } from 'react-icons/hi2'

// Import our new components
import { SankeyChart } from './components/SankeyChart'
import { AnalysisCharts } from './components/AnalysisCharts'
import { FlowControlPanel } from './components/FlowControlPanel'
import { DataRow, FlowStats } from './utils/FlowUtils'
import { fixCycles } from './utils/DetecCycle'
import { SharePanel } from './components/SharePanel'
import { ThemeSwitch } from './components/ThemeSwitch'
import { useDarkMode } from 'lib/util/use-dark-mode'
import { LuCalendar, LuFilter, LuInbox, LuListTodo, LuRoute, LuTriangleAlert } from 'react-icons/lu'
import classNames from 'lib/classnames'
import { EthChainId } from '@sentio/chain'
import { PopupMenuButton } from 'components/menu/PopupMenuButton'
import { ChevronDownIcon } from '@heroicons/react/20/solid'
import { ChainIcon } from 'components/chains/ChainIcons'
import { AppSwitcher } from './components/AppSwitcher'

import useApi from 'lib/data/use-api'
import {
  AnalyticService,
  ExecutionInfo,
  ExecutionStatus,
  QuerySQLResultResponse,
  SQLRequest,
  ExecuteEngine
} from 'gen/service/analytic/protos/service.pb'
import * as CommonCommon from 'gen/service/common/protos/common.pb'
import { Inter } from 'next/font/google'

const roboto = Inter({
  weight: ['300', '400', '500', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true
})

function makeSQLPayload(
  projectOwner: string,
  projectSlug: string,
  chart: { sqlQuery?: string },
  version?: number,
  variables: Record<string, string> = {},
  source?: Source,
  cachePolicy?: CommonCommon.CachePolicy,
  parameters?: CommonCommon.RichStruct,
  engine?: ExecuteEngine
) {
  const { sql, size } = JSON.parse(chart.sqlQuery || '{}')
  return {
    projectSlug,
    projectOwner,
    version,
    sqlQuery: {
      sql,
      variables,
      size,
      parameters
    },
    source: source ?? Source.DASHBOARD,
    cachePolicy,
    engine
  } as SQLRequest
}

function useFundFlowSQLQuery(sql: string | undefined, size = 100) {
  const projectOwner = 'sentio'
  const projectSlug = 'fund-flow'
  const [execRequest, setExecRequest] = useState<SQLRequest | undefined>(undefined)
  const [loading, setLoading] = useState(false)
  const variables = {}
  const source = Source.DASHBOARD
  const cachePolicy = {
    noCache: false,
    cacheTtlSecs: 1296000,
    cacheRefreshTtlSecs: 1800
  }

  useEffect(() => {
    if (!sql) {
      return undefined
    }

    const req = makeSQLPayload(
      projectOwner,
      projectSlug,
      { sqlQuery: JSON.stringify({ sql, size }) },
      undefined,
      variables,
      source,
      cachePolicy,
      undefined,
      ExecuteEngine.SMALL
    )
    setExecRequest((prev) => {
      if (!isEqual(prev, req)) {
        return req
      }
      return prev
    })
  }, [sql, size])

  const { data, fetching } = useApi(AnalyticService.ExecuteSQLAsync, execRequest, false, {
    shouldRetryOnError: false,
    onError: (error) => {
      console.error('error', error)
    }
  })

  const [resultData, setResultData] = useState<ExecutionInfo | undefined>()

  const queryRequest = useMemo(() => {
    if (data?.executionId) {
      return {
        projectOwner,
        projectSlug,
        executionId: data.executionId
      }
    }
  }, [data?.executionId, projectOwner, projectSlug])
  const queryRes = useApi(AnalyticService.QuerySQLResult, queryRequest, false, {
    shouldRetryOnError: true,
    revalidateIfStale: false,
    revalidateOnFocus: false,
    keepPreviousData: true,
    onSuccess: (data: QuerySQLResultResponse) => {
      if (
        data?.executionInfo?.status === ExecutionStatus.PENDING ||
        data?.executionInfo?.status === ExecutionStatus.RUNNING
      ) {
        setLoading(true)
        setTimeout(() => {
          queryRes.mutate()
        }, 1000)
        return
      } else {
        setLoading(false)
      }
    },
    onError: (error) => {
      console.error('error', error)
    }
  })

  useEffect(() => {
    if (queryRes.data?.executionInfo?.result) {
      setResultData(queryRes.data.executionInfo)
    }
  }, [queryRes.data])

  return {
    data: resultData,
    loading: fetching || queryRes.fetching || (!resultData && loading)
  }
}

// Custom param for Dayjs dates
const DateParam = {
  encode: (date: Dayjs | undefined | null) => date?.utc().format('YYYY-MM-DD') || undefined,
  decode: (value: string | (string | null)[] | null | undefined) =>
    typeof value === 'string' ? dayjs.utc(value, 'YYYY-MM-DD') : undefined
}

const sqlTemplate = template(`select startDate, endDate, fromTag, toTag, value
from \`fund_flow\`
where startDate = '<%- startDate %>' and endDate = '<%- endDate %>'
order by endDate desc, startDate asc, value desc`)

export default function FlowPage({ url, imageUrl }: { url: string; imageUrl: string }) {
  const router = useRouter()
  const { og } = router.query
  // const { project } = useProject()

  // Use query params for startDate, endDate, size, chainId, selectedPools, and showUnknown
  const [query, setQuery] = useQueryParams({
    startDate: DateParam,
    endDate: DateParam,
    size: NumberParam,
    chainId: StringParam,
    selectedPools: DelimitedArrayParam,
    showUnknown: BooleanParam
  })

  // Extract values with defaults
  const startDate = query.startDate || dayjs().utc().subtract(2, 'days').startOf('day')
  const endDate = query.endDate || dayjs().utc().subtract(1, 'days').endOf('day')
  const size = query.size || 100
  const chainId = (query.chainId as EthChainId) || EthChainId.ETHEREUM
  const selectedPools = (query.selectedPools || []).filter((pool): pool is string => pool !== null)
  const showUnknown = query.showUnknown || false

  // Initialize URL params if they don't exist (only on first load)
  useEffect(() => {
    if (!query.startDate || !query.endDate || !query.size || !query.chainId) {
      setQuery(
        {
          startDate: query.startDate || dayjs().utc().subtract(2, 'days').startOf('day'),
          endDate: query.endDate || dayjs().utc().subtract(1, 'days').endOf('day'),
          size: query.size || 100,
          chainId: query.chainId || EthChainId.ETHEREUM.toString(),
          selectedPools: query.selectedPools,
          showUnknown: query.showUnknown || false
        },
        'replaceIn'
      )
    }
  }, [query.startDate, query.endDate, query.size, query.chainId, query.selectedPools, query.showUnknown, setQuery]) // Initialize on mount if needed

  // Helper functions to update query params
  const setStartDate = (date: Dayjs | undefined) => {
    setQuery({ startDate: date }, 'replaceIn')
  }

  const setEndDate = (date: Dayjs | undefined) => {
    setQuery({ endDate: date }, 'replaceIn')
  }

  const setDataRange = (startDate: Dayjs | undefined, endDate: Dayjs | undefined) => {
    setQuery({ startDate, endDate }, 'replaceIn')
  }

  const setSize = (newSize: number) => {
    setQuery({ size: newSize }, 'replaceIn')
  }

  const setChainId = (newChainId: EthChainId) => {
    setQuery({ chainId: newChainId.toString() }, 'replaceIn')
  }

  const setSelectedPools = (newSelectedPools: string[]) => {
    setQuery({ selectedPools: newSelectedPools }, 'replaceIn')
  }

  const setShowUnknown = (newShowUnknown: boolean) => {
    setQuery({ showUnknown: newShowUnknown }, 'replaceIn')
  }
  const [poolOptions, setPoolOptions] = useState<string[]>([])
  const [data, setData] = useState<DataRow[]>([])
  const [showOutflow, setShowOutflow] = useState(true)
  const [showMobileMenu, setShowMobileMenu] = useState(false)

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (showMobileMenu) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [showMobileMenu])

  const sql = useMemo(() => {
    return sqlTemplate({ startDate: startDate?.format('YYYY-MM-DD'), endDate: endDate?.format('YYYY-MM-DD') })
  }, [startDate, endDate])

  const { data: panelData, loading: loadingPanelData } = useFundFlowSQLQuery(sql, size)

  useEffect(() => {
    setData(panelData?.result?.rows || [])
  }, [panelData?.result?.rows])

  const totalValuesBySource = useMemo(() => {
    const filteredData = data.filter((item) => item.fromTag !== 'unknown' || showUnknown)
    const totals = filteredData.reduce(
      (acc, item) => {
        acc[item.fromTag] = (acc[item.fromTag] || 0) + parseInt(item.value, 10)
        return acc
      },
      {} as Record<string, number>
    )

    const entries = Object.entries(totals)
    const maxValue = Math.max(...entries.map(([, value]) => value))

    return entries
      .sort(([, a], [, b]) => b - a)
      .map(([tag, value]) => ({
        tag,
        value,
        percentage: (value / maxValue) * 100
      }))
  }, [data, showUnknown])

  const totalValuesByTarget = useMemo(() => {
    const filteredData = data.filter((item) => item.toTag !== 'unknown' || showUnknown)
    const totals = filteredData.reduce(
      (acc, item) => {
        acc[item.toTag] = (acc[item.toTag] || 0) + parseInt(item.value, 10)
        return acc
      },
      {} as Record<string, number>
    )

    const entries = Object.entries(totals)
    const maxValue = Math.max(...entries.map(([, value]) => value))

    return entries
      .sort(([, a], [, b]) => b - a)
      .map(([tag, value]) => ({
        tag,
        value,
        percentage: (value / maxValue) * 100
      }))
  }, [data, showUnknown])

  useEffect(() => {
    const pools = new Set<string>()
    data.forEach((item) => {
      pools.add(item.fromTag)
      pools.add(item.toTag)
    })
    setPoolOptions((prev) => {
      const newPools = Array.from(pools).sort()
      if (isEqual(prev, newPools)) {
        return prev
      }
      return newPools
    })
  }, [data])

  const sankeyData = useMemo(() => {
    const nodes = new Set<string>()
    const links: { source: string; target: string; value: number }[] = []

    data.forEach((item) => {
      const { fromTag, toTag, value } = item
      if (
        (toTag !== 'unknown' || showUnknown) &&
        (fromTag !== 'unknown' || showUnknown) &&
        (selectedPools.length === 0 || selectedPools.includes(fromTag) || selectedPools.includes(toTag))
      ) {
        links.push({ source: fromTag, target: toTag, value: parseInt(value, 10) })
      }
    })

    const { fixedData: links2, logs } = fixCycles(links)
    links2.forEach((link) => {
      nodes.add(link.source)
      nodes.add(link.target)
    })

    // Create a map for sorting based on totalValuesBySource order
    const nodeOrderMap = new Map<string, number>()
    totalValuesBySource.forEach((item, index) => {
      nodeOrderMap.set(item.tag, index)
    })

    // Sort nodes based on totalValuesBySource order
    const sortedNodes = Array.from(nodes).sort((a, b) => {
      const orderA = nodeOrderMap.has(a) ? nodeOrderMap.get(a)! : Number.MAX_SAFE_INTEGER
      const orderB = nodeOrderMap.has(b) ? nodeOrderMap.get(b)! : Number.MAX_SAFE_INTEGER
      return orderA - orderB
    })

    return {
      nodes: sortedNodes.map((name) => ({ name })),
      links: links2.sort((a, b) => b.value - a.value),
      rawLinks: links,
      logs
    }
  }, [showUnknown, selectedPools, data, totalValuesBySource])

  const [topN, setTopN] = useState<number>(20)
  const [sortField, setSortField] = useState<'inflow' | 'outflow' | 'netflow'>('inflow')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  const flowStats = useMemo(() => {
    const statsMap = new Map<string, { inflow: number; outflow: number }>()

    // Calculate inflow and outflow for each node
    sankeyData.rawLinks.forEach((link) => {
      // Outflow from source
      if (!statsMap.has(link.source)) {
        statsMap.set(link.source, { inflow: 0, outflow: 0 })
      }
      statsMap.get(link.source)!.outflow += link.value

      // Inflow to target
      if (!statsMap.has(link.target)) {
        statsMap.set(link.target, { inflow: 0, outflow: 0 })
      }
      statsMap.get(link.target)!.inflow += link.value
    })

    // Convert to array and calculate netflow
    const stats: FlowStats[] = Array.from(statsMap.entries()).map(([node, { inflow, outflow }]) => ({
      node,
      inflow,
      outflow,
      netflow: inflow - outflow
    }))

    return stats
  }, [sankeyData.rawLinks])

  const inflowStats = useMemo(() => {
    return sortBy(flowStats, 'inflow').reverse().slice(0, topN).reverse()
  }, [flowStats, topN])

  const outflowStats = useMemo(() => {
    return sortBy(flowStats, 'outflow').reverse().slice(0, topN).reverse()
  }, [flowStats, topN])

  const netflowStats = useMemo(() => {
    return sortBy(flowStats, (item) => Math.abs(item.netflow))
      .reverse()
      .slice(0, topN)
      .reverse()
  }, [flowStats, topN])

  const sortedTableData = useMemo(() => {
    const sorted = sortBy(flowStats, sortField)
    return sortOrder === 'desc' ? sorted.reverse() : sorted
  }, [flowStats, sortField, sortOrder])

  const isDarkMode = useDarkMode()

  useEffect(() => {
    // Force dark mode for OG
    if (og && typeof window !== 'undefined') {
      setTimeout(() => {
        document.body.classList.remove('light')
        document.body.classList.add('dark')
      }, 500)
    }
  }, [og])

  // Check if data is empty (API returned but no data)
  const hasNoData = !(loadingPanelData || !sql) && panelData && data.length === 0

  return (
    <div className={classNames('flex h-screen flex-col overflow-hidden', roboto.className)}>
      <Head>
        <title>Fund Flow Analysis - Sentio</title>
      </Head>
      <OpenGraphTags
        name="Fund Flow Analysis - Sentio"
        description="Visualize and analyze fund flows between different protocols and addresses with an interactive Sankey diagram. Track value movements, filter by protocols, detect cycles, and get detailed statistics."
        imageUrl={imageUrl}
        url={url}
      />
      <main className="relative flex-1 overflow-auto text-sm">
        <div className={classNames('absolute left-0 right-0 top-0 z-[-1] h-[168px]', og ? 'hidden' : '')}>
          <img
            src={isDarkMode ? '/flow_bg_dark.svg' : '/flow_bg.svg'}
            className="h-full w-full object-cover [mask-image:linear-gradient(to_bottom,black_0%,black_60%,transparent_100%)]"
            alt="Flow Background"
          />
        </div>
        <div className={classNames('mx-4 mt-4 flex justify-between', og ? 'hidden' : '')}>
          <div className="text-icontent font-medium text-gray-500">
            <img
              alt="Sentio Flow"
              src={isDarkMode ? '/flow-dark.png' : '/flow.png'}
              className="ml-1 inline-block h-[36px] w-auto align-text-bottom"
            />
          </div>
          <div className={classNames('flex items-center gap-2')}>
            <PopupMenuButton
              onSelect={(item) => {
                setChainId(item as EthChainId)
              }}
              selectedKey={chainId}
              width={180}
              items={[
                [
                  {
                    label: 'Ethereum',
                    key: EthChainId.ETHEREUM,
                    icon: <ChainIcon chainId={EthChainId.ETHEREUM} className="mr-2 h-5 w-5" />
                  }
                  // {
                  //   label: 'Blast',
                  //   key: EthChainId.BLAST,
                  //   icon: <ChainIcon chainId={EthChainId.BLAST} className="h-5 w-5 mr-2" />
                  // }
                ]
              ]}
              buttonIcon={
                <div className="flex items-center gap-1" title="Select Chain">
                  <ChainIcon chainId={chainId} className="inline-block h-5 w-5 align-text-bottom" />
                  <ChevronDownIcon className="h-4 w-4" />
                </div>
              }
            />
            <div className="flex items-center gap-4">
              <ThemeSwitch size="md" />
              <AppSwitcher
                currentApp="flow"
                onAppChange={(app: string) => {
                  if (app === 'flow') {
                    return
                  }
                  const isTestEnv = location.host.includes('test')
                  if (isTestEnv) {
                    window.location.href = `https://sui-test.sentio.xyz`
                  } else {
                    window.location.href = `https://dash.sentio.xyz`
                  }
                }}
              />
              <SharePanel
                startDate={startDate}
                endDate={endDate}
                selectedPools={selectedPools}
                showOutflow={showOutflow}
                totalValuesBySource={totalValuesBySource}
                totalValuesByTarget={totalValuesByTarget}
              />
            </div>
          </div>
        </div>
        <div className={classNames('absolute bottom-0 left-0 right-0', og ? 'top-0' : 'top-16 lg:top-20')}>
          <Tabs
            linkToHash
            defaultSelectedTab={0}
            containerClassName="h-full"
            // labelClassNames="!px-4"
            tabClassNames={og ? '!hidden' : ''}
            noSeparator
            type="pill"
          >
            <Tab
              value="flow"
              label="Fund Flow"
              className={classNames('relative h-full overflow-hidden', og ? '' : 'lg:pr-[360px]')}
              icon={<LuRoute className="mr-2 inline-block h-4 w-4 align-text-bottom" />}
            >
              <SankeyChart sankeyData={sankeyData} loading={loadingPanelData || !sql} />
              {og && (
                <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
                  <img src={isDarkMode ? '/logo-dark.png' : '/logo.png'} className="h-24 opacity-20" alt="Sentio" />
                </div>
              )}
            </Tab>
            <Tab
              value="analysis"
              label="Analysis"
              className="overflow-hidden"
              icon={<LuListTodo className="mr-2 inline-block h-4 w-4 align-text-bottom" />}
            >
              <AnalysisCharts
                inflowStats={inflowStats}
                outflowStats={outflowStats}
                netflowStats={netflowStats}
                sortedTableData={sortedTableData}
                topN={topN}
                sortField={sortField}
                sortOrder={sortOrder}
                onTopNChange={setTopN}
                onSortFieldChange={setSortField}
                onSortOrderChange={setSortOrder}
              />
            </Tab>
          </Tabs>
        </div>
        {hasNoData && (
          <div className={classNames('absolute bottom-0 left-0 right-0 p-4', og ? 'top-0' : 'lg:top-30 top-12')}>
            <div className="bg-sentio-gray-100 dark:bg-sentio-gray-300 flex h-full w-full items-center justify-center rounded-md">
              <div className="max-w-md px-6 text-center">
                <div className="mb-6">
                  <LuInbox className="mx-auto h-24 w-24 text-gray-400 dark:text-gray-500" />
                </div>
                <h3 className="mb-3 text-xl font-semibold text-gray-900 dark:text-white">No Fund Flow Data</h3>
                <p className="mb-6 text-base leading-relaxed text-gray-500 dark:text-gray-400">
                  No fund flow data found for the selected time period. This could be because:
                </p>
                <div className="space-y-3 text-left">
                  <div className="flex items-start gap-3 text-sm text-gray-600 dark:text-gray-300">
                    <LuCalendar className="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" />
                    <span>The selected date range exceeds 7 days, or has no transactions</span>
                  </div>
                  <div className="flex items-start gap-3 text-sm text-gray-600 dark:text-gray-300">
                    <LuFilter className="mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" />
                    <span>Current filters are too restrictive</span>
                  </div>
                </div>
                <div className="mt-6 text-xs text-gray-400 dark:text-gray-500">
                  Try adjusting your date range or removing some filters to see more data.
                </div>
              </div>
            </div>
          </div>
        )}

        {og ? null : (
          <>
            {/* Desktop Panel */}
            <div className="fixed right-4 top-20 hidden lg:flex">
              <div className="border-border-color dark:bg-sentio-gray-100 scrollbar-thin max-h-[calc(100vh-100px)] overflow-auto rounded-xl border bg-white px-2 shadow-sm">
                <FlowControlPanel
                  startDate={startDate}
                  endDate={endDate}
                  size={size}
                  showUnknown={showUnknown}
                  selectedPools={selectedPools}
                  poolOptions={poolOptions}
                  showOutflow={showOutflow}
                  totalValuesBySource={totalValuesBySource}
                  totalValuesByTarget={totalValuesByTarget}
                  cycleDetectionLogs={sankeyData.logs}
                  onStartDateChange={setStartDate}
                  onEndDateChange={setEndDate}
                  onSizeChange={setSize}
                  onShowUnknownChange={setShowUnknown}
                  onSelectedPoolsChange={setSelectedPools}
                  onShowOutflowChange={setShowOutflow}
                  onSetDataRange={setDataRange}
                />
              </div>
            </div>
          </>
        )}

        {/* Mobile Control Button */}
        {!og && (
          <div className="fixed bottom-4 right-4 z-40 lg:hidden">
            <button
              onClick={() => setShowMobileMenu(true)}
              className="bg-primary-600 hover:bg-primary-700 relative rounded-full p-3 text-white shadow-lg transition-colors duration-200"
            >
              <HiOutlineAdjustmentsHorizontal className="h-6 w-6" />
              {(selectedPools.length > 0 || showUnknown) && (
                <div className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white">
                  {selectedPools.length || (showUnknown ? '!' : '')}
                </div>
              )}
            </button>
          </div>
        )}

        {/* Mobile Full Screen Menu */}
        {showMobileMenu && (
          <div className="animate-in slide-in-from-bottom dark:bg-sentio-gray-200 fixed inset-0 z-50 bg-white duration-300 lg:hidden">
            <div className="flex h-full flex-col">
              {/* Control Panel Content */}
              <div className="flex-1 overflow-auto">
                <FlowControlPanel
                  startDate={startDate}
                  endDate={endDate}
                  size={size}
                  showUnknown={showUnknown}
                  selectedPools={selectedPools}
                  poolOptions={poolOptions}
                  showOutflow={showOutflow}
                  totalValuesBySource={totalValuesBySource}
                  totalValuesByTarget={totalValuesByTarget}
                  cycleDetectionLogs={sankeyData.logs}
                  onStartDateChange={setStartDate}
                  onEndDateChange={setEndDate}
                  onSizeChange={setSize}
                  onShowUnknownChange={setShowUnknown}
                  onSelectedPoolsChange={setSelectedPools}
                  onShowOutflowChange={setShowOutflow}
                  onSetDataRange={setDataRange}
                />
              </div>

              {/* Footer */}
              <div className="border-t border-gray-200 p-4">
                <button
                  onClick={() => setShowMobileMenu(false)}
                  className="bg-primary-600 hover:bg-primary-700 w-full rounded-md py-3 font-medium text-white transition-colors duration-200"
                >
                  Apply Changes
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
