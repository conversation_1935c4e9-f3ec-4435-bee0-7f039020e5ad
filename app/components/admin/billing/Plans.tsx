import { CrudTable } from '../../common/table/CrudTable'
import { PlanDetail } from './PlanDetails'
import { ConfirmDialog } from '../../common/dialogs/ConfirmDialog'
import { useAdminPlans } from '../../../lib/data/use-admin'
import { Plan } from '../../../gen/service/billing/protos/service.pb'
import { NotificationContext } from '../../../lib/data/use-notification'
import { useContext } from 'react'
import { money } from '../../../lib/money'
import BigDecimal from '@sentio/bigdecimal'

const columns = [
  {
    name: 'ID',
    getValue: (plan: Plan) => plan.id
  },
  {
    name: 'Name',
    getValue: (plan: Plan) => plan.name + (plan.trialDurationDays ? ` [${plan.trialDurationDays} days]` : '')
  },
  {
    name: 'Description',
    getValue: (plan: Plan) => plan.description
  },
  {
    name: 'Tier',
    getValue: (plan: Plan) => plan.tierDefaultPlan
  },
  {
    name: 'Flat Fee',
    getValue: (plan: Plan) => {
      if (plan.flatFee) {
        return `$${BigDecimal(money(plan.flatFee).toNumber()).toFormat(0)}`
      }
      return undefined
    }
  },
  {
    name: 'Annual Pay / 12',
    getValue: (plan: Plan) => {
      if (plan.flatFee) {
        const v = BigDecimal(money(plan.flatFee).toNumber()).multipliedBy(plan.yearlyDiscount || 0.7)
        return `$${v.toFormat(0)}`
      }
      return undefined
    }
  },
  {
    name: 'Unit Cap',
    getValue: (plan: Plan) => BigDecimal(plan.unitCap ?? 0).toFormat()
  },
  {
    name: 'Unit Price',
    getValue: (plan: Plan) => (plan.unitPrice ? money(plan.unitPrice).format(9) : undefined)
  }
]

export function Plans() {
  const { plans, loading, savePlan, deletePlan } = useAdminPlans()
  const notification = useContext(NotificationContext)

  function NewPlan() {
    return {
      flatFee: money(59),
      unitPrice: money(0.0001),
      unitCap: '100000'
    } as Plan
  }

  function onSubmitPlan(p: Plan, setEditing) {
    savePlan(p).then(() => {
      notification.showNotification(
        {
          type: 'success',
          title: 'Plan saved',
          message: 'Plan saved successfully'
        },
        3
      )
      setEditing(undefined)
    })
  }

  return (
    <CrudTable columns={columns} data={plans} loading={loading} onNew={NewPlan}>
      {({ deleting, editing, setDeleting, setEditing }) => (
        <>
          <ConfirmDialog
            onClose={() => setDeleting(undefined)}
            onConfirm={() => {
              if (deleting?.id) {
                deletePlan(deleting.id).then(() => {
                  notification.showNotification(
                    {
                      type: 'success',
                      title: 'Plan deleted',
                      message: 'Plan deleted successfully'
                    },
                    3000
                  )
                })
              }
            }}
            title={'Are you sure?'}
            type={'danger'}
            open={deleting != null}
          />
          <PlanDetail
            onClose={() => setEditing(undefined)}
            plan={editing}
            onSubmit={(plan) => onSubmitPlan(plan, setEditing)}
          />
        </>
      )}
    </CrudTable>
  )
}
