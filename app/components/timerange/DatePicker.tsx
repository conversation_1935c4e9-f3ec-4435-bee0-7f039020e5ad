import dayjs from 'dayjs'
import { CalendarIcon, ChevronDownIcon } from '@heroicons/react/20/solid'
import { PopoverButton } from '../common/popper/PopoverButton'
import Calendar from './Calendar'
import classnames from 'lib/classnames'

interface Props {
  value?: dayjs.Dayjs
  onChange?: (value: dayjs.Dayjs) => void
  start?: dayjs.Dayjs
  end?: dayjs.Dayjs
  minDate?: dayjs.Dayjs
  maxDate?: dayjs.Dayjs
  className?: string
  placeholder?: string
  disabled?: boolean
  format?: string
}

export const DatePicker = ({
  value,
  onChange,
  start,
  end,
  minDate,
  maxDate,
  className,
  placeholder = 'Select date',
  disabled = false,
  format = 'YYYY-MM-DD'
}: Props) => {
  const displayValue = value ? value.utc().format(format) : ''
  const enableRangeLimit = !!(start && end)

  return (
    <PopoverButton
      content={({ close }) => {
        const handleDateSelect = (date: dayjs.Dayjs) => {
          onChange?.(date)
          close()
        }
        return (
          <div className="p-4">
            <Calendar
              value={value || dayjs().utc()}
              onSelect={handleDateSelect}
              start={start}
              end={end}
              minDate={minDate}
              maxDate={maxDate}
              enableRangeLimit={enableRangeLimit}
              className="w-full"
              useUTC
            />
          </div>
        )
      }}
      className={classnames(
        'text-icontent flex w-full items-center justify-between rounded-md border border-gray-300 px-3 py-2 shadow-sm',
        'dark:bg-sentio-gray-200 bg-white dark:border-gray-200',
        'focus:ring-primary-500 focus:border-primary-500 focus:outline-none focus:ring-1',
        'hover:border-gray-400 dark:hover:border-gray-500',
        disabled && 'cursor-not-allowed opacity-50',
        !disabled && 'cursor-pointer',
        className
      )}
      contentClassName="bg-white dark:bg-sentio-gray-200 border border-gray-200 rounded-lg shadow-lg"
      portal={true}
      placement="bottom-start"
    >
      <div className="flex min-w-0 flex-1 items-center space-x-2">
        <CalendarIcon className="h-4 w-4 flex-shrink-0 text-gray-400" />
        <span className={classnames('truncate', displayValue ? 'text-gray-900' : 'text-gray-500')}>
          {displayValue || placeholder}
        </span>
      </div>
      {!disabled && <ChevronDownIcon className="h-4 w-4 flex-shrink-0 text-gray-400" />}
    </PopoverButton>
  )
}
