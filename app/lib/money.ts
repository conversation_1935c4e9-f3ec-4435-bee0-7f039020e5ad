import getSymbolFromCurrency from 'currency-symbol-map'
import { NumberFormat } from './number'
import BigDecimal from '@sentio/bigdecimal'

interface MoneyLike {
  currency?: string
  units?: string | number | null
  nanos?: string | number | null
}

const MAX_NANO = BigDecimal('1000000000')

const NF = NumberFormat({
  style: 'decimal'
})

export class Money {
  private readonly currency: string
  private readonly value: BigDecimal // Store total value directly

  constructor(obj: MoneyLike | number | string) {
    if (typeof obj === 'number') {
      this.currency = 'usd'
      // Handle special values (Infinity, -Infinity, NaN)
      if (!isFinite(obj) || isNaN(obj)) {
        this.value = BigDecimal('0')
        return
      }
      // Direct BigDecimal storage with nanosecond precision rounding
      const bd = BigDecimal(obj.toString())
      // Round to nanosecond precision (9 decimal places) but keep the value as-is
      this.value = bd.decimalPlaces(9, BigDecimal.ROUND_HALF_UP)
      return
    }
    if (typeof obj === 'string') {
      try {
        obj = parse(obj)
      } catch (e) {
        this.currency = 'usd'
        this.value = BigDecimal('0')
        return
      }
    }

    // Handle MoneyLike object with proper error checking
    if (!obj || typeof obj !== 'object') {
      this.currency = 'usd'
      this.value = BigDecimal('0')
      return
    }

    this.currency = obj.currency || 'usd'

    // Safely handle potentially undefined units and nanos
    const units = obj.units !== undefined && obj.units !== null ? BigDecimal(obj.units.toString()) : BigDecimal('0')
    const nanos = obj.nanos !== undefined && obj.nanos !== null ? BigDecimal(obj.nanos.toString()) : BigDecimal('0')

    this.value = units.plus(nanos.dividedBy(MAX_NANO))
  }

  // Helper method to create Money directly from BigDecimal value
  private static fromValue(value: BigDecimal, currency = 'usd'): Money {
    // Apply nanosecond precision rounding
    const roundedValue = value.decimalPlaces(9, BigDecimal.ROUND_HALF_UP)
    // Create a MoneyLike object and use constructor
    const totalNanos = roundedValue.multipliedBy(MAX_NANO).integerValue()
    const units = totalNanos.dividedToIntegerBy(MAX_NANO)
    const remainingNanos = totalNanos.mod(MAX_NANO)
    return new Money({
      currency,
      units: units.toString(),
      nanos: remainingNanos.toString()
    })
  }

  public format(fractionDigits = 2, convertToMillion?: boolean): string {
    const currencySymbol = getSymbolFromCurrency((this.currency || 'usd').toUpperCase()) || this.currency

    // Work directly with the stored value
    const isNegative = this.value.isNegative()
    const absValue = this.value.abs()

    // Extract units and fractional parts for formatting
    // Use ROUND_DOWN to get proper truncation instead of rounding
    const units = absValue.integerValue(BigDecimal.ROUND_DOWN)
    const fractional = absValue.minus(units)

    const formattedUnits = NF.format(BigInt(units.toString()))

    // Convert fractional part to nanos and format properly
    const nanos = fractional.multipliedBy(MAX_NANO).integerValue(BigDecimal.ROUND_DOWN)
    let fractionalStr = nanos.toString().padStart(9, '0')

    // Truncate to desired digits
    fractionalStr = fractionalStr.slice(0, fractionDigits)
    if (fractionalStr.length < fractionDigits) {
      fractionalStr = fractionalStr.padEnd(fractionDigits, '0')
    }

    const sign = isNegative ? '-' : ''

    if (convertToMillion) {
      const totalValue = this.value.toNumber()
      if (Math.abs(totalValue) > 0) {
        return `${currencySymbol}${sign}${Math.abs(totalValue * 1000000)
          .toFixed(fractionDigits)
          .replace(/\.?0+$/, '')}/M SU`
      }
    }

    return `${currencySymbol}${sign}${formattedUnits}.${fractionalStr}`
  }

  public symbol(): string {
    return getSymbolFromCurrency((this.currency || 'usd').toUpperCase()) || this.currency
  }

  public toString(convertToMillion?: boolean): string {
    return this.format(undefined, convertToMillion)
  }

  public toJSON(): MoneyLike {
    // Calculate units and nanos only when needed
    const totalNanos = this.value.multipliedBy(MAX_NANO)
    const units = totalNanos.dividedToIntegerBy(MAX_NANO)
    const nanos = totalNanos.mod(MAX_NANO)

    return {
      currency: this.currency,
      units: units.toString(),
      nanos: nanos.toString()
    }
  }

  public multiply(factor: number): Money {
    // Direct multiplication on the stored value
    const result = this.value.multipliedBy(BigDecimal(factor.toString()))
    return Money.fromValue(result, this.currency)
  }

  public add(other: Money): Money {
    if (this.currency !== other.currency) {
      throw new Error('Cannot add money with different currencies')
    }

    // Direct addition on stored values
    const result = this.value.plus(other.value)
    return Money.fromValue(result, this.currency)
  }

  public subtract(other: Money): Money {
    if (this.currency !== other.currency) {
      throw new Error('Cannot subtract money with different currencies')
    }

    // Direct subtraction on stored values
    const result = this.value.minus(other.value)
    return Money.fromValue(result, this.currency)
  }

  public divide(divisor: number): Money {
    if (divisor === 0) {
      throw new Error('Cannot divide by zero')
    }

    // Direct division on stored value
    const result = this.value.dividedBy(BigDecimal(divisor.toString()))
    return Money.fromValue(result, this.currency)
  }

  public toNumber(): number {
    return this.value.toNumber()
  }

  public eq(number: number | MoneyLike): boolean {
    const n = new Money(number)
    return this.value.eq(n.value)
  }

  public gte(number: number | MoneyLike): boolean {
    return this.eq(number) || this.gt(number)
  }

  public lte(number: number | MoneyLike): boolean {
    return this.eq(number) || this.lt(number)
  }

  public lt(number: number | MoneyLike): boolean {
    const n = new Money(number)
    return this.value.lt(n.value)
  }

  public gt(number: number | MoneyLike): boolean {
    const n = new Money(number)
    return this.value.gt(n.value)
  }

  isZero() {
    return this.value.isZero()
  }

  public compareTo(n: number | MoneyLike): number {
    if (this.eq(n)) {
      return 0
    }
    if (this.lt(n)) {
      return -1
    }
    return 1
  }
}

export const Zero: Money = new Money({
  currency: 'usd',
  units: 0,
  nanos: 0
})

export function money(n: number | MoneyLike | string | undefined | null): Money {
  if (n === undefined || n === null) {
    return Zero
  }

  try {
    return new Money(n)
  } catch (error) {
    console.warn('Failed to create Money object, returning Zero:', error)
    return Zero
  }
}

export function parse(m: string): MoneyLike {
  if (!m || typeof m !== 'string') {
    throw new Error('Invalid money string: input is not a string')
  }

  const regexPattern = /(\D*?)([-+]?)(\d+)(?:\D+(\d*))?/
  const match = m.match(regexPattern)

  if (match) {
    const symbol = match[1] || ''
    const sign = match[2] === '-' ? '-' : ''
    const units = match[3] || '0'
    const nanos = match[4] || '0'
    return {
      currency: symbol || 'usd',
      units: sign + units,
      nanos: sign + nanos.padEnd(9, '0')
    }
  } else {
    throw new Error(`Invalid money string: "${m}"`)
  }
}
