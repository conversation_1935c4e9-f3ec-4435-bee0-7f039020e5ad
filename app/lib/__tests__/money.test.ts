import { money, Money, Zero, parse } from '../money'
import { describe, it } from 'node:test'
import assert from 'node:assert'

describe('Money Library', () => {
  describe('Constructor', () => {
    it('should create money from number', () => {
      const m = money(10.5)
      assert.strictEqual(m.toNumber(), 10.5)
    })

    it('should create money from string', () => {
      const m = money('15.25')
      assert.strictEqual(m.toNumber(), 15.25)
    })

    it('should create money from MoneyLike object', () => {
      const m = money({ currency: 'usd', units: 10, nanos: 500000000 })
      assert.strictEqual(m.toNumber(), 10.5)
    })

    it('should handle undefined input', () => {
      const m = money(undefined)
      assert.strictEqual(m.toNumber(), 0)
      assert.strictEqual(m, Zero)
    })

    it('should handle zero', () => {
      const m = money(0)
      assert.strictEqual(m.toNumber(), 0)
      assert.strictEqual(m.isZero(), true)
    })

    it('should handle negative numbers', () => {
      const m = money(-5.75)
      assert.strictEqual(m.toNumber(), -5.75)
    })
  })

  describe('Precision Issues', () => {
    it('should handle very small numbers correctly', () => {
      const m = money(3e-8)
      assert.strictEqual(m.toNumber(), 3e-8)
      assert.notStrictEqual(m.toNumber(), 2.9e-8) // This was the bug

      // Test precision limits: values smaller than 1e-9 (1 nanosecond) are rounded to 0
      assert.strictEqual(money(3e-10).toNumber(), 0) // 3e-10 is smaller than 1 nanosecond precision
      assert.strictEqual(money(1e-9).toNumber(), 1e-9) // 1 nanosecond is the smallest representable value
    })

    it('should handle very large numbers', () => {
      const m = money(1e15)
      assert.strictEqual(m.toNumber(), 1e15)
    })

    it('should handle floating point precision issues', () => {
      const m = money(0.1 + 0.2) // JavaScript: 0.30000000000000004
      const diff = Math.abs(m.toNumber() - 0.3)
      assert.ok(diff < 1e-10)
    })

    it('should handle repeating decimals', () => {
      const m = money(1 / 3)
      // BigDecimal truncates precision, which is normal behavior
      const actualValue = m.toNumber()
      assert.ok(Math.abs(actualValue - 1 / 3) < 0.001, `Expected close to ${1 / 3}, got ${actualValue}`)
    })

    it('should handle scientific notation', () => {
      const m1 = money(1.23e-6)
      assert.strictEqual(m1.toNumber(), 1.23e-6)

      const m2 = money(4.56e12)
      assert.strictEqual(m2.toNumber(), 4.56e12)
    })
  })

  describe('Arithmetic Operations', () => {
    it('should add two money objects', () => {
      const m1 = money(10.5)
      const m2 = money(5.25)
      const result = m1.add(m2)
      assert.strictEqual(result.toNumber(), 15.75)
    })

    it('should subtract two money objects', () => {
      const m1 = money(10.5)
      const m2 = money(5.25)
      const result = m1.subtract(m2)
      assert.strictEqual(result.toNumber(), 5.25)
    })

    it('should multiply money by number', () => {
      const m = money(10.5)
      const result = m.multiply(2)
      assert.strictEqual(result.toNumber(), 21)
    })

    it('should divide money by number', () => {
      const m = money(21)
      const result = m.divide(2)
      assert.strictEqual(result.toNumber(), 10.5)
    })

    it('should handle division by zero', () => {
      const m = money(10)
      assert.throws(() => m.divide(0), /Cannot divide by zero/)
    })

    it('should handle negative arithmetic', () => {
      const m1 = money(-10.5)
      const m2 = money(5.25)
      assert.strictEqual(m1.add(m2).toNumber(), -5.25)
      assert.strictEqual(m1.subtract(m2).toNumber(), -15.75)
      assert.strictEqual(m1.multiply(-2).toNumber(), 21)
    })

    it('should handle overflow in nanos', () => {
      const m1 = money({ currency: 'usd', units: 0, nanos: 900000000 })
      const m2 = money({ currency: 'usd', units: 0, nanos: 500000000 })
      const result = m1.add(m2)
      assert.strictEqual(result.toNumber(), 1.4)
    })
  })

  describe('Comparison Operations', () => {
    it('should compare equality', () => {
      const m1 = money(10.5)
      const m2 = money(10.5)
      const m3 = money(10.6)
      assert.strictEqual(m1.eq(m2.toJSON()), true)
      assert.strictEqual(m1.eq(m3.toJSON()), false)
      assert.strictEqual(m1.eq(10.5), true)
    })

    it('should compare greater than', () => {
      const m1 = money(10.6)
      const m2 = money(10.5)
      assert.strictEqual(m1.gt(m2.toJSON()), true)
      assert.strictEqual(m2.gt(m1.toJSON()), false)
      assert.strictEqual(m1.gt(10.5), true)
    })

    it('should compare less than', () => {
      const m1 = money(10.4)
      const m2 = money(10.5)
      assert.strictEqual(m1.lt(m2.toJSON()), true)
      assert.strictEqual(m2.lt(m1.toJSON()), false)
      assert.strictEqual(m1.lt(10.5), true)
    })

    it('should compare greater than or equal', () => {
      const m1 = money(10.5)
      const m2 = money(10.5)
      const m3 = money(10.4)
      assert.strictEqual(m1.gte(m2.toJSON()), true)
      assert.strictEqual(m1.gte(m3.toJSON()), true)
      assert.strictEqual(m3.gte(m1.toJSON()), false)
    })

    it('should compare less than or equal', () => {
      const m1 = money(10.5)
      const m2 = money(10.5)
      const m3 = money(10.6)
      assert.strictEqual(m1.lte(m2.toJSON()), true)
      assert.strictEqual(m1.lte(m3.toJSON()), true)
      assert.strictEqual(m3.lte(m1.toJSON()), false)
    })

    it('should handle compareTo method', () => {
      const m1 = money(10.5)
      const m2 = money(10.5)
      const m3 = money(10.6)
      const m4 = money(10.4)

      assert.strictEqual(m1.compareTo(m2.toJSON()), 0)
      assert.strictEqual(m1.compareTo(m4.toJSON()), 1)
      assert.strictEqual(m1.compareTo(m3.toJSON()), -1)
    })
  })

  describe('Formatting', () => {
    it('should format with default precision', () => {
      const m = money(10.5)
      const formatted = m.format()
      assert.ok(formatted.includes('10.50'), `Expected format to include '10.50', got: ${formatted}`)
    })

    it('should format with custom precision', () => {
      const m = money(10.12345)
      assert.ok(m.format(4).includes('10.1234'))
    })

    it('should format negative numbers', () => {
      const m = money(-10.5)
      const formatted = m.format()
      assert.ok(formatted.includes('-'), `Expected negative sign, got: ${formatted}`)
      assert.ok(formatted.includes('10.50'), `Expected '10.50', got: ${formatted}`)
    })

    it('should format with currency symbol', () => {
      const m = money(10.5)
      assert.ok(m.format().startsWith('$'))
    })

    it('should convert to million format', () => {
      const m = money(0.000001)
      const formatted = m.format(2, true)
      assert.ok(formatted.includes('/M SU'))
    })

    it('should handle toString method', () => {
      const m = money(10.5)
      const str = m.toString()
      assert.ok(str.includes('10.50'), `Expected '10.50' in toString(), got: ${str}`)
    })

    it('should return currency symbol', () => {
      const m = money(10.5)
      assert.strictEqual(m.symbol(), '$')
    })
  })

  describe('Edge Cases', () => {
    it('should handle maximum safe integer', () => {
      const m = money(Number.MAX_SAFE_INTEGER)
      assert.strictEqual(m.toNumber(), Number.MAX_SAFE_INTEGER)
    })

    it('should handle minimum safe integer', () => {
      const m = money(Number.MIN_SAFE_INTEGER)
      assert.strictEqual(m.toNumber(), Number.MIN_SAFE_INTEGER)
    })

    it('should handle very small positive numbers', () => {
      const m = money(Number.MIN_VALUE)
      // Number.MIN_VALUE is extremely small and may be rounded to 0
      assert.ok(m.toNumber() >= 0, `Expected >= 0, got: ${m.toNumber()}`)
    })

    it('should handle infinity', () => {
      const m = money(Infinity)
      // Infinity is converted to 0 to avoid errors
      assert.strictEqual(m.toNumber(), 0)
    })

    it('should handle negative infinity', () => {
      const m = money(-Infinity)
      // -Infinity is converted to 0 to avoid errors
      assert.strictEqual(m.toNumber(), 0)
    })

    it('should handle NaN input gracefully', () => {
      const m = money(NaN)
      assert.strictEqual(m.isZero(), true) // Should default to zero for invalid input
    })

    it('should handle empty string', () => {
      assert.doesNotThrow(() => money(''))
    })

    it('should handle whitespace string', () => {
      assert.doesNotThrow(() => money('   '))
    })
  })

  describe('JSON Serialization', () => {
    it('should serialize to JSON', () => {
      const m = money(10.5)
      const json = m.toJSON()
      assert.ok('currency' in json)
      assert.ok('units' in json)
      assert.ok('nanos' in json)
      assert.strictEqual(json.currency, 'usd')
    })

    it('should handle round-trip serialization', () => {
      const original = money(10.12345)
      const json = original.toJSON()
      const restored = money(json)
      const diff = Math.abs(restored.toNumber() - original.toNumber())
      assert.ok(diff < 1e-9)
    })
  })

  describe('Currency Support', () => {
    it('should handle different currencies', () => {
      const m = money({ currency: 'eur', units: 10, nanos: 500000000 })
      assert.strictEqual(m.toNumber(), 10.5)
    })

    it('should maintain currency in operations', () => {
      const m1 = money({ currency: 'eur', units: 10, nanos: 0 })
      const m2 = money({ currency: 'eur', units: 5, nanos: 0 })
      const result = m1.add(m2)
      assert.strictEqual(result.toJSON().currency, 'eur')
    })
  })

  describe('Parser Function', () => {
    it('should parse currency strings', () => {
      const parsed = parse('$10.50')
      assert.strictEqual(parsed.currency, '$')
      assert.strictEqual(parsed.units, '10')
      assert.strictEqual(parsed.nanos, '500000000')
    })

    it('should parse negative currency strings', () => {
      const parsed = parse('$-10.50')
      assert.strictEqual(parsed.currency, '$')
      assert.strictEqual(parsed.units, '-10')
      assert.strictEqual(parsed.nanos, '-500000000')
    })

    it('should handle invalid currency strings', () => {
      assert.throws(() => parse('invalid'), /Invalid money string/)
    })

    it('should parse without decimal part', () => {
      const parsed = parse('$10')
      assert.strictEqual(parsed.units, '10')
      assert.strictEqual(parsed.nanos, '000000000')
    })
  })

  describe('Zero Constant', () => {
    it('should have Zero constant', () => {
      assert.strictEqual(Zero.isZero(), true)
      assert.strictEqual(Zero.toNumber(), 0)
    })

    it('should return Zero for undefined input', () => {
      const m = money(undefined)
      assert.strictEqual(m, Zero)
    })
  })

  describe('Chain Operations', () => {
    it('should handle chained operations', () => {
      const result = money(10).add(money(5)).multiply(2).subtract(money(10)).divide(2)

      assert.strictEqual(result.toNumber(), 10)
    })

    it('should handle complex calculations', () => {
      const principal = money(1000)
      const interest = principal.multiply(0.05)
      const total = principal.add(interest)

      assert.strictEqual(total.toNumber(), 1050)
    })
  })

  describe('Performance Edge Cases', () => {
    it('should handle large number of operations', () => {
      let result = money(1)
      for (let i = 0; i < 1000; i++) {
        result = result.add(money(0.01))
      }
      const diff = Math.abs(result.toNumber() - 11)
      assert.ok(diff < 0.01)
    })

    it('should handle repeated divisions', () => {
      let result = money(1024)
      for (let i = 0; i < 10; i++) {
        result = result.divide(2)
      }
      assert.strictEqual(result.toNumber(), 1)
    })
  })

  describe('Specific Bug Tests', () => {
    it('should fix the 3e-8 precision issue', () => {
      const m = money(3e-8)
      assert.strictEqual(m.toNumber(), 3e-8, 'Should return exactly 3e-8')
      assert.notStrictEqual(m.toNumber(), 2.9e-8, 'Should not return 2.9e-8')
    })

    it('should handle 0.03 input correctly', () => {
      const m = money(0.03)
      assert.strictEqual(m.toNumber(), 0.03, 'Should return exactly 0.03')
      assert.notStrictEqual(m.toNumber(), 0.029, 'Should not return 0.029')
    })

    it('should handle small decimal precision', () => {
      const testCases = [0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09]
      testCases.forEach((value) => {
        const m = money(value)
        assert.strictEqual(m.toNumber(), value, `Should handle ${value} correctly`)
      })
    })

    it('should maintain precision through operations', () => {
      const m1 = money(0.03)
      const m2 = money(0.02)
      const result = m1.add(m2)
      assert.strictEqual(result.toNumber(), 0.05, 'Addition should maintain precision')
    })

    it('should handle precision limits correctly', () => {
      // Money precision is limited to nanoseconds (1e-9)
      assert.strictEqual(money(1e-9).toNumber(), 1e-9, 'Should handle 1 nanosecond')
      assert.strictEqual(money(5e-9).toNumber(), 5e-9, 'Should handle 5 nanoseconds')

      // Values smaller than 1 nanosecond should be rounded to 0
      assert.strictEqual(money(3e-10).toNumber(), 0, 'Should round 3e-10 to 0 (smaller than nanosecond precision)')
      assert.strictEqual(money(5e-11).toNumber(), 0, 'Should round 5e-11 to 0 (much smaller than nanosecond precision)')
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle undefined/null units and nanos gracefully', () => {
      // These cases should not throw errors
      const testCases = [
        { currency: 'usd', units: undefined, nanos: undefined },
        { currency: 'usd', units: null, nanos: null },
        { currency: 'usd', units: '10', nanos: undefined },
        { currency: 'usd', units: undefined, nanos: '500000000' },
        { currency: undefined, units: '5', nanos: '500000000' }
      ]

      testCases.forEach((testCase, index) => {
        assert.doesNotThrow(
          () => {
            const result = new Money(testCase)
            assert.ok(result instanceof Money, `Test case ${index + 1} should create valid Money object`)
          },
          `Test case ${index + 1} should not throw error`
        )
      })
    })

    it('should handle null and undefined input to money() function', () => {
      assert.strictEqual(money(null), Zero, 'money(null) should return Zero')
      assert.strictEqual(money(undefined), Zero, 'money(undefined) should return Zero')
    })

    it('should handle malformed MoneyLike objects', () => {
      // Should not throw errors, should return valid Money objects with sensible defaults
      assert.doesNotThrow(() => {
        const result = money({ currency: 'usd', units: undefined, nanos: undefined })
        assert.strictEqual(result.toNumber(), 0, 'Should default to 0 when units and nanos are undefined')
        assert.strictEqual(result.symbol(), '$', 'Should use correct currency symbol')
      })

      assert.doesNotThrow(() => {
        const result = money({ currency: undefined, units: '10', nanos: '500000000' })
        assert.strictEqual(result.toNumber(), 10.5, 'Should handle undefined currency with default to usd')
        assert.strictEqual(result.symbol(), '$', 'Should default to USD symbol')
      })
    })

    it('should handle invalid object types gracefully', () => {
      // These should not throw errors but return Zero
      const invalidInputs = [
        'not-a-valid-money-string-###',
        {},
        [],
        42n // bigint
      ]

      invalidInputs.forEach((input, index) => {
        assert.doesNotThrow(
          () => {
            const result = money(input as any)
            assert.ok(result instanceof Money, `Invalid input ${index + 1} should still return Money instance`)
          },
          `Invalid input ${index + 1} should be handled gracefully`
        )
      })
    })

    it('should handle parse function errors gracefully', () => {
      assert.throws(() => {
        parse('')
      }, 'Empty string should throw error')

      assert.throws(() => {
        parse(null as any)
      }, 'Null input should throw error')

      assert.throws(() => {
        parse(undefined as any)
      }, 'Undefined input should throw error')

      assert.throws(() => {
        parse('###invalid###')
      }, 'Invalid format should throw error')
    })
  })
})
