/* eslint-disable */
// @ts-nocheck
/*
* This file is a generated Typescript file for GRPC Gateway, DO NOT MODIFY
*/

import * as fm from "../../../fetch.pb"
import * as GoogleProtobufEmpty from "../../../google/protobuf/empty.pb"
import * as GoogleProtobufTimestamp from "../../../google/protobuf/timestamp.pb"
import * as Analytic_serviceAnalytic_service from "../../analytic/protos/analytic_service.pb"
import * as CommonCommon from "../../common/protos/common.pb"

type Absent<T, K extends keyof T> = { [k in Exclude<keyof T, K>]?: undefined };
type OneOf<T> =
  | { [k in keyof T]?: undefined }
  | (
    keyof T extends infer K ?
      (K extends string & keyof T ? { [k in K]: T[K] } & Absent<T, K>
        : never)
    : never);
export type ProxyResponseAsyncResponse = {
  queryId?: string
  executionInfo?: Analytic_serviceAnalytic_service.ExecutionInfo
  resultUrl?: string
  resultId?: string
}


type BaseProxyResponse = {
}

export type ProxyResponse = BaseProxyResponse
  & OneOf<{ sqlQueryResult: Analytic_serviceAnalytic_service.QuerySQLResultResponse; asyncResponse: ProxyResponseAsyncResponse; syncSqlResponse: Analytic_serviceAnalytic_service.SyncExecuteSQLResponse }>

export type GetRPCNodesRequest = {
  projectId?: string
}

export type GetRPCNodeRequest = {
  id?: string
}

export type GetRPCNodeByForkRequest = {
  forkId?: string
}

export type DeleteRPCNodeRequest = {
  id?: string
}

export type GetInternalRPCNodeRequest = {
  processorId?: string
}

export type GetInternalRPCNodeResponse = {
  url?: string
  rpcNodeId?: string
}

export type RPCNode = {
  id?: string
  code?: string
  network?: string
  projectId?: string
  creator?: CommonCommon.User
  enabled?: boolean
  createdAt?: GoogleProtobufTimestamp.Timestamp
  updatedAt?: GoogleProtobufTimestamp.Timestamp
  lastCalled?: GoogleProtobufTimestamp.Timestamp
  totalCalls?: string
  forkId?: string
  forkNodeUrl?: string
  type?: string
  processorId?: string
  networks?: string[]
}

export type RPCNodeResponse = {
  rpcNodes?: RPCNode[]
}


type BaseRPCNodeLogRequestFilter = {
}

export type RPCNodeLogRequestFilter = BaseRPCNodeLogRequestFilter
  & OneOf<{ requestId: string; httpStatusCode: number; requestHeaderKv: string; responseHeaderKv: string; method: string; durationGte: string; durationLte: string }>

export type RPCNodeLogRequestPagination = {
  offset?: string
  limit?: string
}

export type RPCNodeLogRequest = {
  rpcNodeId?: string
  startTime?: GoogleProtobufTimestamp.Timestamp
  endTime?: GoogleProtobufTimestamp.Timestamp
  projectOwner?: string
  projectSlug?: string
  filters?: RPCNodeLogRequestFilter[]
  search?: string
  pagination?: RPCNodeLogRequestPagination
}

export type RPCNodeLogResponse = {
  logs?: CommonCommon.RequestLog[]
}

export type RPCNodeStatsRequest = {
  id?: string
  startTime?: GoogleProtobufTimestamp.Timestamp
  endTime?: GoogleProtobufTimestamp.Timestamp
  stepSeconds?: string
}

export type RPCNodeStatsResponseDurations = {
  min?: string
  max?: string
  avg?: string
  median?: string
}

export type RPCNodeStatsResponseTimeSeries = {
  time?: GoogleProtobufTimestamp.Timestamp
  avgDurations?: string
  maxDurations?: string
  avgQueryDurations?: string
  maxQueryDurations?: string
  successCount?: string
  errorCount?: string
}

export type RPCNodeStatsResponse = {
  totalRequests?: string
  totalErrors?: string
  durations?: RPCNodeStatsResponseDurations
  queryDurations?: RPCNodeStatsResponseDurations
  totalCost?: string
  timeSeries?: RPCNodeStatsResponseTimeSeries[]
}

export type RPCNetworkResponse = {
  networks?: RPCNodeNetwork[]
}

export type RPCNodeNetwork = {
  id?: string
  name?: string
  endpoints?: RPCEndpoint[]
}

export type RPCEndpoint = {
  path?: string
  name?: string
}

export class RPCNodeService {
  static SaveRPCNode(req: RPCNode, initReq?: fm.InitReq): Promise<RPCNodeResponse> {
    return fm.fetchReq<RPCNode, RPCNodeResponse>(`/api/v1/RPCNode`, {...initReq, method: "POST", body: JSON.stringify(req, fm.replacer)})
  }
  static GetRPCNodes(req: GetRPCNodesRequest, initReq?: fm.InitReq): Promise<RPCNodeResponse> {
    return fm.fetchReq<GetRPCNodesRequest, RPCNodeResponse>(`/api/v1/RPCNode/${req["projectId"]}/list?${fm.renderURLSearchParams(req, ["projectId"])}`, {...initReq, method: "GET"})
  }
  static GetRPCNode(req: GetRPCNodeRequest, initReq?: fm.InitReq): Promise<RPCNode> {
    return fm.fetchReq<GetRPCNodeRequest, RPCNode>(`/api/v1/RPCNode/${req["id"]}?${fm.renderURLSearchParams(req, ["id"])}`, {...initReq, method: "GET"})
  }
  static GetRPCNodeByForkId(req: GetRPCNodeByForkRequest, initReq?: fm.InitReq): Promise<RPCNode> {
    return fm.fetchReq<GetRPCNodeByForkRequest, RPCNode>(`/api/v1/RPCNode/fork/${req["forkId"]}?${fm.renderURLSearchParams(req, ["forkId"])}`, {...initReq, method: "GET"})
  }
  static DeleteRPCNode(req: DeleteRPCNodeRequest, initReq?: fm.InitReq): Promise<GoogleProtobufEmpty.Empty> {
    return fm.fetchReq<DeleteRPCNodeRequest, GoogleProtobufEmpty.Empty>(`/api/v1/RPCNode/${req["id"]}`, {...initReq, method: "DELETE"})
  }
  static RPCNodeRequestLog(req: RPCNodeLogRequest, initReq?: fm.InitReq): Promise<RPCNodeLogResponse> {
    return fm.fetchReq<RPCNodeLogRequest, RPCNodeLogResponse>(`/api/v1/RPCNode/${req["projectOwner"]}/${req["projectSlug"]}/logs`, {...initReq, method: "POST", body: JSON.stringify(req, fm.replacer)})
  }
  static RPCNodeStats(req: RPCNodeStatsRequest, initReq?: fm.InitReq): Promise<RPCNodeStatsResponse> {
    return fm.fetchReq<RPCNodeStatsRequest, RPCNodeStatsResponse>(`/api/v1/RPCNode/${req["id"]}/stats?${fm.renderURLSearchParams(req, ["id"])}`, {...initReq, method: "GET"})
  }
  static GetNetworks(req: GoogleProtobufEmpty.Empty, initReq?: fm.InitReq): Promise<RPCNetworkResponse> {
    return fm.fetchReq<GoogleProtobufEmpty.Empty, RPCNetworkResponse>(`/api/v1/RPCNode/networks?${fm.renderURLSearchParams(req, [])}`, {...initReq, method: "GET"})
  }
  static GetInternalRPCNode(req: GetInternalRPCNodeRequest, initReq?: fm.InitReq): Promise<GetInternalRPCNodeResponse> {
    return fm.fetchReq<GetInternalRPCNodeRequest, GetInternalRPCNodeResponse>(`/RPCNode_service.RPCNodeService/GetInternalRPCNode`, {...initReq, method: "POST", body: JSON.stringify(req, fm.replacer)})
  }
}