#build --incompatible_strict_action_env
#build --experimental_output_directory_naming_scheme=diff_against_dynamic_baseline
#build --features=external_include_paths

#import %workspace%/bazel/bazelrc/performance.bazelrc

startup --max_idle_secs=0

# try to remove this two
#build --enable_workspace
#build --noincompatible_disallow_ctx_resolve_tools

# build --spawn_strategy=local
build --verbose_failures
# build --sandbox_debug
build --incompatible_enable_cc_toolchain_resolution
build --java_runtime_version=remotejdk_11 --tool_java_runtime_version=remotejdk_11
build --disk_cache=~/.cache/bazel-disk
build --experimental_disk_cache_gc_max_size=80G --experimental_disk_cache_gc_max_age=15d
build --action_env=PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
build --nobuild_tests_only
build --show_result=20
build --incompatible_default_to_explicit_init_py

# This will force test to be execute sequentially, but it's fine locally
test --test_output=errors
test --test_output=streamed
test --test_env=LOG_LEVEL=info

build --workspace_status_command=$(pwd)/workspace_status.sh

# build --bes_results_url=https://app.buildbuddy.io/invocation/
# build --bes_backend=grpcs://remote.buildbuddy.io
# build --remote_header=x-buildbuddy-api-key=G9r4YoGxU3vW19MO3Udo

#build --remote_cache=grpcs://remote.buildbuddy.io
#build --remote_timeout=3600
#build --experimental_remote_cache_compression
#build --experimental_remote_cache_async
#build --incompatible_remote_results_ignore_disk
#build --noremote_upload_local_results # Uploads logs & artifacts without writing to cache
#build --noremote_accept_cached
build --build_metadata=VISIBILITY=PUBLIC
build --worker_sandboxing

run --bes_results_url=
run --bes_backend=

run:prod --ui_event_filters=-info,-stdout,-stderr --noshow_progress

#build:ci --remote_cache=""
build:ci --show_loading_progress=no
build:ci --progress_in_terminal_title=no
build:ci --curses=no --color=yes
#build:ci --action_env BAZEL_DO_NOT_DETECT_CPP_TOOLCHAIN=1
build:ci --platforms=//:linux_amd64
build:ci --copt="-Wno-deprecated-builtins"
build:ci --copt="-Wno-error=gnu-offsetof-extensions"
build:ci --copt="-w"
build:ci --copt="-fno-sanitize=undefined"
build:ci --host_copt="-Wno-deprecated-builtins"
build:ci --host_copt="-Wno-unused-but-set-variable"
build:ci --host_copt="-w"
build:ci --host_copt="-fno-sanitize=undefined"
#build:ci --show_timestamps
# build:ci --spawn_strategy=local
test:ci --build_metadata=ROLE=CI
#test:ci --nocache_test_results
test:ci --test_output=all

# ### to build grpc
build --cxxopt='-std=c++20'
build --host_cxxopt='-std=c++20'
build --cxxopt="-Wno-deprecated"
build --host_cxxopt="-Wno-deprecated"

# # build --client_env=CC=clang
# build --copt=-DGRPC_BAZEL_BUILD
# build --host_copt=-DGRPC_BAZEL_BUILD
# build --action_env=GRPC_BAZEL_RUNTIME=1
# build --define=use_fast_cpp_protos=true

build:opt --compilation_mode=opt
build:opt --copt=-Wframe-larger-than=16384
# build --action_env=CGO_CXXFLAGS="-g -O2"
# build --action_env=CGO_CFLAGS="-g -O2"
build:dbg --compilation_mode=dbg
build:dbg --copt=-Werror=return-stack-address

# remove after offical grpc repo compile with c++ 20
# build --host_cxxopt='-Wno-missing-constinit'
# build --host_cxxopt='-Wno-ambiguous-reversed-operator'

common --@aspect_rules_ts//ts:skipLibCheck=honor_tsconfig
common --@aspect_rules_ts//ts:default_to_tsc_transpiler

common --experimental_isolated_extension_usages
common --incompatible_enable_proto_toolchain_resolution
common --enable_platform_specific_config
build:linux --sandbox_add_mount_pair=/tmp
build:macos --sandbox_add_mount_pair=/var/tmp
build:windows --sandbox_add_mount_pair=C:\Temp

# try to remove this two
# https://github.com/rules-proto-grpc/rules_proto_grpc/issues/390
build --noincompatible_disallow_ctx_resolve_tools

# Load other personal settings
try-import %workspace%/.bazelrc.local
