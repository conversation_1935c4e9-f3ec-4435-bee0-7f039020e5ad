{"private": true, "scripts": {"all": "concurrently -c \"bgGreen.bold,bgBlue.bold,bgMagenta.bold,bg<PERSON>yan.bold,bgRed.bold\" \"yarn:service-*\" \"yarn:web\"", "format": "prettier --write .", "preinstall": "npx only-allow pnpm", "release": "semantic-release", "service-admin": "ibazel run service/admin/server:server", "service-alerts": "ibazel run service/alert/server", "service-app": "ibazel run service/web/server/server", "service-processor": "ibazel run service/processor/server  --run-local=true", "service-webhook": "ibazel run service/webhook/server:server", "web": "cd app; pnpm dev"}, "resolutions": {"@sentio/truffle-debugger": "npm:@sentio/truffle-debugger@^0.0.56", "@truffle/codec": "npm:@sentio/truffle-codec@^0.2.3", "@truffle/compile-solidity": "npm:@sentio/truffle-compile-solidity@^0.0.12", "@truffle/source-map-utils": "npm:@sentio/truffle-source-map-utils@^0.0.6", "@truffle/source-fetcher": "npm:@sentio/truffle-source-fetcher@^0.0.4", "@ensdomains/ensjs": "npm:@sentio/ensjs@^2.1.1", "browserify-zlib": "^0.2.0"}, "dependencies": {"next": "15.3.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@bazel/ibazel": "^0.25.0", "@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.23.0", "@ls-lint/ls-lint": "^2.2.3", "@next/eslint-plugin-next": "^15.0.2", "@sentio/ts-proto": "1.172.0-patch.3", "@types/node": "^22.00.0", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "concurrently": "^8.2.1", "conventional-changelog-conventionalcommits": "^8.0.0", "eslint": "^9.23.0", "eslint-config-next": "^15.0.2", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.0.0", "glob": "^11.0.0", "lint-staged": "^15.0.0", "prettier": "^3.2.4", "prettier-package-json": "^2.8.0", "prettier-plugin-sh": "^0.14.0", "prettier-plugin-tailwindcss": "^0.4.1", "semantic-release": "^24.0.0", "tsx": "^4.15.2", "typescript": "5.2.2"}, "engines": {"node": ">=22"}, "packageManager": "pnpm@10.13.1", "pnpm": {"onlyBuiltDependencies": []}}