load("@npm//:defs.bzl", "npm_link_all_packages")
load("//bazel/nextjs:defs.bzl", "nextjs_build")

npm_link_all_packages(name = "node_modules")

nextjs_build(
    name = "website",
    srcs = glob([
        "pages/**",
        "components/**",
        "lib/**",
        "styles/**",
        "blog/**",
        "public/**",
    ]),
    config = "next.config.ts",
    data = [
        #    "jest.config.js",
        "tsconfig.json",
        #        "next.config.mjs",
        #        "next-env.d.ts",
        "package.json",
        "postcss.config.js",
        "tailwind.config.js",
        "eslint.config.mjs",
        ":node_modules",
        "//:node_modules/@types",
        "//:node_modules/next",
        "//:node_modules/react",
        "//:node_modules/react-dom",
        "//:node_modules/typescript",
    ],
    next_js_binary = "//:next_js_binary",
    tags = [
        "manual",
    ],
)
