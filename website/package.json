{"private": true, "name": "@sentio/website", "version": "0.1.0", "scripts": {"_build": "bazel build website_export", "_dev": "PORT=20000 bazel run website_dev", "_start": "bazel run website_dev -- -p 20000", "build": "next build", "build:deps": "pnpm --filter=$(node -p \"require('./package.json').name\")^... build", "dev": "PORT=20000 next dev", "devt": "PORT=20000 next dev --turbopack", "preinstall": "npx only-allow pnpm", "lint": "eslint .", "start": "next start -p 20000"}, "dependencies": {"@floating-ui/react": "^0.26.2", "@headlessui/react": "1.7.7", "@radix-ui/react-icons": "^1.3.0", "@sentio/chain": "workspace:*", "class-variance-authority": "^0.7.0", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.2", "fetch-jsonp": "^1.3.0", "lodash": "^4.17.21", "react-markdown": "^9.0.1", "react-spinners": "^0.13.8", "react-syntax-highlighter": "^15.5.0", "reading-time": "^1.5.0", "rehype-pretty-code": "^0.14.1", "remark-gfm": "^4.0.0", "shiki": "^3.0.0", "typewriter-effect": "^2.21.0"}, "devDependencies": {"@mdx-js/loader": "^3.0.1", "@mdx-js/react": "^3.0.1", "@next/mdx": "^15.3.2", "@tailwindcss/typography": "^0.5.7", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.0", "@types/mdx": "^2.0.13", "@types/react-syntax-highlighter": "^15.5.11", "autoprefixer": "^10.4.15", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.0.0", "fs-extra": "^11.3.0", "postcss": "^8.4.29", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.3.0", "remark-frontmatter": "^5.0.0", "remark-mdx-frontmatter": "^4.0.0", "remark-parse": "^11.0.0", "remark-reading-time": "^2.0.1", "remark-stringify": "^11.0.0", "slugify": "^1.6.6", "strip-markdown": "^6.0.0", "tailwindcss": "^3.4.1", "to-vfile": "^8.0.0", "typescript": "*", "unified": "^11.0.4"}}