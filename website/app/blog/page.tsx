import { readdir } from 'fs/promises'
import path from 'path'
import { BlogPostList } from 'components/blog/BlogPostList'
import { BlogPost } from 'components/blog/BlogPost'
import { getPosts } from './utils'
import { BlogTags } from 'components/blog/BlogTags'
import { Metadata } from 'next'
import { Suspense } from 'react'

export const metadata: Metadata = {
  title: 'Sentio Blog'
}

export default async function BlogPage() {
  const posts = await getPosts()
  /* const pinnedPost = posts.find((p) => p.pinned) */
  /* const listPosts = posts.filter((p) => !p.pinned) */
  const pinnedPost = posts[0]
  const listPosts = posts.slice(1).map((p) => ({ ...p, pinned: false }))
  const tags = [...new Set(posts.map((p) => p.tags).flat())]

  return (
    <div className="mx-auto px-4 py-12 font-[Inter] text-white xl:w-[1232px] 2xl:w-[1332px] 2xl:max-w-[1332px]">
      {pinnedPost && <BlogPost post={pinnedPost} />}
      {tags.length > 0 && <BlogTags tags={tags} />}
      <Suspense>
        <BlogPostList posts={listPosts} />
      </Suspense>
    </div>
  )
}
