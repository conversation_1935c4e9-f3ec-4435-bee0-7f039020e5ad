#!/bin/bash

set -e

if [[ $(git status --porcelain) ]]; then
  git checkout -b dev/website/import
  git config user.email '<EMAIL>'
  git config user.name '<PERSON><PERSON><PERSON>'
  git add .
  git commit -m 'chore(website): import blog post from X article'
  git push --set-upstream origin dev/website/import
  git push

  gh pr create --fill --base=main \
    --body="import from $URL cc @rnons"
fi
