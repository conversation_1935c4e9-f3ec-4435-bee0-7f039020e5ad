import fs from 'fs-extra'
import slugify from 'slugify'

interface TweetResponse {
  data: {
    tweetResult: {
      result: {
        __typename: 'Tweet'
        core: {
          user_results: {
            result: {
              __typename: 'User'
              core: {
                screen_name: 'sentioxyz'
              }
            }
          }
        }
        article: ArticleData
      }
    }
  }
}
interface ArticleData {
  article_results: {
    result: {
      title: string
      preview_text: string
      cover_media: MediaEntity
      content_state: {
        blocks: Block[]
        entityMap: EntityMapItem[]
      }
      media_entities: MediaEntity[]
      metadata: {
        first_published_at_secs: number
      }
    }
  }
}

interface Block {
  key: string
  data: any
  entityRanges: EntityRange[]
  inlineStyleRanges: InlineStyleRange[]
  text: string
  type: string
}

interface EntityRange {
  key: number
  length: number
  offset: number
}

interface InlineStyleRange {
  length: number
  offset: number
  style: string
}

interface EntityMapItem {
  key: string
  value: {
    type: string
    mutability: string
    data: any
  }
}

interface MediaEntity {
  media_id: string
  media_info: {
    __typename: 'ApiGif' | 'ApiImage'
    original_img_url: string
    variants: { content_type: string; url: string }[]
  }
}

async function processMediaEntities(entities: MediaEntity[], slug: string) {
  for (const { media_id, media_info } of entities) {
    let filename = `${media_id}.jpg`
    let url
    switch (media_info.__typename) {
      case 'ApiGif':
        filename = `${media_id}.mp4`
        url = media_info.variants[0].url
        break
      case 'ApiImage':
        url = media_info.original_img_url
        break
    }
    if (url) {
      const res = await fetch(url)
      const data = await res.arrayBuffer()
      const filepath = `public/blog/${slug}/${filename}`
      await fs.ensureFile(filepath)
      await fs.writeFile(filepath, Buffer.from(data))
    }
  }
}

async function convertJsonToMarkdown(jsonData: ArticleData): Promise<{ slug: string; markdown: string }> {
  const { title, content_state, cover_media, media_entities, metadata } = jsonData.article_results.result
  const slug = slugify(title, { lower: true })
  await processMediaEntities([cover_media], slug)
  await processMediaEntities(media_entities, slug)
  const { blocks, entityMap } = content_state

  // Create a map for quick entity lookup
  const entityLookup = new Map<string, EntityMapItem>()
  entityMap.forEach((entity) => {
    entityLookup.set(entity.key, entity)
  })

  const image = `/blog/${slug}/${cover_media.media_id}.jpg`
  const published = new Date(metadata.first_published_at_secs * 1000).toISOString().split('T')[0]

  let markdown = `---
title: ${title}
image: ${image}
published: ${published}
pinned: true
---

`

  let lastBlockType = ''

  blocks.forEach((block) => {
    const processedText = processBlockText(block, entityLookup)

    if (lastBlockType.includes('list-item') && !block.type.includes('list-item')) {
      markdown += '\n'
    }

    switch (block.type) {
      case 'header-two':
        markdown += `## ${processedText}\n\n`
        break
      case 'unstyled':
        if (processedText.trim()) {
          markdown += `${processedText}\n\n`
        }
        break
      case 'unordered-list-item':
        markdown += `- ${processedText}\n`
        break
      case 'ordered-list-item':
        markdown += `1. ${processedText}\n`
        break
      case 'atomic': {
        // Handle embedded content like code blocks or media
        const atomicContent = processAtomicBlock(block, entityLookup, slug)
        if (atomicContent) {
          markdown += `${atomicContent}\n\n`
        }
        break
      }
      default:
        if (processedText.trim()) {
          markdown += `${processedText}\n\n`
        }
    }

    lastBlockType = block.type
  })

  return { slug, markdown: markdown.trim() }
}

function processBlockText(block: Block, entityLookup: Map<string, EntityMapItem>): string {
  let text = block.text

  // Process inline style ranges - process in reverse order to maintain offsets
  const sortedStyleRanges = [...block.inlineStyleRanges].sort((a, b) => b.offset - a.offset)

  sortedStyleRanges.forEach((styleRange) => {
    const beforeText = text.substring(0, styleRange.offset)
    const styledText = text.substring(styleRange.offset, styleRange.offset + styleRange.length)
    const afterText = text.substring(styleRange.offset + styleRange.length)

    let replacement = styledText

    switch (styleRange.style) {
      case 'Bold':
        replacement = `**${styledText}**`
        break
      case 'Italic':
        replacement = `*${styledText}*`
        break
      case 'Code':
        replacement = `\`${styledText}\``
        break
    }

    text = beforeText + replacement + afterText
  })

  // Process entity ranges (links, etc.) - process in reverse order to maintain offsets
  const sortedEntityRanges = [...block.entityRanges].sort((a, b) => b.offset - a.offset)

  sortedEntityRanges.forEach((entityRange) => {
    const entity = entityLookup.get(entityRange.key.toString())
    if (entity) {
      const beforeText = text.substring(0, entityRange.offset)
      let entityText = text.substring(entityRange.offset, entityRange.offset + entityRange.length)
      let afterText = text.substring(entityRange.offset + entityRange.length)

      let replacement = entityText

      switch (entity.value.type) {
        case 'LINK':
          if (sortedStyleRanges.length) {
            entityText = text
            afterText = ''
          }
          replacement = `[${entityText}](${entity.value.data.url})`
          break
        case 'MARKDOWN':
          // For markdown entities, replace with the actual markdown content
          replacement = entity.value.data.markdown || entityText
          break
        case 'MEDIA':
          // For media, we'll just note it as an image placeholder
          replacement = `![${entityText || 'Image'}]()`
          break
      }

      text = beforeText + replacement + afterText
    }
  })

  return text
}

function processAtomicBlock(block: Block, entityLookup: Map<string, EntityMapItem>, slug: string): string | null {
  // Atomic blocks typically have a single entity range
  if (block.entityRanges.length > 0) {
    const entityRange = block.entityRanges[0]
    const entity = entityLookup.get(entityRange.key.toString())

    if (entity) {
      switch (entity.value.type) {
        case 'MARKDOWN':
          return entity.value.data.markdown || ''
        case 'MEDIA': {
          const { mediaCategory, mediaId } = entity.value.data.mediaItems[0]
          if (mediaCategory == 'DraftTweetGif') {
            return `<video src="/blog/${slug}/${mediaId}.mp4" autoPlay controls loop/>`
          } else {
            return `![image](/blog/${slug}/${mediaId}.jpg)`
          }
        }
        case 'LINK':
          return `[${entity.value.data.url}](${entity.value.data.url})`
      }
    }
  }

  return null
}

async function main() {
  const url = process.argv.at(-1)
  const matches = url?.match(new RegExp('https://x.com/sentioxyz/status/(\\d+)'))
  const tweetId = matches?.[1]
  if (!tweetId) {
    console.error(`Import from ${url} not supported`)
    process.exit(1)
  }

  const endpoint = 'https://api.x.com/graphql/47d_Ot9SHX71y2iUEvrtDw/TweetResultByRestId'
  const params = {
    variables: { tweetId, withCommunity: false, includePromotedContent: false, withVoice: false },
    features: {
      creator_subscriptions_tweet_preview_api_enabled: true,
      premium_content_api_read_enabled: false,
      communities_web_enable_tweet_community_results_fetch: true,
      c9s_tweet_anatomy_moderator_badge_enabled: true,
      responsive_web_grok_analyze_button_fetch_trends_enabled: false,
      responsive_web_grok_analyze_post_followups_enabled: false,
      responsive_web_jetfuel_frame: true,
      responsive_web_grok_share_attachment_enabled: true,
      articles_preview_enabled: true,
      responsive_web_edit_tweet_api_enabled: true,
      graphql_is_translatable_rweb_tweet_is_translatable_enabled: true,
      view_counts_everywhere_api_enabled: true,
      longform_notetweets_consumption_enabled: true,
      responsive_web_twitter_article_tweet_consumption_enabled: true,
      tweet_awards_web_tipping_enabled: false,
      responsive_web_grok_show_grok_translated_post: false,
      responsive_web_grok_analysis_button_from_backend: true,
      creator_subscriptions_quote_tweet_preview_enabled: false,
      freedom_of_speech_not_reach_fetch_enabled: true,
      standardized_nudges_misinfo: true,
      tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled: true,
      longform_notetweets_rich_text_read_enabled: true,
      longform_notetweets_inline_media_enabled: true,
      payments_enabled: false,
      profile_label_improvements_pcf_label_in_post_enabled: true,
      rweb_tipjar_consumption_enabled: true,
      verified_phone_label_enabled: false,
      responsive_web_grok_image_annotation_enabled: true,
      responsive_web_grok_community_note_auto_translation_is_enabled: false,
      responsive_web_graphql_skip_user_profile_image_extensions_enabled: false,
      responsive_web_graphql_timeline_navigation_enabled: true,
      responsive_web_enhance_cards_enabled: false
    },
    fieldToggles: {
      withArticleRichContentState: true,
      withArticlePlainText: false,
      withGrokAnalyze: false,
      withDisallowedReplyControls: false
    }
  }
  const apiUrl = new URL(endpoint)
  for (const [k, v] of Object.entries(params)) {
    apiUrl.searchParams.set(k, JSON.stringify(v))
  }
  const res = await fetch(apiUrl, {
    headers: {
      authorization:
        'Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA'
    }
  })
  const text = await res.text()
  // await fs.writeFile(`${tweetId}.json`, text)
  const json: TweetResponse = JSON.parse(text)

  const { result } = json.data.tweetResult
  const { screen_name } = result.core.user_results.result.core
  if (screen_name != 'sentioxyz') {
    console.error(`Publish article by @${screen_name} not supported`)
    process.exit(1)
  }
  const { slug, markdown } = await convertJsonToMarkdown(result.article)
  await fs.writeFile(`blog/${slug}.mdx`, markdown)
}

main()
