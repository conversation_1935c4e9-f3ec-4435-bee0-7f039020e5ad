// const remarkReadingTime = require('remark-reading-time')
// const readingMdxTime = require('remark-reading-time/mdx')
import createMDX from '@next/mdx'
import remarkReadingTime from 'remark-reading-time'
import readingMdxTime from 'remark-reading-time/mdx.js'
import remarkFrontmatter from 'remark-frontmatter'
import remarkMdxFrontmatter from 'remark-mdx-frontmatter'
import rehypePrettyCode from 'rehype-pretty-code'
import type { NextConfig } from 'next'

const withMDX = createMDX({
  options: {
    remarkPlugins: [remarkReadingTime, readingMdxTime, remarkFrontmatter, remarkMdxFrontmatter],
    rehypePlugins: [[rehypePrettyCode, { defaultLang: 'ts' }]]
  }
})

const nextConfig: NextConfig = {
  pageExtensions: ['mdx', 'ts', 'tsx'],
  reactStrictMode: true,
  trailingSlash: true,
  // https://nextjs.org/docs/messages/export-image-api
  images: {
    unoptimized: true
  },
  output: 'export'
}

export default withMDX(nextConfig)
