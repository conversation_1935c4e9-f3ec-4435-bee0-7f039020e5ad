'use client'

import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import React, { type ReactNode, useEffect, useState } from 'react'
import { useFloating, useHover, useInteractions, safePolygon, offset, FloatingPortal } from '@floating-ui/react'
import { cx } from 'class-variance-authority'
import {
  AlertsIcon,
  AnalyticsIcon,
  CollaborationIcon,
  DebuggerIcon,
  IndexerIcon,
  IntegrationIcon,
  SimulatorIcon,
  CodeIntelligenceIcon,
  WorkIcon,
  AboutIcon
} from './icons'

const ProductItem = ({
  title,
  description,
  icon,
  link
}: {
  title: string
  description?: string
  icon?: ReactNode
  link?: string
}) => {
  return (
    <Link href={link ?? ''} className="group cursor-pointer space-y-[10px]">
      <div className="flex items-center gap-[10px]">
        {icon ? <i className="text-[#9E9E9E] group-hover:text-[#FAFAFA]">{icon}</i> : null}
        <div className="text-base font-medium">{title}</div>
        <i className="animate-bounce-right invisible align-text-bottom group-hover:visible">
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
          >
            <g clipPath="url(#clip0_16637_3890)">
              <path d="M4.5 3L7.5 6L4.5 9" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
            </g>
            <defs>
              <clipPath id="clip0_16637_3890">
                <rect width="12" height="12" fill="white" transform="matrix(-4.37114e-08 1 1 4.37114e-08 0 0)" />
              </clipPath>
            </defs>
          </svg>
        </i>
      </div>
      {description ? <div className="text-blackprimary-2 text-[13px]">{description}</div> : null}
    </Link>
  )
}

const ChevronDown = ({ isOpen }: { isOpen?: boolean }) => {
  return (
    <svg
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cx('inline-block h-5 w-5 transition-transform', isOpen ? '' : 'rotate-180')}
    >
      <g clipPath="url(#clip0_15577_11954)">
        <path
          d="M3.88379 8.75L7.38379 5.25L10.8838 8.75"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_15577_11954">
          <rect width="14" height="14" fill="currentColor" transform="matrix(1 0 0 -1 0.383789 14)" />
        </clipPath>
      </defs>
    </svg>
  )
}

function NavLinks() {
  const pathname = usePathname()
  const getClassName = (href: string | string[]) => {
    return cx(
      'inline-flex gap-1 items-center cursor-pointer font-medium text-[17px] text-white hover:text-[#36f7f7] transition-colors'
      // isActive ? 'text-white' : 'text-white opacity-80'
    )
  }
  const externalClassName = 'inline-block text-white hover:text-[#36f7f7] transition-colors font-medium text-[17px]'

  const [isOpen, setIsOpen] = useState(false)
  const [isDesktopScreen, setDesktopScreen] = useState(false)
  useEffect(() => {
    if (typeof document === 'undefined') return
    const listener = () => {
      setDesktopScreen(document.body.clientWidth > 1024)
    }
    listener()
    window.addEventListener('resize', listener)
    return () => {
      window.removeEventListener('resize', listener)
    }
  }, [])
  useEffect(() => {
    setIsOpen(false)
  }, [pathname])

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement: isDesktopScreen ? 'bottom-start' : 'bottom',
    middleware: [
      offset({
        mainAxis: 10,
        crossAxis: isDesktopScreen ? -220 : 0
      })
    ]
  })

  const hover = useHover(context, {
    handleClose: safePolygon({
      requireIntent: false,
      buffer: 1,
      blockPointerEvents: true
    })
  })

  const { getReferenceProps, getFloatingProps } = useInteractions([hover])

  const [isAboutOpen, setAboutOpen] = useState(false)
  const aboutFloating = useFloating({
    open: isAboutOpen,
    onOpenChange: setAboutOpen,
    placement: isDesktopScreen ? 'bottom-start' : 'bottom',
    middleware: [
      offset({
        mainAxis: 10,
        crossAxis: isDesktopScreen ? -40 : 0
      })
    ]
  })
  const aboutHover = useHover(aboutFloating.context, {
    handleClose: safePolygon({
      requireIntent: false,
      buffer: 1,
      blockPointerEvents: true
    })
  })

  const aboutInteractions = useInteractions([aboutHover])

  return (
    <ul className="flex flex-col items-center gap-12 lg:flex-row">
      <li>
        <div className="group relative w-[80px] overflow-visible">
          <div
            className={cx(getClassName('/'), isOpen ? 'opacity-100' : '')}
            ref={refs.setReference}
            {...getReferenceProps()}
          >
            Products
            <ChevronDown isOpen={isOpen} />
          </div>
          <FloatingPortal>
            <div
              ref={refs.setFloating}
              style={{ ...floatingStyles, position: isDesktopScreen ? 'absolute' : 'fixed' }}
              {...getFloatingProps()}
              className={cx(
                isOpen ? 'block' : 'hidden',
                isDesktopScreen ? '' : 'max-h-[60vh] overflow-y-auto',
                'products-popper grid grid-cols-1 gap-x-[30px] gap-y-6 p-4 text-white lg:grid-cols-3 lg:gap-y-[40px] lg:p-[30px]',
                'z-10',
                'w-[calc(100vw-20px)] lg:w-[1100px]'
              )}
            >
              <ProductItem
                title="Indexer"
                description="Up to 5x faster speed, support one-click subgraph migration"
                icon={<IndexerIcon />}
                link="/indexer"
              />
              <ProductItem
                title="Debugger"
                description="Dive into all details of a transaction, debug with single-stepping mode"
                icon={<DebuggerIcon />}
                link="/debugger"
              />
              <ProductItem
                title="Collaboration"
                description="Built for teams"
                icon={<CollaborationIcon />}
                link="/collaboration"
              />
              <ProductItem
                title="Analytics"
                description="Flexible historical and real-time analytics with powerful visualization, supporting both SQL and GraphQL, Cohort analysis, Retention analysis."
                icon={<AnalyticsIcon />}
                link="/analytics"
              />
              <ProductItem
                title="Simulator"
                description="Preview transaction result, with up to 3x-10x faster speed"
                icon={<SimulatorIcon />}
                link="/simulator"
              />
              <ProductItem
                title="Integration"
                description="Chrome extension, Hardhat plugin, etc…"
                icon={<IntegrationIcon />}
                link="/integration"
              />
              <ProductItem
                title="Alerts"
                description="Add alerts to your preferred channels"
                icon={<AlertsIcon />}
                link="/alerts"
              />
              <ProductItem
                title="Code Intelligence"
                description="Fast code search with all the context you need"
                icon={<CodeIntelligenceIcon />}
                link="/code"
              />
            </div>
          </FloatingPortal>
        </div>
      </li>
      <li>
        <Link href="/solution" className={externalClassName}>
          Solution
        </Link>
      </li>
      <li>
        <a href="https://docs.sentio.xyz/" className={externalClassName}>
          Docs
        </a>
      </li>
      <li>
        <Link href="/blog" className={externalClassName}>
          Blog
        </Link>
      </li>
      <li>
        <Link href="/pricing" className={getClassName('/pricing')}>
          Pricing
        </Link>
      </li>
      <li>
        <Link href="/about">
          <div
            className={cx(getClassName('/'), isAboutOpen ? 'opacity-100' : '')}
            ref={aboutFloating.refs.setReference}
            {...aboutInteractions.getReferenceProps()}
          >
            About
            <ChevronDown isOpen={isAboutOpen} />
          </div>
        </Link>
        <FloatingPortal>
          <div
            ref={aboutFloating.refs.setFloating}
            style={{ ...aboutFloating.floatingStyles, position: isDesktopScreen ? 'absolute' : 'fixed' }}
            {...aboutInteractions.getFloatingProps()}
            className={cx(
              isAboutOpen ? 'block' : 'hidden',
              isDesktopScreen ? '' : 'max-h-[60vh] overflow-y-auto',
              'products-popper grid grid-cols-1 gap-x-[30px] gap-y-6 p-4 text-white',
              'z-10',
              'w-fit'
            )}
          >
            <ProductItem title="About Us" link="/about" />
            <ProductItem title="Work with Us" link="/about#work" />
          </div>
        </FloatingPortal>
      </li>
    </ul>
  )
}

const MobileProductItem = ({
  title,
  description,
  icon,
  link,
  onClick
}: {
  title: string
  description?: string
  icon?: ReactNode
  link?: string
  onClick?: () => void
}) => {
  return (
    <Link href={link ?? ''} className="group/navitem cursor-pointer space-y-[10px]" onClick={onClick}>
      <div className="flex items-center gap-[10px]">
        {icon ? (
          <i className="rounded-md bg-white/10 p-[5px] text-[#9E9E9E] group-hover/navitem:text-[#FAFAFA]">{icon}</i>
        ) : null}
        <div className="text-sm font-medium">{title}</div>
      </div>
      {description ? <div className="text-blackprimary-2 text-xs">{description}</div> : null}
    </Link>
  )
}

function MobileNavLinks({ onClose }: { onClose?: () => void }) {
  const getClassName = (href: string | string[]) => {
    return cx(
      'inline-flex gap-1 items-center cursor-pointer font-medium text-[17px] text-white hover:text-[#36f7f7] transition-colors'
      // isActive ? 'text-white' : 'text-white opacity-80'
    )
  }
  const externalClassName = 'inline-block text-white hover:text-[#36f7f7] transition-colors font-medium text-[17px]'

  const [isProductionMenuOpen, openProductionMenu] = useState(false)
  const [isAboutMenuOpen, openAboutMenu] = useState(false)

  return (
    <ul className="flex flex-col items-stretch divide-y divide-white/10 px-6">
      <li>
        <div className="group w-full overflow-visible">
          <div
            className={cx(
              getClassName('/'),
              isProductionMenuOpen ? 'opacity-100' : '',
              'flex w-full items-center justify-between py-4'
            )}
            onClick={() => {
              openProductionMenu((v) => !v)
            }}
          >
            Products
            <svg
              width="15"
              height="14"
              viewBox="0 0 15 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className={cx('inline-block h-5 w-5 transition-transform', isProductionMenuOpen ? '' : 'rotate-180')}
            >
              <g clipPath="url(#clip0_15577_11954)">
                <path
                  d="M3.88379 8.75L7.38379 5.25L10.8838 8.75"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </g>
              <defs>
                <clipPath id="clip0_15577_11954">
                  <rect width="14" height="14" fill="currentColor" transform="matrix(1 0 0 -1 0.383789 14)" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div
            className={cx(
              isProductionMenuOpen ? 'block' : 'hidden',
              'grid grid-cols-1 gap-x-[30px] gap-y-6 text-white',
              'w-[calc(100vw-20px)]',
              'pb-4'
            )}
          >
            <MobileProductItem
              title="Indexer"
              description="Up to 5x faster speed, support one-click subgraph migration"
              icon={<IndexerIcon />}
              link="/indexer"
            />
            <MobileProductItem
              title="Debugger"
              description="Dive into all details of a transaction, debug with single-stepping mode"
              icon={<DebuggerIcon />}
              link="/debugger"
            />
            <MobileProductItem
              title="Collaboration"
              description="Built for teams"
              icon={<CollaborationIcon />}
              link="/collaboration"
            />
            <MobileProductItem
              title="Analytics"
              description="Flexible historical and real-time analytics with powerful visualization, supporting both SQL and GraphQL, Cohort analysis, Retention analysis."
              icon={<AnalyticsIcon />}
              link="/analytics"
            />
            <MobileProductItem
              title="Simulator"
              description="Preview transaction result, with up to 3x-10x faster speed"
              icon={<SimulatorIcon />}
              link="/simulator"
            />
            <MobileProductItem
              title="Integration"
              description="Chrome extension, Hardhat plugin, etc…"
              icon={<IntegrationIcon />}
              link="/integration"
            />
            <MobileProductItem
              title="Alerts"
              description="Add alerts to your preferred channels"
              icon={<AlertsIcon />}
              link="/alerts"
            />
            <MobileProductItem
              title="Code Intelligence"
              description="Fast code search with all the context you need"
              icon={<CodeIntelligenceIcon />}
              link="/code"
            />
          </div>
        </div>
      </li>
      <li className="py-4">
        <Link href="/solution" className={externalClassName}>
          Solution
          <div className="ml-1 inline-block -translate-y-1 font-['Inter-Medium',_sans-serif] text-[13px] font-medium text-[#ef35ff]">
            NEW
          </div>
        </Link>
      </li>
      <li className="py-4">
        <a href="https://docs.sentio.xyz/" className={externalClassName}>
          Docs
        </a>
      </li>
      <li className="py-4">
        <a href="https://sentioxyz.medium.com/" className={externalClassName}>
          Blog
        </a>
      </li>
      <li className="py-4">
        <Link href="/pricing" className={getClassName('/pricing')}>
          Pricing
        </Link>
      </li>
      <li>
        <div className="group w-full overflow-visible">
          <div
            className={cx(
              getClassName('/'),
              isAboutMenuOpen ? 'opacity-100' : '',
              'flex w-full items-center justify-between py-4'
            )}
            onClick={() => {
              openAboutMenu((v) => !v)
            }}
          >
            About
            <svg
              width="15"
              height="14"
              viewBox="0 0 15 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className={cx('inline-block h-5 w-5 transition-transform', isAboutMenuOpen ? '' : 'rotate-180')}
            >
              <g clipPath="url(#clip0_15577_11954)">
                <path
                  d="M3.88379 8.75L7.38379 5.25L10.8838 8.75"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </g>
              <defs>
                <clipPath id="clip0_15577_11954">
                  <rect width="14" height="14" fill="currentColor" transform="matrix(1 0 0 -1 0.383789 14)" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <div
            className={cx(
              isAboutMenuOpen ? 'block' : 'hidden',
              'grid grid-cols-1 gap-x-[30px] gap-y-6 text-white',
              'w-[calc(100vw-20px)]',
              'pb-4'
            )}
          >
            <MobileProductItem
              title="About Us"
              link="/about"
              icon={<AboutIcon className="h-5 w-5" />}
              onClick={onClose}
            />
            <MobileProductItem
              title="Work with Us"
              link="/about#work"
              icon={<WorkIcon className="h-5 w-5" />}
              onClick={onClose}
            />
          </div>
        </div>
      </li>
    </ul>
  )
}

export const Header = () => {
  const [overlayShown, setOverlayShown] = useState(false)
  const pathname = usePathname()

  useEffect(() => {
    console.log(pathname)
    setOverlayShown(false)
  }, [pathname])

  useEffect(() => {
    if (overlayShown) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'auto'
    }
  }, [overlayShown])

  return (
    <>
      <div className="sticky top-0 z-20 bg-[#121212] sm:static sm:z-0">
        <header className="container flex w-full items-center justify-between py-5 xl:w-[1232px] 2xl:w-[1332px] 2xl:max-w-[1332px]">
          <Link href="/">
            <Image src="/logo-dark.png" alt="Sentio" width={135} height={40} />
          </Link>
          <nav className="hidden lg:block">
            <NavLinks />
          </nav>
          <section className="inline-flex items-center gap-4">
            <a
              href="https://app.sentio.xyz/explorer"
              className="hidden rounded-md border border-slate-300 px-[18px] py-2 text-[17px] font-medium leading-3 text-slate-100 transition-colors hover:border-[#0F86FC66] hover:bg-[#0756D566] hover:text-white lg:inline-block"
            >
              Launch Explorer
            </a>
            <a
              href="https://app.sentio.xyz/"
              className="inline-block h-fit rounded-md border border-[#0F86FC] bg-[#0756D5] px-[18px] py-1.5 text-xs font-medium text-white transition-colors hover:bg-[#0756D5ef] sm:py-2 sm:text-[17px] sm:leading-3"
            >
              Launch App
            </a>
            {overlayShown ? (
              <button
                className="p-1 text-white lg:hidden"
                onClick={() => {
                  setOverlayShown(false)
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth="1.5"
                  stroke="currentColor"
                  className="h-7 w-7"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            ) : (
              <button
                className="p-1 text-white lg:hidden"
                onClick={() => {
                  setOverlayShown(true)
                }}
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clipPath="url(#clip0_22696_5558)">
                    <path
                      d="M4 6H20"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M4 12H20"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M4 18H20"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_22696_5558">
                      <rect width="24" height="24" fill="currentColor" />
                    </clipPath>
                  </defs>
                </svg>
              </button>
            )}
          </section>
        </header>
      </div>
      <nav
        className={`${
          overlayShown ? 'translate-x-0' : 'translate-x-full'
        } fixed inset-0 top-[60px] z-10 overflow-y-auto bg-[#121212] transition-all`}
      >
        <MobileNavLinks
          onClose={() => {
            setOverlayShown(false)
          }}
        />
        <div className="flex w-full items-center justify-center gap-8 border-t border-t-white/10 px-4 py-4">
          <a
            href="https://app.sentio.xyz/explorer"
            className="flex-1 rounded-md border border-solid border-[rgba(255,255,255,0.08)] bg-[rgba(255,255,255,0.10)] pb-2 pl-[18px] pr-[18px] pt-2"
          >
            <div className="relative text-center text-xs font-medium leading-3 text-[#ffffff]">Launch Explorer</div>
          </a>
          <a
            href="https://app.sentio.xyz/"
            className="inline-block h-fit flex-1 rounded-md border border-[#0F86FC] bg-[#0756D5] px-[18px] py-1.5 text-center text-xs font-medium text-white transition-colors hover:bg-[#0756D5ef] sm:py-2 sm:text-[17px] sm:leading-3"
          >
            Launch App
          </a>
        </div>
      </nav>
    </>
  )
}
