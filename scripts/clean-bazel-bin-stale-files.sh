#!/bin/bash
set -euo pipefail

echo "=== Cleaning bazel-bin/app of stale files ==="

BASEDIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$BASEDIR/.."

BIN="bazel-bin/app"
SRC="app"

for d in "$BIN"/*/; do
  [[ -d "$d" ]] || continue
  name="$(basename "$d")"
  [[ -d "$SRC/$name" ]] || continue

  rsync -ai --dry-run --delete "$SRC/$name/" "$BIN/$name/" \
    | awk -v n="$name" '/^\*deleting /{ sub(/^\*deleting /, ""); print n "/" $0 }' \
    | while IFS= read -r rel; do
      path="$BIN/$rel"
      if [ -f "$path" ] || [ -L "$path" ]; then
        echo "deleting $rel"
      fi
      rm -rf -- "$path"
    done
done

echo "=== Cleaning done ==="
