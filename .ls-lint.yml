# https://ls-lint.org/1.x/configuration/the-rules.html
ls:
  .dir: kebab-case | snake_case | regex:.[a-z0-9]+ | regex:[[a-z0-9]+] # TODO clean this up
  .go: snake_case
  .deepcopy.go: snake_case
  .proto: snake_case
  .bazel: SCREAMING_SNAKE_CASE
  .html: kebab-case
  .css: kebab-case
  .png: kebab-case
  .jpg: kebab-case
  .json: kebab-case | regex:.[a-z0-9]+
  .js: kebab-case
  .config.js: kebab-case
  .ts: kebab-case | regex:_[a-z0-9]+
  .tsx: kebab-case | regex:_[a-z0-9]+ | regex:[[a-z0-9]+]
  .test.ts: kebab-case
  .test.js: kebab-case
  .test.tsx: kebab-case
  .d.ts: kebab-case
  .test.d.ts: kebab-case
  .common.json: lowercase

  app/components:
    .stories.tsx: regex:.* # ignore storybook file
    .tsx: PascalCase
  website/src/components:
    .tsx: PascalCase
  browser/**/components:
    .tsx: PascalCase
  browser/src/content/lib/log:
    .tsx: PascalCase


ignore:
  - .git
  - .venv
  - api
  - bazel-*
  - node_modules
  - '*/node_modules/'
  - '*/*/node_modules/'
  - .ijwb
  - .clwb
  - .idea
  - .release.config.js
  - deploy/data
  - service/ai/docs
  - .github
  - .bazel_fix_commands.json
  - openapi/dist
  - openapi/openapi-ts.config.ts
  - app/gen
  - app/openapi
  - app/pages
  - app/.next
  - app/.idea
  - app/.swc
  - sentio-sdk
  - app/lib
  - app/cypress
  - app/cypress.config.ts
  - app/sentry.*
  - maven_install.json
  - chainquery/src/main/resources
  - browser/dist
  - website/.idea
  - website/.next
  - website/out
  - website/components
  - website/.eslintrc.js
  - website/public
  - website/app/blog/**/assets
  - website/blog
  - remix-plugin/.next
  - remix-plugin/out
  - remix-plugin/components
  - remix-plugin/tailwind.config.ts
  - provider/solana/generated
  - integration-tests/testcases
  - deploy
  - .claude
  - "*/next.config.ts"