load("@rules_go//go:def.bzl", "go_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_proto_grpc_go//:defs.bzl", "go_grpc_library")
load("//bazel:ts-rules.bzl", "ts_proto")

filegroup(
    name = "proto_file",
    srcs = ["processor.proto"],
    visibility = ["//visibility:public"],
)

proto_library(
    name = "processor_proto",
    srcs = ["processor.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "//service/common/protos:protos_proto",
        "@protobuf//:empty_proto",
        "@protobuf//:struct_proto",
        "@protobuf//:timestamp_proto",
    ],
)

# TODO bazel7
# keep
go_grpc_library(
    name = "protos_go_proto",
    importpath = "sentioxyz/sentio/processor/protos",
    protos = [":processor_proto"],
    visibility = ["//visibility:public"],
    deps = ["//service/common/protos"],
)

go_library(
    name = "processor",
    embed = [":protos_go_proto"],
    importpath = "sentioxyz/sentio/processor/protos",
    visibility = ["//visibility:public"],
)

ts_proto(
    name = "processor_ts_proto",
    esm = True,
    grpc = True,
    protos = [
        ":processor_proto",
    ],
    visibility = ["//visibility:public"],
)

ts_proto(
    name = "processor_ts_proto_no_deprecate",
    esm = True,
    grpc = True,
    protos = [
        ":processor_proto",
    ],
    remove_deprecated = True,
    visibility = ["//visibility:public"],
)

ts_proto(
    name = "empty_ts_proto",
    esm = True,
    protos = [
        "@protobuf//:empty_proto",
    ],
    visibility = ["//visibility:public"],
)

ts_proto(
    name = "struct_ts_proto",
    esm = True,
    protos = [
        "@protobuf//:struct_proto",
    ],
    visibility = ["//visibility:public"],
)

ts_proto(
    name = "timestamp_ts_proto",
    esm = True,
    protos = [
        "@protobuf//:timestamp_proto",
    ],
    visibility = ["//visibility:public"],
)

ts_proto(
    name = "money_ts_proto",
    esm = True,
    protos = [
        "@googleapis//google/type:money_proto",
    ],
    visibility = ["//visibility:public"],
)
