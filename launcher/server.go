package launcher

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/InVisionApp/go-health/v2"
	"github.com/gorilla/handlers"
	"google.golang.org/grpc/reflection"

	"sentioxyz/sentio/common/config"
	"sentioxyz/sentio/common/jsonrpc"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/monitoring"
	server "sentioxyz/sentio/service/common/rpc"
	"sentioxyz/sentio/service/sysstatus"
	sysStatusProtos "sentioxyz/sentio/service/sysstatus/protos"
	"sentioxyz/sentio/service/usage"
	"sentioxyz/sentio/service/usage/protos"
	"sentioxyz/sentio/service/usage/store"
)

func BuildJSONRPCV2Server(ctx context.Context, c config.Config, name string) Task {
	listen := c.Get("listen").String("", config.NotEmpty)
	handler := jsonrpc.NewHandler(
		name,
		c.Get("debug").Bool(false),
		BuildDriverJobGetter(ctx, c.Get("driver-job-getter").String("")),
		jsonrpcAccessUsed)
	svrConfs := c.Get("service").Array(config.SizeGt[config.Config](0))
	for _, svrConf := range svrConfs {
		t := svrConf.Get("type").String("evm")
		switch t {
		case "evm":
			handler.RegisterMiddleware(BuildEvmMiddlewares(ctx, svrConf, name)...)
		//case "btc":
		//	handler.RegisterMiddleware(BuildBTCMiddlewares(ctx, svrConf, name)...)
		case "fuel":
			handler.RegisterMiddleware(BuildFuelMiddlewares(ctx, svrConf, name)...)
		case "sui":
			handler.RegisterMiddleware(BuildSuiMiddlewares(ctx, svrConf, name)...)
		case "aptos2":
			handler.RegisterMiddleware(BuildAptosMiddlewares(ctx, svrConf, name)...)
		case "sol":
			handler.RegisterMiddleware(BuildSolMiddlewares(ctx, svrConf, name)...)
		default:
			log.Fatalf("unsupport chain type %s", t)
		}
	}
	return func(ctx context.Context) error {
		handler := monitoring.NewWrappedHandler(handler, name)
		return ListenAndServe(ctx, listen, handler)
	}
}

func BuildGrpcServer(ctx context.Context, c config.Config, name string) Task {
	listen := c.Get("listen").String("", config.NotEmpty)

	// TODO check if this can be flip
	grpcServer := server.NewServer(c.Get("enable-tls").Bool(true))
	var healthCheckers []*health.Config
	mux := server.NewServeMux()

	// proxyContext is used for proxying http request to grpc server,
	// so use context.Background(), means proxy forever
	proxyContext := context.Background()

	defaultEnableHTTP := c.Get("enable-http").Bool(true)

	for _, svcConf := range c.Get("service").Array() {
		svcType := svcConf.Get("type").String("", config.In(
			"UsageService", "SysStatusService", "InscriptionService"))
		switch svcType {
		case "UsageService":
			gwStore := BuildUsageStore(ctx, svcConf.Get("store").String("", config.NotEmpty))
			historyStore := make(map[time.Time]store.Storage)
			for _, hsConf := range svcConf.Get("history-store").Array() {
				hsStoreEnd := hsConf.Get("end").Time(time.RFC3339, "")
				hsStore := BuildUsageStore(ctx, hsConf.Get("store").String("", config.NotEmpty))
				historyStore[hsStoreEnd] = hsStore
			}
			service := usage.NewService(BuildAuthManager(svcConf.Get("auth")), gwStore, historyStore)
			protos.RegisterUsageServiceServer(grpcServer, service)
			if svcConf.Get("enable-http").Bool(defaultEnableHTTP) {
				err := protos.RegisterUsageServiceHandlerFromEndpoint(
					proxyContext,
					mux,
					listen,
					server.GRPCGatewayDialOptions,
				)
				if err != nil {
					panic(err)
				}
			}
			healthCheckers = append(healthCheckers, &health.Config{
				Name:     svcConf.Get("health.name").String(svcType+"-check", config.NotEmpty),
				Interval: svcConf.Get("health.interval").Duration(time.Second*5, config.Gt[time.Duration](0)),
				Checker:  service,
				Fatal:    true,
			})
		case "SysStatusService":
			service, err := sysstatus.NewChainStatusService(
				BuildAuthManager(svcConf.Get("auth")),
				svcConf.Get("namespace").String("", config.NotEmpty),
				svcConf.Get("minRefreshInterval").Duration(time.Second*10),
				svcConf.Get("commonConfigFile").String("/etc/sentio/commonChainsConfig.yaml"),
				svcConf.Get("driverConfigFile").String("/etc/sentio/driverChainsConfig.yaml"),
				svcConf.Get("solidityConfigFile").String("/etc/sentio/solidityChainsConfig.yaml"))
			if err != nil {
				panic(err)
			}
			sysStatusProtos.RegisterSysStatusServiceServer(grpcServer, service)
			if svcConf.Get("enable-http").Bool(defaultEnableHTTP) {
				err := sysStatusProtos.RegisterSysStatusServiceHandlerFromEndpoint(
					proxyContext,
					mux,
					listen,
					server.GRPCGatewayDialOptions,
				)
				if err != nil {
					panic(err)
				}
			}
			healthCheckers = append(healthCheckers, &health.Config{
				Name:     svcConf.Get("health.name").String(svcType+"-check", config.NotEmpty),
				Interval: svcConf.Get("health.interval").Duration(time.Second*5, config.Gt[time.Duration](0)),
				Checker:  service,
				Fatal:    true,
			})
		}
	}

	healthzHandler, err := server.HealthCheckerWithCheckers(healthCheckers...)
	if err != nil {
		panic(c.BuildErr(fmt.Errorf("prepare healthz handler failed: %w", err)))
	}
	if err := mux.HandlePath("GET", "/healthz", healthzHandler); err != nil {
		panic(c.BuildErr(fmt.Errorf("registry healthz path failed: %w", err)))
	}

	if c.Get("enable-grpc-reflection").Bool(true) {
		reflection.Register(grpcServer)
	}

	httpHandler := server.WithLogger(mux)
	if c.Get("enable-http-compress").Bool(true) {
		httpHandler = handlers.CompressHandler(httpHandler)
	}

	return func(ctx context.Context) error {
		return server.ListenAndServe(ctx, listen, httpHandler, grpcServer)
	}
}

func ListenAndServe(ctx context.Context, addr string, handler http.Handler) error {
	if !strings.ContainsRune(addr, ':') {
		addr = ":" + addr
	}
	svr := http.Server{
		Addr:    addr,
		Handler: handler,
		BaseContext: func(listener net.Listener) context.Context {
			return ctx
		},
	}
	_, logger := log.FromContext(ctx)
	logger.Infof("server start %q", addr)
	go func() {
		<-ctx.Done()
		_ = svr.Close()
	}()
	return svr.ListenAndServe()
}
