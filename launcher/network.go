package launcher

import (
	"context"
	"fmt"
	clickhousev2 "github.com/ClickHouse/clickhouse-go/v2"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	"math"
	"sentioxyz/sentio/chain/aptos"
	"sentioxyz/sentio/chain/chain"
	"sentioxyz/sentio/chain/clickhouse"
	"sentioxyz/sentio/chain/evm"
	"sentioxyz/sentio/chain/fuel"
	"sentioxyz/sentio/chain/node"
	"sentioxyz/sentio/chain/slot"
	"sentioxyz/sentio/chain/sol"
	"sentioxyz/sentio/chain/sui"
	"sentioxyz/sentio/common/config"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/number"
	"time"
)

type NetworkOptions struct {
	chainID string

	fallBehindThreshold     uint64
	fallBehindTimeThreshold time.Duration

	// evm
	evm *evm.NetworkOptions

	// aptos
	// ...

	// sui
	// ...
}

type ChainType interface {
	GetName() string
	HasNetwork(network string) error
	GetNetworkOptions() map[string]NetworkOptions

	BuildCopyDimensionTask(ctx context.Context, c config.Config, network string) Task
	BuildCopyChainTask(ctx context.Context, c config.Config, network string) Task
	BuildRepairDimensionTask(ctx context.Context, c config.Config, network string) Task
	BuildRepairChainTask(ctx context.Context, c config.Config, network string) Task
	BuildSyncChainTask(ctx context.Context, c config.Config, network string) Task

	BuildObserver(ctx context.Context, network string, dimension string) Task
}

type chainType[SLOT slot.Slot] struct {
	Name                string
	ExtDimensionBuilder func(
		ctx context.Context,
		c config.Config,
		name string,
		network string,
		options NetworkOptions) chain.Dimension[SLOT]
	ClickhouseSchemaMgrBuilder func(
		ctx context.Context,
		c config.Config,
		dimName string,
		network string,
		conn clickhousev2.Conn,
		options NetworkOptions) clickhouse.SchemaMgr[SLOT]
	ChainAdapterBuilder   func(options NetworkOptions, endpoint, wssEndpoint string) (node.ChainAdapter, error)
	ObserverClientBuilder func(nodeClient node.NodeClient) ObserverClient
	Networks              map[string]NetworkOptions
}

func (ct chainType[SLOT]) GetName() string {
	return ct.Name
}

func (ct chainType[SLOT]) HasNetwork(network string) error {
	_, ok := ct.Networks[network]
	if ok {
		return nil
	}
	return fmt.Errorf("unknown network %q for chain type %q", network, ct.Name)
}

func (ct chainType[SLOT]) BuildSimpleDimension(
	ctx context.Context,
	c config.Config,
	dimName string,
	network string,
) chain.Dimension[SLOT] {
	return chain.NewSimpleDimension(
		network,
		BuildRangeStore(ctx, c.Get("range-store", config.Exist)),
		ct.BuildSimpleSlotStore(ctx, c.Get("slot-store", config.Exist), dimName, network))
}

func (ct chainType[SLOT]) BuildSimpleSlotStore(
	ctx context.Context,
	c config.Config,
	dimName string,
	network string,
) chain.SimpleSlotStore[SLOT] {
	switch c.Get("driver").String("", config.In("clickhouse")) {
	case "clickhouse":
		conn, _ := BuildClickhouseConnect(ctx, c)
		schemaMgr := ct.ClickhouseSchemaMgrBuilder(ctx, c, dimName, network, conn, ct.Networks[network])
		store, err := clickhouse.NewSimpleSlotStore(
			ctx,
			conn,
			schemaMgr,
			c.Get("flush-batch-size").Int(2000),
			c.Get("slow-flush-threshold").Duration(time.Millisecond*300),
			c.Get("convert-concurrency").Uint(10, config.Gt[uint](0)),
			c.Get("flush-concurrency").Uint(10, config.Gt[uint](0)))
		if err != nil {
			panic(c.BuildErr(err))
		}
		return store
	default:
		panic("unreachable")
	}
}

func (ct chainType[SLOT]) BuildDimension(ctx context.Context, network, name string) chain.Dimension[SLOT] {
	key := "dimension." + network + "." + name
	return getCacheRes(key, func() (dim chain.Dimension[SLOT]) {
		c := globalConf.Get(key)
		switch c.Get("type").String("", config.In("external", "segmented", "simple")) {
		case "external":
			dim = ct.ExtDimensionBuilder(ctx, c, name, network, ct.Networks[network])
		case "simple":
			dim = ct.BuildSimpleDimension(ctx, c, name, network)
		default:
			panic("unreachable")
		}
		InitDimension(ctx, c, network, ct.Networks[network].chainID, name, dim)
		return dim
	})
}

func (ct chainType[SLOT]) BuildChain(ctx context.Context, network, name string) chain.Chain[SLOT] {
	var ch chain.Chain[SLOT]
	dimNames := globalConf.Get("chain."+network+"."+name).StringArray(nil, config.SizeGt[string](0))
	for _, dimName := range dimNames {
		ch = append(ch, ct.BuildDimension(ctx, network, dimName))
	}
	return ch
}

func (ct chainType[SLOT]) BuildCopyDimensionTask(ctx context.Context, c config.Config, network string) Task {
	src := ct.BuildDimension(ctx, network, c.Get("source").String("", config.NotEmpty))
	dst := ct.BuildDimension(ctx, network, c.Get("destination").String("", config.NotEmpty))
	interval := BuildRange(c.Get("range"), 0, number.MaxNumber)
	overwrite := c.Get("overwrite").Bool(false)
	disorderedMode := c.Get("disordered-mode").Bool(false)
	return func(ctx context.Context) error {
		return chain.Copy(ctx, src, dst, interval, overwrite, disorderedMode)
	}
}

func (ct chainType[SLOT]) BuildCopyChainTask(ctx context.Context, c config.Config, network string) Task {
	src := ct.BuildChain(ctx, network, c.Get("source").String("", config.NotEmpty))
	dst := ct.BuildChain(ctx, network, c.Get("destination").String("", config.NotEmpty))
	interval := BuildRange(c.Get("range"), 0, number.MaxNumber)
	overwrite := c.Get("overwrite").Bool(false)
	disorderedMode := c.Get("disordered-mode").Bool(false)
	return func(ctx context.Context) error {
		return chain.CopyChain(ctx, src, dst, interval, overwrite, disorderedMode)
	}
}

func (ct chainType[SLOT]) BuildRepairDimensionTask(ctx context.Context, c config.Config, network string) Task {
	src := ct.BuildDimension(ctx, network, c.Get("source").String("", config.NotEmpty))
	dst := ct.BuildDimension(ctx, network, c.Get("destination").String("", config.NotEmpty))
	interval := BuildRange(c.Get("range"), 0, number.MaxNumber)
	disorderedMode := c.Get("disordered-mode").Bool(false)
	return func(ctx context.Context) error {
		return chain.Repair(ctx, src, dst, interval, disorderedMode)
	}
}

func (ct chainType[SLOT]) BuildRepairChainTask(ctx context.Context, c config.Config, network string) Task {
	src := ct.BuildChain(ctx, network, c.Get("source").String("", config.NotEmpty))
	dst := ct.BuildChain(ctx, network, c.Get("destination").String("", config.NotEmpty))
	interval := BuildRange(c.Get("range"), 0, number.MaxNumber)
	disorderedMode := c.Get("disordered-mode").Bool(false)
	return func(ctx context.Context) error {
		return chain.RepairChain(ctx, src, dst, interval, disorderedMode)
	}
}

func (ct chainType[SLOT]) BuildSyncChainTask(ctx context.Context, c config.Config, network string) Task {
	src := ct.BuildChain(ctx, network, c.Get("source").String("", config.NotEmpty))
	dst := ct.BuildChain(ctx, network, c.Get("destination").String("", config.NotEmpty))
	cutter := BuildRangeCutter(c, 0, false)
	syncTolerance := c.Get("sync-tolerance").Int64(int64(cutter.Size*3), config.Gt[int64](0))
	_, err := meter.RegisterCallback(func(ctx context.Context, o metric.Observer) error {
		o.ObserveInt64(
			syncToleranceGauge,
			syncTolerance,
			metric.WithAttributes(attribute.String("network", network)),
		)
		return nil
	}, syncToleranceGauge)
	if err != nil {
		panic(c.BuildErr(fmt.Errorf("register async metric callback failed: %w", err)))
	}
	backfillThreshold := c.Get("backfill.threshold").Uint64(math.MaxUint64)
	var backfillSrc chain.Chain[SLOT]
	var backfillWait time.Duration
	if backfillThreshold < math.MaxUint64 {
		backfillSrc = ct.BuildChain(ctx, network, c.Get("backfill.source").String("", config.NotEmpty))
		backfillWait = c.Get("backfull.wait").Duration(time.Second*3, config.Gt[time.Duration](0))
	}
	var repairSrc chain.Chain[SLOT]
	if repairSrcName := c.Get("repair.source").String(""); repairSrcName != "" {
		repairSrc = ct.BuildChain(ctx, network, repairSrcName)
	}

	compactionEnabled := c.Get("compaction.enabled").Bool(false)
	var compactionConfig *chain.CompactionConfig
	if compactionEnabled {
		compactionWaitSlots := c.Get("compaction.wait").Uint64(cutter.Size * 10) // do not compact latest 10 sectors
		maxSectors := c.Get("compaction.max-sectors").Int(math.MaxInt, config.Gt[int](0))
		targetSectorSize := c.Get("compaction.target-sector-size").Uint64(
			512*1024*1024, config.Gt[uint64](0))
		sizeRatioAfterCompaction := c.Get("compaction.size-ratio-after-compaction").Float64(
			1.0, config.Gt[float64](0.0))
		maxLookBackSpan := c.Get("compaction.max-look-back-span").Uint64(0)
		purgeWait := c.Get("compaction.purge-wait").Uint64(10000)
		compactionConfig = &chain.CompactionConfig{
			CompactionWaitSlots:      number.Number(compactionWaitSlots),
			MaxSectorsToCompact:      maxSectors,
			TargetSectorSize:         targetSectorSize,
			SizeRatioAfterCompaction: sizeRatioAfterCompaction,
			MaxLookBackSpan:          number.Number(maxLookBackSpan),
			PurgeWaitSlots:           number.Number(purgeWait),
		}
	}
	disorderedMode := c.Get("disordered-mode").Bool(false)
	return func(ctx context.Context) error {
		return chain.SmartSyncChain(
			ctx,
			src,
			dst,
			cutter,
			repairSrc,
			backfillSrc,
			backfillThreshold,
			backfillWait,
			compactionConfig,
			disorderedMode)
	}
}

type ObserverClient interface {
	GetObserverRange(ctx context.Context) (number.Range, error)
}

func (ct chainType[SLOT]) BuildObserver(ctx context.Context, network, dimension string) Task {
	const observerInterval = time.Minute
	networkOpt := ct.GetNetworkOptions()[network]
	nodeClient := BuildNodeClient(ctx, network, dimension, func(endpoint, wssEndpoint string) (node.ChainAdapter, error) {
		return ct.ChainAdapterBuilder(networkOpt, endpoint, wssEndpoint)
	}, func(clientConfig *node.SingleClientConfig) {
		clientConfig.KeepWatchAuto = false
		clientConfig.KeepWatch = observerInterval
		clientConfig.WSSEndpoint = "" // do not use subscribe mode
	})
	var observerClient ObserverClient
	if ct.ObserverClientBuilder != nil {
		observerClient = ct.ObserverClientBuilder(nodeClient)
	}
	chainID := ct.Networks[network].chainID
	fallBehindTimeThreshold := ct.Networks[network].fallBehindTimeThreshold
	return func(ctx context.Context) error {
		_, logger := log.FromContext(ctx, "network", network, "dimension", dimension)
		ticker := time.NewTicker(observerInterval)
		defer ticker.Stop()
		var obsLatest number.Range
		var dimLatest uint64
		for {
			getCtx, cancel := context.WithTimeout(ctx, time.Second*3)
			// get block interval
			bi := nodeClient.BlockInterval()
			if bi.Interval > 0 {
				fallBehindThresholdGauge.Record(ctx, int64(fallBehindTimeThreshold/bi.Interval), metric.WithAttributes(
					attribute.String("network", network),
					attribute.String("dimension", dimension),
					attribute.String("chainID", chainID)))
			}
			// record dimension latest, used for our owned alert
			if cur, err := nodeClient.BlockNumber(getCtx); err != nil {
				logger.Errorfe(err, "get range for observer range gauge failed")
			} else {
				dimLatest = cur
			}
			dimensionRangeGauge.Record(ctx, int64(dimLatest), metric.WithAttributes(
				attribute.String("network", network),
				attribute.String("dimension", dimension),
				attribute.String("chainID", chainID),
				attribute.String("side", "right")))
			// record observer latest, used for user
			if observerClient != nil {
				if cur, err := observerClient.GetObserverRange(getCtx); err != nil {
					logger.Errorfe(err, "get range for observer range gauge failed")
				} else {
					obsLatest = cur
				}
			} else {
				obsLatest = number.NewRange(0, number.Number(dimLatest))
			}
			observerRangeGauge.Record(ctx, int64(obsLatest.R()), metric.WithAttributes(
				attribute.String("network", network),
				attribute.String("dimension", dimension),
				attribute.String("chainID", chainID),
				attribute.String("side", "right")))
			// metrics recorded
			cancel()
			select {
			case <-ticker.C:
			case <-ctx.Done():
				return ctx.Err()
			}
		}
	}
}

func (ct chainType[SLOT]) GetNetworkOptions() map[string]NetworkOptions {
	return ct.Networks
}

// ========================================
// data part
// ========================================

var (
	chainTypeAptos = &chainType[*aptos.Slot]{
		Name:                       "aptos2",
		ExtDimensionBuilder:        BuildAptosExtDimension,
		ClickhouseSchemaMgrBuilder: BuildAptosClickhouseSchemaMgr,
		ChainAdapterBuilder: func(options NetworkOptions, endpoint, wssEndpoint string) (node.ChainAdapter, error) {
			return aptos.AdapterBuilder(endpoint, wssEndpoint)
		},
		ObserverClientBuilder: func(nodeClient node.NodeClient) ObserverClient {
			return &aptos.Client{NodeClient: nodeClient}
		},
		Networks: make(map[string]NetworkOptions),
	}

	chainTypeEvm = &chainType[*evm.Slot]{
		Name:                       "evm",
		ExtDimensionBuilder:        BuildEvmExpDimension,
		ClickhouseSchemaMgrBuilder: BuildEvmClickhouseSchemaMgr,
		ChainAdapterBuilder: func(options NetworkOptions, endpoint, wssEndpoint string) (node.ChainAdapter, error) {
			return evm.NewAdapterBuilder(options.chainID)(endpoint, wssEndpoint)
		},
		Networks: make(map[string]NetworkOptions),
	}

	chainTypeSui = &chainType[*sui.Slot]{
		Name:                       "sui",
		ExtDimensionBuilder:        BuildSuiExpDimension,
		ClickhouseSchemaMgrBuilder: BuildSuiClickhouseSchemaMgr,
		ChainAdapterBuilder: func(options NetworkOptions, endpoint, wssEndpoint string) (node.ChainAdapter, error) {
			return sui.NewAdapterBuilder(options.chainID)(endpoint, wssEndpoint)
		},
		Networks: make(map[string]NetworkOptions),
	}

	chainTypeFuel = &chainType[*fuel.Slot]{
		Name:                       "fuel",
		ExtDimensionBuilder:        BuildFuelExtDimension,
		ClickhouseSchemaMgrBuilder: BuildFuelClickhouseSchemaMgr,
		ChainAdapterBuilder: func(options NetworkOptions, endpoint, wssEndpoint string) (node.ChainAdapter, error) {
			return fuel.AdapterBuilder(endpoint, wssEndpoint)
		},
		Networks: make(map[string]NetworkOptions),
	}

	chainTypeSol = &chainType[*evm.Slot]{
		Name: "sol",
		ChainAdapterBuilder: func(options NetworkOptions, endpoint, wssEndpoint string) (node.ChainAdapter, error) {
			return sol.AdapterBuilder(endpoint, wssEndpoint)
		},
		Networks: make(map[string]NetworkOptions),
	}

	//chainTypeBTC = &chainType[*btc.Slot, *rpcclient.Client]{
	//	Name:                       "btc",
	//	ExtDimensionBuilder:        BuildBTCExtDimension,
	//	ClickhouseSchemaMgrBuilder: BuildBTCClickhouseSchemaMgr,
	//	Networks:                   make(map[string]NetworkOptions),
	//}

	allChainTypes = []ChainType{
		chainTypeAptos,
		chainTypeEvm,
		chainTypeSui,
		chainTypeFuel,
		chainTypeSol,
		//chainTypeBTC,
	}
)

func InitNetworks(c *config.Config) {
	for _, ct := range allChainTypes {
		networks := c.Get("network." + ct.GetName()).Array()
		for _, network := range networks {
			name := network.Get("name").String("", config.NotEmpty)
			options := NetworkOptions{
				chainID:                 network.Get("chain-id").String(""),
				fallBehindThreshold:     network.Get("fall-behind-threshold").Uint64(300),
				fallBehindTimeThreshold: network.Get("fall-behind-time-threshold").Duration(time.Minute * 3),
				evm: &evm.NetworkOptions{
					DisableTrace:             network.Get("disable-evm-trace").Bool(false),
					EnableReceipt:            network.Get("enable-evm-receipt").Bool(false),
					UseGethTrace:             network.Get("use-geth-trace").Bool(false),
					IgnoreExtraTraces:        network.Get("ignore-extra-traces").Bool(false),
					IgnoreMissTraces:         network.Get("ignore-miss-traces").Bool(false),
					UseGetTransactionReceipt: network.Get("use-get-transaction-receipt").Bool(false),
					ArbClassicMaxBlockNumber: network.Get("arb-classic-max-block-number").Uint64(0),
				},
			}
			ct.GetNetworkOptions()[name] = options
		}
	}
}

func GetChainType(c config.Config) ChainType {
	s := c.String("")
	for _, ct := range allChainTypes {
		if ct.GetName() == s {
			return ct
		}
	}
	panic(c.BuildErr(fmt.Errorf("unknown chain type %q", s)))
}

func FindChainTypeByNetwork(network string) ChainType {
	for _, ct := range allChainTypes {
		if ct.HasNetwork(network) == nil {
			return ct
		}
	}
	panic(fmt.Errorf("unknown network %q", network))
}

func HasNetwork(network string) error {
	for _, ct := range allChainTypes {
		if ct.HasNetwork(network) == nil {
			return nil
		}
	}
	return fmt.Errorf("unknown network %q", network)
}

func GetNetworkOptions(network string) NetworkOptions {
	for _, ct := range allChainTypes {
		if ct.HasNetwork(network) == nil {
			return ct.GetNetworkOptions()[network]
		}
	}
	panic(fmt.Errorf("unknown network %q", network))
}
