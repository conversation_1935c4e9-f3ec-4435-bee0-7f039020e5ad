package launcher

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"sentioxyz/sentio/common/config"
	"sentioxyz/sentio/common/hypertable"
	"sentioxyz/sentio/common/period"
	"sentioxyz/sentio/common/utils"
	"sentioxyz/sentio/service/common/protos"
	"sentioxyz/sentio/service/usage/store"
	"sentioxyz/sentio/service/usage/store/models"
)

const (
	LongBefore = "1970-01-01T00:00:00Z"
	LongAfter  = "2100-01-01T00:00:00Z"
)

func BuildUsageStore(ctx context.Context, name string) *store.StdStorage {
	buildAggregateConfig := func(c config.Config) hypertable.AggregateConfig {
		return hypertable.AggregateConfig{
			AggregatePeriod:    c.Get("aggregate-period").Period(period.Zero, config.NotZero),
			RefreshStartOffset: c.Get("refresh-start-offset").Period(period.Zero, config.NotZero),
			RefreshEndOffset:   c.Get("refresh-end-offset").Period(period.Zero),
			CompressAfter:      c.Get("compress-after").Period(period.Zero),
			DropAfter:          c.Get("drop-after").Period(period.Zero),
		}
	}

	return getCacheRes("usage-store."+name, func() *store.StdStorage {
		c := globalConf.Get("usage-store." + name)
		defaultLimiters := make(map[int32][]models.Limiter)
		limitersConf := c.Get("default-limiters")
		for tierStr := range limitersConf.Map(nil) {
			tierConf := limitersConf.Get(tierStr)
			tier, has := protos.Tier_value[tierStr]
			if !has {
				panic(tierConf.BuildErr(fmt.Errorf("unknown tier %q, valid tier: %v",
					tierStr, utils.GetOrderedMapKeys(protos.Tier_value))))
			}
			for _, limiterConf := range tierConf.Array() {
				defaultLimiters[tier] = append(defaultLimiters[tier], models.Limiter{
					Category:      fmt.Sprintf("%s_OWNER_DEFAULT_LIMITER", tierStr),
					EffectiveTime: limiterConf.Get("effective-time").Time(time.RFC3339, LongBefore),
					ExpireTime:    limiterConf.Get("expire-time").Time(time.RFC3339, LongAfter),
					LimiterTarget: models.LimiterTarget{
						ProjectID: limiterConf.Get("projectID").String(""),
						SKU:       limiterConf.Get("sku").String(""),
					},
					Period:  limiterConf.Get("period").Period(period.Zero, config.NotZero),
					Cost:    limiterConf.Get("cost").Uint64(0),
					Succeed: limiterConf.Get("total").Uint64(0),
				})
			}
		}

		var aggregatedConfigs []hypertable.AggregateConfig
		for _, aggrConf := range c.Get("aggregated-configs").Array() {
			aggregatedConfigs = append(aggregatedConfigs, buildAggregateConfig(aggrConf))
		}
		if len(aggregatedConfigs) == 0 {
			// at least have one period
			aggregatedConfigs = append(aggregatedConfigs, hypertable.AggregateConfig{
				AggregatePeriod:    period.Day,
				RefreshStartOffset: period.Day,
			})
		}

		var redisClient *redis.Client
		redisAddrConf := c.Get("basic-redis")
		redisAddr := redisAddrConf.String("")
		if redisAddr != "" {
			redisClient = redis.NewClient(&redis.Options{
				Addr:     redisAddr,
				PoolSize: c.Get("basic-redis-pool-size").Int(100),
				DB:       c.Get("basic-redis-db").Int(0),
			})
		}

		var deepStore store.DeepStorage
		switch c.Get("type").String("tsdb", config.In("tsdb", "clickhouse")) {
		case "clickhouse":
			var err error
			deepStore, err = store.NewClickhouseStorage(
				ctx,
				c.Get("dialogue-dsn").String("", config.NotEmpty),
			)
			if err != nil {
				panic(c.BuildErr(fmt.Errorf("create usage deep storage failed: %w", err)))
			}
		case "tsdb":
			var err error
			deepStore, err = store.NewTSDBStore(
				c.Get("dialogue-db-url").String("", config.NotEmpty),
				c.Get("dialogue-chunk-time-interval").Period(period.Day),
				c.Get("dialogue-retention-period").Period(period.Day.Multi(3)),
				aggregatedConfigs,
			)
			if err != nil {
				panic(c.BuildErr(fmt.Errorf("create usage deep storage failed: %w", err)))
			}
		}

		gwStore, err := store.NewStdStorage(
			c.Get("db-url").String("", config.NotEmpty),
			c.Get("basic-cache-expiration").Duration(time.Minute, config.Gt[time.Duration](time.Second)),
			redisClient,
			c.Get("flush-period").Duration(time.Second*30, config.Gt[time.Duration](0)),
			c.Get("flush-size").Int(1000, config.Gt(0)),
			defaultLimiters,
			c.Get("debug").Bool(false),
			deepStore)
		if err != nil {
			panic(c.BuildErr(fmt.Errorf("create usage storage failed: %w", err)))
		}
		go gwStore.Flush(ctx)
		return gwStore
	})
}
