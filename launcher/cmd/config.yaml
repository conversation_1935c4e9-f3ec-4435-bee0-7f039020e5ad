network:
  aptos2:
    - name: aptos-mainnet
      fall-behind-threshold: 1000
      chain-id: 'aptos_mainnet'
    - name: aptos-testnet
      fall-behind-threshold: 10000
      chain-id: 'aptos_testnet'
  evm:
    - name: eth-mainnet
      enable-evm-receipt: true
      chain-id: '1'
    - name: opt-mainnet
      disable-evm-trace: true
      chain-id: '10'
    - name: linea-mainnet
      disable-evm-trace: true
      chain-id: '59144'
    - name: merlin-mainnet
      disable-evm-trace: true
      chain-id: '4200'
    - name: moonbeam
      enable-evm-receipt: true
      chain-id: '1284'
    - name: bsc-mainnet
      enable-evm-receipt: true
      chain-id: '56'
    - name: berachain
      enable-evm-receipt: true
      chain-id: '80094'
    - name: cronos-zkevm
      enable-evm-receipt: true
      use-geth-trace: true
      use-get-transaction-receipt: true
      chain-id: '388'
    - name: zircuit-mainnet
      enable-evm-receipt: true
      use-geth-trace: true
      use-get-transaction-receipt: true
      chain-id: '48900'
  sui:
    - name: sui-mainnet
      chain-id: 'sui_mainnet'
  fuel:
    - name: fuel-mainnet
      chain-id: 'fuel_mainnet'
    - name: fuel-testnet
      chain-id: 'fuel_testnet'
  btc:
    - name: btc-mainnet
      chain-id: 'btc_mainnet'
  sol:
    - name: sol-mainnet
      chain-id: 'sol_mainnet'
    - name: sol-pyth
      chain-id: 'sol_pyth'

endpoint-pool:
  aptos-mainnet-node:
    - url: https://api.mainnet.aptoslabs.com
      qps: 1
  aptos-mainnet:
    - url: http://nodes.sea.sentio.xyz/aptos-mainnet
      qps: 100
  aptos-mainnet-lb:
    - url: http://127.0.0.1:8081
  aptos-testnet:
    - url: https://fullnode.testnet.aptoslabs.com
      qps: 1
  coingecko:
    - url: https://pro-api.coingecko.com
      qps: 1
  coingecko-free:
    - url: https://api.coingecko.com
      qps: 1
  eth-mainnet:
    - url: http://127.0.0.1:8545/
      qps: 10
  cronos-zkevm-ext:
    - url: http://127.0.0.1:8080/cronos-zkevm/
      qps: 5
  cronos-zkevm-public:
    - url: https://mainnet.zkevm.cronos.org
      qps: 3
  moonbeam-ext:
    - url: http://nodes.sea.sentio.xyz/moonbeam
      qps: 1
  eth-mainnet-ext:
    #- url: http://sentio-0.sentio.xyz/sentio-internal-api/ethereum/
    #  qps: 1
    #- url: http://sentio-0.sentio.xyz/sentio-internal-api/ethereumx/
    #  qps: 1
    - url: http://nodes.sea.sentio.xyz/ethereum
      qps: 10
  eth-mainnet-ext2:
    - url: https://eth-mainnet.alchemyapi.io/v2/BJQpOPPKYT7mr3X6iRL3IawOJPnyiG-P
      qps: 1
  eth-mainnet-node:
    - url: http://sentio-0.sentio.xyz/sentio-internal-api/ethereum/
      qps: 1000
  eth-mainnet-local:
    #- url: http://mainnet-erigon-2.nodes:8545/
    #    - url: http://127.0.0.1:8545/
    - url: https://astar.api.onfinality.io/rpc?apikey=e2d7d4d9-36d4-4c84-87a0-9b8ad822b229
  opt-mainnet-ext:
    - url: https://opt-mainnet.g.alchemy.com/v2/********************************
      qps: 5
  zircuit-mainnet-ext:
    - url: http://nodes.sea.sentio.xyz/zircuit-mainnet
      qps: 5
  sui-testnet:
    #- url: http://127.0.0.1:9001
    - url: https://rpc-testnet.suiscan.xyz
  sui-mainnet-ext:
    - url: http://nodes.sea.sentio.xyz/sui-mainnet
      qps: 100
    - url: http://nodes.sea.sentio.xyz/sui-mainnet-2
      qps: 100
    #- url: https://rpc-mainnet.suiscan.xyz
  linea-mainnet:
    - url: https://linea-mainnet.infura.io/v3/********************************
      qps: 1
    - url: https://linea-mainnet.unifra.io/v1/07390cca6ab24f8a964b68bd90fb5f4b
      qps: 5
  fuel-testnet-ext:
    - url: https://testnet.fuel.network/v1/graphql
      qps: 5
    #- url: http://nodes.sea.sentio.xyz/fuel-testnet/v1/graphql
    #  qps: 10
  merlin-mainnet-node:
    - url: http://nodes.sea.sentio.xyz/merlin-mainnet
      qps: 10
  merlin-mainnet-external:
    - url: https://rpc.merlinchain.io
      qps: 1
    - url: https://merlin.blockpi.network/v1/rpc/public
      qps: 1
  btc-mainnet-external:
    - url: https://bitcoin-mainnet.public.blastapi.io
      qps: 5

redis:
  local:
    address: 127.0.0.1:6379
    db: 0

chain:
  aptos-mainnet:
    external:
      - external
    simple-clickhouse:
      - simple-clickhouse
  btc-mainnet:
    external:
      - external
    clickhouse:
      - clickhouse
  eth-mainnet:
    external:
      - external
    simple-clickhouse:
      - simple-clickhouse
  cronos-zkevm:
    external:
      - external
    clickhouse:
      - clickhouse
  zircuit-mainnet:
    external:
      - external
    clickhouse:
      - clickhouse
  sui-mainnet:
    external:
      - external
    simple-clickhouse:
      - simple-clickhouse
  fuel-mainnet:
    external:
      - external
    simple-clickhouse:
      - simple-clickhouse
  fuel-testnet:
    external:
      - external
    simple-clickhouse:
      - simple-clickhouse

node-client:
  eth-mainnet:
    lb:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: test
      name: endpoints-sentio-config
      key: eth-mainnet-lb
  bsc-mainnet:
    lb:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: prod
      name: endpoints-sentio-config
      key: bsc-mainnet-lb
  berachain:
    external-free:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: test
      name: endpoints-sentio-config
      key: berachain-external-free
  sui-mainnet:
    lb:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: test
      name: endpoints-sentio-config
      key: sui-mainnet-lb
  aptos-mainnet:
    lb:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: test
      name: endpoints-sentio-config
      key: aptos-mainnet-lb
  fuel-testnet:
    lb:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: test
      name: endpoints-sentio-config
      key: fuel-testnet-lb
  fuel-mainnet:
    lb:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: test
      name: endpoints-sentio-config
      key: fuel-mainnet-lb
  sol-mainnet:
    lb:
      type: configMap
      kube-config-file: /Users/<USER>/.kube/config
      namespace: test
      name: endpoints-sentio-config
      key: sol-mainnet-lb

dimension:
  btc-mainnet:
    external:
      type: external
      load-concurrency: 1
      load-batch-size: 1
      load-thinning-slots: true
      #valid-number-range:
      #  start: 857880
      endpoint-pool: btc-mainnet-external
    clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9000/my_database?dial_timeout=10s'
        table: btc.range
        cluster: ''
        engine: ''
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9000/my_database?dial_timeout=10s'
        table: btc.blocks,btc.transactions,btc.inputs,btc.outputs
        flush-batch-size: 10
        block-partition-size: 10000
        cluster: ''
        clickhouse-schema-mgr:
          engine: ''
  eth-mainnet:
    external:
      type: external
      load-concurrency: 10
      valid-number-range:
        start: 18000000
        end: 18000100
      client: lb
    simple-clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table: ethereum.range
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table-prefix: ethereum
        flush-batch-size: 10
        slow-flush-threshold: 500ms
        block-partition-size: 500000
  cronos-zkevm:
    external:
      type: external
      load-concurrency: 10
      load-batch-size: 1
      valid-number-range:
        start: 1290
        end: 1299
      max-retry: 20
      wait-retry-interval: 3s
      endpoint-pool: cronos-zkevm-ext
    clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9000/my_database?dial_timeout=10s'
        cluster: ''
        engine: ''
        table: cronos-zkevm.range
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9000/my_database?dial_timeout=10s'
        cluster: ''
        table: cronos-zkevm.blocks,cronos-zkevm.transactions,cronos-zkevm.logs,cronos-zkevm.traces,cronos-zkevm.withdrawals
        flush-batch-size: 10
        slot-version: v2
        clickhouse-schema-mgr:
          engine: ''
  zircuit-mainnet:
    external:
      type: external
      load-concurrency: 10
      load-batch-size: 1
      valid-number-range:
        start: 6587400
        end: 6587499
      max-retry: 20
      wait-retry-interval: 3s
      endpoint-pool: zircuit-mainnet-ext
    clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9000/my_database?dial_timeout=10s'
        cluster: ''
        engine: ''
        table: zircuit.range
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9000/my_database?dial_timeout=10s'
        cluster: ''
        table: zircuit.blocks,zircuit.transactions,zircuit.logs,zircuit.traces,zircuit.withdrawals
        flush-batch-size: 10
        slot-version: v2
        clickhouse-schema-mgr:
          engine: ''
  aptos-mainnet:
    external:
      type: external
      load-concurrency: 10
      valid-number-range:
        start: 252000000
        end: 252002000
      client: lb
    simple-clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table: aptos.range
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table-prefix: aptos
        flush-batch-size: 1000
  sui-mainnet:
    external:
      type: external
      load-concurrency: 10
      valid-number-range:
        #end: 1000
        start: 68054600
        end: 68055599
      client: lb
    simple-clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table: sui.v4.range
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table-prefix: sui.v4
        flush-batch-size: 10
        slot-version: v3
        clickhouse-schema-mgr:
          client: lb
  fuel-mainnet:
    external:
      type: external
      load-concurrency: 10
      load-batch-size: 10
      valid-number-range:
        start: 19087800
        end: 19088200
      client: lb
    simple-clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table: fuel-mainnet.range
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table-prefix: fuel-mainnet
        flush-batch-size: 10
        slot-version: v2
  fuel-testnet:
    external:
      type: external
      load-concurrency: 10
      load-batch-size: 10
      valid-number-range:
        start: 5404300
        end: 5404399
        #start: 558100
        #start: 10376100
        #start: 3799870
      wait-retry-interval: 3s
      endpoint-pool: fuel-testnet-ext
      client: lb
    simple-clickhouse:
      type: simple
      range-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table: fuel-testnet.range
      slot-store:
        driver: clickhouse
        dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
        table-prefix: fuel-testnet
        flush-batch-size: 10
        slot-version: v2

task:
  solidity:
    sync-contract-ethereum-mainnet:
      type: sync-contract-compilations
      gcs-bucket: sentio-solidity-data-dev
      contract-list-file: contract_list/ethereum_mainnet_contracts.json
      external-debugger-server: :4000
      syncer-total: 1
      syncer-index: 0
      disable-optimizer: true
      num-workers: 4

    sync-contract-goerli-testnet:
      type: sync-contract-compilations
      gcs-bucket: sentio-solidity-data-dev
      contract-list-file: contract_list/goerli_testnet_contracts_test.json
      external-debugger-server: :4000
      syncer-total: 1
      syncer-index: 0
      disable-optimizer: true
      num-workers: 4

    sync-contract-polygon-mainnet:
      type: sync-contract-compilations
      gcs-bucket: sentio-solidity-data-dev
      contract-list-file: contract_list/polygon_mainnet_contracts_test.json
      external-debugger-server: :4000
      syncer-total: 1
      syncer-index: 0
      disable-optimizer: true
      num-workers: 4
  btc-mainnet:
    sync-ext-to-clickhouse:
      type: sync-chain
      source: external
      destination: clickhouse
      step:
        size: 1
        align-zero: true
      backfill:
        threshold: 1000
        source: external
      repair:
        source: external
  eth-mainnet:
    sync-ext-to-simple-clickhouse:
      type: sync-chain
      source: external
      destination: simple-clickhouse
      step:
        size: 10
        align-zero: true
      backfill:
        threshold: 100
        source: external
      repair:
        source: external
  cronos-zkevm:
    sync-ext-to-clickhouse:
      type: sync-chain
      source: external
      destination: clickhouse
      step:
        size: 10
        align-zero: true
      backfill:
        threshold: 100
        source: external
      repair:
        source: external
  zircuit-mainnet:
    sync-ext-to-clickhouse:
      type: sync-chain
      source: external
      destination: clickhouse
      step:
        size: 10
        align-zero: true
      backfill:
        threshold: 100
        source: external
      repair:
        source: external
  aptos-mainnet:
    sync-ext-to-simple-clickhouse:
      type: sync-chain
      source: external
      destination: simple-clickhouse
      step:
        size: 10
        align-zero: true
      backfill:
        threshold: 50
        source: external
      repair:
        source: external
  sui-mainnet:
    sync-ext-to-simple-clickhouse:
      type: sync-chain
      source: external
      destination: simple-clickhouse
      step:
        size: 10
        align-zero: true
      backfill:
        threshold: 50
        source: external
      repair:
        source: external
  fuel-testnet:
    sync-ext-to-simple-clickhouse:
      type: sync-chain
      source: external
      destination: simple-clickhouse
      step:
        size: 10
        align-zero: true
      backfill:
        threshold: 50
        source: external
      repair:
        source: external

  inscription-indexer:
    eth-mainnet:
      type: inscription-indexer
      cacheDataDir: /tmp/inscription-validator
      chainConfigFile: /Users/<USER>/Projects/github.com/sentioxyz/sentio/launcher/cmd/.temp/inscriptionConfig.yaml

proxy-recorder:
  usage:
    usage-server: localhost:10010
    processor-server: localhost:10010

usage-store:
  std:
    db-url: 'postgres://postgres:postgres@localhost:5432/postgres'
    dialogue-db-url: 'postgres://postgres:postgres@127.0.0.1:5433/postgres'
    dialogue-chunk-time-interval: 1d
    dialogue-retention-period: 3d
    flush-period: 5s
    flush-size: 1000
    debug: true
    default-limiters:
      FREE:
        - cost: 10
          period: 1m
        - cost: 20
          period: 3m
        - sku: foo
          cost: 30
          period: 1m
      DEV:
        - cost: 200
          period: 1m
        - cost: 400
          period: 3m
        - cost: 200
          period: 1m
      PRO: []
      ENTERPRISE: []
    aggregated-configs:
      - aggregate-period: 1m
        refresh-start-offset: 2h
        drop-after: 1w
      - aggregate-period: 1h
        refresh-start-offset: 1d
        drop-after: 1month
      - aggregate-period: 1d
        refresh-start-offset: 1d
    skus:
      - name: composer
        cost: 10
      - name: foo
        cost: 5

server:
  coingecko-proxy-v2:
    type: proxy-v2
    listen: ':8647'
    forwarder: coingecko-free
    storage: std
    interface:
      - meta: coingecko-CoinHistory
      - meta: coingecko-SimplePrice

  btc-mainnet-rpc-v2:
    type: json-rpc-v2
    listen: 18332
    debug: false
    service:
      - type: btc
        network: btc-mainnet
        network-id: '1'
        cache:
          persistent-dimension: external
          size: 1
          sector-size: 5
          keep-growth-interval: 5s
        #          path: /tmp/eth-mainnet-cache
        clickhouse:
          dsn: 'clickhouse://sentio:2vwzZBJ6cbZbyoKm4j@127.0.0.1:19000/chain_db?dial_timeout=10s'
          table: bitcoin.blocks,bitcoin.transactions,bitcoin.inputs,bitcoin.outputs
          range-store:
            driver: clickhouse
            dsn: 'clickhouse://sentio:2vwzZBJ6cbZbyoKm4j@127.0.0.1:19000/chain_db?dial_timeout=10s'
            table: bitcoin.range
        proxy:
          endpoint:
            - url: http://localhost:8332
              qps: 1000
          cache:
            enable: true
            path: /tmp/eth-mainnet-proxy
            clean-interval: 10s

  aptos-mainnet-super-node:
    type: json-rpc-v2
    listen: 18081
    debug: false
    service:
      - type: aptos2
        network: aptos-mainnet
        client: lb
        cache:
          persistent-dimension: external
          size: 10
          sector-size: 5
          keep-growth-interval: 5s
          keep-dump-interval: 8s
          redis: local
          redis-token: aptos-mainnet
        clickhouse:
          dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
          table: aptos-mainnet.blocks,aptos-mainnet.transactions,aptos-mainnet.events,aptos-mainnet.changes,aptos-mainnet.resources,aptos-mainnet.modules,aptos-mainnet.table_items
          table-prefix: aptos
          range-store:
            driver: clickhouse
            dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
            table: aptos.range
        proxy:
          cache:
            enable: false

  aptos-testnet-rpc-v2:
    type: json-rpc-v2
    listen: 18081
    debug: false
    service:
      - type: aptos2
        network: aptos-testnet
        cache:
          persistent-dimension: external
          size: 10
          sector-size: 5
          keep-growth-interval: 5s
          path: /tmp/aptos2-testnet-cache
        clickhouse:
          dsn: 'clickhouse://sentio:2vwzZBJ6cbZbyoKm4j@127.0.0.1:19000/chain_db?dial_timeout=10s'
          table: aptos-testnet.blocks,aptos-testnet.transactions,aptos-testnet.events,aptos-testnet.changes,aptos-testnet.resources,aptos-testnet.modules,aptos-testnet.table_items
          range-store:
            driver: clickhouse
            dsn: 'clickhouse://sentio:2vwzZBJ6cbZbyoKm4j@127.0.0.1:19000/chain_db?dial_timeout=10s'
            table: aptos-testnet.range
        proxy:
          endpoint:
            - url: http://localhost:28080
              qps: 1000
          cache:
            enable: true
            path: /tmp/aptos-proxy
            clean-interval: 10s

  sui-mainnet-super-node:
    type: json-rpc-v2
    listen: 8546
    debug: true
    service:
      - type: sui
        network: sui-mainnet
        network-id: 'sui-mainnet'
        client: lb
        cache:
          persistent-dimension: external
          size: 1
          sector-size: 5
          keep-growth-interval: 5s
        slot-version: v3
        clickhouse:
          dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
          table-prefix: sui.v4
          range-store:
            driver: clickhouse
            dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
            table: sui.v4.range
          settings:
            use_query_cache: 1
        proxy:
          cache:
            enable: false
          cache-methods:
            - sui_getTransactionBlock
            - sui_tryMultiGetPastObjects
            - suix_getCoinMetadata

  eth-mainnet-super-node:
    type: json-rpc-v2
    listen: 8545
    service:
      - type: evm
        network: eth-mainnet
        network-id: '1'
        client: lb
        # proxy-only: true
        cache:
          persistent-dimension: external
          size: 1
          sector-size: 1
          keep-growth-interval: 5s
          keep-dump-interval: 10s
          #redis: local
        clickhouse:
          dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
          connect-options:
            max_open_conns: 100
            max_idle_conns: 30
            conn_max_lifetime: 300s
            settings:
              max_execution_time: 600
              use_query_cache: 1
              query_cache_ttl: 300
          table-prefix: ethereum
          range-store:
            driver: clickhouse
            dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
            table: ethereum.range
            read-only: true
        proxy:
          cache:
            enable: false
          forced-proxy-methods:
            - eth_getBlockByNumber
            - eth_getBlockByHash

  bsc-mainnet-super-node:
    type: json-rpc-v2
    listen: 8545
    service:
      - type: evm
        network: bsc-mainnet
        client: lb
        proxy-only: true
        proxy:
          cache:
            enable: false
          forced-proxy-methods:
            - eth_getBlockByNumber
            - eth_getBlockByHash

  fuel-mainnet-super-node:
    type: json-rpc-v2
    listen: 9005
    service:
      - type: fuel
        network: fuel-mainnet
        client: lb
        cache:
          size: 10
          sector-size: 2
          persistent-dimension: external
        clickhouse:
          dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
          table-prefix: fuel-mainnet
          range-store:
            driver: clickhouse
            dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
            table: fuel-mainnet.range
            read-only: true
        proxy:
          cache:
            enable: false

  fuel-testnet-super-node:
    type: json-rpc-v2
    listen: 9004
    service:
      - type: fuel
        network: fuel-testnet
        client: lb
        cache:
          size: 10
          sector-size: 2
          persistent-dimension: external
          keep-growth-interval: 5s
          keep-dump-interval: 30s
        clickhouse:
          dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
          connect-options:
            max_open_conns: 100
            max_idle_conns: 30
            conn_max_lifetime: 300s
            settings:
              max_execution_time: 600
              use_query_cache: 1
              query_cache_ttl: 300
          table-prefix: fuel-testnet
          range-store:
            driver: clickhouse
            dsn: 'clickhouse://default:password@127.0.0.1:9011/my_database?dial_timeout=10s'
            table: fuel-testnet.range
            read-only: true
        proxy:
          cache:
            enable: false

  sol-mainnet-super-node:
    type: json-rpc-v2
    listen: 8080
    service:
      - type: sol
        network: sol-mainnet
        client: lb
        proxy-only: true
        proxy:
          client: lb
          cache:
            enable: false

  usage-server:
    type: grpc
    listen: ':10090'
    enable-http: true
    enable-tls: true
    service:
      - type: UsageService
        store: std
        auth:
          db-url: 'postgres://postgres:postgres@localhost:5432/postgres'
          issuer-url: https://sentio-dev.us.auth0.com/
          audience: http://localhost:8080/v1
          #client-id: JREam3EysMTM49eFbAjNK02OCykpmda3
          #redis: localhost:6379

  sysstatus-server:
    type: grpc
    listen: ':10010'
    service:
      - type: SysStatusService
        namespace: test
        auth:
          db-url: 'postgres://postgres:postgres@localhost:5432/postgres'
          issuer-url: https://sentio-dev.us.auth0.com/
          audience: http://localhost:8080/v1
          #client-id: JREam3EysMTM49eFbAjNK02OCykpmda3
          #redis: localhost:6379
        commonConfigFile: /Users/<USER>/Projects/github.com/sentioxyz/sentio/launcher/cmd/.temp/commonConfig.yaml
        driverConfigFile: /Users/<USER>/Projects/github.com/sentioxyz/sentio/launcher/cmd/.temp/driverConfig.yaml
        solidityConfigFile: /Users/<USER>/Projects/github.com/sentioxyz/sentio/launcher/cmd/.temp/solidityConfig.yaml

  driver-job-controller:
    type: driver-controller
    processor-server: localhost:10020
    k8s-clusters-file: /Users/<USER>/Projects/github.com/sentioxyz/sentio/launcher/cmd/.temp/k8s-clusters-file.yaml
    reconciler:
      concurrency: 20
      auto-restart-if-config-mismatch: false
      driver-job-check-watching-interval: 10s
      driver-job-max-restart-times: 10000
      driver-job-long-wait-restart-times: 5
      driver-job-backfill-concurrency-limit: FREE:1
