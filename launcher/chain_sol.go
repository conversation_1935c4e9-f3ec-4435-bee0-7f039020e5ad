package launcher

import (
	"context"
	"sentioxyz/sentio/chain/sol"
	"sentioxyz/sentio/common/config"
	"sentioxyz/sentio/common/jsonrpc"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/ready"
	"time"
)

func BuildSolMiddlewares(ctx context.Context, c config.Config, svrName string) []jsonrpc.Middleware {
	network := c.Get("network").String("")
	rpcCtx, _ := log.FromContext(ctx, "svrName", svrName, "network", network)
	client := BuildNodeClient(
		ctx,
		network,
		c.Get("client").String("", config.NotEmpty),
		sol.AdapterBuilder,
		nil,
	)
	proxySvr := BuildJSONRPCProxyServiceV2(rpcCtx, c.Get("proxy"), network, client)
	ready.Registry(svrName, func() error {
		probeCtx, cancel := context.WithTimeout(context.Background(), time.Second*1)
		defer cancel()
		_, err := client.BlockNumber(probeCtx)
		return err
	})
	return sol.NewSimpleProxyService(proxySvr)
}
