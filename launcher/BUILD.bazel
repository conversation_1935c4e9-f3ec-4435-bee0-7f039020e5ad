load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "launcher",
    srcs = [
        "auth.go",
        "chain.go",
        "chain_aptos.go",
        "chain_btc.go",
        "chain_evm.go",
        "chain_fuel.go",
        "chain_sol.go",
        "chain_sui.go",
        "connection.go",
        "driver_controller.go",
        "endpoint.go",
        "launcher.go",
        "metric.go",
        "network.go",
        "proxy.go",
        "server.go",
        "task.go",
        "usage.go",
    ],
    importpath = "sentioxyz/sentio/launcher",
    visibility = ["//visibility:public"],
    deps = [
        "//chain/aptos",
        "//chain/btc",
        "//chain/chain",
        "//chain/clickhouse",
        "//chain/evm",
        "//chain/evm/ch",
        "//chain/evm/middlewares",
        "//chain/fuel",
        "//chain/node",
        "//chain/proxyv3",
        "//chain/proxyv3/cache",
        "//chain/proxyv3/recorder",
        "//chain/redis",
        "//chain/slot",
        "//chain/sol",
        "//chain/sui",
        "//chain/sui/chv3",
        "//chain/sui/tools",
        "//common/clickhouse",
        "//common/clickhouse/helper",
        "//common/config",
        "//common/endpointpool",
        "//common/errgroup",
        "//common/hypertable",
        "//common/jsonrpc",
        "//common/k8sconf",
        "//common/log",
        "//common/monitoring",
        "//common/number",
        "//common/period",
        "//common/ready",
        "//common/redislock",
        "//common/utils",
        "//k8s/client",
        "//k8s/controllers",
        "//service/common/auth",
        "//service/common/protos",
        "//service/common/redis",
        "//service/common/repository",
        "//service/common/rpc",
        "//service/mvcontroller/refresh",
        "//service/price/sync",
        "//service/processor/protos",
        "//service/solidity/sync",
        "//service/sysstatus",
        "//service/sysstatus/protos",
        "//service/usage",
        "//service/usage/protos",
        "//service/usage/store",
        "//service/usage/store/models",
        "@com_github_btcsuite_btcd//rpcclient",
        "@com_github_clickhouse_clickhouse_go_v2//:clickhouse-go",
        "@com_github_gorilla_handlers//:handlers",
        "@com_github_invisionapp_go_health_v2//:go-health",
        "@com_github_redis_go_redis_v9//:go-redis",
        "@com_github_syndtr_goleveldb//leveldb",
        "@com_github_syndtr_goleveldb//leveldb/opt",
        "@io_gorm_gorm//:gorm",
        "@io_k8s_sigs_controller_runtime//pkg/controller",
        "@io_opentelemetry_go_otel//:otel",
        "@io_opentelemetry_go_otel//attribute",
        "@io_opentelemetry_go_otel_metric//:metric",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//reflection",
        "@org_golang_x_net//context",
        "@org_uber_go_zap//:zap",
    ],
)
