package launcher

import (
	"context"
	"fmt"
	clickhousev2 "github.com/ClickHouse/clickhouse-go/v2"
	"sentioxyz/sentio/chain/chain"
	"sentioxyz/sentio/chain/clickhouse"
	"sentioxyz/sentio/chain/redis"
	"sentioxyz/sentio/chain/sui"
	"sentioxyz/sentio/chain/sui/chv3"
	"sentioxyz/sentio/common/config"
	"sentioxyz/sentio/common/jsonrpc"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/common/number"
	"time"
)

func BuildSuiExpDimension(
	ctx context.Context,
	c config.Config,
	name string,
	network string,
	networkOpts NetworkOptions,
) chain.Dimension[*sui.Slot] {
	return sui.NewExtServerDimension(
		&sui.Client{NodeClient: BuildNodeClient(
			ctx,
			network,
			c.Get("client").String("", config.NotEmpty),
			sui.NewAdapterBuilder(networkOpts.chainID),
			nil,
		)},
		network,
		c.Get("load-concurrency").Uint(10, config.Gt[uint](0)),
		BuildRange(c.Get("valid-number-range"), 0, number.MaxNumber),
	)
}

func BuildSuiClickhouseSchemaMgr(
	ctx context.Context,
	c config.Config,
	dimName string,
	network string,
	conn clickhousev2.Conn,
	options NetworkOptions,
) clickhouse.SchemaMgr[*sui.Slot] {
	switch slotVersion := c.Get("slot-version").String("v3", config.In("v3")); slotVersion {
	case "v3":
		schemaMgrConf := c.Get("clickhouse-schema-mgr")
		return chv3.NewClickhouseSchemaMgr(
			c.Get("table-prefix").String("", config.NotEmpty),
			BuildClickhouseCreateTableOption(ctx, schemaMgrConf, conn),
			chv3.NewSlotConverter(
				&sui.Client{NodeClient: BuildNodeClient(
					ctx,
					network,
					schemaMgrConf.Get("client").String("", config.NotEmpty),
					sui.NewAdapterBuilder(options.chainID),
					nil,
				)},
				schemaMgrConf.Get("fetch-concurrency").Int(10, config.Gt[int](0)),
				schemaMgrConf.Get("fetch-page-size").Int(50, config.Gt[int](0)),
			),
			c.Get("checkpoint-partition-size").Uint64(500000),
		)
	default:
		panic(c.BuildErr(fmt.Errorf("unknown slot version %q", slotVersion)))
	}
}

func BuildSuiMiddlewares(ctx context.Context, c config.Config, svrName string) []jsonrpc.Middleware {
	network := c.Get("network").String("", chainTypeSui.HasNetwork)
	rpcCtx, _ := log.FromContext(ctx, "svrName", svrName, "network", network)
	networkOpts := GetNetworkOptions(network)

	client := &sui.Client{NodeClient: BuildNodeClient(
		ctx,
		network,
		c.Get("client").String("", config.NotEmpty),
		sui.NewAdapterBuilder(networkOpts.chainID),
		nil,
	)}

	// latest slot cache
	cacheConf := c.Get("cache")
	slotCache := BuildLatestSlotCache(
		rpcCtx,
		cacheConf,
		svrName,
		network,
		client,
		chainTypeSui.BuildDimension(ctx, network, cacheConf.Get("persistent-dimension").String("", config.NotEmpty)),
		&sui.SlotListParser{})

	// cached checkpoint time
	var err error
	var cachedCheckpointTime chain.KVStore[sui.CheckpointTime]
	ctCacheConf := c.Get("cache-checkpoint-time")
	switch ctCacheConf.Get("driver").String("mem-lru", config.In("mem-lru", "redis")) {
	case "mem-lru":
		cachedCheckpointTime, err = chain.NewLRUKVStore[sui.CheckpointTime](ctCacheConf.Get("size").Int(1000000))
		if err != nil {
			panic(ctCacheConf.BuildErr(err))
		}
	case "redis":
		cachedCheckpointTime = redis.NewKVStore[sui.CheckpointTime](
			BuildRedisClient(ctCacheConf.Get("name").String("", config.NotEmpty)),
			ctCacheConf.Get("key-prefix").String(fmt.Sprintf("super/%s/%s/CheckpointTime/", svrName, network)),
			ctCacheConf.Get("ttl").Duration(time.Hour*24, config.Gt[time.Duration](0)),
		)
	}

	// cached object creation
	var cachedObjectCreation chain.KVStore[sui.ObjectCreation]
	ocCacheConf := c.Get("cache-object-creation")
	switch ocCacheConf.Get("driver").String("mem-lru", config.In("mem-lru", "redis")) {
	case "mem-lru":
		cachedObjectCreation, err = chain.NewLRUKVStore[sui.ObjectCreation](ocCacheConf.Get("size").Int(1000000))
		if err != nil {
			panic(ocCacheConf.BuildErr(err))
		}
	case "redis":
		cachedObjectCreation = redis.NewKVStore[sui.ObjectCreation](
			BuildRedisClient(ocCacheConf.Get("name").String("", config.NotEmpty)),
			ocCacheConf.Get("key-prefix").String(fmt.Sprintf("super/%s/%s/ObjectCreationV1/", svrName, network)),
			ocCacheConf.Get("ttl").Duration(time.Hour*24*7, config.Gt[time.Duration](0)),
		)
	}

	// storage
	var storage sui.Storage
	switch slotVersion := c.Get("slot-version").String("v3", config.In("v3")); slotVersion {
	case "v3":
		chConf := c.Get("clickhouse")
		conn, _ := BuildClickhouseConnect(ctx, chConf)
		tablePrefix := chConf.Get("table-prefix").String("", config.NotEmpty)
		rangeStore := BuildRangeStore(ctx, chConf.Get("range-store", config.Exist))
		storage = chv3.NewController(conn, tablePrefix, rangeStore)
		go KeepReportSuperNodeClickhouseRange(rpcCtx, rangeStore, network, networkOpts.chainID)
	default:
		panic(c.BuildErr(fmt.Errorf("unknown slot version %q", slotVersion)))
	}

	// super service
	superSvr := sui.NewSuperService(
		slotCache,
		cachedCheckpointTime,
		cachedObjectCreation,
		storage,
	)

	// proxy service
	proxySvr := BuildJSONRPCProxyServiceV2(
		rpcCtx,
		c.Get("proxy"),
		network,
		client)

	return sui.NewSuperNode(superSvr, proxySvr)
}
