{"lockFileVersion": 18, "registryFileHashes": {"https://bcr.bazel.build/bazel_registry.json": "8a28e4aff06ee60aed2a8c281907fb8bcbf3b753c91fb5a5c57da3215d5b3497", "https://bcr.bazel.build/modules/abseil-cpp/20210324.2/MODULE.bazel": "7cd0312e064fde87c8d1cd79ba06c876bd23630c83466e9500321be55c96ace2", "https://bcr.bazel.build/modules/abseil-cpp/20211102.0/MODULE.bazel": "70390338f7a5106231d20620712f7cccb659cd0e9d073d1991c038eb9fc57589", "https://bcr.bazel.build/modules/abseil-cpp/20220623.1/MODULE.bazel": "73ae41b6818d423a11fd79d95aedef1258f304448193d4db4ff90e5e7a0f076c", "https://bcr.bazel.build/modules/abseil-cpp/20230125.1/MODULE.bazel": "89047429cb0207707b2dface14ba7f8df85273d484c2572755be4bab7ce9c3a0", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0.bcr.1/MODULE.bazel": "1c8cec495288dccd14fdae6e3f95f772c1c91857047a098fad772034264cc8cb", "https://bcr.bazel.build/modules/abseil-cpp/20230802.0/MODULE.bazel": "d253ae36a8bd9ee3c5955384096ccb6baf16a1b1e93e858370da0a3b94f77c16", "https://bcr.bazel.build/modules/abseil-cpp/20230802.1/MODULE.bazel": "fa92e2eb41a04df73cdabeec37107316f7e5272650f81d6cc096418fe647b915", "https://bcr.bazel.build/modules/abseil-cpp/20240116.0/MODULE.bazel": "98dc378d64c12a4e4741ad3362f87fb737ee6a0886b2d90c3cdbb4d93ea3e0bf", "https://bcr.bazel.build/modules/abseil-cpp/20240116.1/MODULE.bazel": "37bcdb4440fbb61df6a1c296ae01b327f19e9bb521f9b8e26ec854b6f97309ed", "https://bcr.bazel.build/modules/abseil-cpp/20240116.1/source.json": "9be551b8d4e3ef76875c0d744b5d6a504a27e3ae67bc6b28f46415fd2d2957da", "https://bcr.bazel.build/modules/apple_support/1.15.1/MODULE.bazel": "a0556fefca0b1bb2de8567b8827518f94db6a6e7e7d632b4c48dc5f865bc7c85", "https://bcr.bazel.build/modules/apple_support/1.15.1/source.json": "517f2b77430084c541bc9be2db63fdcbb7102938c5f64c17ee60ffda2e5cf07b", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.0.0/MODULE.bazel": "e118477db5c49419a88d78ebc7a2c2cea9d49600fe0f490c1903324a2c16ecd9", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.14.0/MODULE.bazel": "2b31ffcc9bdc8295b2167e07a757dbbc9ac8906e7028e5170a3708cecaac119f", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.16.0/MODULE.bazel": "852f9ebbda017572a7c113a2434592dd3b2f55cd9a0faea3d4be5a09a59e4900", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.19.4/MODULE.bazel": "d39e4b18e594d81c526d7cfc513e7ecfa8ca9eb5b61488d1d790faa94b34f2d9", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.20.0/MODULE.bazel": "c5565bac49e1973227225b441fad1c938d498d83df62dc5da95b2fab0f0626a2", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.20.0/source.json": "3eaada79dd3c65b6c57d5fc33c57ffd2896c4ebd78c4c9001a790a70f7f50e61", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.7.2/MODULE.bazel": "780d1a6522b28f5edb7ea09630748720721dfe27690d65a2d33aa7509de77e07", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.7.7/MODULE.bazel": "491f8681205e31bb57892d67442ce448cda4f472a8e6b3dc062865e29a64f89c", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.8.1/MODULE.bazel": "812d2dd42f65dca362152101fbec418029cc8fd34cbad1a2fde905383d705838", "https://bcr.bazel.build/modules/aspect_bazel_lib/2.9.3/MODULE.bazel": "66baf724dbae7aff4787bf2245cc188d50cb08e07789769730151c0943587c14", "https://bcr.bazel.build/modules/aspect_rules_js/2.0.0/MODULE.bazel": "b45b507574aa60a92796e3e13c195cd5744b3b8aff516a9c0cb5ae6a048161c5", "https://bcr.bazel.build/modules/aspect_rules_js/2.4.2/MODULE.bazel": "0d01db38b96d25df7ed952a5e96eac4b3802723d146961974bf020f6dd07591d", "https://bcr.bazel.build/modules/aspect_rules_js/2.4.2/source.json": "854a600536a6fa4efae974a19271ae3d86d39705094cc41331724583398bb0b6", "https://bcr.bazel.build/modules/aspect_rules_py/1.6.1/MODULE.bazel": "58d499730474d4e401ef1e6d58bb67570f90a94e408c02da61c07b92986e7084", "https://bcr.bazel.build/modules/aspect_rules_py/1.6.1/source.json": "4c504911b49f2d7621d73aa4da6451c99974cf6a30d578098be2722129b75159", "https://bcr.bazel.build/modules/aspect_rules_ts/3.6.3/MODULE.bazel": "d09db394970f076176ce7bab5b5fa7f0d560fd4f30b8432ea5e2c2570505b130", "https://bcr.bazel.build/modules/aspect_rules_ts/3.6.3/source.json": "641e58c62e5090d52a0d3538451893acdb2d79a36e8b3d1d30a013c580bc2058", "https://bcr.bazel.build/modules/aspect_tools_telemetry/0.2.0/MODULE.bazel": "61d2a12f7c6f063fc215c4258c5ca1ee9a5791e81945cbe24ded9dcbe2d94b29", "https://bcr.bazel.build/modules/aspect_tools_telemetry/0.2.3/MODULE.bazel": "20f53b145f40957a51077ae90b37b7ce83582a1daf9350349f0f86179e19dd0d", "https://bcr.bazel.build/modules/aspect_tools_telemetry/0.2.3/source.json": "e0a34c61e5315d41e9b90e4771a60e0924f80a2810ec15e7d489e6249c0dea56", "https://bcr.bazel.build/modules/bazel_features/1.1.0/MODULE.bazel": "cfd42ff3b815a5f39554d97182657f8c4b9719568eb7fded2b9135f084bf760b", "https://bcr.bazel.build/modules/bazel_features/1.1.1/MODULE.bazel": "27b8c79ef57efe08efccbd9dd6ef70d61b4798320b8d3c134fd571f78963dbcd", "https://bcr.bazel.build/modules/bazel_features/1.10.0/MODULE.bazel": "f75e8807570484a99be90abcd52b5e1f390362c258bcb73106f4544957a48101", "https://bcr.bazel.build/modules/bazel_features/1.11.0/MODULE.bazel": "f9382337dd5a474c3b7d334c2f83e50b6eaedc284253334cf823044a26de03e8", "https://bcr.bazel.build/modules/bazel_features/1.15.0/MODULE.bazel": "d38ff6e517149dc509406aca0db3ad1efdd890a85e049585b7234d04238e2a4d", "https://bcr.bazel.build/modules/bazel_features/1.17.0/MODULE.bazel": "039de32d21b816b47bd42c778e0454217e9c9caac4a3cf8e15c7231ee3ddee4d", "https://bcr.bazel.build/modules/bazel_features/1.18.0/MODULE.bazel": "1be0ae2557ab3a72a57aeb31b29be347bcdc5d2b1eb1e70f39e3851a7e97041a", "https://bcr.bazel.build/modules/bazel_features/1.19.0/MODULE.bazel": "59adcdf28230d220f0067b1f435b8537dd033bfff8db21335ef9217919c7fb58", "https://bcr.bazel.build/modules/bazel_features/1.21.0/MODULE.bazel": "675642261665d8eea09989aa3b8afb5c37627f1be178382c320d1b46afba5e3b", "https://bcr.bazel.build/modules/bazel_features/1.3.0/MODULE.bazel": "cdcafe83ec318cda34e02948e81d790aab8df7a929cec6f6969f13a489ccecd9", "https://bcr.bazel.build/modules/bazel_features/1.30.0/MODULE.bazel": "a14b62d05969a293b80257e72e597c2da7f717e1e69fa8b339703ed6731bec87", "https://bcr.bazel.build/modules/bazel_features/1.30.0/source.json": "b07e17f067fe4f69f90b03b36ef1e08fe0d1f3cac254c1241a1818773e3423bc", "https://bcr.bazel.build/modules/bazel_features/1.4.1/MODULE.bazel": "e45b6bb2350aff3e442ae1111c555e27eac1d915e77775f6fdc4b351b758b5d7", "https://bcr.bazel.build/modules/bazel_features/1.9.0/MODULE.bazel": "885151d58d90d8d9c811eb75e3288c11f850e1d6b481a8c9f766adee4712358b", "https://bcr.bazel.build/modules/bazel_features/1.9.1/MODULE.bazel": "8f679097876a9b609ad1f60249c49d68bfab783dd9be012faf9d82547b14815a", "https://bcr.bazel.build/modules/bazel_skylib/1.0.3/MODULE.bazel": "bcb0fd896384802d1ad283b4e4eb4d718eebd8cb820b0a2c3a347fb971afd9d8", "https://bcr.bazel.build/modules/bazel_skylib/1.1.1/MODULE.bazel": "1add3e7d93ff2e6998f9e118022c84d163917d912f5afafb3058e3d2f1545b5e", "https://bcr.bazel.build/modules/bazel_skylib/1.2.0/MODULE.bazel": "44fe84260e454ed94ad326352a698422dbe372b21a1ac9f3eab76eb531223686", "https://bcr.bazel.build/modules/bazel_skylib/1.2.1/MODULE.bazel": "f35baf9da0efe45fa3da1696ae906eea3d615ad41e2e3def4aeb4e8bc0ef9a7a", "https://bcr.bazel.build/modules/bazel_skylib/1.3.0/MODULE.bazel": "20228b92868bf5cfc41bda7afc8a8ba2a543201851de39d990ec957b513579c5", "https://bcr.bazel.build/modules/bazel_skylib/1.4.0/MODULE.bazel": "2ab127ef8d56a739a99bb2ce00ec4c7d1ecc7977d4370c0ca6efd0d8f03d6d99", "https://bcr.bazel.build/modules/bazel_skylib/1.4.1/MODULE.bazel": "a0dcb779424be33100dcae821e9e27e4f2901d9dfd5333efe5ac6a8d7ab75e1d", "https://bcr.bazel.build/modules/bazel_skylib/1.4.2/MODULE.bazel": "3bd40978e7a1fac911d5989e6b09d8f64921865a45822d8b09e815eaa726a651", "https://bcr.bazel.build/modules/bazel_skylib/1.5.0/MODULE.bazel": "32880f5e2945ce6a03d1fbd588e9198c0a959bb42297b2cfaf1685b7bc32e138", "https://bcr.bazel.build/modules/bazel_skylib/1.6.1/MODULE.bazel": "8fdee2dbaace6c252131c00e1de4b165dc65af02ea278476187765e1a617b917", "https://bcr.bazel.build/modules/bazel_skylib/1.7.0/MODULE.bazel": "0db596f4563de7938de764cc8deeabec291f55e8ec15299718b93c4423e9796d", "https://bcr.bazel.build/modules/bazel_skylib/1.7.1/MODULE.bazel": "3120d80c5861aa616222ec015332e5f8d3171e062e3e804a2a0253e1be26e59b", "https://bcr.bazel.build/modules/bazel_skylib/1.8.0/MODULE.bazel": "2fb3fb53675f6adfc1ca5bfbd5cfb655ae350fba4706d924a8ec7e3ba945671c", "https://bcr.bazel.build/modules/bazel_skylib/1.8.1/MODULE.bazel": "88ade7293becda963e0e3ea33e7d54d3425127e0a326e0d17da085a5f1f03ff6", "https://bcr.bazel.build/modules/bazel_skylib/1.8.1/source.json": "7ebaefba0b03efe59cac88ed5bbc67bcf59a3eff33af937345ede2a38b2d368a", "https://bcr.bazel.build/modules/boringssl/0.0.0-20211025-d4f1ab9/MODULE.bazel": "6ee6353f8b1a701fe2178e1d925034294971350b6d3ac37e67e5a7d463267834", "https://bcr.bazel.build/modules/boringssl/0.0.0-20230215-5c22014/MODULE.bazel": "4b03dc0d04375fa0271174badcd202ed249870c8e895b26664fd7298abea7282", "https://bcr.bazel.build/modules/boringssl/0.0.0-20230215-5c22014/source.json": "f90873cd3d891bb63ece55a527d97366da650f84c79c2109bea29c17629bee20", "https://bcr.bazel.build/modules/buildifier_prebuilt/6.4.0/MODULE.bazel": "37389c6b5a40c59410b4226d3bb54b08637f393d66e2fa57925c6fcf68e64bf4", "https://bcr.bazel.build/modules/buildifier_prebuilt/6.4.0/source.json": "83eb01b197ed0b392f797860c9da5ed1bf95f4d0ded994d694a3d44731275916", "https://bcr.bazel.build/modules/buildozer/7.1.2/MODULE.bazel": "2e8dd40ede9c454042645fd8d8d0cd1527966aa5c919de86661e62953cd73d84", "https://bcr.bazel.build/modules/buildozer/7.1.2/source.json": "c9028a501d2db85793a6996205c8de120944f50a0d570438fcae0457a5f9d1f8", "https://bcr.bazel.build/modules/c-ares/1.15.0/MODULE.bazel": "ba0a78360fdc83f02f437a9e7df0532ad1fbaa59b722f6e715c11effebaa0166", "https://bcr.bazel.build/modules/c-ares/1.15.0/source.json": "5e3ed991616c5ec4cc09b0893b29a19232de4a1830eb78c567121bfea87453f7", "https://bcr.bazel.build/modules/curl/8.4.0/MODULE.bazel": "0bc250aa1cb69590049383df7a9537c809591fcf876c620f5f097c58fdc9bc10", "https://bcr.bazel.build/modules/curl/8.4.0/source.json": "8b9532397af6a24be4ec118d8637b1f4e3e5a0d4be672c94b2275d675c7f7d6b", "https://bcr.bazel.build/modules/gazelle/0.27.0/MODULE.bazel": "3446abd608295de6d90b4a8a118ed64a9ce11dcb3dda2dc3290a22056bd20996", "https://bcr.bazel.build/modules/gazelle/0.30.0/MODULE.bazel": "f888a1effe338491f35f0e0e85003b47bb9d8295ccba73c37e07702d8d31c65b", "https://bcr.bazel.build/modules/gazelle/0.32.0/MODULE.bazel": "b499f58a5d0d3537f3cf5b76d8ada18242f64ec474d8391247438bf04f58c7b8", "https://bcr.bazel.build/modules/gazelle/0.33.0/MODULE.bazel": "a13a0f279b462b784fb8dd52a4074526c4a2afe70e114c7d09066097a46b3350", "https://bcr.bazel.build/modules/gazelle/0.34.0/MODULE.bazel": "abdd8ce4d70978933209db92e436deb3a8b737859e9354fb5fd11fb5c2004c8a", "https://bcr.bazel.build/modules/gazelle/0.36.0/MODULE.bazel": "e375d5d6e9a6ca59b0cb38b0540bc9a05b6aa926d322f2de268ad267a2ee74c0", "https://bcr.bazel.build/modules/gazelle/0.37.0/MODULE.bazel": "d1327ba0907d0275ed5103bfbbb13518f6c04955b402213319d0d6c0ce9839d4", "https://bcr.bazel.build/modules/gazelle/0.44.0/MODULE.bazel": "fd3177ca0938da57a1e416cad3f39b9c4334defbc717e89aba9d9ddbbb0341da", "https://bcr.bazel.build/modules/gazelle/0.44.0/source.json": "7fb65ef9c1ce470d099ca27fd478673d9d64c844af28d0d472b0874c7d590cb6", "https://bcr.bazel.build/modules/google_benchmark/1.8.2/MODULE.bazel": "a70cf1bba851000ba93b58ae2f6d76490a9feb74192e57ab8e8ff13c34ec50cb", "https://bcr.bazel.build/modules/google_benchmark/1.8.4/MODULE.bazel": "c6d54a11dcf64ee63545f42561eda3fd94c1b5f5ebe1357011de63ae33739d5e", "https://bcr.bazel.build/modules/google_benchmark/1.8.4/source.json": "84590f7bc5a1fd99e1ef274ee16bb41c214f705e62847b42e705010dfa81fe53", "https://bcr.bazel.build/modules/googleapis-rules-registry/1.0.0/MODULE.bazel": "97c6a4d413b373d4cc97065da3de1b2166e22cbbb5f4cc9f05760bfa83619e24", "https://bcr.bazel.build/modules/googleapis-rules-registry/1.0.0/source.json": "cf611c836a60e98e2e2ab2de8004f119e9f06878dcf4ea2d95a437b1b7a89fe9", "https://bcr.bazel.build/modules/googleapis/0.0.0-20240326-1c8d509c5/MODULE.bazel": "a4b7e46393c1cdcc5a00e6f85524467c48c565256b22b5fae20f84ab4a999a68", "https://bcr.bazel.build/modules/googleapis/0.0.0-20241220-5e258e33/MODULE.bazel": "571b018644920302f5a69520b91dae189c17d566e730a1b87c9dbeefe39bd6a5", "https://bcr.bazel.build/modules/googleapis/0.0.0-20241220-5e258e33/source.json": "2172f9bad88838c509e92489545b6ec41d99fb0de8cf7b2a2da61dae7587a0e4", "https://bcr.bazel.build/modules/googletest/1.11.0/MODULE.bazel": "3a83f095183f66345ca86aa13c58b59f9f94a2f81999c093d4eeaa2d262d12f4", "https://bcr.bazel.build/modules/googletest/1.14.0.bcr.1/MODULE.bazel": "22c31a561553727960057361aa33bf20fb2e98584bc4fec007906e27053f80c6", "https://bcr.bazel.build/modules/googletest/1.14.0.bcr.1/source.json": "41e9e129f80d8c8bf103a7acc337b76e54fad1214ac0a7084bf24f4cd924b8b4", "https://bcr.bazel.build/modules/googletest/1.14.0/MODULE.bazel": "cfbcbf3e6eac06ef9d85900f64424708cc08687d1b527f0ef65aa7517af8118f", "https://bcr.bazel.build/modules/grpc-java/1.62.2/MODULE.bazel": "99b8771e8c7cacb130170fed2a10c9e8fed26334a93e73b42d2953250885a158", "https://bcr.bazel.build/modules/grpc/1.41.0/MODULE.bazel": "5bcbfc2b274dabea628f0649dc50c90cf36543b1cfc31624832538644ad1aae8", "https://bcr.bazel.build/modules/grpc/1.56.3.bcr.1/MODULE.bazel": "cd5b1eb276b806ec5ab85032921f24acc51735a69ace781be586880af20ab33f", "https://bcr.bazel.build/modules/grpc/1.65.0/MODULE.bazel": "a8601bc0ee3fea20dd4a47e5a2d6c20d29cf5208fbf986e1d6612988c53e649d", "https://bcr.bazel.build/modules/grpc/1.65.0/source.json": "8890a442b3f53ac1bd908f857c35e3a6c8889af6afd8c98a0fe052d15259115e", "https://bcr.bazel.build/modules/hermetic_cc_toolchain/4.0.1/MODULE.bazel": "0809d28e562d804e478c683b06a9f3adeedccfdb42a426c2cc69e39cbc7e3bf3", "https://bcr.bazel.build/modules/hermetic_cc_toolchain/4.0.1/source.json": "527d73a9964cd34ceeb73a1d5e5d04d9e6238401363c783c1f3021d5b25b8a63", "https://bcr.bazel.build/modules/jq.bzl/0.1.0/MODULE.bazel": "2ce69b1af49952cd4121a9c3055faa679e748ce774c7f1fda9657f936cae902f", "https://bcr.bazel.build/modules/jq.bzl/0.1.0/source.json": "746bf13cac0860f091df5e4911d0c593971cd8796b5ad4e809b2f8e133eee3d5", "https://bcr.bazel.build/modules/jsoncpp/1.9.5/MODULE.bazel": "31271aedc59e815656f5736f282bb7509a97c7ecb43e927ac1a37966e0578075", "https://bcr.bazel.build/modules/jsoncpp/1.9.5/source.json": "4108ee5085dd2885a341c7fab149429db457b3169b86eb081fa245eadf69169d", "https://bcr.bazel.build/modules/libpfm/4.11.0/MODULE.bazel": "45061ff025b301940f1e30d2c16bea596c25b176c8b6b3087e92615adbd52902", "https://bcr.bazel.build/modules/libpfm/4.11.0/source.json": "caaffb3ac2b59b8aac456917a4ecf3167d40478ee79f15ab7a877ec9273937c9", "https://bcr.bazel.build/modules/nlohmann_json/3.11.3/MODULE.bazel": "87023db2f55fc3a9949c7b08dc711fae4d4be339a80a99d04453c4bb3998eefc", "https://bcr.bazel.build/modules/nlohmann_json/3.11.3/source.json": "296c63a90c6813e53b3812d24245711981fc7e563d98fe15625f55181494488a", "https://bcr.bazel.build/modules/nlohmann_json/3.6.1/MODULE.bazel": "6f7b417dcc794d9add9e556673ad25cb3ba835224290f4f848f8e2db1e1fca74", "https://bcr.bazel.build/modules/opentelemetry-cpp/1.14.2/MODULE.bazel": "089a5613c2a159c7dfde098dabfc61e966889c7d6a81a98422a84c51535ed17d", "https://bcr.bazel.build/modules/opentelemetry-cpp/1.14.2/source.json": "0c5f85ab9e5894c6f1382cf58ba03a6cd024f0592bee2229f99db216ef0c6764", "https://bcr.bazel.build/modules/opentelemetry-proto/1.1.0/MODULE.bazel": "a49f406e99bf05ab43ed4f5b3322fbd33adfd484b6546948929d1316299b68bf", "https://bcr.bazel.build/modules/opentelemetry-proto/1.1.0/source.json": "39ffadc4b7d9ccc0c0f45422510cbaeb8eca7b26e68d4142fc3ff18b4c2711b6", "https://bcr.bazel.build/modules/opentracing-cpp/1.6.0/MODULE.bazel": "b3925269f63561b8b880ae7cf62ccf81f6ece55b62cd791eda9925147ae116ec", "https://bcr.bazel.build/modules/opentracing-cpp/1.6.0/source.json": "da1cb1add160f5e5074b7272e9db6fd8f1b3336c15032cd0a653af9d2f484aed", "https://bcr.bazel.build/modules/package_metadata/0.0.2/MODULE.bazel": "fb8d25550742674d63d7b250063d4580ca530499f045d70748b1b142081ebb92", "https://bcr.bazel.build/modules/package_metadata/0.0.2/source.json": "e53a759a72488d2c0576f57491ef2da0cf4aab05ac0997314012495935531b73", "https://bcr.bazel.build/modules/platforms/0.0.10/MODULE.bazel": "8cb8efaf200bdeb2150d93e162c40f388529a25852b332cec879373771e48ed5", "https://bcr.bazel.build/modules/platforms/0.0.11/MODULE.bazel": "0daefc49732e227caa8bfa834d65dc52e8cc18a2faf80df25e8caea151a9413f", "https://bcr.bazel.build/modules/platforms/0.0.11/source.json": "f7e188b79ebedebfe75e9e1d098b8845226c7992b307e28e1496f23112e8fc29", "https://bcr.bazel.build/modules/platforms/0.0.4/MODULE.bazel": "9b328e31ee156f53f3c416a64f8491f7eb731742655a47c9eec4703a71644aee", "https://bcr.bazel.build/modules/platforms/0.0.5/MODULE.bazel": "5733b54ea419d5eaf7997054bb55f6a1d0b5ff8aedf0176fef9eea44f3acda37", "https://bcr.bazel.build/modules/platforms/0.0.6/MODULE.bazel": "ad6eeef431dc52aefd2d77ed20a4b353f8ebf0f4ecdd26a807d2da5aa8cd0615", "https://bcr.bazel.build/modules/platforms/0.0.7/MODULE.bazel": "72fd4a0ede9ee5c021f6a8dd92b503e089f46c227ba2813ff183b71616034814", "https://bcr.bazel.build/modules/platforms/0.0.8/MODULE.bazel": "9f142c03e348f6d263719f5074b21ef3adf0b139ee4c5133e2aa35664da9eb2d", "https://bcr.bazel.build/modules/platforms/0.0.9/MODULE.bazel": "4a87a60c927b56ddd67db50c89acaa62f4ce2a1d2149ccb63ffd871d5ce29ebc", "https://bcr.bazel.build/modules/prometheus-cpp/1.2.4/MODULE.bazel": "0fbe5dcff66311947a3f6b86ebc6a6d9328e31a28413ca864debc4a043f371e5", "https://bcr.bazel.build/modules/prometheus-cpp/1.2.4/source.json": "aa58bb10d0bb0dcaf4ad2c509ddcec23d2e94c3935e21517a5adbc2363248a55", "https://bcr.bazel.build/modules/protobuf/21.7/MODULE.bazel": "a5a29bb89544f9b97edce05642fac225a808b5b7be74038ea3640fae2f8e66a7", "https://bcr.bazel.build/modules/protobuf/23.1/MODULE.bazel": "88b393b3eb4101d18129e5db51847cd40a5517a53e81216144a8c32dfeeca52a", "https://bcr.bazel.build/modules/protobuf/24.4/MODULE.bazel": "7bc7ce5f2abf36b3b7b7c8218d3acdebb9426aeb35c2257c96445756f970eb12", "https://bcr.bazel.build/modules/protobuf/26.0.bcr.2/MODULE.bazel": "62e0b84ca727bdeb55a6fe1ef180e6b191bbe548a58305ea1426c158067be534", "https://bcr.bazel.build/modules/protobuf/27.0/MODULE.bazel": "7873b60be88844a0a1d8f80b9d5d20cfbd8495a689b8763e76c6372998d3f64c", "https://bcr.bazel.build/modules/protobuf/27.1/MODULE.bazel": "703a7b614728bb06647f965264967a8ef1c39e09e8f167b3ca0bb1fd80449c0d", "https://bcr.bazel.build/modules/protobuf/29.0-rc2/MODULE.bazel": "6241d35983510143049943fc0d57937937122baf1b287862f9dc8590fc4c37df", "https://bcr.bazel.build/modules/protobuf/29.0-rc3/MODULE.bazel": "33c2dfa286578573afc55a7acaea3cada4122b9631007c594bf0729f41c8de92", "https://bcr.bazel.build/modules/protobuf/29.0/MODULE.bazel": "319dc8bf4c679ff87e71b1ccfb5a6e90a6dbc4693501d471f48662ac46d04e4e", "https://bcr.bazel.build/modules/protobuf/29.1/MODULE.bazel": "557c3457560ff49e122ed76c0bc3397a64af9574691cb8201b4e46d4ab2ecb95", "https://bcr.bazel.build/modules/protobuf/29.2/MODULE.bazel": "5435497c190d86f79b0568698c45044df7c8d97692886cda9fe9cf9053aea712", "https://bcr.bazel.build/modules/protobuf/29.2/source.json": "fe7090cc34072609b26d9beafb122916dabc1d47ba61b242c26c4b06c51384ab", "https://bcr.bazel.build/modules/protobuf/3.19.0/MODULE.bazel": "6b5fbb433f760a99a22b18b6850ed5784ef0e9928a72668b66e4d7ccd47db9b0", "https://bcr.bazel.build/modules/protobuf/3.19.2/MODULE.bazel": "532ffe5f2186b69fdde039efe6df13ba726ff338c6bc82275ad433013fa10573", "https://bcr.bazel.build/modules/protobuf/3.19.6/MODULE.bazel": "9233edc5e1f2ee276a60de3eaa47ac4132302ef9643238f23128fea53ea12858", "https://bcr.bazel.build/modules/pybind11_bazel/2.11.1/MODULE.bazel": "88af1c246226d87e65be78ed49ecd1e6f5e98648558c14ce99176da041dc378e", "https://bcr.bazel.build/modules/pybind11_bazel/2.11.1/source.json": "be4789e951dd5301282729fe3d4938995dc4c1a81c2ff150afc9f1b0504c6022", "https://bcr.bazel.build/modules/re2/2021-09-01/MODULE.bazel": "bcb6b96f3b071e6fe2d8bed9cc8ada137a105f9d2c5912e91d27528b3d123833", "https://bcr.bazel.build/modules/re2/2023-09-01/MODULE.bazel": "cb3d511531b16cfc78a225a9e2136007a48cf8a677e4264baeab57fe78a80206", "https://bcr.bazel.build/modules/re2/2023-09-01/source.json": "e044ce89c2883cd957a2969a43e79f7752f9656f6b20050b62f90ede21ec6eb4", "https://bcr.bazel.build/modules/rules_android/0.1.1/MODULE.bazel": "48809ab0091b07ad0182defb787c4c5328bd3a278938415c00a7b69b50c4d3a8", "https://bcr.bazel.build/modules/rules_android/0.1.1/source.json": "e6986b41626ee10bdc864937ffb6d6bf275bb5b9c65120e6137d56e6331f089e", "https://bcr.bazel.build/modules/rules_apple/3.5.1/MODULE.bazel": "3d1bbf65ad3692003d36d8a29eff54d4e5c1c5f4bfb60f79e28646a924d9101c", "https://bcr.bazel.build/modules/rules_apple/3.5.1/source.json": "e7593cdf26437d35dbda64faeaf5b82cbdd9df72674b0f041fdde75c1d20dda7", "https://bcr.bazel.build/modules/rules_cc/0.0.1/MODULE.bazel": "cb2aa0747f84c6c3a78dad4e2049c154f08ab9d166b1273835a8174940365647", "https://bcr.bazel.build/modules/rules_cc/0.0.10/MODULE.bazel": "ec1705118f7eaedd6e118508d3d26deba2a4e76476ada7e0e3965211be012002", "https://bcr.bazel.build/modules/rules_cc/0.0.13/MODULE.bazel": "0e8529ed7b323dad0775ff924d2ae5af7640b23553dfcd4d34344c7e7a867191", "https://bcr.bazel.build/modules/rules_cc/0.0.14/MODULE.bazel": "5e343a3aac88b8d7af3b1b6d2093b55c347b8eefc2e7d1442f7a02dc8fea48ac", "https://bcr.bazel.build/modules/rules_cc/0.0.15/MODULE.bazel": "6704c35f7b4a72502ee81f61bf88706b54f06b3cbe5558ac17e2e14666cd5dcc", "https://bcr.bazel.build/modules/rules_cc/0.0.16/MODULE.bazel": "7661303b8fc1b4d7f532e54e9d6565771fea666fbdf839e0a86affcd02defe87", "https://bcr.bazel.build/modules/rules_cc/0.0.2/MODULE.bazel": "6915987c90970493ab97393024c156ea8fb9f3bea953b2f3ec05c34f19b5695c", "https://bcr.bazel.build/modules/rules_cc/0.0.5/MODULE.bazel": "be41f87587998fe8890cd82ea4e848ed8eb799e053c224f78f3ff7fe1a1d9b74", "https://bcr.bazel.build/modules/rules_cc/0.0.6/MODULE.bazel": "abf360251023dfe3efcef65ab9d56beefa8394d4176dd29529750e1c57eaa33f", "https://bcr.bazel.build/modules/rules_cc/0.0.8/MODULE.bazel": "964c85c82cfeb6f3855e6a07054fdb159aced38e99a5eecf7bce9d53990afa3e", "https://bcr.bazel.build/modules/rules_cc/0.0.9/MODULE.bazel": "836e76439f354b89afe6a911a7adf59a6b2518fafb174483ad78a2a2fde7b1c5", "https://bcr.bazel.build/modules/rules_cc/0.1.1/MODULE.bazel": "2f0222a6f229f0bf44cd711dc13c858dad98c62d52bd51d8fc3a764a83125513", "https://bcr.bazel.build/modules/rules_cc/0.1.1/source.json": "d61627377bd7dd1da4652063e368d9366fc9a73920bfa396798ad92172cf645c", "https://bcr.bazel.build/modules/rules_foreign_cc/0.10.1/MODULE.bazel": "b9527010e5fef060af92b6724edb3691970a5b1f76f74b21d39f7d433641be60", "https://bcr.bazel.build/modules/rules_foreign_cc/0.10.1/source.json": "9300e71df0cdde0952f10afff1401fa664e9fc5d9ae6204660ba1b158d90d6a6", "https://bcr.bazel.build/modules/rules_foreign_cc/0.9.0/MODULE.bazel": "c9e8c682bf75b0e7c704166d79b599f93b72cfca5ad7477df596947891feeef6", "https://bcr.bazel.build/modules/rules_fuzzing/0.5.2/MODULE.bazel": "40c97d1144356f52905566c55811f13b299453a14ac7769dfba2ac38192337a8", "https://bcr.bazel.build/modules/rules_fuzzing/0.5.2/source.json": "c8b1e2c717646f1702290959a3302a178fb639d987ab61d548105019f11e527e", "https://bcr.bazel.build/modules/rules_go/0.33.0/MODULE.bazel": "a2b11b64cd24bf94f57454f53288a5dacfe6cb86453eee7761b7637728c1910c", "https://bcr.bazel.build/modules/rules_go/0.38.1/MODULE.bazel": "fb8e73dd3b6fc4ff9d260ceacd830114891d49904f5bda1c16bc147bcc254f71", "https://bcr.bazel.build/modules/rules_go/0.39.1/MODULE.bazel": "d34fb2a249403a5f4339c754f1e63dc9e5ad70b47c5e97faee1441fc6636cd61", "https://bcr.bazel.build/modules/rules_go/0.41.0/MODULE.bazel": "55861d8e8bb0e62cbd2896f60ff303f62ffcb0eddb74ecb0e5c0cbe36fc292c8", "https://bcr.bazel.build/modules/rules_go/0.42.0/MODULE.bazel": "8cfa875b9aa8c6fce2b2e5925e73c1388173ea3c32a0db4d2b4804b453c14270", "https://bcr.bazel.build/modules/rules_go/0.46.0/MODULE.bazel": "3477df8bdcc49e698b9d25f734c4f3a9f5931ff34ee48a2c662be168f5f2d3fd", "https://bcr.bazel.build/modules/rules_go/0.48.0/MODULE.bazel": "d00ebcae0908ee3f5e6d53f68677a303d6d59a77beef879598700049c3980a03", "https://bcr.bazel.build/modules/rules_go/0.49.0/MODULE.bazel": "61cfc1ba17123356d1b12b6c50f6e0162b2cc7fd6f51753c12471e973a0f72a5", "https://bcr.bazel.build/modules/rules_go/0.51.0/MODULE.bazel": "b6920f505935bfd69381651c942496d99b16e2a12f3dd5263b90ded16f3b4d0f", "https://bcr.bazel.build/modules/rules_go/0.52.0/MODULE.bazel": "0cf080a2706aa8fc9abf64286cee60fdf0238db37b7f1793b0f7d550d59ea3ae", "https://bcr.bazel.build/modules/rules_go/0.52.0/source.json": "441bc7591044993dce9fb0377fcadf3086d6afac621b909d17d53858a4a1b8d4", "https://bcr.bazel.build/modules/rules_java/4.0.0/MODULE.bazel": "5a78a7ae82cd1a33cef56dc578c7d2a46ed0dca12643ee45edbb8417899e6f74", "https://bcr.bazel.build/modules/rules_java/5.1.0/MODULE.bazel": "324b6478b0343a3ce7a9add8586ad75d24076d6d43d2f622990b9c1cfd8a1b15", "https://bcr.bazel.build/modules/rules_java/5.3.5/MODULE.bazel": "a4ec4f2db570171e3e5eb753276ee4b389bae16b96207e9d3230895c99644b86", "https://bcr.bazel.build/modules/rules_java/6.0.0/MODULE.bazel": "8a43b7df601a7ec1af61d79345c17b31ea1fedc6711fd4abfd013ea612978e39", "https://bcr.bazel.build/modules/rules_java/6.3.0/MODULE.bazel": "a97c7678c19f236a956ad260d59c86e10a463badb7eb2eda787490f4c969b963", "https://bcr.bazel.build/modules/rules_java/6.4.0/MODULE.bazel": "e986a9fe25aeaa84ac17ca093ef13a4637f6107375f64667a15999f77db6c8f6", "https://bcr.bazel.build/modules/rules_java/6.5.2/MODULE.bazel": "1d440d262d0e08453fa0c4d8f699ba81609ed0e9a9a0f02cd10b3e7942e61e31", "https://bcr.bazel.build/modules/rules_java/7.1.0/MODULE.bazel": "30d9135a2b6561c761bd67bd4990da591e6bdc128790ce3e7afd6a3558b2fb64", "https://bcr.bazel.build/modules/rules_java/7.10.0/MODULE.bazel": "530c3beb3067e870561739f1144329a21c851ff771cd752a49e06e3dc9c2e71a", "https://bcr.bazel.build/modules/rules_java/7.12.2/MODULE.bazel": "579c505165ee757a4280ef83cda0150eea193eed3bef50b1004ba88b99da6de6", "https://bcr.bazel.build/modules/rules_java/7.2.0/MODULE.bazel": "06c0334c9be61e6cef2c8c84a7800cef502063269a5af25ceb100b192453d4ab", "https://bcr.bazel.build/modules/rules_java/7.3.2/MODULE.bazel": "50dece891cfdf1741ea230d001aa9c14398062f2b7c066470accace78e412bc2", "https://bcr.bazel.build/modules/rules_java/7.4.0/MODULE.bazel": "a592852f8a3dd539e82ee6542013bf2cadfc4c6946be8941e189d224500a8934", "https://bcr.bazel.build/modules/rules_java/7.6.1/MODULE.bazel": "2f14b7e8a1aa2f67ae92bc69d1ec0fa8d9f827c4e17ff5e5f02e91caa3b2d0fe", "https://bcr.bazel.build/modules/rules_java/8.12.0/MODULE.bazel": "8e6590b961f2defdfc2811c089c75716cb2f06c8a4edeb9a8d85eaa64ee2a761", "https://bcr.bazel.build/modules/rules_java/8.12.0/source.json": "cbd5d55d9d38d4008a7d00bee5b5a5a4b6031fcd4a56515c9accbcd42c7be2ba", "https://bcr.bazel.build/modules/rules_java/8.3.2/MODULE.bazel": "7336d5511ad5af0b8615fdc7477535a2e4e723a357b6713af439fe8cf0195017", "https://bcr.bazel.build/modules/rules_java/8.5.1/MODULE.bazel": "d8a9e38cc5228881f7055a6079f6f7821a073df3744d441978e7a43e20226939", "https://bcr.bazel.build/modules/rules_jvm_external/4.4.2/MODULE.bazel": "a56b85e418c83eb1839819f0b515c431010160383306d13ec21959ac412d2fe7", "https://bcr.bazel.build/modules/rules_jvm_external/5.1/MODULE.bazel": "33f6f999e03183f7d088c9be518a63467dfd0be94a11d0055fe2d210f89aa909", "https://bcr.bazel.build/modules/rules_jvm_external/5.2/MODULE.bazel": "d9351ba35217ad0de03816ef3ed63f89d411349353077348a45348b096615036", "https://bcr.bazel.build/modules/rules_jvm_external/5.3/MODULE.bazel": "bf93870767689637164657731849fb887ad086739bd5d360d90007a581d5527d", "https://bcr.bazel.build/modules/rules_jvm_external/6.0/MODULE.bazel": "37c93a5a78d32e895d52f86a8d0416176e915daabd029ccb5594db422e87c495", "https://bcr.bazel.build/modules/rules_jvm_external/6.1/MODULE.bazel": "75b5fec090dbd46cf9b7d8ea08cf84a0472d92ba3585b476f44c326eda8059c4", "https://bcr.bazel.build/modules/rules_jvm_external/6.3/MODULE.bazel": "c998e060b85f71e00de5ec552019347c8bca255062c990ac02d051bb80a38df0", "https://bcr.bazel.build/modules/rules_jvm_external/6.3/source.json": "6f5f5a5a4419ae4e37c35a5bb0a6ae657ed40b7abc5a5189111b47fcebe43197", "https://bcr.bazel.build/modules/rules_kotlin/1.9.0/MODULE.bazel": "ef85697305025e5a61f395d4eaede272a5393cee479ace6686dba707de804d59", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/MODULE.bazel": "d269a01a18ee74d0335450b10f62c9ed81f2321d7958a2934e44272fe82dcef3", "https://bcr.bazel.build/modules/rules_kotlin/1.9.6/source.json": "2faa4794364282db7c06600b7e5e34867a564ae91bda7cae7c29c64e9466b7d5", "https://bcr.bazel.build/modules/rules_license/0.0.3/MODULE.bazel": "627e9ab0247f7d1e05736b59dbb1b6871373de5ad31c3011880b4133cafd4bd0", "https://bcr.bazel.build/modules/rules_license/0.0.7/MODULE.bazel": "088fbeb0b6a419005b89cf93fe62d9517c0a2b8bb56af3244af65ecfe37e7d5d", "https://bcr.bazel.build/modules/rules_license/1.0.0/MODULE.bazel": "a7fda60eefdf3d8c827262ba499957e4df06f659330bbe6cdbdb975b768bb65c", "https://bcr.bazel.build/modules/rules_license/1.0.0/source.json": "a52c89e54cc311196e478f8382df91c15f7a2bfdf4c6cd0e2675cc2ff0b56efb", "https://bcr.bazel.build/modules/rules_multirun/0.12.0/MODULE.bazel": "8c2a45d1ed9ad938a4f8088e78781017974f6a90e6fc4172f6e3f275812a0b48", "https://bcr.bazel.build/modules/rules_multirun/0.12.0/source.json": "95c835bcba571ddffda1ea34e48dc7d93ca204e669db2fd2681f1104f0812e4d", "https://bcr.bazel.build/modules/rules_multitool/0.11.0/MODULE.bazel": "8d9dda78d2398e136300d3ef4fbcc89ede7c32c158d8c016fa7d032df41c4aaf", "https://bcr.bazel.build/modules/rules_multitool/0.11.0/source.json": "0b86574a1eaff37c33aafaff095ea16d6ac846beb94ffc74c4fcf626f8f80681", "https://bcr.bazel.build/modules/rules_nodejs/6.2.0/MODULE.bazel": "ec27907f55eb34705adb4e8257952162a2d4c3ed0f0b3b4c3c1aad1fac7be35e", "https://bcr.bazel.build/modules/rules_nodejs/6.3.0/MODULE.bazel": "45345e4aba35dd6e4701c1eebf5a4e67af4ed708def9ebcdc6027585b34ee52d", "https://bcr.bazel.build/modules/rules_nodejs/6.5.0/MODULE.bazel": "546d0cf79f36f9f6e080816045f97234b071c205f4542e3351bd4424282a8810", "https://bcr.bazel.build/modules/rules_nodejs/6.5.0/source.json": "ac075bc5babebc25a0adc88ee885f2c8d8520d141f6e139ba9dfa0eedb5be908", "https://bcr.bazel.build/modules/rules_oci/2.2.6/MODULE.bazel": "2ba6ddd679269e00aeffe9ca04faa2d0ca4129650982c9246d0d459fe2da47d9", "https://bcr.bazel.build/modules/rules_oci/2.2.6/source.json": "94e7decb8f95d9465b0bbea71c65064cd16083be1350c7468f131818641dc4a5", "https://bcr.bazel.build/modules/rules_pkg/0.7.0/MODULE.bazel": "df99f03fc7934a4737122518bb87e667e62d780b610910f0447665a7e2be62dc", "https://bcr.bazel.build/modules/rules_pkg/1.0.1/MODULE.bazel": "5b1df97dbc29623bccdf2b0dcd0f5cb08e2f2c9050aab1092fd39a41e82686ff", "https://bcr.bazel.build/modules/rules_pkg/1.1.0/MODULE.bazel": "9db8031e71b6ef32d1846106e10dd0ee2deac042bd9a2de22b4761b0c3036453", "https://bcr.bazel.build/modules/rules_pkg/1.1.0/source.json": "fef768df13a92ce6067e1cd0cdc47560dace01354f1d921cfb1d632511f7d608", "https://bcr.bazel.build/modules/rules_proto/4.0.0/MODULE.bazel": "a7a7b6ce9bee418c1a760b3d84f83a299ad6952f9903c67f19e4edd964894e06", "https://bcr.bazel.build/modules/rules_proto/5.3.0-21.7/MODULE.bazel": "e8dff86b0971688790ae75528fe1813f71809b5afd57facb44dad9e8eca631b7", "https://bcr.bazel.build/modules/rules_proto/6.0.0-rc1/MODULE.bazel": "1e5b502e2e1a9e825eef74476a5a1ee524a92297085015a052510b09a1a09483", "https://bcr.bazel.build/modules/rules_proto/6.0.0/MODULE.bazel": "b531d7f09f58dce456cd61b4579ce8c86b38544da75184eadaf0a7cb7966453f", "https://bcr.bazel.build/modules/rules_proto/6.0.2/MODULE.bazel": "ce916b775a62b90b61888052a416ccdda405212b6aaeb39522f7dc53431a5e73", "https://bcr.bazel.build/modules/rules_proto/7.0.2/MODULE.bazel": "bf81793bd6d2ad89a37a40693e56c61b0ee30f7a7fdbaf3eabbf5f39de47dea2", "https://bcr.bazel.build/modules/rules_proto/7.1.0/MODULE.bazel": "002d62d9108f75bb807cd56245d45648f38275cb3a99dcd45dfb864c5d74cb96", "https://bcr.bazel.build/modules/rules_proto/7.1.0/source.json": "39f89066c12c24097854e8f57ab8558929f9c8d474d34b2c00ac04630ad8940e", "https://bcr.bazel.build/modules/rules_proto_grpc/5.0.1/MODULE.bazel": "af7a76546e6fb5cfb37d30ece061bad276ceb785eb4ea43d6f74fc35cff71dfc", "https://bcr.bazel.build/modules/rules_proto_grpc/5.0.1/source.json": "eb2a5cd4344970803514e64bce3bb16840fe9476a4e9695d95c6e0475d821606", "https://bcr.bazel.build/modules/rules_proto_grpc_cpp/5.0.1/MODULE.bazel": "2bd7210df98e3690c7146b7679b1361a802eb01ec9431167ef5cafc641d9c94d", "https://bcr.bazel.build/modules/rules_proto_grpc_cpp/5.0.1/source.json": "aa40ef927cf13362c51c2c8796d6a67278e0de8c7b2f4874003dc503b6b4ff35", "https://bcr.bazel.build/modules/rules_proto_grpc_go/5.0.1/MODULE.bazel": "1d6a9096ae9d4714b5d82d693fb714fb0b1554d56e6fb1c18595c1e35a2121fb", "https://bcr.bazel.build/modules/rules_proto_grpc_go/5.0.1/source.json": "ec321eca680b081db23700e5e4b18f5c4e03feb438d26715aee99830889b3385", "https://bcr.bazel.build/modules/rules_proto_grpc_grpc_gateway/5.0.1/MODULE.bazel": "cb7f583257592d9e12f5997a3c84e359ae3313c739450dea194e097406726266", "https://bcr.bazel.build/modules/rules_proto_grpc_grpc_gateway/5.0.1/source.json": "260b735e74f52d4e1437539ccbbe81d218acc5554ca67a8ab1f5586b8623d0e2", "https://bcr.bazel.build/modules/rules_python/0.10.2/MODULE.bazel": "cc82bc96f2997baa545ab3ce73f196d040ffb8756fd2d66125a530031cd90e5f", "https://bcr.bazel.build/modules/rules_python/0.20.0/MODULE.bazel": "bfe14d17f20e3fe900b9588f526f52c967a6f281e47a1d6b988679bd15082286", "https://bcr.bazel.build/modules/rules_python/0.23.1/MODULE.bazel": "49ffccf0511cb8414de28321f5fcf2a31312b47c40cc21577144b7447f2bf300", "https://bcr.bazel.build/modules/rules_python/0.25.0/MODULE.bazel": "72f1506841c920a1afec76975b35312410eea3aa7b63267436bfb1dd91d2d382", "https://bcr.bazel.build/modules/rules_python/0.28.0/MODULE.bazel": "cba2573d870babc976664a912539b320cbaa7114cd3e8f053c720171cde331ed", "https://bcr.bazel.build/modules/rules_python/0.29.0/MODULE.bazel": "2ac8cd70524b4b9ec49a0b8284c79e4cd86199296f82f6e0d5da3f783d660c82", "https://bcr.bazel.build/modules/rules_python/0.31.0/MODULE.bazel": "93a43dc47ee570e6ec9f5779b2e64c1476a6ce921c48cc9a1678a91dd5f8fd58", "https://bcr.bazel.build/modules/rules_python/0.34.0/MODULE.bazel": "1d623d026e075b78c9fde483a889cda7996f5da4f36dffb24c246ab30f06513a", "https://bcr.bazel.build/modules/rules_python/0.36.0/MODULE.bazel": "a4ce1ccea92b9106c7d16ab9ee51c6183107e78ba4a37aa65055227b80cd480c", "https://bcr.bazel.build/modules/rules_python/0.4.0/MODULE.bazel": "9208ee05fd48bf09ac60ed269791cf17fb343db56c8226a720fbb1cdf467166c", "https://bcr.bazel.build/modules/rules_python/0.40.0/MODULE.bazel": "9d1a3cd88ed7d8e39583d9ffe56ae8a244f67783ae89b60caafc9f5cf318ada7", "https://bcr.bazel.build/modules/rules_python/1.0.0/MODULE.bazel": "898a3d999c22caa585eb062b600f88654bf92efb204fa346fb55f6f8edffca43", "https://bcr.bazel.build/modules/rules_python/1.5.2/MODULE.bazel": "5e4bbe842ffdb54dfb6ddd78de3d28151a4cc3417a685cee30c8fe4d1dc655a2", "https://bcr.bazel.build/modules/rules_python/1.5.2/source.json": "687aa52b6da283ee45cb702bc292386fd9c717264c38c0277b4957eeafbe1904", "https://bcr.bazel.build/modules/rules_shell/0.2.0/MODULE.bazel": "fda8a652ab3c7d8fee214de05e7a9916d8b28082234e8d2c0094505c5268ed3c", "https://bcr.bazel.build/modules/rules_shell/0.3.0/MODULE.bazel": "de4402cd12f4cc8fda2354fce179fdb068c0b9ca1ec2d2b17b3e21b24c1a937b", "https://bcr.bazel.build/modules/rules_shell/0.4.1/MODULE.bazel": "00e501db01bbf4e3e1dd1595959092c2fadf2087b2852d3f553b5370f5633592", "https://bcr.bazel.build/modules/rules_shell/0.4.1/source.json": "4757bd277fe1567763991c4425b483477bb82e35e777a56fd846eb5cceda324a", "https://bcr.bazel.build/modules/rules_swift/1.18.0/MODULE.bazel": "a6aba73625d0dc64c7b4a1e831549b6e375fbddb9d2dde9d80c9de6ec45b24c9", "https://bcr.bazel.build/modules/rules_swift/1.18.0/source.json": "9e636cabd446f43444ea2662341a9cbb74ecd87ab0557225ae73f1127cb7ff52", "https://bcr.bazel.build/modules/rules_uv/0.86.0/MODULE.bazel": "77417675ce4488aab2a5e647b8b16c357317b7a6326e5971b73b0406cb374426", "https://bcr.bazel.build/modules/rules_uv/0.86.0/source.json": "f01cf43e08da469d363512b517c2721fbaf6502c18d48be98c7deaaa01597601", "https://bcr.bazel.build/modules/stardoc/0.5.1/MODULE.bazel": "1a05d92974d0c122f5ccf09291442580317cdd859f07a8655f1db9a60374f9f8", "https://bcr.bazel.build/modules/stardoc/0.5.3/MODULE.bazel": "c7f6948dae6999bf0db32c1858ae345f112cacf98f174c7a8bb707e41b974f1c", "https://bcr.bazel.build/modules/stardoc/0.5.4/MODULE.bazel": "6569966df04610b8520957cb8e97cf2e9faac2c0309657c537ab51c16c18a2a4", "https://bcr.bazel.build/modules/stardoc/0.5.6/MODULE.bazel": "c43dabc564990eeab55e25ed61c07a1aadafe9ece96a4efabb3f8bf9063b71ef", "https://bcr.bazel.build/modules/stardoc/0.6.2/MODULE.bazel": "7060193196395f5dd668eda046ccbeacebfd98efc77fed418dbe2b82ffaa39fd", "https://bcr.bazel.build/modules/stardoc/0.7.0/MODULE.bazel": "05e3d6d30c099b6770e97da986c53bd31844d7f13d41412480ea265ac9e8079c", "https://bcr.bazel.build/modules/stardoc/0.7.1/MODULE.bazel": "3548faea4ee5dda5580f9af150e79d0f6aea934fc60c1cc50f4efdd9420759e7", "https://bcr.bazel.build/modules/stardoc/0.7.2/MODULE.bazel": "fc152419aa2ea0f51c29583fab1e8c99ddefd5b3778421845606ee628629e0e5", "https://bcr.bazel.build/modules/stardoc/0.7.2/source.json": "58b029e5e901d6802967754adf0a9056747e8176f017cfe3607c0851f4d42216", "https://bcr.bazel.build/modules/tar.bzl/0.2.1/MODULE.bazel": "52d1c00a80a8cc67acbd01649e83d8dd6a9dc426a6c0b754a04fe8c219c76468", "https://bcr.bazel.build/modules/tar.bzl/0.2.1/source.json": "600ac6ff61744667a439e7b814ae59c1f29632c3984fccf8000c64c9db8d7bb6", "https://bcr.bazel.build/modules/toolchains_protoc/0.3.1/MODULE.bazel": "b6574a2a314cbd40cafb5ed87b03d1996e015315f80a7e33116c8b2e209cb5cf", "https://bcr.bazel.build/modules/toolchains_protoc/0.4.3/MODULE.bazel": "54daf5468a9c3e52f6c8a96c8e0b867f7b30029dfe1e74f5a59bf081921d91a3", "https://bcr.bazel.build/modules/toolchains_protoc/0.4.3/source.json": "fbf3886395e08c407caca84f92f8c9ad92b05ce126a94883def1e150edd6b417", "https://bcr.bazel.build/modules/upb/0.0.0-20211020-160625a/MODULE.bazel": "6cced416be2dc5b9c05efd5b997049ba795e5e4e6fafbe1624f4587767638928", "https://bcr.bazel.build/modules/upb/0.0.0-20220923-a547704/MODULE.bazel": "7298990c00040a0e2f121f6c32544bab27d4452f80d9ce51349b1a28f3005c43", "https://bcr.bazel.build/modules/upb/0.0.0-20230516-61a97ef/MODULE.bazel": "c0df5e35ad55e264160417fd0875932ee3c9dda63d9fccace35ac62f45e1b6f9", "https://bcr.bazel.build/modules/upb/0.0.0-20230907-e7430e6/MODULE.bazel": "3a7dedadf70346e678dc059dbe44d05cbf3ab17f1ce43a1c7a42edc7cbf93fd9", "https://bcr.bazel.build/modules/upb/0.0.0-20230907-e7430e6/source.json": "6e513de1d26d1ded97a1c98a8ee166ff9be371a71556d4bc91220332dd3aa48e", "https://bcr.bazel.build/modules/yq.bzl/0.1.1/MODULE.bazel": "9039681f9bcb8958ee2c87ffc74bdafba9f4369096a2b5634b88abc0eaefa072", "https://bcr.bazel.build/modules/yq.bzl/0.1.1/source.json": "2d2bad780a9f2b9195a4a370314d2c17ae95eaa745cefc2e12fbc49759b15aa3", "https://bcr.bazel.build/modules/zlib/1.2.11/MODULE.bazel": "07b389abc85fdbca459b69e2ec656ae5622873af3f845e1c9d80fe179f3effa0", "https://bcr.bazel.build/modules/zlib/1.2.12/MODULE.bazel": "3b1a8834ada2a883674be8cbd36ede1b6ec481477ada359cd2d3ddc562340b27", "https://bcr.bazel.build/modules/zlib/1.2.13/MODULE.bazel": "aa6deb1b83c18ffecd940c4119aff9567cd0a671d7bba756741cb2ef043a29d5", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.1/MODULE.bazel": "6a9fe6e3fc865715a7be9823ce694ceb01e364c35f7a846bf0d2b34762bc066b", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.5/MODULE.bazel": "eec517b5bbe5492629466e11dae908d043364302283de25581e3eb944326c4ca", "https://bcr.bazel.build/modules/zlib/1.3.1.bcr.5/source.json": "22bc55c47af97246cfc093d0acf683a7869377de362b5d1c552c2c2e16b7a806", "https://bcr.bazel.build/modules/zlib/1.3.1/MODULE.bazel": "751c9940dcfe869f5f7274e1295422a34623555916eb98c174c1e945594bf198", "https://bcr.bazel.build/modules/zlib/1.3/MODULE.bazel": "6a9c02f19a24dcedb05572b2381446e27c272cd383aed11d41d99da9e3167a72"}, "selectedYankedVersions": {}, "moduleExtensions": {"@@apple_support+//crosstool:setup.bzl%apple_cc_configure_extension": {"general": {"bzlTransitiveDigest": "E970FlMbwpgJPdPUQzatKh6BMfeE0ZpWABvwshh7Tmg=", "usagesDigest": "yAC1H7cg3wkisnNswc7hxM2fAxrH04yqn7CXVasZPgc=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"local_config_apple_cc_toolchains": {"repoRuleId": "@@apple_support+//crosstool:setup.bzl%_apple_cc_autoconf_toolchains", "attributes": {}}, "local_config_apple_cc": {"repoRuleId": "@@apple_support+//crosstool:setup.bzl%_apple_cc_autoconf", "attributes": {}}}, "recordedRepoMappingEntries": [["apple_support+", "bazel_tools", "bazel_tools"], ["bazel_tools", "rules_cc", "rules_cc+"]]}}, "@@aspect_rules_js+//npm:extensions.bzl%pnpm": {"general": {"bzlTransitiveDigest": "y9+lsmi4sD4YOdFkJSc5Vqu2ZKXpxgzr4iHHF0T2odA=", "usagesDigest": "OjMGppzZwOZvn2C5v9krRBo66EZmRm1wvSyrb/DRaBs=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"pnpm": {"repoRuleId": "@@aspect_rules_js+//npm/private:npm_import.bzl%npm_import_rule", "attributes": {"package": "pnpm", "version": "10.13.1", "root_package": "", "link_workspace": "", "link_packages": {}, "integrity": "sha512-N+vxpcejDV+r4MXfRO6NpMllygxa89urKMOhaBtwolYhjQXIHJwNz3Z+9rhVHrW5YAQrntQwDFkkIzY3fgHPrQ==", "url": "", "commit": "", "patch_args": ["-p0"], "patches": [], "custom_postinstall": "", "npm_auth": "", "npm_auth_basic": "", "npm_auth_username": "", "npm_auth_password": "", "lifecycle_hooks": [], "extra_build_content": "load(\"@aspect_rules_js//js:defs.bzl\", \"js_binary\")\njs_binary(name = \"pnpm\", data = glob([\"package/**\"]), entry_point = \"package/dist/pnpm.cjs\", visibility = [\"//visibility:public\"])", "generate_bzl_library_targets": false, "extract_full_archive": true, "exclude_package_contents": [], "system_tar": "auto"}}, "pnpm__links": {"repoRuleId": "@@aspect_rules_js+//npm/private:npm_import.bzl%npm_import_links", "attributes": {"package": "pnpm", "version": "10.13.1", "dev": false, "root_package": "", "link_packages": {}, "deps": {}, "transitive_closure": {}, "lifecycle_build_target": false, "lifecycle_hooks_env": [], "lifecycle_hooks_execution_requirements": ["no-sandbox"], "lifecycle_hooks_use_default_shell_env": false, "bins": {}, "package_visibility": ["//visibility:public"], "replace_package": "", "exclude_package_contents": []}}}, "recordedRepoMappingEntries": [["aspect_bazel_lib+", "bazel_skylib", "bazel_skylib+"], ["aspect_bazel_lib+", "bazel_tools", "bazel_tools"], ["aspect_bazel_lib+", "tar.bzl", "tar.bzl+"], ["aspect_rules_js+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["aspect_rules_js+", "aspect_rules_js", "aspect_rules_js+"], ["aspect_rules_js+", "aspect_tools_telemetry_report", "aspect_tools_telemetry++telemetry+aspect_tools_telemetry_report"], ["aspect_rules_js+", "bazel_features", "bazel_features+"], ["aspect_rules_js+", "bazel_skylib", "bazel_skylib+"], ["aspect_rules_js+", "bazel_tools", "bazel_tools"], ["bazel_features+", "bazel_features_globals", "bazel_features++version_extension+bazel_features_globals"], ["bazel_features+", "bazel_features_version", "bazel_features++version_extension+bazel_features_version"], ["tar.bzl+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["tar.bzl+", "bazel_skylib", "bazel_skylib+"], ["tar.bzl+", "tar.bzl", "tar.bzl+"]]}}, "@@aspect_rules_py+//py:extensions.bzl%py_tools": {"general": {"bzlTransitiveDigest": "ohS3BJb0sPO700eGxgxyblOkuflooRkg4hDSdyaANek=", "usagesDigest": "OFyp8YJOi6iA3wAXkgaqSKHonRa2op1AgAWho7GAIGc=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"bsd_tar_darwin_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "darwin_amd64"}}, "bsd_tar_darwin_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "darwin_arm64"}}, "bsd_tar_linux_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "linux_amd64"}}, "bsd_tar_linux_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "linux_arm64"}}, "bsd_tar_windows_amd64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "windows_amd64"}}, "bsd_tar_windows_arm64": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%bsdtar_binary_repo", "attributes": {"platform": "windows_arm64"}}, "bsd_tar_toolchains": {"repoRuleId": "@@aspect_bazel_lib+//lib/private:tar_toolchain.bzl%tar_toolchains_repo", "attributes": {"user_repository_name": "bsd_tar"}}, "rules_py_tools.darwin_amd64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:repo.bzl%prebuilt_tool_repo", "attributes": {"platform": "darwin_amd64"}}, "rules_py_tools.darwin_arm64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:repo.bzl%prebuilt_tool_repo", "attributes": {"platform": "darwin_arm64"}}, "rules_py_tools.linux_amd64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:repo.bzl%prebuilt_tool_repo", "attributes": {"platform": "linux_amd64"}}, "rules_py_tools.linux_arm64": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:repo.bzl%prebuilt_tool_repo", "attributes": {"platform": "linux_arm64"}}, "rules_py_tools": {"repoRuleId": "@@aspect_rules_py+//py/private/toolchain:repo.bzl%toolchains_repo", "attributes": {"user_repository_name": "rules_py_tools"}}, "rules_py_pex_2_3_1": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://files.pythonhosted.org/packages/e7/d0/fbda2a4d41d62d86ce53f5ae4fbaaee8c34070f75bb7ca009090510ae874/pex-2.3.1-py2.py3-none-any.whl"], "sha256": "64692a5bf6f298403aab930d22f0d836ae4736c5bc820e262e9092fe8c56f830", "downloaded_file_path": "pex-2.3.1-py2.py3-none-any.whl"}}}, "recordedRepoMappingEntries": [["aspect_bazel_lib+", "bazel_tools", "bazel_tools"], ["aspect_rules_py+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["aspect_rules_py+", "aspect_tools_telemetry_report", "aspect_tools_telemetry++telemetry+aspect_tools_telemetry_report"], ["aspect_rules_py+", "bazel_skylib", "bazel_skylib+"], ["aspect_rules_py+", "bazel_tools", "bazel_tools"]]}}, "@@aspect_rules_ts+//ts:extensions.bzl%ext": {"general": {"bzlTransitiveDigest": "aVqwKoRPrSXO367SJABlye04kmpR/9VM2xiXB3nh3Ls=", "usagesDigest": "DUgr9xnkjfNbXY80AKYzOmFR3Cqmx4BSSedvlwZmvhw=", "recordedFileInputs": {"@@//package.json": "a246f48f0a9996ac3f74169b7ccc9539d2bae8e845fe348db3a014ff3dd478d0"}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"npm_typescript": {"repoRuleId": "@@aspect_rules_ts+//ts/private:npm_repositories.bzl%http_archive_version", "attributes": {"bzlmod": true, "version": "", "version_from": "@@//:package.json", "integrity": "", "build_file": "@@aspect_rules_ts+//ts:BUILD.typescript", "build_file_substitutions": {"bazel_worker_version": "5.4.2", "google_protobuf_version": "3.20.1"}, "urls": ["https://registry.npmjs.org/typescript/-/typescript-{}.tgz"]}}}, "recordedRepoMappingEntries": [["aspect_rules_ts+", "bazel_tools", "bazel_tools"]]}}, "@@aspect_tools_telemetry+//:extension.bzl%telemetry": {"general": {"bzlTransitiveDigest": "cLuD0cAZWm2SwvVSu2NHX+0x33L7A5+Shk+6Qcw9oik=", "usagesDigest": "qwFyhYXgEoQpvkg6HDinZUd3YaGghaeW3S0NUhtmHjs=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"aspect_tools_telemetry_report": {"repoRuleId": "@@aspect_tools_telemetry+//:extension.bzl%tel_repository", "attributes": {"deps": {"aspect_rules_js": "2.4.2", "aspect_rules_py": "1.6.1", "aspect_tools_telemetry": "0.2.3"}}}}, "recordedRepoMappingEntries": [["aspect_tools_telemetry+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["aspect_tools_telemetry+", "bazel_skylib", "bazel_skylib+"]]}}, "@@buildifier_prebuilt+//:defs.bzl%buildifier_prebuilt_deps_extension": {"general": {"bzlTransitiveDigest": "y2GJUI2VOwO/vv3fwJwIPYNwcHBgaDeGXzvJQ5gZ/3Y=", "usagesDigest": "m+RORtK3MOrJs2auGj/7mY7N11R7swVsHYHg1jls5hs=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"buildifier_darwin_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildifier-darwin-amd64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "eeb47b2de27f60efe549348b183fac24eae80f1479e8b06cac0799c486df5bed"}}, "buildifier_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildifier-darwin-arm64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "fa07ba0d20165917ca4cc7609f9b19a8a4392898148b7babdf6bb2a7dd963f05"}}, "buildifier_linux_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildifier-linux-amd64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "be63db12899f48600bad94051123b1fd7b5251e7661b9168582ce52396132e92"}}, "buildifier_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildifier-linux-arm64"], "downloaded_file_path": "buildifier", "executable": true, "sha256": "18540fc10f86190f87485eb86963e603e41fa022f88a2d1b0cf52ff252b5e1dd"}}, "buildifier_windows_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildifier-windows-amd64.exe"], "downloaded_file_path": "buildifier.exe", "executable": true, "sha256": "da8372f35e34b65fb6d997844d041013bb841e55f58b54d596d35e49680fe13c"}}, "buildozer_darwin_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildozer-darwin-amd64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "d29e347ecd6b5673d72cb1a8de05bf1b06178dd229ff5eb67fad5100c840cc8e"}}, "buildozer_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildozer-darwin-arm64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "9b9e71bdbec5e7223871e913b65d12f6d8fa026684daf991f00e52ed36a6978d"}}, "buildozer_linux_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildozer-linux-amd64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "8dfd6345da4e9042daa738d7fdf34f699c5dfce4632f7207956fceedd8494119"}}, "buildozer_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildozer-linux-arm64"], "downloaded_file_path": "<PERSON><PERSON><PERSON>", "executable": true, "sha256": "6559558fded658c8fa7432a9d011f7c4dcbac6b738feae73d2d5c352e5f605fa"}}, "buildozer_windows_amd64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"urls": ["https://github.com/bazelbuild/buildtools/releases/download/v6.4.0/buildozer-windows-amd64.exe"], "downloaded_file_path": "buildozer.exe", "executable": true, "sha256": "e7f05bf847f7c3689dd28926460ce6e1097ae97380ac8e6ae7147b7b706ba19b"}}, "buildifier_prebuilt_toolchains": {"repoRuleId": "@@buildifier_prebuilt+//:defs.bzl%_buildifier_toolchain_setup", "attributes": {"assets_json": "[{\"arch\":\"amd64\",\"name\":\"buildifier\",\"platform\":\"darwin\",\"sha256\":\"eeb47b2de27f60efe549348b183fac24eae80f1479e8b06cac0799c486df5bed\",\"version\":\"v6.4.0\"},{\"arch\":\"arm64\",\"name\":\"buildifier\",\"platform\":\"darwin\",\"sha256\":\"fa07ba0d20165917ca4cc7609f9b19a8a4392898148b7babdf6bb2a7dd963f05\",\"version\":\"v6.4.0\"},{\"arch\":\"amd64\",\"name\":\"buildifier\",\"platform\":\"linux\",\"sha256\":\"be63db12899f48600bad94051123b1fd7b5251e7661b9168582ce52396132e92\",\"version\":\"v6.4.0\"},{\"arch\":\"arm64\",\"name\":\"buildifier\",\"platform\":\"linux\",\"sha256\":\"18540fc10f86190f87485eb86963e603e41fa022f88a2d1b0cf52ff252b5e1dd\",\"version\":\"v6.4.0\"},{\"arch\":\"amd64\",\"name\":\"buildifier\",\"platform\":\"windows\",\"sha256\":\"da8372f35e34b65fb6d997844d041013bb841e55f58b54d596d35e49680fe13c\",\"version\":\"v6.4.0\"},{\"arch\":\"amd64\",\"name\":\"buildozer\",\"platform\":\"darwin\",\"sha256\":\"d29e347ecd6b5673d72cb1a8de05bf1b06178dd229ff5eb67fad5100c840cc8e\",\"version\":\"v6.4.0\"},{\"arch\":\"arm64\",\"name\":\"buildozer\",\"platform\":\"darwin\",\"sha256\":\"9b9e71bdbec5e7223871e913b65d12f6d8fa026684daf991f00e52ed36a6978d\",\"version\":\"v6.4.0\"},{\"arch\":\"amd64\",\"name\":\"buildozer\",\"platform\":\"linux\",\"sha256\":\"8dfd6345da4e9042daa738d7fdf34f699c5dfce4632f7207956fceedd8494119\",\"version\":\"v6.4.0\"},{\"arch\":\"arm64\",\"name\":\"buildozer\",\"platform\":\"linux\",\"sha256\":\"6559558fded658c8fa7432a9d011f7c4dcbac6b738feae73d2d5c352e5f605fa\",\"version\":\"v6.4.0\"},{\"arch\":\"amd64\",\"name\":\"buildozer\",\"platform\":\"windows\",\"sha256\":\"e7f05bf847f7c3689dd28926460ce6e1097ae97380ac8e6ae7147b7b706ba19b\",\"version\":\"v6.4.0\"}]"}}}, "recordedRepoMappingEntries": [["buildifier_prebuilt+", "bazel_skylib", "bazel_skylib+"], ["buildifier_prebuilt+", "bazel_tools", "bazel_tools"]]}}, "@@openapi_tools_generator_bazel+//:extension.bzl%openapi_gen": {"general": {"bzlTransitiveDigest": "OpLFSbBZQIsnXk7RdEflIQTNoFCU+0FDFCogz5A2rAc=", "usagesDigest": "SaFhS5QWvxieHYmjnoneMFGwHZgu8fP1ohVQ2z1dIZY=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"openapi_tools_generator_bazel_cli": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:jvm.bzl%jvm_import_external", "attributes": {"generated_rule_name": "openapi_tools_generator_bazel_cli", "artifact_urls": ["https://repo1.maven.org/maven2/org/openapitools/openapi-generator-cli/7.12.0/openapi-generator-cli-7.12.0.jar"], "srcjar_urls": [], "canonical_id": "org.openapitools:openapi-generator-cli:7.12.0", "rule_name": "java_import", "tags": ["maven_coordinates=org.openapitools:openapi-generator-cli:7.12.0"], "artifact_sha256": "33e7dfa7a1f04d58405ee12ae19e2c6fc2a91497cf2e56fa68f1875a95cbf220"}}}, "recordedRepoMappingEntries": [["openapi_tools_generator_bazel+", "bazel_tools", "bazel_tools"]]}}, "@@rules_foreign_cc+//foreign_cc:extensions.bzl%tools": {"general": {"bzlTransitiveDigest": "ginC3lIGOKKivBi0nyv2igKvSiz42Thm8yaX2RwVaHg=", "usagesDigest": "9LXdVp01HkdYQT8gYPjYLO6VLVJHo9uFfxWaU1ymiRE=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"rules_foreign_cc_framework_toolchain_linux": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:linux_commands.bzl", "exec_compatible_with": ["@platforms//os:linux"]}}, "rules_foreign_cc_framework_toolchain_freebsd": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:freebsd_commands.bzl", "exec_compatible_with": ["@platforms//os:freebsd"]}}, "rules_foreign_cc_framework_toolchain_windows": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:windows_commands.bzl", "exec_compatible_with": ["@platforms//os:windows"]}}, "rules_foreign_cc_framework_toolchain_macos": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository", "attributes": {"commands_src": "@rules_foreign_cc//foreign_cc/private/framework/toolchains:macos_commands.bzl", "exec_compatible_with": ["@platforms//os:macos"]}}, "rules_foreign_cc_framework_toolchains": {"repoRuleId": "@@rules_foreign_cc+//foreign_cc/private/framework:toolchain.bzl%framework_toolchain_repository_hub", "attributes": {}}, "cmake_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "f316b40053466f9a416adf981efda41b160ca859e97f6a484b447ea299ff26aa", "strip_prefix": "cmake-3.23.2", "urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2.tar.gz"]}}, "gnumake_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "581f4d4e872da74b3941c874215898a7d35802f03732bdccee1d4a7979105d18", "strip_prefix": "make-4.4", "urls": ["https://mirror.bazel.build/ftpmirror.gnu.org/gnu/make/make-4.4.tar.gz", "http://ftpmirror.gnu.org/gnu/make/make-4.4.tar.gz"]}}, "ninja_build_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "31747ae633213f1eda3842686f83c2aa1412e0f5691d1c14dbbcc67fe7400cea", "strip_prefix": "ninja-1.11.1", "urls": ["https://github.com/ninja-build/ninja/archive/v1.11.1.tar.gz"]}}, "meson_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "exports_files([\"meson.py\"])\n\nfilegroup(\n    name = \"runtime\",\n    srcs = glob([\"mesonbuild/**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "strip_prefix": "meson-1.1.1", "url": "https://github.com/mesonbuild/meson/releases/download/1.1.1/meson-1.1.1.tar.gz"}}, "glib_dev": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\nload(\"@rules_cc//cc:defs.bzl\", \"cc_library\")\n\ncc_import(\n    name = \"glib_dev\",\n    hdrs = glob([\"include/**\"]),\n    shared_library = \"@glib_runtime//:bin/libglib-2.0-0.dll\",\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "bdf18506df304d38be98a4b3f18055b8b8cca81beabecad0eece6ce95319c369", "urls": ["https://download.gnome.org/binaries/win64/glib/2.26/glib-dev_2.26.1-1_win64.zip"]}}, "glib_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\ncc_import(\n    name = \"msvc_hdr\",\n    hdrs = [\"msvc_recommended_pragmas.h\"],\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "bc96f63112823b7d6c9f06572d2ad626ddac7eb452c04d762592197f6e07898e", "strip_prefix": "glib-2.26.1", "urls": ["https://download.gnome.org/sources/glib/2.26/glib-2.26.1.tar.gz"]}}, "glib_runtime": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\nexports_files(\n    [\n        \"bin/libgio-2.0-0.dll\",\n        \"bin/libglib-2.0-0.dll\",\n        \"bin/libgmodule-2.0-0.dll\",\n        \"bin/libgobject-2.0-0.dll\",\n        \"bin/libgthread-2.0-0.dll\",\n    ],\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "88d857087e86f16a9be651ee7021880b3f7ba050d34a1ed9f06113b8799cb973", "urls": ["https://download.gnome.org/binaries/win64/glib/2.26/glib_2.26.1-1_win64.zip"]}}, "gettext_runtime": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "\ncc_import(\n    name = \"gettext_runtime\",\n    shared_library = \"bin/libintl-8.dll\",\n    visibility = [\"//visibility:public\"],\n)\n        ", "sha256": "1f4269c0e021076d60a54e98da6f978a3195013f6de21674ba0edbc339c5b079", "urls": ["https://download.gnome.org/binaries/win64/dependencies/gettext-runtime_0.18.1.1-2_win64.zip"]}}, "pkgconfig_src": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"build_file_content": "filegroup(\n    name = \"all_srcs\",\n    srcs = glob([\"**\"]),\n    visibility = [\"//visibility:public\"],\n)\n", "sha256": "6fc69c01688c9458a57eb9a1664c9aba372ccda420a02bf4429fe610e7e7d591", "strip_prefix": "pkg-config-0.29.2", "patches": ["@@rules_foreign_cc+//toolchains:pkgconfig-detectenv.patch", "@@rules_foreign_cc+//toolchains:pkgconfig-makefile-vc.patch"], "urls": ["https://pkgconfig.freedesktop.org/releases/pkg-config-0.29.2.tar.gz"]}}, "bazel_skylib": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://mirror.bazel.build/github.com/bazelbuild/bazel-skylib/releases/download/1.2.1/bazel-skylib-1.2.1.tar.gz", "https://github.com/bazelbuild/bazel-skylib/releases/download/1.2.1/bazel-skylib-1.2.1.tar.gz"], "sha256": "f7be3474d42aae265405a592bb7da8e171919d74c16f082a5457840f06054728"}}, "rules_python": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "84aec9e21cc56fbc7f1335035a71c850d1b9b5cc6ff497306f84cced9a769841", "strip_prefix": "rules_python-0.23.1", "url": "https://github.com/bazelbuild/rules_python/archive/refs/tags/0.23.1.tar.gz"}}, "cmake-3.23.2-linux-aarch64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-linux-aarch64.tar.gz"], "sha256": "f2654bf780b53f170bbbec44d8ac67d401d24788e590faa53036a89476efa91e", "strip_prefix": "cmake-3.23.2-linux-aarch64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-linux-x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-linux-x86_64.tar.gz"], "sha256": "aaced6f745b86ce853661a595bdac6c5314a60f8181b6912a0a4920acfa32708", "strip_prefix": "cmake-3.23.2-linux-x86_64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-macos-universal": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-macos-universal.tar.gz"], "sha256": "853a0f9af148c5ef47282ffffee06c4c9f257be2635936755f39ca13c3286c88", "strip_prefix": "cmake-3.23.2-macos-universal/CMake.app/Contents", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-windows-i386": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-windows-i386.zip"], "sha256": "6a4fcd6a2315b93cb23c93507efccacc30c449c2bf98f14d6032bb226c582e07", "strip_prefix": "cmake-3.23.2-windows-i386", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake.exe\",\n    target = \":cmake_data\",\n)\n"}}, "cmake-3.23.2-windows-x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/Kitware/CMake/releases/download/v3.23.2/cmake-3.23.2-windows-x86_64.zip"], "sha256": "2329387f3166b84c25091c86389fb891193967740c9bcf01e7f6d3306f7ffda0", "strip_prefix": "cmake-3.23.2-windows-x86_64", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"cmake_data\",\n    srcs = glob(\n        [\n            \"**\",\n        ],\n        exclude = [\n            \"WORKSPACE\",\n            \"WORKSPACE.bazel\",\n            \"BUILD\",\n            \"BUILD.bazel\",\n        ],\n    ),\n)\n\nnative_tool_toolchain(\n    name = \"cmake_tool\",\n    path = \"bin/cmake.exe\",\n    target = \":cmake_data\",\n)\n"}}, "cmake_3.23.2_toolchains": {"repoRuleId": "@@rules_foreign_cc+//toolchains:prebuilt_toolchains_repository.bzl%prebuilt_toolchains_repository", "attributes": {"repos": {"cmake-3.23.2-linux-aarch64": ["@platforms//cpu:aarch64", "@platforms//os:linux"], "cmake-3.23.2-linux-x86_64": ["@platforms//cpu:x86_64", "@platforms//os:linux"], "cmake-3.23.2-macos-universal": ["@platforms//os:macos"], "cmake-3.23.2-windows-i386": ["@platforms//cpu:x86_32", "@platforms//os:windows"], "cmake-3.23.2-windows-x86_64": ["@platforms//cpu:x86_64", "@platforms//os:windows"]}, "tool": "cmake"}}, "ninja_1.11.1_linux": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-linux.zip"], "sha256": "b901ba96e486dce377f9a070ed4ef3f79deb45f4ffe2938f8e7ddc69cfb3df77", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_mac": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-mac.zip"], "sha256": "482ecb23c59ae3d4f158029112de172dd96bb0e97549c4b1ca32d8fad11f873e", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_win": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"urls": ["https://github.com/ninja-build/ninja/releases/download/v1.11.1/ninja-win.zip"], "sha256": "524b344a1a9a55005eaf868d991e090ab8ce07fa109f1820d40e74642e289abc", "strip_prefix": "", "build_file_content": "load(\"@rules_foreign_cc//toolchains/native_tools:native_tools_toolchain.bzl\", \"native_tool_toolchain\")\n\npackage(default_visibility = [\"//visibility:public\"])\n\nfilegroup(\n    name = \"ninja_bin\",\n    srcs = [\"ninja.exe\"],\n)\n\nnative_tool_toolchain(\n    name = \"ninja_tool\",\n    env = {\"NINJA\": \"$(execpath :ninja_bin)\"},\n    path = \"$(execpath :ninja_bin)\",\n    target = \":ninja_bin\",\n)\n"}}, "ninja_1.11.1_toolchains": {"repoRuleId": "@@rules_foreign_cc+//toolchains:prebuilt_toolchains_repository.bzl%prebuilt_toolchains_repository", "attributes": {"repos": {"ninja_1.11.1_linux": ["@platforms//cpu:x86_64", "@platforms//os:linux"], "ninja_1.11.1_mac": ["@platforms//cpu:x86_64", "@platforms//os:macos"], "ninja_1.11.1_win": ["@platforms//cpu:x86_64", "@platforms//os:windows"]}, "tool": "ninja"}}}, "recordedRepoMappingEntries": [["rules_foreign_cc+", "bazel_tools", "bazel_tools"], ["rules_foreign_cc+", "rules_foreign_cc", "rules_foreign_cc+"]]}}, "@@rules_kotlin+//src/main/starlark/core/repositories:bzlmod_setup.bzl%rules_kotlin_extensions": {"general": {"bzlTransitiveDigest": "hUTp2w+RUVdL7ma5esCXZJAFnX7vLbVfLd7FwnQI6bU=", "usagesDigest": "QI2z8ZUR+mqtbwsf2fLqYdJAkPOHdOV+tF2yVAUgRzw=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"com_github_jetbrains_kotlin_git": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:compiler.bzl%kotlin_compiler_git_repository", "attributes": {"urls": ["https://github.com/JetBrains/kotlin/releases/download/v1.9.23/kotlin-compiler-1.9.23.zip"], "sha256": "93137d3aab9afa9b27cb06a824c2324195c6b6f6179d8a8653f440f5bd58be88"}}, "com_github_jetbrains_kotlin": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:compiler.bzl%kotlin_capabilities_repository", "attributes": {"git_repository_name": "com_github_jetbrains_kotlin_git", "compiler_version": "1.9.23"}}, "com_github_google_ksp": {"repoRuleId": "@@rules_kotlin+//src/main/starlark/core/repositories:ksp.bzl%ksp_compiler_plugin_repository", "attributes": {"urls": ["https://github.com/google/ksp/releases/download/1.9.23-1.0.20/artifacts.zip"], "sha256": "ee0618755913ef7fd6511288a232e8fad24838b9af6ea73972a76e81053c8c2d", "strip_version": "1.9.23-1.0.20"}}, "com_github_pinterest_ktlint": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"sha256": "01b2e0ef893383a50dbeb13970fe7fa3be36ca3e83259e01649945b09d736985", "urls": ["https://github.com/pinterest/ktlint/releases/download/1.3.0/ktlint"], "executable": true}}, "rules_android": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_archive", "attributes": {"sha256": "cd06d15dd8bb59926e4d65f9003bfc20f9da4b2519985c27e190cddc8b7a7806", "strip_prefix": "rules_android-0.1.1", "urls": ["https://github.com/bazelbuild/rules_android/archive/v0.1.1.zip"]}}}, "recordedRepoMappingEntries": [["rules_kotlin+", "bazel_tools", "bazel_tools"]]}}, "@@rules_multitool+//multitool:extension.bzl%multitool": {"general": {"bzlTransitiveDigest": "F/gq5cF9lcIjruwcMimPkE1tSGYtXAE1r4NFG6NkBFg=", "usagesDigest": "2K3ETTVFXAEX9lUqwGJp0LP7SiDd8nzOHZG9Ccu4oNA=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"multitool.linux_arm64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "linux", "cpu": "arm64"}}, "multitool.linux_x86_64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "linux", "cpu": "x86_64"}}, "multitool.macos_arm64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "macos", "cpu": "arm64"}}, "multitool.macos_x86_64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "macos", "cpu": "x86_64"}}, "multitool.windows_arm64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "windows", "cpu": "arm64"}}, "multitool.windows_x86_64": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_env_specific_tools", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"], "os": "windows", "cpu": "x86_64"}}, "multitool": {"repoRuleId": "@@rules_multitool+//multitool/private:multitool.bzl%_multitool_hub", "attributes": {"lockfiles": ["@@rules_uv+//uv/private:uv.lock.json"]}}}, "recordedRepoMappingEntries": [["bazel_features+", "bazel_features_globals", "bazel_features++version_extension+bazel_features_globals"], ["bazel_features+", "bazel_features_version", "bazel_features++version_extension+bazel_features_version"], ["rules_multitool+", "bazel_features", "bazel_features+"]]}}, "@@rules_nodejs+//nodejs:extensions.bzl%node": {"general": {"bzlTransitiveDigest": "hdICB1K7PX7oWtO8oksVTBDNt6xxiNERpcO4Yxoa0Gc=", "usagesDigest": "iBHAAvIvyPcKqE3TCA+qwUmFnIBlMs/lHW/pHIa8PX8=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"nodejs_linux_amd64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "linux_amd64"}}, "nodejs_linux_arm64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "linux_arm64"}}, "nodejs_linux_s390x": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "linux_s390x"}}, "nodejs_linux_ppc64le": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "linux_ppc64le"}}, "nodejs_darwin_amd64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "darwin_amd64"}}, "nodejs_darwin_arm64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "darwin_arm64"}}, "nodejs_windows_amd64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "windows_amd64"}}, "nodejs_windows_arm64": {"repoRuleId": "@@rules_nodejs+//nodejs:repositories.bzl%_nodejs_repositories", "attributes": {"node_download_auth": {}, "node_repositories": {}, "node_urls": ["https://nodejs.org/dist/v{version}/{filename}"], "node_version": "22.18.0", "include_headers": false, "platform": "windows_arm64"}}, "nodejs": {"repoRuleId": "@@rules_nodejs+//nodejs/private:nodejs_repo_host_os_alias.bzl%nodejs_repo_host_os_alias", "attributes": {"user_node_repository_name": "nodejs"}}, "nodejs_host": {"repoRuleId": "@@rules_nodejs+//nodejs/private:nodejs_repo_host_os_alias.bzl%nodejs_repo_host_os_alias", "attributes": {"user_node_repository_name": "nodejs"}}, "nodejs_toolchains": {"repoRuleId": "@@rules_nodejs+//nodejs/private:nodejs_toolchains_repo.bzl%nodejs_toolchains_repo", "attributes": {"user_node_repository_name": "nodejs"}}}, "recordedRepoMappingEntries": []}}, "@@rules_oci+//oci:extensions.bzl%oci": {"general": {"bzlTransitiveDigest": "iQZOkLD5gZw8LZ9chmHTJF8IzhJjzPejnvw6vAz6Qmk=", "usagesDigest": "M53E2AOOcdAIXInsCMvpoAvVze9HbnrOb3ABmGW4u2Y=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"go_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"www_authenticate_challenges": {}, "scheme": "https", "registry": "gcr.io", "repository": "distroless/base", "identifier": "debug", "platform": "linux/amd64", "target_name": "go_base_image_linux_amd64", "bazel_tags": []}}, "go_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "go_base_image", "www_authenticate_challenges": {}, "scheme": "https", "registry": "gcr.io", "repository": "distroless/base", "identifier": "debug", "platforms": {"@@platforms//cpu:x86_64": "@go_base_image_linux_amd64"}, "bzlmod_repository": "go_base_image", "reproducible": true}}, "python_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"www_authenticate_challenges": {}, "scheme": "https", "registry": "ghcr.io", "repository": "sentioxyz/python", "identifier": "3.10-1.1.0", "platform": "linux/amd64", "target_name": "python_base_image_linux_amd64", "bazel_tags": []}}, "python_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "python_base_image", "www_authenticate_challenges": {}, "scheme": "https", "registry": "ghcr.io", "repository": "sentioxyz/python", "identifier": "3.10-1.1.0", "platforms": {"@@platforms//cpu:x86_64": "@python_base_image_linux_amd64"}, "bzlmod_repository": "python_base_image", "reproducible": true}}, "node_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"www_authenticate_challenges": {}, "scheme": "https", "registry": "ghcr.io", "repository": "sentioxyz/node", "identifier": "22-1.3.0", "platform": "linux/amd64", "target_name": "node_base_image_linux_amd64", "bazel_tags": []}}, "node_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "node_base_image", "www_authenticate_challenges": {}, "scheme": "https", "registry": "ghcr.io", "repository": "sentioxyz/node", "identifier": "22-1.3.0", "platforms": {"@@platforms//cpu:x86_64": "@node_base_image_linux_amd64"}, "bzlmod_repository": "node_base_image", "reproducible": true}}, "clickhouse_client_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"www_authenticate_challenges": {}, "scheme": "https", "registry": "index.docker.io", "repository": "clickhouse/clickhouse-server", "identifier": "head-alpine", "platform": "linux/amd64", "target_name": "clickhouse_client_image_linux_amd64", "bazel_tags": []}}, "clickhouse_client_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "clickhouse_client_image", "www_authenticate_challenges": {}, "scheme": "https", "registry": "index.docker.io", "repository": "clickhouse/clickhouse-server", "identifier": "head-alpine", "platforms": {"@@platforms//cpu:x86_64": "@clickhouse_client_image_linux_amd64"}, "bzlmod_repository": "clickhouse_client_image", "reproducible": true}}, "move_base_image_linux_amd64": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_pull", "attributes": {"www_authenticate_challenges": {}, "scheme": "https", "registry": "index.docker.io", "repository": "google/cloud-sdk", "identifier": "slim", "platform": "linux/amd64", "target_name": "move_base_image_linux_amd64", "bazel_tags": []}}, "move_base_image": {"repoRuleId": "@@rules_oci+//oci/private:pull.bzl%oci_alias", "attributes": {"target_name": "move_base_image", "www_authenticate_challenges": {}, "scheme": "https", "registry": "index.docker.io", "repository": "google/cloud-sdk", "identifier": "slim", "platforms": {"@@platforms//cpu:x86_64": "@move_base_image_linux_amd64"}, "bzlmod_repository": "move_base_image", "reproducible": true}}, "oci_crane_darwin_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "darwin_amd64", "crane_version": "v0.18.0"}}, "oci_crane_darwin_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "darwin_arm64", "crane_version": "v0.18.0"}}, "oci_crane_linux_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_arm64", "crane_version": "v0.18.0"}}, "oci_crane_linux_armv6": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_armv6", "crane_version": "v0.18.0"}}, "oci_crane_linux_i386": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_i386", "crane_version": "v0.18.0"}}, "oci_crane_linux_s390x": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_s390x", "crane_version": "v0.18.0"}}, "oci_crane_linux_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "linux_amd64", "crane_version": "v0.18.0"}}, "oci_crane_windows_armv6": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "windows_armv6", "crane_version": "v0.18.0"}}, "oci_crane_windows_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%crane_repositories", "attributes": {"platform": "windows_amd64", "crane_version": "v0.18.0"}}, "oci_crane_toolchains": {"repoRuleId": "@@rules_oci+//oci/private:toolchains_repo.bzl%toolchains_repo", "attributes": {"toolchain_type": "@rules_oci//oci:crane_toolchain_type", "toolchain": "@oci_crane_{platform}//:crane_toolchain"}}, "oci_regctl_darwin_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "darwin_amd64"}}, "oci_regctl_darwin_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "darwin_arm64"}}, "oci_regctl_linux_arm64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "linux_arm64"}}, "oci_regctl_linux_s390x": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "linux_s390x"}}, "oci_regctl_linux_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "linux_amd64"}}, "oci_regctl_windows_amd64": {"repoRuleId": "@@rules_oci+//oci:repositories.bzl%regctl_repositories", "attributes": {"platform": "windows_amd64"}}, "oci_regctl_toolchains": {"repoRuleId": "@@rules_oci+//oci/private:toolchains_repo.bzl%toolchains_repo", "attributes": {"toolchain_type": "@rules_oci//oci:regctl_toolchain_type", "toolchain": "@oci_regctl_{platform}//:regctl_toolchain"}}}, "moduleExtensionMetadata": {"explicitRootModuleDirectDeps": ["go_base_image", "go_base_image_linux_amd64", "python_base_image", "python_base_image_linux_amd64", "node_base_image", "node_base_image_linux_amd64", "clickhouse_client_image", "clickhouse_client_image_linux_amd64", "move_base_image", "move_base_image_linux_amd64"], "explicitRootModuleDirectDevDeps": [], "useAllRepos": "NO", "reproducible": false}, "recordedRepoMappingEntries": [["aspect_bazel_lib+", "bazel_tools", "bazel_tools"], ["bazel_features+", "bazel_tools", "bazel_tools"], ["rules_oci+", "aspect_bazel_lib", "aspect_bazel_lib+"], ["rules_oci+", "bazel_features", "bazel_features+"], ["rules_oci+", "bazel_skylib", "bazel_skylib+"]]}}, "@@rules_proto_grpc_grpc_gateway+//:module_extensions.bzl%download_plugins": {"general": {"bzlTransitiveDigest": "kXkHAXk639Wjh9pho0V/vHgXAwqap57TokMNsuhfulw=", "usagesDigest": "SAVBhlI5cUPQRQrVj2z1TD66zkr/YpJm2shRU2/fHJo=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"grpc_gateway_plugin_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "8fdc478fddc691771bcaeb2db2d5d8a7b02abd66eff37b5b585047f244d13a81", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-darwin-arm64"}}, "grpc_gateway_plugin_darwin_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "448b136143017da93ad6ed0ad6aad40a76bfcd47a443362516c31eaf1fd7bcdb", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-darwin-x86_64"}}, "grpc_gateway_plugin_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "05a34c88c48f25c0bfccc5548cd522188f6a09ae839dc442eaefd18a83420b4b", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-linux-arm64"}}, "grpc_gateway_plugin_linux_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "9435a60c0ad0f9d535cc28998087e43ebf54fb87f491408752ddec3e89a3fdf3", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-linux-x86_64"}}, "grpc_gateway_plugin_windows_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "e79caf845fe8cb4ad534281ba32c1b607ef64e50f94bb278d7d9514541efad2b", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-windows-arm64.exe"}}, "grpc_gateway_plugin_windows_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "25cfbcfc9c555145e373a85cc0dfc5eaef6c9df49c556e82f526fac51070f6d6", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-grpc-gateway-v2.21.0-windows-x86_64.exe"}}, "openapiv2_plugin_darwin_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "ff82e0513c99fcef2ddfc4432092bcb8fb770086bedb166811fd4c60f1b4f950", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-darwin-arm64"}}, "openapiv2_plugin_darwin_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "4903651de013d031c33976730b2f91f82dbe116ed91af7dcc718656809ff8a9a", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-darwin-x86_64"}}, "openapiv2_plugin_linux_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "92ca757d3be792b0164b07606d6b69ccd3f0f6d765c6c38c01503c72ae51dfbc", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-linux-arm64"}}, "openapiv2_plugin_linux_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "d17ed6eb57ba2df1fef60a60c2bbce1bd47a05152ce54666cb9333d5c35792b2", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-linux-x86_64"}}, "openapiv2_plugin_windows_arm64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "d456387c82d37322408b2c53f0d587659e90ff7645fa62d83571d913135dc08a", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-windows-arm64.exe"}}, "openapiv2_plugin_windows_x86_64": {"repoRuleId": "@@bazel_tools//tools/build_defs/repo:http.bzl%http_file", "attributes": {"executable": true, "sha256": "1d2c9687cd90a58872a664691e07f4d99fb1625de6b92a60bdb5058614248fcb", "url": "https://github.com/grpc-ecosystem/grpc-gateway/releases/download/v2.21.0/protoc-gen-openapiv2-v2.21.0-windows-x86_64.exe"}}}, "moduleExtensionMetadata": {"useAllRepos": "REGULAR", "reproducible": false}, "recordedRepoMappingEntries": [["rules_proto_grpc_grpc_gateway+", "bazel_tools", "bazel_tools"]]}}, "@@rules_python+//python/uv:uv.bzl%uv": {"general": {"bzlTransitiveDigest": "bGHlxez0Lkvq2VwrlfCLraKHiJIRHSIJb432X2+pky8=", "usagesDigest": "NpiUV+X/c1CxiPtmJPhqWybdhuV/lDqN1w8O2uMmmjg=", "recordedFileInputs": {}, "recordedDirentsInputs": {}, "envVariables": {}, "generatedRepoSpecs": {"uv": {"repoRuleId": "@@rules_python+//python/uv/private:uv_toolchains_repo.bzl%uv_toolchains_repo", "attributes": {"toolchain_type": "'@@rules_python+//python/uv:uv_toolchain_type'", "toolchain_names": ["none"], "toolchain_implementations": {"none": "'@@rules_python+//python:none'"}, "toolchain_compatible_with": {"none": ["@platforms//:incompatible"]}, "toolchain_target_settings": {}}}}, "recordedRepoMappingEntries": [["rules_python+", "bazel_tools", "bazel_tools"], ["rules_python+", "platforms", "platforms"]]}}}}