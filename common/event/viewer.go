package event

import (
	"context"
	"reflect"
	"time"

	"sentioxyz/sentio/service/analytic/clients"
	"sentioxyz/sentio/service/common/auth"
	"sentioxyz/sentio/service/processor/models"

	protosanalytic "sentioxyz/sentio/service/analytic/protos"
	common "sentioxyz/sentio/service/common/models"
	protoscommon "sentioxyz/sentio/service/common/protos"

	"github.com/ClickHouse/clickhouse-go/v2"
)

type ExportCompression int

const (
	ExportCompressionNone ExportCompression = iota
	ExportCompressionGzip
	ExportCompressionZstd
)

type (
	TableValidator func(ctx context.Context, processorID string,
		repository Repository, authManager auth.AuthManager) bool
	ColumnType interface {
		Name() string
		ScanType() reflect.Type
		DatabaseTypeName() string
	}

	Repository interface {
		GetProcessor(ctx context.Context, processorID string) (*models.Processor, error)
	}
)

type Conn interface {
	GetClickhouseConn() clickhouse.Conn
	GetDatabaseName() string
	GetHost() string
	GetUsername() string
	GetPassword() string
	GetCluster() string
	GetContextSettings() clickhouse.Settings
}

type ProcessorProperties interface {
	GetProject() *common.Project
}

type ShardingConn interface {
	GetIndex() int32
	GetClickhouseClientBin() string

	GetDSNByUser(protocol, role string, external bool) string
	GetConnectInfoByUser(protocol, role string, external bool) (string, string, string, string, string)
	GetRemoteTableByUser(table, role string, external bool) string

	GetUndoneMutations() (int32, error)
	GetProcessList() (any, error)
	GetK8sCluster() string
	GetSentioReplicas() []Conn
	GetSubgraphReplicas() []Conn

	GetSentioConn() Conn
	GetSentioViewConn() Conn
	GetSubgraphConn() Conn
	GetSubgraphViewConn() Conn
	GetAllConns() map[string]Conn
	GetConnByName(name string) Conn
	GetReadonlyConns(category string) map[string]Conn
	GetReadWriteConns(category string) map[string]Conn

	// deprecated, use GetDSNByRole instead
	GetDSNByDataType(protocol, dataType string, external, readOnly bool) string
	// deprecated, use GetConnectInfoByRole instead
	GetConnectInfoByDataType(protocol, dataType string, external, readOnly bool) (string, string, string, string, string)
	// deprecated, use GetRemoteTableByRole instead
	GetRemoteTableByDataType(table, dataType string, external bool) string
}

type MultiSharding interface {
	Pick(properties ProcessorProperties) int32
	GetShard(i int32) ShardingConn
	GetDefaultShard() ShardingConn
	All() []ShardingConn
	GetUndoneMutations() (map[int32]int32, error)
	GetProcessList() (map[int32]any, error)
}

type ExportProgress interface {
	WroteRows() uint64
	WroteBytes() uint64
	Elapsed() time.Duration
}

type RemoteArgs interface {
	Host() string
	Database() string
	Table() string
	Username() string
	Password() string
}

type DatabaseTableArgs interface {
	Database() string
	Table() string
}

type CommonTableExprArgs interface {
	Alias() string
	SQL() string
}

type ExecuteArgs interface {
	SQL() string
	Cursor() string
	DatabaseTableMapping() map[string]DatabaseTableArgs
	RemoteArgsMapping() map[string]RemoteArgs
	CommonTableExprArgs() map[string]CommonTableExprArgs
	Limit() int
	Repo() Repository
	AuthManager() auth.AuthManager
	Validator() TableValidator
	Rewriter() clients.RewriterServiceClient
	InternalCall() bool
	QuerySettings() clickhouse.Settings
	Parameters() *protoscommon.RichStruct
	Sign() string
	QueryID() string
	RewriteOnly() bool
	ExecuteEngine() protosanalytic.ExecuteEngine
}

type ExecuteResult interface {
	Rows() [][]any
	Columns() []ColumnType
	Cursor() string
	SQL() string
	Error() error
}

type ClickhouseViewer interface {
	GetSchema() Schema
	GetConn() Conn
	GetTableName() string
	ShowTables(ctx context.Context, tableNamePrefix string) ([]string, error)
	ShowDatabases(ctx context.Context) ([]string, error)
	DescribeTable(ctx context.Context, tableName string) (map[string]string, error)
	GetReservedKeyword(ctx context.Context) map[string]struct{}
	PrepareExecute(ctx context.Context, conn Conn, args map[string]any) error
	Execute(ctx context.Context, args ExecuteArgs) ExecuteResult
	Export(ctx context.Context, k8sContextUse string, shardingConn ShardingConn, progress chan ExportProgress, filePath, sql string, compression int) error
}
