package clickhouse

import (
	"fmt"
	"testing"
	"time"

	"sentioxyz/sentio/common/clickhouse/roles"

	"github.com/stretchr/testify/assert"
)

func Test_MultiShardingForConn(t *testing.T) {
	setup()
	tenants := []string{
		roles.SentioConn, roles.SentioViewConn, roles.SentioDefaultViewConn, roles.SentioSmallViewConn, roles.SentioMediumViewConn, roles.SentioLargeViewConn, roles.SentioUltraViewConn,
		roles.SubgraphConn, roles.SubgraphViewConn, roles.SubgraphDefaultViewConn, roles.SubgraphSmallViewConn, roles.SubgraphMediumViewConn, roles.SubgraphLargeViewConn, roles.SubgraphUltraViewConn,
	}
	configPath := "clickhouse_docker_config.yaml"
	if config, err := LoadConfig(configPath); err != nil {
		t.Fatalf("failed to load config: %v", err)
	} else if mt, err := NewMultiSharding(configPath); err != nil {
		t.Fatalf("failed to create multi sharding: %v", err)
	} else {
		for _, tenant := range tenants {
			for _, shard := range config.Shards {
				var tenantName string
				switch tenant {
				case roles.SentioConn:
					tenantName = roles.SentioConn
				case roles.SentioViewConn, roles.SentioSmallViewConn, roles.SentioMediumViewConn, roles.SentioLargeViewConn, roles.SentioUltraViewConn, roles.SentioDefaultViewConn:
					tenantName = roles.SentioViewConn
				case roles.SubgraphConn:
					tenantName = roles.SubgraphConn
				case roles.SubgraphViewConn, roles.SubgraphSmallViewConn, roles.SubgraphMediumViewConn, roles.SubgraphLargeViewConn, roles.SubgraphUltraViewConn, roles.SubgraphDefaultViewConn:
					tenantName = roles.SubgraphViewConn
				}

				shardConnByName := mt.GetShard(shard.Index).GetConnByName(tenant)
				scn, _ := shardConnByName.(*ConnInfo)

				assert.Equal(t, config.ConnectParams[tenantName].Database, shardConnByName.GetDatabaseName(), "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, config.ConnectParams[tenantName].Username, shardConnByName.GetUsername(), "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, config.ConnectParams[tenantName].Password, shardConnByName.GetPassword(), "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, config.Shards[shard.Index].Addresses[internalTCPAddr], shardConnByName.GetHost(), "tenant: %s, shard: %d", tenant, shard.Index)

				assert.Equal(t, 1, shardConnByName.GetContextSettings()["optimize_aggregation_in_order"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, 1, shardConnByName.GetContextSettings()["allow_experimental_object_type"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, 50000, shardConnByName.GetContextSettings()["max_ast_depth"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, uint64(536870912000), shardConnByName.GetContextSettings()["max_partition_size_to_drop"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, uint64(536870912000), shardConnByName.GetContextSettings()["max_table_size_to_drop"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, "true", shardConnByName.GetContextSettings()["allow_experimental_inverted_index"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, "true", shardConnByName.GetContextSettings()["allow_experimental_lightweight_delete"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, "ALL", shardConnByName.GetContextSettings()["union_default_mode"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, 120000, shardConnByName.GetContextSettings()["connect_timeout_with_failover_ms"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, "ignore", shardConnByName.GetContextSettings()["query_cache_system_table_handling"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, 1, shardConnByName.GetContextSettings()["allow_experimental_json_type"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, 1, shardConnByName.GetContextSettings()["output_format_native_write_json_as_string"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, 0, shardConnByName.GetContextSettings()["allow_push_predicate_ast_for_distributed_subqueries"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, 1, shardConnByName.GetContextSettings()["enable_json_type"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, "ignore", shardConnByName.GetContextSettings()["query_cache_nondeterministic_function_handling"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *MaxMemoryUsage, shardConnByName.GetContextSettings()["max_memory_usage"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *UseQueryCache, shardConnByName.GetContextSettings()["use_query_cache"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *MaxExecutionTime, shardConnByName.GetContextSettings()["max_execution_time"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *TimeoutBeforeCheckingExecutionSpeed, shardConnByName.GetContextSettings()["timeout_before_checking_execution_speed"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *MinExecutionSpeed, shardConnByName.GetContextSettings()["min_execution_speed"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *MaxBytesBeforeExternalGroupBy, shardConnByName.GetContextSettings()["max_bytes_before_external_group_by"], "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *MaxQuerySize, shardConnByName.GetContextSettings()["max_query_size"], "tenant: %s, shard: %d", tenant, shard.Index)

				assert.Equal(t, *config.ReadTimeout, int(scn.Option.ReadTimeout/time.Second), "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *config.DialTimeout, int(scn.Option.DialTimeout/time.Second), "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *config.MaxIdleConns, scn.Option.MaxIdleConns, "tenant: %s, shard: %d", tenant, shard.Index)
				assert.Equal(t, *config.MaxOpenConns, scn.Option.MaxOpenConns, "tenant: %s, shard: %d", tenant, shard.Index)
			}
		}
	}
}

func Test_MultiShardingForReplica(t *testing.T) {
	setup()
	configPath := "clickhouse_docker_config.yaml"
	if config, err := LoadConfig(configPath); err != nil {
		t.Fatalf("failed to load config: %v", err)
	} else if mt, err := NewMultiSharding(configPath); err != nil {
		t.Fatalf("failed to create multi sharding v2: %v", err)
	} else {
		for _, shard := range config.Shards {
			sentioReplicas := mt.GetShard(shard.Index).GetSentioReplicas()
			subgraphReplicas := mt.GetShard(shard.Index).GetSubgraphReplicas()
			for _, replica := range sentioReplicas {
				assert.Equal(t, config.ConnectParams[roles.SentioConn].Database, replica.GetDatabaseName())
				assert.Equal(t, config.ConnectParams[roles.SentioConn].Username, replica.GetUsername())
				assert.Equal(t, config.ConnectParams[roles.SentioConn].Password, replica.GetPassword())
				assert.Contains(t, shard.Replicas, replica.GetHost(), "Replica host should be in shard replicas")
			}
			for _, replica := range subgraphReplicas {
				assert.Equal(t, config.ConnectParams[roles.SubgraphConn].Database, replica.GetDatabaseName())
				assert.Equal(t, config.ConnectParams[roles.SentioConn].Username, replica.GetUsername())
				assert.Equal(t, config.ConnectParams[roles.SentioConn].Password, replica.GetPassword())
				assert.Contains(t, shard.Replicas, replica.GetHost(), "Replica host should be in shard replicas")
			}
		}
	}
}

func Test_dsnInfoForSentio(t *testing.T) {
	setup()
	type Node struct {
		external        bool
		protocol        string
		dataType        string
		readOnly        bool
		expect_host     string
		expect_port     string
		expect_database string
		expect_username string
		expect_password string
	}
	configPath := "clickhouse_docker_config.yaml"
	config, _ := LoadConfig(configPath)
	caseMap := map[int]Node{
		0:   {false, "tcp", roles.SentioConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		1:   {false, "clickhouse", roles.SentioConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		2:   {false, "http", roles.SentioConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		3:   {false, "unknown", roles.SentioConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		4:   {true, "tcp", roles.SentioConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		5:   {true, "clickhouse", roles.SentioConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		6:   {true, "http", roles.SentioConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		7:   {true, "unknown", roles.SentioConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		100: {false, "tcp", roles.SentioConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		101: {false, "clickhouse", roles.SentioConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		102: {false, "http", roles.SentioConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		103: {false, "unknown", roles.SentioConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		104: {true, "tcp", roles.SentioConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		105: {true, "clickhouse", roles.SentioConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		106: {true, "http", roles.SentioConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		107: {true, "unknown", roles.SentioConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		10:  {false, "tcp", roles.SentioViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		11:  {false, "clickhouse", roles.SentioViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		12:  {false, "http", roles.SentioViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		13:  {false, "unknown", roles.SentioViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		14:  {true, "tcp", roles.SentioViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		15:  {true, "clickhouse", roles.SentioViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		16:  {true, "http", roles.SentioViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		17:  {true, "unknown", roles.SentioViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		110: {false, "tcp", roles.SentioViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		111: {false, "clickhouse", roles.SentioViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		112: {false, "http", roles.SentioViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		113: {false, "unknown", roles.SentioViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		114: {true, "tcp", roles.SentioViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		115: {true, "clickhouse", roles.SentioViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		116: {true, "http", roles.SentioViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		117: {true, "unknown", roles.SentioViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		20:  {false, "tcp", roles.SentioDefaultViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		21:  {false, "clickhouse", roles.SentioDefaultViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		22:  {false, "http", roles.SentioDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		23:  {false, "unknown", roles.SentioDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		24:  {true, "tcp", roles.SentioDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		25:  {true, "clickhouse", roles.SentioDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		26:  {true, "http", roles.SentioDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		27:  {true, "unknown", roles.SentioDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		120: {false, "tcp", roles.SentioDefaultViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		121: {false, "clickhouse", roles.SentioDefaultViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		122: {false, "http", roles.SentioDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		123: {false, "unknown", roles.SentioDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		124: {true, "tcp", roles.SentioDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		125: {true, "clickhouse", roles.SentioDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		126: {true, "http", roles.SentioDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		127: {true, "unknown", roles.SentioDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		30:  {false, "tcp", roles.SentioSmallViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		31:  {false, "clickhouse", roles.SentioSmallViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		32:  {false, "http", roles.SentioSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		33:  {false, "unknown", roles.SentioSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		34:  {true, "tcp", roles.SentioSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		35:  {true, "clickhouse", roles.SentioSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		36:  {true, "http", roles.SentioSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		37:  {true, "unknown", roles.SentioSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		130: {false, "tcp", roles.SentioSmallViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		131: {false, "clickhouse", roles.SentioSmallViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		132: {false, "http", roles.SentioSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		133: {false, "unknown", roles.SentioSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		134: {true, "tcp", roles.SentioSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		135: {true, "clickhouse", roles.SentioSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		136: {true, "http", roles.SentioSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		137: {true, "unknown", roles.SentioSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		40:  {false, "tcp", roles.SentioMediumViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		41:  {false, "clickhouse", roles.SentioMediumViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		42:  {false, "http", roles.SentioMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		43:  {false, "unknown", roles.SentioMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		44:  {true, "tcp", roles.SentioMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		45:  {true, "clickhouse", roles.SentioMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		46:  {true, "http", roles.SentioMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		47:  {true, "unknown", roles.SentioMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		140: {false, "tcp", roles.SentioMediumViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		141: {false, "clickhouse", roles.SentioMediumViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		142: {false, "http", roles.SentioMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		143: {false, "unknown", roles.SentioMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		144: {true, "tcp", roles.SentioMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		145: {true, "clickhouse", roles.SentioMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		146: {true, "http", roles.SentioMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		147: {true, "unknown", roles.SentioMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		50:  {false, "tcp", roles.SentioLargeViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		51:  {false, "clickhouse", roles.SentioLargeViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		52:  {false, "http", roles.SentioLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		53:  {false, "unknown", roles.SentioLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		54:  {true, "tcp", roles.SentioLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		55:  {true, "clickhouse", roles.SentioLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		56:  {true, "http", roles.SentioLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		57:  {true, "unknown", roles.SentioLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		150: {false, "tcp", roles.SentioLargeViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		151: {false, "clickhouse", roles.SentioLargeViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		152: {false, "http", roles.SentioLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		153: {false, "unknown", roles.SentioLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		154: {true, "tcp", roles.SentioLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		155: {true, "clickhouse", roles.SentioLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		156: {true, "http", roles.SentioLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		157: {true, "unknown", roles.SentioLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		60:  {false, "tcp", roles.SentioUltraViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		61:  {false, "clickhouse", roles.SentioUltraViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		62:  {false, "http", roles.SentioUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		63:  {false, "unknown", roles.SentioUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		64:  {true, "tcp", roles.SentioUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		65:  {true, "clickhouse", roles.SentioUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		66:  {true, "http", roles.SentioUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		67:  {true, "unknown", roles.SentioUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		160: {false, "tcp", roles.SentioUltraViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		161: {false, "clickhouse", roles.SentioUltraViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		162: {false, "http", roles.SentioUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		163: {false, "unknown", roles.SentioUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		164: {true, "tcp", roles.SentioUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		165: {true, "clickhouse", roles.SentioUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		166: {true, "http", roles.SentioUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		167: {true, "unknown", roles.SentioUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
	}

	if mt, err := NewMultiSharding(configPath); err != nil {
		t.Fatalf("failed to create multi sharding: %v", err)
	} else {
		shardConnList := mt.All()
		for _, shardConn := range shardConnList {
			for caseNo, caseNode := range caseMap {
				prefix := caseNode.protocol
				if prefix == "tcp" || prefix == "unknown" || prefix == "http" {
					prefix = "clickhouse"
				}
				expect_dsn := fmt.Sprintf("%s://%s:%s@%s:%s/%s", prefix, caseNode.expect_username, caseNode.expect_password, caseNode.expect_host, caseNode.expect_port, caseNode.expect_database)
				active_dsn := shardConn.GetDSNByDataType(caseNode.protocol, caseNode.dataType, caseNode.external, caseNode.readOnly)
				active_host, active_port, active_database, active_username, active_password := shardConn.GetConnectInfoByDataType(caseNode.protocol, caseNode.dataType, caseNode.external, caseNode.readOnly)
				assert.Equal(t, expect_dsn, active_dsn, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_host, active_host, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_port, active_port, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_database, active_database, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_username, active_username, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_password, active_password, "case %d: dsn mismatch %+v", caseNo, caseNode)

				username := caseNode.expect_username
				password := caseNode.expect_password
				if caseNode.readOnly {
					username = roles.SentioConn
					password = config.ConnectParams[roles.SentioConn].Password
				}
				if caseNode.external {
					except_remotetable := fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
						config.Shards[shardConn.GetIndex()].Addresses[externalTCPAddr],
						caseNode.expect_database, "table", username, password)
					active_remotetable := shardConn.GetRemoteTableByDataType("table", caseNode.dataType, caseNode.external)
					assert.Equal(t, except_remotetable, active_remotetable, "case %d: dsn mismatch %+v", caseNo, caseNode)
				} else {
					except_remotetable := fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
						config.Shards[shardConn.GetIndex()].Addresses[internalTCPAddr],
						caseNode.expect_database, "table", username, password)
					active_remotetable := shardConn.GetRemoteTableByDataType("table", caseNode.dataType, caseNode.external)
					assert.Equal(t, except_remotetable, active_remotetable, "case %d: dsn mismatch %+v", caseNo, caseNode)
				}
			}
		}
	}
}

func Test_dsnInfoForSubgraph(t *testing.T) {
	setup()
	type Node struct {
		external        bool
		protocol        string
		dataType        string
		readOnly        bool
		expect_host     string
		expect_port     string
		expect_database string
		expect_username string
		expect_password string
	}
	configPath := "clickhouse_docker_config.yaml"
	config, _ := LoadConfig(configPath)
	caseMap := map[int]Node{
		0:   {false, "tcp", roles.SubgraphConn, false, "localhost", "19000", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		1:   {false, "clickhouse", roles.SubgraphConn, false, "localhost", "19000", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		2:   {false, "http", roles.SubgraphConn, false, "*************", "30004", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		3:   {false, "unknown", roles.SubgraphConn, false, "*************", "30004", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		4:   {true, "tcp", roles.SubgraphConn, false, "*************", "30004", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		5:   {true, "clickhouse", roles.SubgraphConn, false, "*************", "30004", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		6:   {true, "http", roles.SubgraphConn, false, "*************", "30004", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		7:   {true, "unknown", roles.SubgraphConn, false, "*************", "30004", config.ConnectParams[roles.SubgraphConn].Database, config.ConnectParams[roles.SubgraphConn].Username, config.ConnectParams[roles.SubgraphConn].Password},
		100: {false, "tcp", roles.SubgraphConn, true, "localhost", "19000", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},
		101: {false, "clickhouse", roles.SubgraphConn, true, "localhost", "19000", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},
		102: {false, "http", roles.SubgraphConn, true, "*************", "30004", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},
		103: {false, "unknown", roles.SubgraphConn, true, "*************", "30004", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},
		104: {true, "tcp", roles.SubgraphConn, true, "*************", "30004", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},
		105: {true, "clickhouse", roles.SubgraphConn, true, "*************", "30004", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},
		106: {true, "http", roles.SubgraphConn, true, "*************", "30004", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},
		107: {true, "unknown", roles.SubgraphConn, true, "*************", "30004", config.ConnectParams[roles.SubgraphViewConn].Database, config.ConnectParams[roles.SubgraphViewConn].Username, config.ConnectParams[roles.SubgraphViewConn].Password},

		10:  {false, "tcp", roles.SubgraphViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		11:  {false, "clickhouse", roles.SubgraphViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		12:  {false, "http", roles.SubgraphViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		13:  {false, "unknown", roles.SubgraphViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		14:  {true, "tcp", roles.SubgraphViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		15:  {true, "clickhouse", roles.SubgraphViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		16:  {true, "http", roles.SubgraphViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		17:  {true, "unknown", roles.SubgraphViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		110: {false, "tcp", roles.SubgraphViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		111: {false, "clickhouse", roles.SubgraphViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		112: {false, "http", roles.SubgraphViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		113: {false, "unknown", roles.SubgraphViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		114: {true, "tcp", roles.SubgraphViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		115: {true, "clickhouse", roles.SubgraphViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		116: {true, "http", roles.SubgraphViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		117: {true, "unknown", roles.SubgraphViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		20:  {false, "tcp", roles.SubgraphDefaultViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		21:  {false, "clickhouse", roles.SubgraphDefaultViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		22:  {false, "http", roles.SubgraphDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		23:  {false, "unknown", roles.SubgraphDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		24:  {true, "tcp", roles.SubgraphDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		25:  {true, "clickhouse", roles.SubgraphDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		26:  {true, "http", roles.SubgraphDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		27:  {true, "unknown", roles.SubgraphDefaultViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		120: {false, "tcp", roles.SubgraphDefaultViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		121: {false, "clickhouse", roles.SubgraphDefaultViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		122: {false, "http", roles.SubgraphDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		123: {false, "unknown", roles.SubgraphDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		124: {true, "tcp", roles.SubgraphDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		125: {true, "clickhouse", roles.SubgraphDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		126: {true, "http", roles.SubgraphDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		127: {true, "unknown", roles.SubgraphDefaultViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		30:  {false, "tcp", roles.SubgraphSmallViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		31:  {false, "clickhouse", roles.SubgraphSmallViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		32:  {false, "http", roles.SubgraphSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		33:  {false, "unknown", roles.SubgraphSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		34:  {true, "tcp", roles.SubgraphSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		35:  {true, "clickhouse", roles.SubgraphSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		36:  {true, "http", roles.SubgraphSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		37:  {true, "unknown", roles.SubgraphSmallViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		130: {false, "tcp", roles.SubgraphSmallViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		131: {false, "clickhouse", roles.SubgraphSmallViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		132: {false, "http", roles.SubgraphSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		133: {false, "unknown", roles.SubgraphSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		134: {true, "tcp", roles.SubgraphSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		135: {true, "clickhouse", roles.SubgraphSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		136: {true, "http", roles.SubgraphSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		137: {true, "unknown", roles.SubgraphSmallViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		40:  {false, "tcp", roles.SubgraphMediumViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		41:  {false, "clickhouse", roles.SubgraphMediumViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		42:  {false, "http", roles.SubgraphMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		43:  {false, "unknown", roles.SubgraphMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		44:  {true, "tcp", roles.SubgraphMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		45:  {true, "clickhouse", roles.SubgraphMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		46:  {true, "http", roles.SubgraphMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		47:  {true, "unknown", roles.SubgraphMediumViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		140: {false, "tcp", roles.SubgraphMediumViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		141: {false, "clickhouse", roles.SubgraphMediumViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		142: {false, "http", roles.SubgraphMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		143: {false, "unknown", roles.SubgraphMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		144: {true, "tcp", roles.SubgraphMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		145: {true, "clickhouse", roles.SubgraphMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		146: {true, "http", roles.SubgraphMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		147: {true, "unknown", roles.SubgraphMediumViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		50:  {false, "tcp", roles.SubgraphLargeViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		51:  {false, "clickhouse", roles.SubgraphLargeViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		52:  {false, "http", roles.SubgraphLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		53:  {false, "unknown", roles.SubgraphLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		54:  {true, "tcp", roles.SubgraphLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		55:  {true, "clickhouse", roles.SubgraphLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		56:  {true, "http", roles.SubgraphLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		57:  {true, "unknown", roles.SubgraphLargeViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		150: {false, "tcp", roles.SubgraphLargeViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		151: {false, "clickhouse", roles.SubgraphLargeViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		152: {false, "http", roles.SubgraphLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		153: {false, "unknown", roles.SubgraphLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		154: {true, "tcp", roles.SubgraphLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		155: {true, "clickhouse", roles.SubgraphLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		156: {true, "http", roles.SubgraphLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		157: {true, "unknown", roles.SubgraphLargeViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},

		60:  {false, "tcp", roles.SubgraphUltraViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		61:  {false, "clickhouse", roles.SubgraphUltraViewConn, false, "localhost", "19000", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		62:  {false, "http", roles.SubgraphUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		63:  {false, "unknown", roles.SubgraphUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		64:  {true, "tcp", roles.SubgraphUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		65:  {true, "clickhouse", roles.SubgraphUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		66:  {true, "http", roles.SubgraphUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		67:  {true, "unknown", roles.SubgraphUltraViewConn, false, "*************", "30004", config.ConnectParams[roles.SentioConn].Database, config.ConnectParams[roles.SentioConn].Username, config.ConnectParams[roles.SentioConn].Password},
		160: {false, "tcp", roles.SubgraphUltraViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		161: {false, "clickhouse", roles.SubgraphUltraViewConn, true, "localhost", "19000", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		162: {false, "http", roles.SubgraphUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		163: {false, "unknown", roles.SubgraphUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		164: {true, "tcp", roles.SubgraphUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		165: {true, "clickhouse", roles.SubgraphUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		166: {true, "http", roles.SubgraphUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
		167: {true, "unknown", roles.SubgraphUltraViewConn, true, "*************", "30004", config.ConnectParams[roles.SentioViewConn].Database, config.ConnectParams[roles.SentioViewConn].Username, config.ConnectParams[roles.SentioViewConn].Password},
	}

	if mt, err := NewMultiSharding(configPath); err != nil {
		t.Fatalf("failed to create multi sharding: %v", err)
	} else {
		shardConnList := mt.All()
		for _, shardConn := range shardConnList {
			for caseNo, caseNode := range caseMap {
				prefix := caseNode.protocol
				if prefix == "tcp" || prefix == "unknown" || prefix == "http" {
					prefix = "clickhouse"
				}
				expect_dsn := fmt.Sprintf("%s://%s:%s@%s:%s/%s", prefix, caseNode.expect_username, caseNode.expect_password, caseNode.expect_host, caseNode.expect_port, caseNode.expect_database)
				active_dsn := shardConn.GetDSNByDataType(caseNode.protocol, caseNode.dataType, caseNode.external, caseNode.readOnly)
				active_host, active_port, active_database, active_username, active_password := shardConn.GetConnectInfoByDataType(caseNode.protocol, caseNode.dataType, caseNode.external, caseNode.readOnly)
				assert.Equal(t, expect_dsn, active_dsn, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_host, active_host, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_port, active_port, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_database, active_database, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_username, active_username, "case %d: dsn mismatch %+v", caseNo, caseNode)
				assert.Equal(t, caseNode.expect_password, active_password, "case %d: dsn mismatch %+v", caseNo, caseNode)

				username := caseNode.expect_username
				password := caseNode.expect_password
				if caseNode.readOnly || caseNode.dataType != roles.SubgraphConn {
					username = roles.SentioConn
					password = config.ConnectParams[roles.SentioConn].Password
				}
				if caseNode.external {
					except_remotetable := fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
						config.Shards[shardConn.GetIndex()].Addresses[externalTCPAddr],
						caseNode.expect_database, "table", username, password)
					active_remotetable := shardConn.GetRemoteTableByDataType("table", caseNode.dataType, caseNode.external)
					assert.Equal(t, except_remotetable, active_remotetable, "case %d: dsn mismatch %+v", caseNo, caseNode)
				} else {
					except_remotetable := fmt.Sprintf("remote('%s', '%s', '%s', '%s', '%s')",
						config.Shards[shardConn.GetIndex()].Addresses[internalTCPAddr],
						caseNode.expect_database, "table", username, password)
					active_remotetable := shardConn.GetRemoteTableByDataType("table", caseNode.dataType, caseNode.external)
					assert.Equal(t, except_remotetable, active_remotetable, "case %d: dsn mismatch %+v", caseNo, caseNode)
				}
			}
		}
	}
}
