package clickhouse

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/clickhouse/cursor"
	"sentioxyz/sentio/common/clickhouse/metadata"
	"sentioxyz/sentio/common/clickhouse/roles"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	subgraphchs "sentioxyz/sentio/driver/entity/clickhouse"
	"sentioxyz/sentio/driver/entity/schema"
	"sentioxyz/sentio/service/analytic/clients"
	"sentioxyz/sentio/service/analytic/protos"
	protosanalytic "sentioxyz/sentio/service/analytic/protos"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/pkg/errors"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

var (
	sgViewInitializedSet   = make(map[string]bool)
	sgViewInitializedMutex = sync.Mutex{}
)

type SgViewer struct {
	*Executor
	schema           *schema.Schema
	store            *subgraphchs.Store
	tableNameMapping map[string]string
	viewNameMapping  map[string]string
	viewFields       map[string]map[string]string
	initialized      bool
	processorID      string
}

func NewSgViewer(processorID string,
	metadataClient metadata.MetadataClient, schema *schema.Schema, store *subgraphchs.Store,
	conn event.Conn, connsByUser map[string]event.Conn) *SgViewer {
	return &SgViewer{
		Executor:         NewExecutor(metadataClient, conn, connsByUser),
		schema:           schema,
		store:            store,
		tableNameMapping: make(map[string]string),
		viewNameMapping:  make(map[string]string),
		viewFields:       make(map[string]map[string]string),
		initialized:      false,
		processorID:      processorID,
	}
}

func (s *SgViewer) GetViewNameMapping() map[string]string {
	if err := s.fillMappings(context.Background()); err != nil {
		log.Warnf("fill mappings failed: %v", err)
	}
	return s.viewNameMapping
}

func (s *SgViewer) GetViewFields() map[string]map[string]string {
	if err := s.fillMappings(context.Background()); err != nil {
		log.Warnf("fill mappings failed: %v", err)
	}
	return s.viewFields
}

func (s *SgViewer) fetchViewFields(entity schema.EntityOrInterface) map[string]string {
	fields := make(map[string]string)
	for _, field := range s.store.NewEntity(entity).Fields {
		names := field.FieldNames()
		dbTypes := field.ViewFieldsDBType()
		for i, name := range names {
			dbType := dbTypes[i]
			if dbType == "" {
				continue
			}
			fields[name] = dbType
		}
	}
	return fields
}

func (s *SgViewer) fillMappings(ctx context.Context) error {
	if s.initialized {
		return nil
	}
	for _, item := range s.schema.ListEntitiesAndInterfacesAndAggregations() {
		s.tableNameMapping[item.GetName()] = strings.Trim(s.store.TableName(item), "`")
		s.viewNameMapping[item.GetName()] = strings.Trim(s.store.LatestViewName(item), "`")
		s.viewNameMapping[item.GetName()+subgraphchs.SubgraphTableDisplayNameRawSuffix] = strings.Trim(s.store.ViewName(item), "`")
		s.viewFields[item.GetName()] = s.fetchViewFields(item)
	}
	s.initialized = true
	return nil
}

func (s *SgViewer) PrepareExecute(ctx context.Context, _ event.Conn, _ map[string]any) error {
	var initialized = false
	sgViewInitializedMutex.Lock()
	initialized = sgViewInitializedSet[s.processorID]
	sgViewInitializedMutex.Unlock()

	if !initialized {
		if err := s.store.CreateViews(ctx); err != nil {
			log.WithContext(ctx).Warnf("prepare subgraph executor failed: %v", err)
			return err
		}
		// if err := s.store.CallGrant(ctx); err != nil {
		// 	log.WithContext(ctx).Warnf("prepare subgraph executor failed: %v", err)
		// 	return err
		// }
		sgViewInitializedMutex.Lock()
		sgViewInitializedSet[s.processorID] = true
		sgViewInitializedMutex.Unlock()
	}
	if err := s.fillMappings(ctx); err != nil {
		log.WithContext(ctx).Warnf("prepare subgraph executor failed: %v", err)
		return err
	}
	return nil
}

func (s *SgViewer) rewrite(ctx context.Context, args event.ExecuteArgs, cursorMeta *cursor.Metadata) (string, error) {
	ctx, logger := log.FromContext(ctx)
	if args.Rewriter() == nil {
		logger.Warnf("rewriter v3 is not supported")
		return "", errors.New("rewriter v3 is not supported")
	}
	req := &clients.RewriteSQLRequest{}
	if cursorMeta != nil {
		s.cursor = cursorMeta.Cursor
		req.Sql = cursorMeta.SQL
		setLimitOffset(req, int32(s.cursor.GetLimit()), int32(s.cursor.GetOffset()), 0)
		setSettings(req, args.Sign(), args.QuerySettings())
	} else {
		if !args.RewriteOnly() {
			if limit := args.Limit(); limit > 0 {
				s.cursor = cursor.NewInfiniteCursorWithStep(limit)
				setLimitOffset(req, int32(s.cursor.GetLimit()), int32(s.cursor.GetOffset()), int32(limit))
			} else {
				s.cursor = cursor.NewCursor(nil, nil)
				setLimitOffset(req, int32(s.cursor.GetLimit()), int32(s.cursor.GetOffset()), 10000)
			}
		} else {
			setLimitOffset(req, int32(*UserMvLimit), 0, 0)
		}
		if args.Parameters() != nil && len(args.Parameters().Fields) > 0 {
			req.Sql = builder.FormatSQLTemplateWithOptions(args.SQL(), richStructParameterToMap(args.Parameters()),
				builder.WithParameterIdentity("$", ""),
				builder.WithParameterIdentity("${", "}"))
		} else {
			req.Sql = args.SQL()
		}
		setSettings(req, args.Sign(), args.QuerySettings())
		setCommonTableExpr(req, req.Sql, args.CommonTableExprArgs())
		var databaseTableMapping = make(map[string]*clients.RewriteTableNameArgs_TableWithDatabase)
		remoteMapping := make(map[string]*clients.RewriteTableNameArgs_RemoteTable)
		for k, v := range s.GetViewNameMapping() {
			databaseTableMapping[k] = &clients.RewriteTableNameArgs_TableWithDatabase{
				Table: v,
			}
		}
		if t := args.DatabaseTableMapping(); len(t) > 0 {
			for k, v := range t {
				databaseTableMapping[k] = &clients.RewriteTableNameArgs_TableWithDatabase{
					Database: v.Database(),
					Table:    v.Table(),
				}
			}
		}
		if remoteArgsMapping := args.RemoteArgsMapping(); len(remoteArgsMapping) > 0 {
			for k, v := range remoteArgsMapping {
				remoteMapping[k] = &clients.RewriteTableNameArgs_RemoteTable{
					Addr:     v.Host(),
					Database: v.Database(),
					Table:    v.Table(),
					User:     v.Username(),
					Password: v.Password(),
				}
			}
		}
		req.Options = append(req.Options, &clients.RewriteOption{
			Op: clients.RewriteOp_TableNameRewrite,
			Value: &clients.RewriteOption_TableNameArgs{
				TableNameArgs: &clients.RewriteTableNameArgs{
					RemoteTableMap:       remoteMapping,
					TableWithDatabaseMap: databaseTableMapping,
				},
			},
		})
	}
	resp, err := args.Rewriter().Rewrite(ctx, req)
	if err != nil {
		logger.Warnf("call rewriter service failed, err: %v", err)
		return "", err
	}
	switch resp.Code {
	case clients.RewriteCode_Success:
		return resp.SqlAfterRewrite, nil
	case clients.RewriteCode_SyntaxError:
		logger.Warnf("rewriter has syntax parse failed, err: %s", resp.Message)
		return "", NewErrorWrapper(fmt.Errorf("code: 62, %s", resp.Message))
	case clients.RewriteCode_RewriteError:
		logger.Warnf("rewriter has rewrite failed, err: %s", resp.Message)
		return "", NewErrorWrapper(fmt.Errorf("code: 1001, %s", resp.Message))
	}
	return "", errors.New("rewriter has unknown error")
}

func (s *SgViewer) clickhouseCtx(ctx context.Context, conn event.Conn, queryID string,
	additionalSettings clickhouse.Settings) context.Context {
	baseSettings := conn.GetContextSettings()
	var mergedSettings = make(clickhouse.Settings)
	for k, v := range baseSettings {
		if _, ok := additionalSettings[k]; !ok {
			mergedSettings[k] = v
		}
	}
	var queryOptions []clickhouse.QueryOption
	if queryID != "" {
		queryOptions = append(queryOptions, clickhouse.WithQueryID(queryID))
	}
	for k, v := range additionalSettings {
		mergedSettings[k] = v
	}
	queryOptions = append(queryOptions, clickhouse.WithSettings(mergedSettings))
	tracer := otel.Tracer("clickhouse")
	ctx, _ = tracer.Start(ctx, "operation")
	spanCtx := trace.SpanContextFromContext(ctx)
	queryOptions = append(queryOptions, clickhouse.WithSpan(spanCtx))
	return clickhouse.Context(ctx, queryOptions...)
}

func (s *SgViewer) Execute(ctx context.Context, args event.ExecuteArgs) event.ExecuteResult {
	result := ExecuteResult{
		rows:    [][]any{},
		columns: []event.ColumnType{},
		cursor:  "",
		sql:     "",
	}
	var cursorMeta *cursor.Metadata
	if cursorStr := args.Cursor(); cursorStr != "" {
		var err error
		cursorMeta, err = s.loadCursorMeta(ctx, cursorStr)
		if err != nil {
			result.err = NewErrorWrapper(ErrCursorNotFound)
			return result
		}
		if cursorMeta == nil {
			result.err = NewErrorWrapper(errors.Errorf("cursor has unknown format"))
			return result
		}
	}
	result.sql, result.err = s.rewrite(ctx, args, cursorMeta)
	if result.err != nil {
		return result
	}
	if args.RewriteOnly() {
		result.rows = [][]any{}
		result.columns = []event.ColumnType{}
		return result
	}
	var user string
	switch args.ExecuteEngine() {
	case protosanalytic.ExecuteEngine_SMALL, protosanalytic.ExecuteEngine_LITE:
		user = roles.SubgraphSmallViewConn
	case protosanalytic.ExecuteEngine_MEDIUM, protosanalytic.ExecuteEngine_PRO:
		user = roles.SubgraphMediumViewConn
	case protosanalytic.ExecuteEngine_LARGE, protosanalytic.ExecuteEngine_MAX:
		user = roles.SubgraphLargeViewConn
	case protos.ExecuteEngine_ULTRA:
		user = roles.SubgraphUltraViewConn
	default:
		user = roles.SubgraphDefaultViewConn
	}
	clickhouseCtx := s.clickhouseCtx(ctx, s.conn, args.QueryID(), args.QuerySettings())
	result.rows, result.columns, result.err = s.execute(clickhouseCtx, user, result.sql)
	if result.err != nil {
		return result
	}
	result.cursor, result.err = s.afterExecute(ctx, result.rows, result.sql)
	return result
}

func (s *SgViewer) GetReservedKeyword(ctx context.Context) map[string]struct{} {
	return map[string]struct{}{}
}

func (s *SgViewer) Export(ctx context.Context, k8sContextUse string,
	shardingConn event.ShardingConn, progress chan event.ExportProgress, filePath,
	sql string, compression int) error {
	sql = strings.TrimSpace(sql)
	sql = strings.TrimSuffix(sql, ";")
	exportSQL := fmt.Sprintf("%s INTO OUTFILE '%s'", sql, filePath)
	switch compression {
	case int(event.ExportCompressionGzip):
		exportSQL = fmt.Sprintf("%s COMPRESSION '%s'", exportSQL, "gzip")
	case int(event.ExportCompressionZstd):
		exportSQL = fmt.Sprintf("%s COMPRESSION '%s'", exportSQL, "zstd")
	default:
		// do nothing
	}
	ctx, logger := log.FromContext(ctx)

	logger.Debugf("export sql: %s", exportSQL)
	var channelClosed = new(atomic.Bool)
	channelClosed.Store(false)
	var mu sync.Mutex
	defer func() {
		mu.Lock()
		defer mu.Unlock()
		channelClosed.Store(true)
		close(progress)
	}()
	var start = time.Now()
	if _, err := os.Stat(filePath); !os.IsNotExist(err) {
		return errors.Errorf("file already exists: %s", filePath)
	}
	go func() {
		for {
			mu.Lock()
			if channelClosed.Load() {
				mu.Unlock()
				break
			}
			fileBytes, rowCount := fileInfoGetter(filePath)
			progress <- Progress{
				wroteBytes: fileBytes,
				wroteRows:  uint64(rowCount),
				elapsed:    time.Since(start),
			}
			mu.Unlock()
			time.Sleep(time.Millisecond * 500)
		}
	}()
	var crossK8sCluster = shardingConn.GetK8sCluster() != k8sContextUse
	cmd := exec.CommandContext(ctx, shardingConn.GetClickhouseClientBin(),
		"client",
		shardingConn.GetDSNByDataType("tcp", "subgraph", crossK8sCluster, true), "--query", exportSQL, "--progress")
	cmd.Dir = *ExportWorkDir
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		logger.Warnf("get stderr pipe failed, err: %v", err)
		return err
	}
	if err := cmd.Start(); err != nil {
		logger.Warnf("export start failed, command: %s, err: %v", cmd.String(), err)
		return err
	}
	scanner := bufio.NewScanner(stderr)
	for scanner.Scan() {
		line := scanner.Text()
		if ExportErrRegexp.MatchString(line) {
			matches := ExportErrRegexp.FindStringSubmatch(line)
			code, _ := strconv.Atoi(matches[1])
			err = errors.Errorf("code: %d, message: %s", code, matches[2])
			logger.Warnf("export failed, command: %s, err: %v", cmd.String(), err)
			return err
		} else {
			logger.Infof("export progress: %s", line)
		}
	}
	if err = cmd.Wait(); err != nil {
		logger.Warnf("export wait failed, command: %s, err: %v", cmd.String(), err)
		return err
	}
	return nil
}

func (s *SgViewer) GetSchema() event.Schema {
	return nil
}

func (s *SgViewer) GetConn() event.Conn {
	return s.conn
}

func (s *SgViewer) GetTableName() string {
	return ""
}

func (s *SgViewer) ShowTables(ctx context.Context, tableNamePrefix string) ([]string, error) {
	statements := fmt.Sprintf(`SHOW TABLES LIKE '%s_%%'`, tableNamePrefix)
	log.Debugf("show tables, statements: %s", statements)
	rows, err := s.conn.GetClickhouseConn().Query(ctx, statements)
	if err != nil {
		log.WithContext(ctx).Warnf("show views failed, err: %v", err)
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()
	var views []string
	for rows.Next() {
		var view string
		if err := rows.Scan(&view); err != nil {
			log.WithContext(ctx).Warnf("scan view failed, err: %v", err)
			continue
		}
		views = append(views, view)
	}
	if err = rows.Err(); err != nil {
		log.WithContext(ctx).Warnf("scan rows failed, err: %v", err)
		return nil, NewErrorWrapper(err)
	}
	return views, nil
}

func (s *SgViewer) ShowDatabases(ctx context.Context) ([]string, error) {
	statements := `SHOW DATABASES`
	rows, err := s.conn.GetClickhouseConn().Query(ctx, statements)
	if err != nil {
		log.WithContext(ctx).Warnf("show databases failed, err: %v", err)
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()
	var databases []string
	for rows.Next() {
		var database string
		if err := rows.Scan(&database); err != nil {
			log.WithContext(ctx).Warnf("scan database failed, err: %v", err)
			continue
		}
		databases = append(databases, database)
	}
	if err = rows.Err(); err != nil {
		log.WithContext(ctx).Warnf("scan values failed, err: %v", err)
		return nil, NewErrorWrapper(err)
	}
	return databases, nil
}

func (s *SgViewer) DescribeTable(ctx context.Context, tableName string) (map[string]string, error) {
	stmt := fmt.Sprintf("DESCRIBE TABLE %s", tableName)
	rows, err := s.conn.GetClickhouseConn().Query(ctx, stmt)
	if err != nil {
		log.WithContext(ctx).Warnf("describe table failed, err: %v", err)
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()

	var columns = make(map[string]string)
	for rows.Next() {
		var name, typ string
		var placeholder1, placeholder2, placeholder3, placeholder4, placeholder5 string
		if err := rows.Scan(&name, &typ, &placeholder1, &placeholder2, &placeholder3, &placeholder4, &placeholder5); err != nil {
			log.WithContext(ctx).Warnf("scan table failed, err: %v", err)
			continue
		}
		columns[name] = typ
	}
	if err = rows.Err(); err != nil {
		log.WithContext(ctx).Warnf("scan rows failed, err: %v", err)
		return nil, NewErrorWrapper(err)
	}
	return columns, nil
}
