package clickhouse

import (
	"bufio"
	"context"
	_ "embed"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"sentioxyz/sentio/common/clickhouse/builder"
	"sentioxyz/sentio/common/clickhouse/cursor"
	"sentioxyz/sentio/common/clickhouse/metadata"
	chmodels "sentioxyz/sentio/common/clickhouse/models"
	"sentioxyz/sentio/common/clickhouse/roles"
	"sentioxyz/sentio/common/clickhouse/schema"
	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/common/log"
	"sentioxyz/sentio/service/analytic/clients"
	"sentioxyz/sentio/service/analytic/protos"
	protosanalytic "sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/analytic/util"
	processormodels "sentioxyz/sentio/service/processor/models"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

var (
	ErrSchemaNotFound   = errors.New("schema not found")
	ErrRewriteFailed    = errors.New("rewrite failed")
	ErrSaveCursorFailed = errors.New("save cursor failed")

	sentioViewInitialized      = make(map[string]bool)
	sentioViewInitializedMutex = sync.RWMutex{}
)

type Viewer[T event.Row] struct {
	*Executor
	processorID string
	processor   *processormodels.Processor
	schema      *schema.Schema
	tableName   string
	row         T
}

func NewEventViewer[T event.Row](
	ctx context.Context, conn event.Conn, connsByUser map[string]event.Conn,
	processor *processormodels.Processor,
	metadataClient metadata.MetadataClient,
	schemaLoadMode SchemaLoadMode) event.ClickhouseViewer {
	if conn == nil {
		log.WithContext(ctx).Error("clickhouse conn is nil, need init first")
		return nil
	}
	var defaultRow T
	v := &Viewer[T]{
		Executor:    NewExecutor(metadataClient, conn, connsByUser),
		processorID: processor.ID,
		processor:   processor,
		schema:      nil,
		tableName:   defaultRow.TableName(processor),
		row:         defaultRow,
	}
	s, err := v.loadSchema(ctx, schemaLoadMode)
	if err != nil {
		if !errors.Is(err, ErrSchemaNotFound) {
			log.WithContext(ctx).Infof("get schema failed, err: %v", err)
		}
	}
	if s != nil {
		v.schema = s
	} else {
		v.schema = schema.NewTemplateSchema(defaultRow)
	}
	return v
}

func (v *Viewer[T]) signCtx(ctx context.Context) context.Context {
	if callSign := util.GetClickhouseCtxDataCallSign(ctx); callSign != "" {
		ctx = clickhouse.Context(ctx, clickhouse.WithSettings(
			map[string]any{
				"log_comment": callSign,
			}))
	}
	return ctx
}

func (v *Viewer[T]) loadSchema(ctx context.Context, schemaLoadMode SchemaLoadMode) (s *schema.Schema, err error) {
	err = ErrSchemaNotFound
	s = nil
	if processormodels.IsMockProcessorID(v.processorID) {
		return schema.CopySchema(chmodels.LogEventSchemaTemplate), nil
	}
	if schemaLoadMode&SchemaFromMetadata != 0 {
		s = v.loadSchemaFromMetadata(ctx)
		if s != nil {
			log.WithContext(ctx).Debugf("load schema from metadata, processor_id: %s", v.processorID)
			return s, nil
		}
	}
	if schemaLoadMode&SchemaFromDB != 0 {
		s, err = v.loadSchemaFromClickhouse(ctx)
		if err != nil {
			log.WithContext(ctx).Infof("load schema from clickhouse failed, err: %v", err)
			return nil, err
		}
		log.WithContext(ctx).Debugf("load schema from clickhouse, processor_id: %s", v.processorID)
		return s, nil
	}
	return s, err
}

func (v *Viewer[T]) loadSchemaFromMetadata(ctx context.Context) *schema.Schema {
	if v.schema != nil {
		return v.schema
	}
	schemaBytes, err := v.metadataClient.Get(ctx, metadataKey(v.tableName, v.processorID))
	if err == nil {
		s := schema.NewSchema()
		if err := s.Load(schemaBytes); err == nil {
			v.schema = s
			return s
		} else {
			log.WithContext(ctx).Errorf("load schema failed, processor_id: %s, err: %v", v.processorID, err)
		}
	} else {
		log.WithContext(ctx).Debugf("load schema from metadata failed, processor_id: %s, err: %v", v.processorID, err)
	}
	return nil
}

func (v *Viewer[T]) loadSchemaFromClickhouse(ctx context.Context) (*schema.Schema, error) {
	rows, err := v.conn.GetClickhouseConn().Query(ctx, `SHOW TABLES LIKE ?`, v.tableName)
	if err != nil {
		log.Warnf("show tables failed, err: %v", err)
		return nil, ErrSchemaNotFound
	}
	defer func() {
		_ = rows.Close()
	}()
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			log.Warnf("scan table failed, err: %v", err)
			return nil, ErrSchemaNotFound
		}
		if tableName == v.tableName {
			s, err := rebuildSchema[T](ctx, v.conn, v.processorID, v.tableName, v.processor.EventlogVersion)
			if err == nil {
				// viewer save schema have ttl, avoid invalid processor_id in metadata
				_ = v.metadataClient.SaveTTL(ctx, metadataKey(v.tableName, v.processorID), s.Dump(), time.Hour)
			}
			return s, err
		}
	}
	return nil, ErrSchemaNotFound
}

func (v *Viewer[T]) GetSchema() event.Schema {
	return v.schema
}

func (v *Viewer[T]) GetConn() event.Conn {
	return v.conn
}

func (v *Viewer[T]) GetTableName() string {
	return v.tableName
}

func (v *Viewer[T]) ShowTables(ctx context.Context, tableNamePrefix string) ([]string, error) {
	ctx = v.signCtx(ctx)
	statements := fmt.Sprintf(`SHOW TABLES LIKE '%s_%%'`, tableNamePrefix)
	log.Debugf("show tables, statements: %s", statements)
	rows, err := v.conn.GetClickhouseConn().Query(ctx, statements)
	if err != nil {
		log.WithContext(ctx).Warnf("show views failed, err: %v", err)
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()
	var views []string
	for rows.Next() {
		var view string
		if err := rows.Scan(&view); err != nil {
			log.WithContext(ctx).Warnf("scan view failed, err: %v", err)
			continue
		}
		views = append(views, view)
	}
	if err = rows.Err(); err != nil {
		log.WithContext(ctx).Warnf("scan rows failed, err: %v", err)
		return nil, NewErrorWrapper(err)
	}
	return views, nil
}

func (v *Viewer[T]) ShowDatabases(ctx context.Context) ([]string, error) {
	ctx = v.signCtx(ctx)
	statements := `SHOW DATABASES`
	rows, err := v.conn.GetClickhouseConn().Query(ctx, statements)
	if err != nil {
		log.WithContext(ctx).Warnf("show databases failed, err: %v", err)
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()
	var databases []string
	for rows.Next() {
		var database string
		if err := rows.Scan(&database); err != nil {
			log.WithContext(ctx).Warnf("scan database failed, err: %v", err)
			continue
		}
		databases = append(databases, database)
	}
	if err = rows.Err(); err != nil {
		log.WithContext(ctx).Warnf("scan values failed, err: %v", err)
		return nil, NewErrorWrapper(err)
	}
	return databases, nil
}

func (v *Viewer[T]) DescribeTable(ctx context.Context, tableName string) (map[string]string, error) {
	ctx = v.signCtx(ctx)
	stmt := fmt.Sprintf("DESCRIBE TABLE %s", tableName)
	rows, err := v.conn.GetClickhouseConn().Query(ctx, stmt)
	if err != nil {
		log.WithContext(ctx).Warnf("describe table failed, err: %v", err)
		return nil, err
	}
	defer func() {
		_ = rows.Close()
	}()

	var columns = make(map[string]string)
	for rows.Next() {
		var name, typ string
		var placeholder1, placeholder2, placeholder3, placeholder4, placeholder5 string
		if err := rows.Scan(&name, &typ, &placeholder1, &placeholder2, &placeholder3, &placeholder4, &placeholder5); err != nil {
			log.WithContext(ctx).Warnf("scan table failed, err: %v", err)
			continue
		}
		columns[name] = typ
	}
	if err = rows.Err(); err != nil {
		log.WithContext(ctx).Warnf("scan rows failed, err: %v", err)
		return nil, NewErrorWrapper(err)
	}
	return columns, nil
}

func (v *Viewer[T]) viewExists(ctx context.Context, eventNames []string) bool {
	ctx = v.signCtx(ctx)
	viewNamePrefix := v.row.ViewNamePrefix(v.processorID, v.schema.Hash())
	viewNames, err := v.ShowTables(ctx, viewNamePrefix)
	if err != nil || (len(eventNames) != 0 && len(eventNames) != len(viewNames)) {
		log.WithContext(ctx).Infof("need rebuild view"+
			", view_name_prefix: %s, event_names: %s, exists_views: %s",
			viewNamePrefix, eventNames, viewNames)
		return false
	}

	viewNameMap := map[string]struct{}{}
	for _, viewName := range viewNames {
		viewNameMap[viewName] = struct{}{}
	}
	for _, eventName := range eventNames {
		viewName := v.row.ViewName(v.processorID, v.schema.Hash(), eventName)
		if _, ok := viewNameMap[viewName]; !ok {
			log.WithContext(ctx).Infof("need rebuild view"+
				", view_name_prefix: %s, event_names: %s, exists_views: %s",
				viewNamePrefix, eventNames, viewNames)
			return false
		}
	}

	return true
}

func (v *Viewer[T]) PrepareExecute(ctx context.Context,
	conn event.Conn, args map[string]any) (err error) {
	var fieldNames []string
	if v.schema != nil {
		for f := range v.schema.InvertedIndex[v.row.ViewSplitKey()] {
			fieldNames = append(fieldNames, f)
		}
	}
	args[chmodels.ProcessorID] = v.processorID
	args[chmodels.SchemaHash] = v.schema.Hash()
	args[chmodels.EventNames] = fieldNames
	args[chmodels.Database] = conn.GetDatabaseName()

	sentioViewInitializedMutex.RLock()
	var exists = sentioViewInitialized[v.processorID]
	sentioViewInitializedMutex.RUnlock()
	switch {
	case !exists:
		err = rebuildViews[T](ctx, conn, v.tableName, v.schema, v.processor, args)
	default:
		var rebuild = !v.viewExists(ctx, fieldNames)
		if rebuild {
			err = rebuildViews[T](ctx, conn, v.tableName, v.schema, v.processor, args)
		}
	}
	if err != nil {
		return err
	}
	sentioViewInitializedMutex.Lock()
	defer sentioViewInitializedMutex.Unlock()
	sentioViewInitialized[v.processorID] = true
	return nil
}

func (v *Viewer[T]) rewrite(ctx context.Context, args event.ExecuteArgs, cursorMeta *cursor.Metadata, argv ...interface{}) (string, error) {
	ctx, logger := log.FromContext(ctx)
	if len(argv) > 0 && len(argv)%2 == 0 {
		logger = logger.With(argv...)
	}

	if args.Rewriter() == nil {
		logger.Warnf("rewriter v3 is not supported")
		return "", errors.New("rewriter v3 is not supported")
	}
	req := &clients.RewriteSQLRequest{}
	if cursorMeta != nil {
		v.cursor = cursorMeta.Cursor
		req.Sql = cursorMeta.SQL
		setLimitOffset(req, int32(v.cursor.GetLimit()), int32(v.cursor.GetOffset()), 0)
		setSettings(req, args.Sign(), args.QuerySettings())
	} else {
		if !args.RewriteOnly() {
			if limit := args.Limit(); limit > 0 {
				v.cursor = cursor.NewInfiniteCursorWithStep(limit)
				setLimitOffset(req, int32(v.cursor.GetLimit()), int32(v.cursor.GetOffset()), int32(limit))
			} else {
				v.cursor = cursor.NewCursor(nil, nil)
				setLimitOffset(req, int32(v.cursor.GetLimit()), int32(v.cursor.GetOffset()), 10000)
			}
		} else {
			setLimitOffset(req, int32(*UserMvLimit), 0, 0)
		}
		if args.Parameters() != nil && len(args.Parameters().Fields) > 0 {
			req.Sql = builder.FormatSQLTemplateWithOptions(args.SQL(), richStructParameterToMap(args.Parameters()),
				builder.WithParameterIdentity("$", ""),
				builder.WithParameterIdentity("${", "}"))
		} else {
			req.Sql = args.SQL()
		}
		setSettings(req, args.Sign(), args.QuerySettings())
		setCommonTableExpr(req, req.Sql, args.CommonTableExprArgs())
		tableWithDatabaseMapping := make(map[string]*clients.RewriteTableNameArgs_TableWithDatabase)
		remoteMapping := make(map[string]*clients.RewriteTableNameArgs_RemoteTable)
		if v.schema != nil {
			for tableName := range v.schema.InvertedIndex[v.row.ViewSplitKey()] {
				newName := v.row.ViewName(v.processorID, v.schema.Hash(), tableName)
				if !args.InternalCall() && !tableNameValidate[T](ctx, newName, args.Repo(), args.AuthManager(), args.Validator()) {
					continue
				}
				tableWithDatabaseMapping[tableName] = &clients.RewriteTableNameArgs_TableWithDatabase{
					Table: newName,
				}
			}
		}
		if t := args.DatabaseTableMapping(); len(t) > 0 {
			for k, v := range t {
				tableWithDatabaseMapping[k] = &clients.RewriteTableNameArgs_TableWithDatabase{
					Database: v.Database(),
					Table:    v.Table(),
				}
			}
		}
		if remoteArgsMapping := args.RemoteArgsMapping(); len(remoteArgsMapping) > 0 {
			for k, v := range remoteArgsMapping {
				remoteMapping[k] = &clients.RewriteTableNameArgs_RemoteTable{
					Addr:     v.Host(),
					Database: v.Database(),
					Table:    v.Table(),
					User:     v.Username(),
					Password: v.Password(),
				}
			}
		}
		req.Options = append(req.Options, &clients.RewriteOption{
			Op: clients.RewriteOp_TableNameRewrite,
			Value: &clients.RewriteOption_TableNameArgs{
				TableNameArgs: &clients.RewriteTableNameArgs{
					RemoteTableMap:       remoteMapping,
					TableWithDatabaseMap: tableWithDatabaseMapping,
				},
			},
		})
	}
	resp, err := args.Rewriter().Rewrite(ctx, req)
	if err != nil {
		logger.Warnf("call rewriter service failed, err: %v", err)
		return "", err
	}
	switch resp.Code {
	case clients.RewriteCode_Success:
		return resp.SqlAfterRewrite, nil
	case clients.RewriteCode_SyntaxError:
		logger.Warnf("rewriter has syntax parse failed, err: %s", resp.Message)
		return "", NewErrorWrapper(fmt.Errorf("code: 62, %s", resp.Message))
	case clients.RewriteCode_RewriteError:
		logger.Warnf("rewriter has rewrite failed, err: %s", resp.Message)
		return "", NewErrorWrapper(fmt.Errorf("code: 1001, %s", resp.Message))
	}
	return "", errors.New("rewriter has unknown error")
}

func (v *Viewer[T]) clickhouseCtx(ctx context.Context, conn event.Conn, queryID string,
	additionalSettings clickhouse.Settings) context.Context {
	baseSettings := conn.GetContextSettings()
	var mergedSettings = make(clickhouse.Settings)
	for k, v := range baseSettings {
		if _, ok := additionalSettings[k]; !ok {
			mergedSettings[k] = v
		}
	}
	var queryOptions []clickhouse.QueryOption
	if queryID != "" {
		queryOptions = append(queryOptions, clickhouse.WithQueryID(queryID))
	}
	for k, v := range additionalSettings {
		mergedSettings[k] = v
	}
	queryOptions = append(queryOptions, clickhouse.WithSettings(mergedSettings))
	tracer := otel.Tracer("clickhouse")
	ctx, _ = tracer.Start(ctx, "operation")
	spanCtx := trace.SpanContextFromContext(ctx)
	queryOptions = append(queryOptions, clickhouse.WithSpan(spanCtx))
	return clickhouse.Context(ctx, queryOptions...)
}

func (v *Viewer[T]) Execute(ctx context.Context, args event.ExecuteArgs) event.ExecuteResult {
	result := ExecuteResult{
		rows:    [][]any{},
		columns: []event.ColumnType{},
		cursor:  "",
		sql:     "",
	}
	var cursorMeta *cursor.Metadata
	if cursorStr := args.Cursor(); cursorStr != "" {
		var err error
		cursorMeta, err = v.loadCursorMeta(ctx, cursorStr)
		if err != nil {
			result.err = NewErrorWrapper(ErrCursorNotFound)
			return result
		}
		if cursorMeta == nil {
			result.err = NewErrorWrapper(errors.Errorf("cursor has unknown format"))
			return result
		}
	}
	clickhouseCtx := v.clickhouseCtx(ctx, v.conn, args.QueryID(), args.QuerySettings())
	var user string
	switch args.ExecuteEngine() {
	case protosanalytic.ExecuteEngine_SMALL, protosanalytic.ExecuteEngine_LITE:
		user = roles.SentioSmallViewConn
	case protosanalytic.ExecuteEngine_MEDIUM, protosanalytic.ExecuteEngine_PRO:
		user = roles.SentioMediumViewConn
	case protosanalytic.ExecuteEngine_LARGE, protosanalytic.ExecuteEngine_MAX:
		user = roles.SentioLargeViewConn
	case protos.ExecuteEngine_ULTRA:
		user = roles.SentioUltraViewConn
	default:
		user = roles.SentioDefaultViewConn
	}
	execute := func() ([][]any, []event.ColumnType, string, error) {
		stmt, err := v.rewrite(ctx, args, cursorMeta,
			"original_sql", args.SQL())
		if err != nil {
			return nil, nil, "", err
		}
		if args.RewriteOnly() {
			return nil, nil, stmt, nil
		}
		results, columnTypes, err := v.execute(clickhouseCtx, user,
			stmt, "original_sql", args.SQL(), "rewrite_sql", stmt)
		if err != nil {
			return nil, nil, "", err
		}
		return results, columnTypes, stmt, nil
	}
	try := func(executeFunc func() ([][]any, []event.ColumnType, string, error)) (
		rows [][]any, columns []event.ColumnType, stmt, nextCursor string, completed, continuable bool, err error) {
		rows, columns, stmt, err = executeFunc()
		switch {
		case err == nil && args.RewriteOnly():
			return rows, columns, stmt, "", true, false, nil
		case err == nil && !args.RewriteOnly():
			nextCursor, executeErr := v.afterExecute(ctx, rows, stmt)
			if executeErr == nil {
				return rows, columns, stmt, nextCursor, true, false, nil
			} else {
				return nil, nil, stmt, "", false, false,
					errors.Wrapf(ErrSaveCursorFailed, "err: %v", err)
			}
		default:
			return nil, nil, "", "", false, true, err
		}
	}

	result.rows, result.columns, result.sql, result.cursor, _, _, result.err = try(execute)
	return result
}

func (v *Viewer[T]) getReservedKeywordFromDB(ctx context.Context) []string {
	var reservedKeyword = make(map[string]struct{})
	reservedKeyword[v.row.TableReservedName()] = struct{}{}
	tableNames, err := v.ShowTables(ctx, v.row.TableReservedName())
	if err == nil {
		for _, tableName := range tableNames {
			reservedKeyword[tableName] = struct{}{}
		}
	}
	viewNames, err := v.ShowTables(ctx, v.row.ViewNamePrefix())
	if err == nil {
		for _, viewName := range viewNames {
			reservedKeyword[viewName] = struct{}{}
		}
	}
	databases, err := v.ShowDatabases(ctx)
	if err == nil {
		for _, database := range databases {
			reservedKeyword[database] = struct{}{}
		}
	}
	return lo.MapToSlice(reservedKeyword, func(name string, _ struct{}) string {
		return name
	})
}

func (v *Viewer[T]) GetReservedKeyword(ctx context.Context) map[string]struct{} {
	cacheKeyword := fmt.Sprintf("%s-%s", ReservedTableNamePrefix, v.conn.GetDatabaseName())
	bytes, err := v.metadataClient.Get(ctx, cacheKeyword)
	if err != nil {
		if !errors.Is(err, metadata.ErrMetadataKeyNotFound) {
			log.WithContext(ctx).Infof("get reserved keyword from metadata failed, err: %v", err)
		}
		reservedKeywordList := v.getReservedKeywordFromDB(ctx)
		_ = v.metadataClient.SaveTTL(ctx, cacheKeyword, strings.Join(reservedKeywordList, ","), time.Minute*30)
		return lo.SliceToMap(reservedKeywordList, func(name string) (string, struct{}) {
			return name, struct{}{}
		})
	}
	tableNames := strings.Split(string(bytes), ",")
	return lo.SliceToMap(tableNames, func(name string) (string, struct{}) {
		return name, struct{}{}
	})
}

var (
	ExportErrRegexp = regexp.MustCompile(`Code: (\d+). (.*)`)

	fileInfoGetter = func(filePath string) (fileBytes uint64, rowCount int) {
		fileBytes = 0
		rowCount = 0
		file, err := os.Open(filePath)
		if err != nil {
			return
		}
		defer file.Close()

		fileInfo, err := file.Stat()
		if err != nil {
			return
		}
		fileBytes = uint64(fileInfo.Size())

		scanner := bufio.NewScanner(file)
		for scanner.Scan() {
			rowCount++
		}

		if err := scanner.Err(); err != nil {
			return
		}
		return fileBytes, rowCount
	}
)

func (v *Viewer[T]) Export(ctx context.Context, k8sContextUse string,
	shardingConn event.ShardingConn,
	progress chan event.ExportProgress, filePath, sql string, compression int) error {
	sql = strings.TrimSpace(sql)
	sql = strings.TrimSuffix(sql, ";")
	exportSQL := fmt.Sprintf("%s INTO OUTFILE '%s'", sql, filePath)
	switch compression {
	case int(event.ExportCompressionGzip):
		exportSQL = fmt.Sprintf("%s COMPRESSION '%s'", exportSQL, "gzip")
	case int(event.ExportCompressionZstd):
		exportSQL = fmt.Sprintf("%s COMPRESSION '%s'", exportSQL, "zstd")
	default:
		// do nothing
	}
	ctx, logger := log.FromContext(ctx)

	logger.Debugf("export sql: %s", exportSQL)
	var channelClosed = new(atomic.Bool)
	channelClosed.Store(false)
	var mu sync.Mutex
	defer func() {
		mu.Lock()
		defer mu.Unlock()
		channelClosed.Store(true)
		close(progress)
	}()
	var start = time.Now()
	if _, err := os.Stat(filePath); !os.IsNotExist(err) {
		return errors.Errorf("file already exists: %s", filePath)
	}
	go func() {
		for {
			mu.Lock()
			if channelClosed.Load() {
				mu.Unlock()
				break
			}
			fileBytes, rowCount := fileInfoGetter(filePath)
			progress <- Progress{
				wroteBytes: fileBytes,
				wroteRows:  uint64(rowCount),
				elapsed:    time.Since(start),
			}
			mu.Unlock()
			time.Sleep(time.Millisecond * 500)
		}
	}()
	var crossK8sCluster = k8sContextUse != shardingConn.GetK8sCluster()
	cmd := exec.CommandContext(ctx, shardingConn.GetClickhouseClientBin(),
		"client",
		shardingConn.GetDSNByDataType("tcp", "sentio", crossK8sCluster, true), "--query", exportSQL, "--progress")
	cmd.Dir = *ExportWorkDir
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,
	}
	stderr, err := cmd.StderrPipe()
	if err != nil {
		logger.Warnf("get stderr pipe failed, err: %v", err)
		return err
	}
	if err := cmd.Start(); err != nil {
		logger.Warnf("export start failed, command: %s, err: %v", cmd.String(), err)
		return err
	}
	scanner := bufio.NewScanner(stderr)
	for scanner.Scan() {
		line := scanner.Text()
		if ExportErrRegexp.MatchString(line) {
			matches := ExportErrRegexp.FindStringSubmatch(line)
			code, _ := strconv.Atoi(matches[1])
			err = errors.Errorf("code: %d, message: %s", code, matches[2])
			logger.Warnf("export failed, command: %s, err: %v", cmd.String(), err)
			return err
		} else {
			logger.Infof("export progress: %s", line)
		}
	}
	if err = cmd.Wait(); err != nil {
		logger.Warnf("export wait failed, command: %s, err: %v", cmd.String(), err)
		return err
	}
	return nil
}
