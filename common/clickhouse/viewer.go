package clickhouse

import (
	"reflect"
	"strings"
	"time"

	"sentioxyz/sentio/common/event"
	"sentioxyz/sentio/service/analytic/clients"
	protosanalytic "sentioxyz/sentio/service/analytic/protos"
	"sentioxyz/sentio/service/common/auth"
	protoscommon "sentioxyz/sentio/service/common/protos"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/samber/lo"
)

var (
	setLimitOffset = func(req *clients.RewriteSQLRequest, limit, offset, threshold int32) {
		req.Options = append(req.Options, &clients.RewriteOption{
			Op: clients.RewriteOp_OffsetRewrite,
			Value: &clients.RewriteOption_OffsetArgs{
				OffsetArgs: &clients.RewriteOffsetArgs{
					Offset: offset,
				},
			},
		})
		if threshold == 0 {
			req.Options = append(req.Options, &clients.RewriteOption{
				Op: clients.RewriteOp_LimitRewrite,
				Value: &clients.RewriteOption_LimitArgs{
					LimitArgs: &clients.RewriteLimitArgs{
						Limit: limit,
						Value: &clients.RewriteLimitArgs_ForceLimit{
							ForceLimit: limit,
						},
					},
				},
			})
		} else {
			req.Options = append(req.Options, &clients.RewriteOption{
				Op: clients.RewriteOp_LimitRewrite,
				Value: &clients.RewriteOption_LimitArgs{
					LimitArgs: &clients.RewriteLimitArgs{
						Limit: limit,
						Value: &clients.RewriteLimitArgs_ReplaceLimit_{
							ReplaceLimit: &clients.RewriteLimitArgs_ReplaceLimit{
								Threshold: threshold,
								ReplaceTo: limit,
							},
						},
					},
				},
			})
		}
	}

	setSettings = func(req *clients.RewriteSQLRequest, sign string, chSettings clickhouse.Settings) {
		var settings []*clients.RewriteSettingsArgs_Setting
		if sign != "" {
			settings = append(settings, &clients.RewriteSettingsArgs_Setting{
				Key:  "log_comment",
				Type: clients.SettingType_String,
				Value: &clients.RewriteSettingsArgs_Setting_StringValue{
					StringValue: sign,
				},
			})
		}
		for k, v := range chSettings {
			switch v.(type) {
			case string:
				settings = append(settings, &clients.RewriteSettingsArgs_Setting{
					Key:  k,
					Type: clients.SettingType_String,
					Value: &clients.RewriteSettingsArgs_Setting_StringValue{
						StringValue: v.(string),
					},
				})
			case int32, int, int8, int16, int64:
				settings = append(settings, &clients.RewriteSettingsArgs_Setting{
					Key:  k,
					Type: clients.SettingType_Int,
					Value: &clients.RewriteSettingsArgs_Setting_IntValue{
						IntValue: int32(reflect.ValueOf(v).Int()),
					},
				})
			case uint32, uint, uint8, uint16, uint64:
				settings = append(settings, &clients.RewriteSettingsArgs_Setting{
					Key:  k,
					Type: clients.SettingType_Uint64,
					Value: &clients.RewriteSettingsArgs_Setting_Uint64Value{
						Uint64Value: reflect.ValueOf(v).Uint(),
					},
				})
			case float32, float64:
				settings = append(settings, &clients.RewriteSettingsArgs_Setting{
					Key:  k,
					Type: clients.SettingType_Int,
					Value: &clients.RewriteSettingsArgs_Setting_IntValue{
						IntValue: int32(reflect.ValueOf(v).Float()),
					},
				})
			case bool:
				settings = append(settings, &clients.RewriteSettingsArgs_Setting{
					Key:  k,
					Type: clients.SettingType_Bool,
					Value: &clients.RewriteSettingsArgs_Setting_BoolValue{
						BoolValue: v.(bool),
					},
				})
			}
		}
		req.Options = append(req.Options, &clients.RewriteOption{
			Op: clients.RewriteOp_SettingsRewrite,
			Value: &clients.RewriteOption_SettingsArgs{
				SettingsArgs: &clients.RewriteSettingsArgs{
					Settings: settings,
				},
			},
		})
	}

	setCommonTableExpr = func(req *clients.RewriteSQLRequest, sql string, commonTableExprArgsMapping map[string]event.CommonTableExprArgs) {
		if len(commonTableExprArgsMapping) == 0 {
			return
		}

		var commonTableExprs []*clients.RewriteCommonTableExprArgs_CommonTableExpr
		for _, args := range commonTableExprArgsMapping {
			if strings.Contains(strings.ToLower(sql), strings.ToLower(args.Alias())) {
				commonTableExprs = append(commonTableExprs, &clients.RewriteCommonTableExprArgs_CommonTableExpr{
					Alias: args.Alias(),
					Sql:   args.SQL(),
				})
			}
		}
		if len(commonTableExprs) > 0 {
			req.Options = append(req.Options, &clients.RewriteOption{
				Op: clients.RewriteOp_CommonTableExprRewrite,
				Value: &clients.RewriteOption_CommonTableExprArgs{
					CommonTableExprArgs: &clients.RewriteCommonTableExprArgs{
						CteMap: lo.SliceToMap(commonTableExprs, func(item *clients.RewriteCommonTableExprArgs_CommonTableExpr) (string, *clients.RewriteCommonTableExprArgs_CommonTableExpr) {
							return item.Alias, item
						}),
					},
				},
			})
		}
	}
)

type RemoteArgs struct {
	host     string
	database string
	table    string
	username string
	password string
}

func NewRemoteArgs(host, database, table, username, password string) RemoteArgs {
	return RemoteArgs{
		host:     host,
		database: database,
		table:    table,
		username: username,
		password: password,
	}
}

func (r RemoteArgs) Host() string {
	return r.host
}

func (r RemoteArgs) Database() string {
	return r.database
}

func (r RemoteArgs) Table() string {
	return r.table
}

func (r RemoteArgs) Username() string {
	return r.username
}

func (r RemoteArgs) Password() string {
	return r.password
}

type DatabaseTableArgs struct {
	database string
	table    string
}

func NewDatabaseTableArgs(database, table string) DatabaseTableArgs {
	return DatabaseTableArgs{
		database: database,
		table:    table,
	}
}

func (d DatabaseTableArgs) Database() string {
	return d.database
}

func (d DatabaseTableArgs) Table() string {
	return d.table
}

type CommonTableArgs struct {
	alias string
	sql   string
}

func NewCommonTableArgs(alias, sql string) CommonTableArgs {
	return CommonTableArgs{
		alias: alias,
		sql:   sql,
	}
}

func (c CommonTableArgs) Alias() string {
	return c.alias
}

func (c CommonTableArgs) SQL() string {
	return c.sql
}

type ExecuteArgs struct {
	sql                        string
	cursor                     string
	parameters                 *protoscommon.RichStruct
	databaseTableMappings      map[string]event.DatabaseTableArgs
	remoteArgsMapping          map[string]event.RemoteArgs
	commonTableExprArgsMapping map[string]event.CommonTableExprArgs

	limit          int
	repo           event.Repository
	authManager    auth.AuthManager
	validator      event.TableValidator
	rewriterClient clients.RewriterServiceClient
	internalCall   bool
	querySettings  clickhouse.Settings
	sign           string
	queryID        string
	rewriteOnly    bool
	engine         protosanalytic.ExecuteEngine
}

func NewExecuteArgs(sql, cursor string, parameters *protoscommon.RichStruct,
	databaseTableMapping map[string]event.DatabaseTableArgs,
	remoteArgsMapping map[string]event.RemoteArgs,
	commonTableExprArgsMapping map[string]event.CommonTableExprArgs,
	limit int,
	repo event.Repository, authManager auth.AuthManager, validator event.TableValidator,
	rewriterClient clients.RewriterServiceClient, internalCall bool, querySettings clickhouse.Settings,
	sign, queryID string, rewriteOnly bool, engine protosanalytic.ExecuteEngine) ExecuteArgs {
	return ExecuteArgs{
		sql:                        sql,
		cursor:                     cursor,
		parameters:                 parameters,
		databaseTableMappings:      databaseTableMapping,
		remoteArgsMapping:          remoteArgsMapping,
		commonTableExprArgsMapping: commonTableExprArgsMapping,
		limit:                      limit,
		repo:                       repo,
		authManager:                authManager,
		validator:                  validator,
		rewriterClient:             rewriterClient,
		internalCall:               internalCall,
		querySettings:              querySettings,
		sign:                       sign,
		queryID:                    queryID,
		rewriteOnly:                rewriteOnly,
		engine:                     engine,
	}
}

func (e ExecuteArgs) SQL() string {
	return e.sql
}

func (e ExecuteArgs) Cursor() string {
	return e.cursor
}

func (e ExecuteArgs) DatabaseTableMapping() map[string]event.DatabaseTableArgs {
	if e.databaseTableMappings == nil {
		return make(map[string]event.DatabaseTableArgs)
	}
	return e.databaseTableMappings
}

func (e ExecuteArgs) Limit() int {
	return e.limit
}

func (e ExecuteArgs) Repo() event.Repository {
	return e.repo
}

func (e ExecuteArgs) AuthManager() auth.AuthManager {
	return e.authManager
}

func (e ExecuteArgs) Validator() event.TableValidator {
	return e.validator
}

func (e ExecuteArgs) Rewriter() clients.RewriterServiceClient {
	return e.rewriterClient
}

func (e ExecuteArgs) RemoteArgsMapping() map[string]event.RemoteArgs {
	if e.remoteArgsMapping == nil {
		return make(map[string]event.RemoteArgs)
	}
	return e.remoteArgsMapping
}

func (e ExecuteArgs) InternalCall() bool {
	return e.internalCall
}

func (e ExecuteArgs) QuerySettings() clickhouse.Settings {
	return e.querySettings
}

func (e ExecuteArgs) Parameters() *protoscommon.RichStruct {
	return e.parameters
}

func (e ExecuteArgs) Sign() string {
	if e.rewriteOnly {
		// rewrite only, no need to sign
		return ""
	}
	return e.sign
}

func (e ExecuteArgs) QueryID() string {
	return e.queryID
}

func (e ExecuteArgs) RewriteOnly() bool {
	return e.rewriteOnly
}

func (e ExecuteArgs) CommonTableExprArgs() map[string]event.CommonTableExprArgs {
	if e.commonTableExprArgsMapping == nil {
		return make(map[string]event.CommonTableExprArgs)
	}
	return e.commonTableExprArgsMapping
}

func (e ExecuteArgs) ExecuteEngine() protosanalytic.ExecuteEngine {
	return e.engine
}

type ExecuteResult struct {
	rows    [][]any
	columns []event.ColumnType
	cursor  string
	sql     string
	err     error
}

func (e ExecuteResult) Rows() [][]any {
	return e.rows
}

func (e ExecuteResult) Columns() []event.ColumnType {
	return e.columns
}

func (e ExecuteResult) Cursor() string {
	return e.cursor
}

func (e ExecuteResult) SQL() string {
	return e.sql
}

func (e ExecuteResult) Error() error {
	return e.err
}

type Progress struct {
	wroteRows  uint64
	wroteBytes uint64
	elapsed    time.Duration
}

func (p Progress) WroteRows() uint64 {
	return p.wroteRows
}

func (p Progress) WroteBytes() uint64 {
	return p.wroteBytes
}

func (p Progress) Elapsed() time.Duration {
	return p.elapsed
}
