numpy>=1.26.0
scipy>=1.14.0

langchain==0.3.27
openai==1.99.1
requests==2.31.0
distro==1.9.0
jiter==0.8.2
certifi==2023.7.22
tqdm==4.65.0
pydantic==2.10.6
pydantic_core==2.27.2
annotated_types==0.7.0
dataclasses-json>=0.6.0
aiohttp>=3.9.0
aiohappyeyeballs>=2.3.0
langsmith>=0.3.45
orjson>=3.10.1
requests_toolbelt==1.0.0
charset-normalizer==3.2.0
idna==3.4
urllib3==1.26.2
numexpr>=2.10.0
tenacity==8.2.2
SQLAlchemy==2.0.19
PyYAML==6.0.1
async-timeout==4.0.2
frozenlist>=1.5.0
multidict>=6.1.0
aiosignal==1.3.1
yarl>=1.10.0
propcache>=0.2.1
attrs==23.1.0
typing-inspect==0.9.0
typing_extensions==4.12.2
marshmallow==3.20.1
mypy-extensions==1.0.0
packaging==23.2
greenlet>=3.0.0
GitPython==3.1.32
gitdb==4.0.10
smmap==5.0.0
anyio>=4.5.0
certifi==2023.7.22
exceptiongroup==1.1.3
h11>=0.16.0
httpcore>=1.0.0,<2.0.0
httpx>=0.27.0
idna==3.4
# python-telegram-bot==20.4
sniffio==1.3.0
python_dateutil==2.8.2
loguru==0.7.0
dnspython==2.4.2
six==1.16.0
tiktoken==0.9.0
regex==2023.8.8
elasticsearch==8.10.0
jsonpatch==1.33
elastic_transport==8.4.0
jsonpointer>=1.9
mmh3==5.0.1
redis==5.2.1
python_dotenv==1.0.0
httpx_sse==0.4.0
pydantic_settings>=2.5.2
langchain_text_splitters>=0.3.9,<1.0.0
langchain_core>=0.3.72
langchain_community>=0.3.12,<1.0.0
langchain_openai>=0.3.11,<1.0.0
langchain-anthropic>=0.3.10,<1.0.0
anthropic>=0.49.0,<1.0
langgraph>=0.3.34,<1.0.0
langgraph-checkpoint>=2.0.10,<3.0.0
langgraph-prebuilt>=0.6
langgraph-sdk>=0.1.42,<0.2.0
xxhash>=3.5.0,<4.0.0
ormsgpack>=1.8.0,<2.0.0
langchain-mcp-adapters>=0.1.0
mcp>=1.7
python-multipart>=0.0.9
sse-starlette>=1.6.1
starlette>=0.27
uvicorn>=0.23.1
click>=7.0
typing-inspection>=0.4.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
referencing==0.36.2
rpds-py==0.25.1
claude-code-sdk>=0.0.19
protobuf>=5.28.0
grpcio>=1.62.0
zstandard>=0.22.0